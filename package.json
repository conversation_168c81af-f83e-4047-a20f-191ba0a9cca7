{"name": "wisdom-ops-project", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "dev": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@element-plus/icons-vue": "^2.1.0", "@fullcalendar/core": "^5.7.2", "@fullcalendar/daygrid": "^5.7.2", "@fullcalendar/interaction": "^5.7.2", "@fullcalendar/timegrid": "^5.7.2", "@fullcalendar/vue3": "^5.7.2", "@types/file-saver": "^2.0.3", "@types/js-cookie": "^2.2.6", "@vue/runtime-core": "^3.0.11", "@vueup/vue-quill": "^1.0.0-alpha.40", "axios": "^0.21.1", "better-scroll": "^2.5.1", "core-js": "^3.10.1", "crypto-js": "^4.1.1", "echarts": "^5.0.2", "echarts-liquidfill": "^3.0.0", "element-plus": "^2.3.5", "file-saver": "^2.0.5", "html2canvas": "^1.4.1", "js-base64": "^3.6.0", "js-cookie": "^2.2.1", "jspdf": "^2.5.1", "mitt": "^2.1.0", "postcss-px2rem": "^0.3.0", "postcss-pxtorem": "^6.0.0", "qrcode": "^1.4.4", "qrcodejs2": "^0.0.2", "qs": "^6.10.1", "short-uuid": "^4.2.0", "socket.io-client": "^4.1.2", "swiper": "^9.0.5", "vee-validate": "^3.4.8", "vue": "3.2.36", "vue-baidu-map": "^0.21.22", "vue-class-component": "^8.0.0-rc.1", "vue-monoplasty-slide-verify": "^1.1.3", "vue-router": "^4.0.8", "vue-socket.io": "^3.0.10", "vuex": "^4.0.1"}, "devDependencies": {"@types/bmapgl": "^0.0.3", "@vue/cli-plugin-babel": "^4.5.13", "@vue/cli-plugin-router": "^4.5.12", "@vue/cli-plugin-typescript": "^4.5.12", "@vue/cli-plugin-vuex": "^4.5.12", "@vue/cli-service": "^4.5.12", "@vue/compiler-sfc": "3.2.36", "fork-ts-checker-webpack-plugin": "^6.2.10", "sass": "^1.32.8", "sass-loader": "^8.0.2", "typescript": "^4.1.5", "vue-seamless-scroll": "^1.1.23"}}