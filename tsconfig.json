{
  "compilerOptions": {
    "allowJs": true,
    "target": "esnext",
    "module": "esnext",
    "strict": true,
    "jsx": "preserve",
    "importHelpers": true,
    "moduleResolution": "node",
    "experimentalDecorators": true,
    "skipLibCheck": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "sourceMap": true,
    "baseUrl": ".",
    "types": ["webpack-env"],
    "paths": {
      "@/*": ["src/*"]
    },
    "lib": ["esnext", "dom", "dom.iterable", "scripthost"]
  },
  
  "include": [
    "src/**/*.ts",
    "src/**/*.tsx",
    "src/**/*.vue",
    "tests/**/*.ts",
    "tests/**/*.tsx",
    "src/components/verifition/api/index.ts",
    "src/components/verifition/utils/util.ts",
    "src/components/verifition/utils/ase.ts",
    "src/components/verifition/utils/axios.ts",
    "src/settings.ts",
    "src/utils/socket.ts",
    "src/main.js",
    "src/directive/el-drag-dialog/drag.ts",
    "src/components/map/ComplexCustomOverlay.js",
    "src/model/point.ts",
    "src/views/Emergency/components/eventBus.js", "src/direactives/drag.js", "src/utils/eval.js",
  ],
  "exclude": ["node_modules"]
}
