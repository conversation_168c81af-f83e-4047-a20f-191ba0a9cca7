!function(){"use strict";var e=window.hostConfig||{APIHost:"https://api.bimface.com",resourceHost:"https://m.bimface.com",staticHost:"https://static.bimface.com",dataEnvType:"BIMFACE",securityApi:!0};void 0===Object.assign&&(Object.assign=function(e){if(null==e)throw new TypeError("Cannot convert undefined or null to object");for(var t=Object(e),n=1;n<arguments.length;n++){var o=arguments[n];if(null!=o)for(var r in o)Object.prototype.hasOwnProperty.call(o,r)&&(t[r]=o[r])}return t});let t=Object.freeze({Release:"Release",Debug:"Debug"}),n=Object.freeze({Normal:"Normal",DrawingView:"drawingView"}),o=Object.freeze({BIMFACE:"BIMFACE",Local:"Local"}),r=Object.freeze({zh_CN:"zh_CN",en_GB:"en_GB",sv_SE:"sv_SE",zh_TW:"zh_TW"}),a=Object.freeze({Normal:"Normal",Bake:"Bake"});window.BimfaceSDKLoaderConfig=function(){if(window.hostConfig){for(let t in window.hostConfig)e[t]=window.hostConfig[t];e.securityApi=window.hostConfig.securityApi}return{staticHost:`${e.staticHost}/api`,APIHost:e.APIHost,language:"zh_CN",viewToken:null,configuration:t.Release,dataEnvType:e.dataEnvType||"BIMFACE",viewType:n.Normal,visualStyle:a.Bake,version:"",securityApi:e.securityApi}},window.BimfaceEnvOption=o,window.BimfaceLanguageOption=r,window.BimfaceConfigrationOption=t,window.BimfaceViewTypeOption=n;let i=function(e){let t=e.split("/");return t.pop(),t.join("/")+"/"};var s=function(e,t){for(var n=0;n<t.length;n++)t[n]=e+t[n]},c=function(e){var t=e.sdkVersion,o=e.options,r=o.configuration,a=[],i=[`/${t}/${o.language}.js`,`/${t}/Application${r}.js`];return a=function(e,t){return"drawingView"==e.renderType||t.viewType==n.DrawingView}(e.metadata,e.options)?[...a,...i,`/${t}/Drawing.css`,`/${t}/bimface.bufferfly.js`,`/${t}/Drawing${r}.js`]:[...a,...i,`/${t}/Bimface.css`,`/${t}/thirdparty.js`,`/${t}/lib/loaders/BimTilesLoader.js`,`/${t}/Bimface${r}.js`],a};window.postProcessing=function(e){var n=e.metadata,o=e.options,r=e.successCb,a=c(e);s(o.staticHost,a),n.databagId=`${n.databagId}`,o.path?(n.path=o.path,n.dataPath="./"):o.resourcePath&&(n.path=o.resourcePath.replace("viewToken","")),n.sdkPath=o.sdkPath,0==a.length?r(n):d(a,(function(){if(o.build===t.Debug&&o.dataPath){let e=o.dataPath.split("/");r({databagId:e.pop(),path:e.join("/")})}r(n)}))};var d=function(e,n,o){var r=e.length,a=0,i=function(l){if(l&&"error"==l.message&&l.element.indexOf("bimface.index")>-1){a=0;var f=o.options;f.build,t.Release;var h=c(o);return s(f.staticHost,h),void d(h,n)}++a==r?n():u(e[a],i)};u(e[a],i)},l=[],u=function(e,t){if(!(l.indexOf(e.split("/").pop())>-1)){var n,o=document.getElementsByTagName("head")[0];return e.indexOf(".css")>-1?((n=document.createElement("link")).setAttribute("href",e),n.setAttribute("rel","stylesheet")):(n=document.createElement("script")).setAttribute("src",e),n.url=e,o.appendChild(n),n.addEventListener("load",(function(){l.push(this.url.split("/").pop()),t&&t({message:"success"})})),n.addEventListener("error",(function(){t&&t({element:e,message:"error"})})),n}t()};window.loadResource=d;class f extends class{constructor(e){this.indexedDB=window.indexedDB||window.webkitIndexedDB||window.mozIndexedDB||window.msIndexedDB,this.indexedDB||console.log("IndexedDB not supported"),this._db=null,this._opt=e}open(e,t){const n=e||this._opt.name,o=t||this._opt.version||1,r=this.indexedDB.open(n,o);return new Promise(((e,t)=>{r.onsuccess=t=>{this._db=r.result,e(this._db)},r.onupgradeneeded=e=>{let t=this._db=e.target.result;(this._opt.storeList||[]).forEach((e=>!t.objectStoreNames.contains(e)&&t.createObjectStore(e)))},r.onerror=e=>{t(e)}}))}getDB(){return new Promise(((e,t)=>{this._db?e(this._db):this.open().then(e).catch(t)}))}addObject(e,t,n){return new Promise(((o,r)=>{this.getDB().then((a=>{const i=a.transaction(e,"readwrite");i.objectStore(e).put(t,n).onsuccess=e=>{o(e.target.result)},i.onerror=e=>{r(e)}})).catch(r)}))}getObject(e,t){return new Promise(((n,o)=>{this.getDB().then((r=>{const a=r.transaction(e,"readonly");a.objectStore(e).get(t).onsuccess=e=>{let t=e.target.result;t?n(t):o(e)},a.onerror=e=>{o(e)}})).catch(o)}))}deleteObject(e,t){return new Promise(((n,o)=>{this.getDB().then((r=>{const a=r.transaction(e,"readwrite");a.objectStore(e).delete(t).onsuccess=e=>{n(e.target.result)},a.onerror=e=>{o(e)}})).catch(o)}))}clearStore(e){return new Promise(((t,n)=>{this.getDB().then((o=>{const r=o.transaction(e,"readwrite");r.objectStore(e).clear().onsuccess=e=>{t(e.target.result)},r.onerror=e=>{n(e)}})).catch(n)}))}deleteDB(e){return new Promise((t=>{this.indexedDB.deleteDatabase(e),t()}))}getAllKeys(e){return new Promise(((t,n)=>{this.getDB().then((o=>{const r=o.transaction(e,"readonly");r.objectStore(e).getAllKeys().onsuccess=e=>t(e.target.result),r.onerror=n})).catch(n)}))}getAll(e){return new Promise(((t,n)=>{this.getDB().then((o=>{const r=o.transaction(e,"readonly");r.objectStore(e).getAll().onsuccess=e=>t(e.target.result),r.onerror=n})).catch(n)}))}}{constructor(){super({name:"Bf_Loader",version:1,storeList:["d","t"]})}getDatabagInfo(e,t){return new Promise(((n,o)=>{t?this.getObject("d",e).then((e=>this.addTemp(e,t).then((()=>n(e))).catch(o))).catch(o):this.getObject("d",e).then(n).catch(o)}))}addDatabagInfo(e,t){return new Promise(((n,o)=>{const r=e.modelId,a=()=>Promise.all([this.addObject("d",e,r),this.addTemp(e,t)]).then(n).catch(o);this.getDatabagInfo(r).then((t=>{t.databagId!==e.databagId&&this.deleteDB(`Bf_${data.databagId}`),a()})).catch((()=>{a()}))}))}deleteDatabagInfo(e){return new Promise(((t,n)=>{this.deleteObject("d",e).then(t).catch(n)}))}addTemp(e,t){return new Promise(((n,o)=>{this.clearStore("t").then((()=>{this.addObject("t",e,t).then(n).catch(o)})).catch(o)}))}getTemp(e){return new Promise(((t,n)=>{this.getObject("t",e).then(t).catch(n)}))}deleteStorageByModelId(e){return new Promise((t=>{this.getDatabagInfo(e).then((n=>{let o="gisView"===n.renderType?`Bg_${n.modelId}`:`Bf_${n.databagId}`;Promise.allSettled([this.deleteDB(o),this.deleteDatabagInfo(e)]).then(t)})).catch(t)}))}getStoredModelIds(){return this.getAllKeys("d")}getStoredModelInfo(){return new Promise(((e,t)=>{this.getAll("d").then((t=>{let n=t.map((e=>{let{modelId:t,name:n,renderType:o}=e;return{modelId:t,name:n,type:o}}));e(n)})).catch(t)}))}}var h=function(e,t,n){if(e.metadata)return n(e.metadata);const r=o=>{if(e.enableStorage&&e.modelId){(new f).getDatabagInfo(e.modelId,e.viewToken).then((e=>n&&n(e))).catch((()=>{console.error("[BIMFACE ERROR]: failed get model info from storage"),t&&t(o)}))}else t&&t(o)};if(!e.viewToken&&"Local"!==e.dataEnvType)return void r();const a=e.dataEnvType!==o.Local,i=a&&e.securityApi;!function(e){var t,n=Object.assign({type:"get",data:null,success:null,failure:null},e);(t=window.XMLHttpRequest?new XMLHttpRequest:new ActiveXObject("Microsoft.XMLHTTP")).onreadystatechange=function(){if(4==t.readyState){var e=t.status;e>=200&&e<300||304==e||0===e&&"file:"===window.location.protocol?n.success&&n.success(t.responseText,t.responseXML):n.failure&&n.failure(e)}},t.open(n.type,n.url,n.async),t.send(n.data)}({type:i?"post":"get",url:a&&!i?`${e.url}?viewToken=${e.viewToken}`:e.url,async:!0,data:i?e.data:void 0,requestHeader:e.requestHeader,success:function(t){var o=JSON.parse(t);if("Local"!==e.dataEnvType&&"success"!==o.code)return o.message&&console.error(`[BIMFACE ERROR]: ${o.message}`),void r(t);if(o=o.data||o,e.enableStorage){(new f).addDatabagInfo(o,e.viewToken).then((()=>n&&n(o))).catch((()=>n&&n(o)))}else n&&n(o)},failure:r})},p=function(e,t,o){h(e,o,(function(o){var r=function(e,t){let o,r=t.version;if(e.renderVersion,/\d+?\.\d+?\.\d+/.test(r)&&r.split(".")[0]>=3){window.bimfaceSdkVersion=r;const[e,t,n]=r.split(".");"6"===t&&Number(n)<143&&(r=`Bimface@${r}`)}else if("Debug"==t.build)r="Bimface",o="Application";else if(t.sdkPath)r=o="bimface";else if(t.viewType==n.DrawingView&&"drawingView"!=e.renderType){var a=e.subRenders;if(a&&0!=a.length)for(var i=0;i<a.length;i++)a[i].renderType==n.DrawingView&&(r=a[i].jsSDKVersion,o=a[i].jsSDKVersion)}else r=e.jsSDKVersion,o=e.jsSDKVersion;return{ui:o,sdk:r}}(o,e);window.BimfaceLoaderConfig.fullStaticHost="Local"==BimfaceLoaderConfig.dataEnvType?e.staticHost+"/bimface":e.staticHost+"/"+r.sdk;var a={metadata:o,options:e,successCb:t,sdkVersion:r.sdk,uiVersion:r.ui},i=window.BimfaceLoaderConfig.fullStaticHost+"/bimface.index.js";d([i],(function(){postProcessing(a)}),a)}))},w=function(e,t){var n="bimView"==e.renderType?"3DView":e.renderType,o=(e.subRenders,{dataEnvType:t.dataEnvType,viewToken:t.viewToken,staticHost:t.staticHost,APIHost:t.APIHost,viewType:n});return Object.assign(o,e)},g={getStorage(){return this.storage=this.storage||new f,this.storage},deleteStorageByModelId(e){return this.getStorage().deleteStorageByModelId(e)},getStoredModelIds(){return this.getStorage().getStoredModelIds()},getStoredModelInfo(){return this.getStorage().getStoredModelInfo()},store(e){let{url:t,viewToken:n,storeMaterialOverride:o,successCallback:r,progressCallback:a,errorCallback:i,conditions:s,storeByQueue:c}=e;const d=c&&c.enable,l=d&&(c.delay||2e3);if(!n)return;t=t||[],r=r||function(){},a=a||function(){},i=i||function(){};const u=new BimfaceSDKLoaderConfig;u.enableStorage=!0,u.viewToken=n,BimfaceSDKLoader.load(u).then((e=>{if("3DView"==e.viewType){const c=document.createElement("div"),u=new Glodon.Bimface.Viewer.Viewer3DConfig;u.domElement=c,u.enableStorage=!0;const f=new Glodon.Bimface.Viewer.Viewer3D(u),h=new Promise((t=>{f.addModel(e),d||f.addEventListener(Glodon.Bimface.Viewer.Viewer3DEvent.ViewLoading,(e=>a(e.progress)));const r=()=>{requestAnimationFrame((()=>{f.destroy(),t()}))};let i=!1;f.addEventListener(Glodon.Bimface.Viewer.Viewer3DEvent.ViewAdded,(()=>{if(i)return;const e=()=>{i=!0;const e=f.getDefaultModel(),t=e.getCloudViewer().getModelManager().getModel(e.modelId),o=[...t._handler.layerProvider.getAllLayerIdxData().layerKeys],s=[...t._handler.layerProvider.getLayerKeyAttributes()];let c=0;const d={},u=()=>{if(a(Math.floor(100*(c+1)/o.length)),c===o.length)return void r();o[c].split("-").forEach(((e,t)=>d[s[t]]=e));const e=f.getDefaultModel();e&&e.destroy();const t=isNaN(l)?2e3:Number(l);setTimeout((()=>{f.addView(n)}),t),c++};f.addEventListener(Glodon.Bimface.Viewer.Viewer3DEvent.ModelAdded,(()=>{f.showExclusiveComponentsByObjectData([d],null,u)})),u()};o?f.loadMaterialOverrideSet(f.getModel().modelId,n,(function(){s?f.showExclusiveComponentsByObjectData(s,null,r):d?e():(f.showAllComponents(),f.addEventListener(Glodon.Bimface.Viewer.Viewer3DEvent.DemandLoaded,r))})):s?f.showExclusiveComponentsByObjectData(s,null,r):d?e():r()}))}));Promise.all([h,f._storeData(t)]).then(r).catch(i)}})).catch()}},m=function(){return m=Object.assign||function(e){for(var t,n=1,o=arguments.length;n<o;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},m.apply(this,arguments)};function v(e,t,n,o){return new(n||(n=Promise))((function(r,a){function i(e){try{c(o.next(e))}catch(e){a(e)}}function s(e){try{c(o.throw(e))}catch(e){a(e)}}function c(e){var t;e.done?r(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(i,s)}c((o=o.apply(e,t||[])).next())}))}function b(e,t){var n,o,r,a,i={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]};return a={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function s(a){return function(s){return function(a){if(n)throw new TypeError("Generator is already executing.");for(;i;)try{if(n=1,o&&(r=2&a[0]?o.return:a[0]?o.throw||((r=o.return)&&r.call(o),0):o.next)&&!(r=r.call(o,a[1])).done)return r;switch(o=0,r&&(a=[2&a[0],r.value]),a[0]){case 0:case 1:r=a;break;case 4:return i.label++,{value:a[1],done:!1};case 5:i.label++,o=a[1],a=[0];continue;case 7:a=i.ops.pop(),i.trys.pop();continue;default:if(!(r=i.trys,(r=r.length>0&&r[r.length-1])||6!==a[0]&&2!==a[0])){i=0;continue}if(3===a[0]&&(!r||a[1]>r[0]&&a[1]<r[3])){i.label=a[1];break}if(6===a[0]&&i.label<r[1]){i.label=r[1],r=a;break}if(r&&i.label<r[2]){i.label=r[2],i.ops.push(a);break}r[2]&&i.ops.pop(),i.trys.pop();continue}a=t.call(e,i)}catch(e){a=[6,e],o=0}finally{n=r=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,s])}}}window.hostConfig=window.hostConfig||{APIHost:"https://api.bimface.com",resourceHost:"https://m.bimface.com",staticHost:"https://static.bimface.com",dataEnvType:"BIMFACE",securityApi:!0};var y=function(){window.hostConfig=window.hostConfig||{APIHost:"https://api.bimface.com",resourceHost:"https://m.bimface.com",staticHost:"https://static.bimface.com",dataEnvType:"BIMFACE",securityApi:!0};var e=m({},window.hostConfig);return e.staticHost=window.hostConfig.staticHost+"/api",e},B=function(){function e(e){this._config=e}return e.prototype.getModuleData=function(e){var t=this;return new Promise((function(n,o){var r;(r={type:"post",data:e,url:t._config.APIHost+"/inside/databag"},new Promise((function(e,t){var n=m(m({},{type:"get",data:null}),r),o=new XMLHttpRequest;o.onreadystatechange=function(){if(4==o.readyState){var n=o.status;if(n>=200&&n<300||304==n||0===n&&"file:"===window.location.protocol){var r=void 0;try{r=JSON.parse(o.responseText)}catch(e){throw e}e({json:r,responseText:o.responseText,responseXML:o.responseXML})}else t(n)}},o.open(n.type,n.url,n.async),o.send(n.data)}))).then((function(e){e.json.data?n(e.json.data):o(e.json)}))}))},e.prototype.loadScript=function(e){return new Promise((function(t,n){var o=document.createElement("script");o.setAttribute("src",e),o.onload=function(){t(!0)},o.onerror=function(e){n(e),console.error(e)},document.getElementsByTagName("head")[0].append(o)}))},e.prototype.loadCss=function(e){return new Promise((function(t,n){var o=document.createElement("link");o.setAttribute("href",e),o.setAttribute("rel","stylesheet"),o.onload=function(){t(!0)},o.onerror=function(e){n(e),console.error(e)},document.getElementsByTagName("head")[0].append(o)}))},e.prototype.getStaticMainSrc=function(e){return this._config.staticHost+"/Glodon/"+("Debug"===this._config.build?"Bimface":e)},e.prototype.loadSDKLoader=function(){var e=this;return new Promise((function(t,n){if(window.BimfaceSDKLoader)t(!0);else{var o=e._config.staticHost+"/BimfaceSDKLoader/<EMAIL>";e.loadScript(o).then((function(){return t(!0)})).catch((function(){return n()}))}}))},e.prototype.loadUIComponents=function(e){var t=this;return new Promise((function(n,o){var r,a,i;if(null===(i=null===(a=null===(r=window.Glodon)||void 0===r?void 0:r.Bimface)||void 0===a?void 0:a.Tiles)||void 0===i?void 0:i.UI)return n(!0);var s=t.getStaticMainSrc(e)+"/bimface.ui.css";t.loadCss(s).then((function(){return n(!0)})).catch((function(){return o()}))}))},e}(),D=window.Glodon||{};window.Glodon=D;var T=D.Bimface=D.Bimface||{},S=T.Module=T.Module||{},P=function(){function e(e){this._loadedModule={},this._config=e,this._helper=new B(e)}return e.prototype.loadModule=function(e){return v(this,void 0,void 0,(function(){var t,n,o,r,a;return b(this,(function(i){switch(i.label){case 0:return t=new window.BimfaceSDKLoaderConfig,(t=m(m({},t),this._config)).viewToken=e.viewToken,[4,window.BimfaceSDKLoader.load(t).catch((function(e){return console.log(e)}))];case 1:return i.sent(),[4,this._helper.loadSDKLoader()];case 2:return i.sent(),[4,this._helper.getModuleData(e.viewToken)];case 3:return"moduleData"!=(n=i.sent()).modelType?[2,Promise.reject("[BIMFACE ERROR]: Invalid ViewToken, viewToken: ["+e.viewToken+"]")]:(n.viewToken=e.viewToken,o=n.jsSDKVersion,[4,this._helper.loadUIComponents(o)]);case 4:return i.sent(),this._loadedModule[e.moduleType]?[3,6]:(this._helper.loadCss(this._helper.getStaticMainSrc(o)+"/modules/"+e.moduleType+".css"),[4,import(this._helper.getStaticMainSrc(o)+"/modules/"+e.moduleType+".js")]);case 5:r=i.sent(),this._loadedModule[e.moduleType]=r.default,i.label=6;case 6:for(a in this._loadedModule[e.moduleType])this._loadedModule[e.moduleType][a].setCurrentModuleData&&this._loadedModule[e.moduleType][a].setCurrentModuleData(n);return[2,n]}}))}))},e}();S.ModuleManager=P,S.ModuleManagerConfig=y;var j={Version:"2023-1-11-18-11",load:function(e,n,r){window.BimfaceLoaderConfig=e,null==e.build&&(e.build=t.Release);var a=function(e){let n=Object.assign({},e),r="/Glodon";return e.path?(n.dataEnvType=o.Local,n.url=n.path,n.staticHost=n.sdkPath||i(n.path),n.resourcePath=i(n.path),n.path=i(n.path),r=n.sdkPath?"":"/jssdk"):e.resourcePath?(n.dataEnvType=o.Local,n.url=n.resourcePath,n.resourcePath=i(n.resourcePath)):(n.sdkPath&&(r="",n.staticHost=n.sdkPath),n.data=e.viewToken,n.url=`${e.APIHost}/inside/databag`),n.staticHost+=r,e.build!=t.Debug&&n.configuration!=t.Release||(n.configuration=""),n.configuration=n.configuration?`-${n.configuration.toLowerCase()}`:"",n}(e);if(!n&&!r)return new Promise(((t,n)=>{p(a,(function(n){t(w(n,e))}),n)}));p(a,(function(t){n&&n(w(t,e))}),(function(e){r&&r()}))},Storage:g};window.BimfaceSDKLoader=j}();