var Glodon = window.Glodon || {};
window.Glodon = Glodon;

var BimfaceNS = Glodon.Bimface = Glodon.Bimface || {};
var ModuleNS = BimfaceNS.Module = BimfaceNS.Module || {};

/******************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */
/* global Reflect, Promise */

var extendStatics = function(d, b) {
    extendStatics = Object.setPrototypeOf ||
        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
        function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
    return extendStatics(d, b);
};

function __extends(d, b) {
    if (typeof b !== "function" && b !== null)
        throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
    extendStatics(d, b);
    function __() { this.constructor = d; }
    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
}

var __assign = function() {
    __assign = Object.assign || function __assign(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};

function __awaiter(thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
}

function __generator(thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
}

/** @deprecated */
function __spreadArrays() {
    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;
    for (var r = Array(s), k = 0, i = 0; i < il; i++)
        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)
            r[k] = a[j];
    return r;
}

// 事件处理基类
var EventManager = /** @class */ (function () {
    function EventManager() {
        this._eventContainer = {};
    }
    // 添加监听事件
    EventManager.prototype.addEvent = function (type, fn) {
        this._eventContainer[type] = this._eventContainer[type] || [];
        this._eventContainer[type].push(fn);
        return this;
    };
    // 删除监听事件
    EventManager.prototype.removeEvent = function (type, fn) {
        if (this._eventContainer[type]) {
            var index = this._eventContainer[type].indexOf(fn);
            index >= 0 && this._eventContainer[type].splice(index, 1);
        }
        return this;
    };
    // 触发监听事件
    EventManager.prototype.fireEvent = function (type) {
        var args = [];
        for (var _i = 1; _i < arguments.length; _i++) {
            args[_i - 1] = arguments[_i];
        }
        if (type && this._eventContainer[type]) {
            __spreadArrays(this._eventContainer[type]).forEach(function (fn) {
                fn.apply(null, args);
            });
        }
        return this;
    };
    // reset
    EventManager.prototype.reset = function () {
        this._eventContainer = {};
    };
    return EventManager;
}());

// 事件处理类
var EventEmmiter = /** @class */ (function (_super) {
    __extends(EventEmmiter, _super);
    function EventEmmiter() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this._onceEvents = {};
        _this._pausedEvents = {};
        return _this;
    }
    // 增加监听事件
    EventEmmiter.prototype.on = function (type, fn) {
        return this.addEvent(type, fn);
    };
    // 删除监听事件
    EventEmmiter.prototype.off = function (type, fn) {
        var index;
        if (this._onceEvents[type]) { // once列表中查找并删除此事件
            (index = this._onceEvents[type].indexOf(fn)) >= 0 && this._onceEvents[type].splice(index, 1);
            this._onceEvents[type].length === 0 && delete this._onceEvents[type];
        }
        if (this._pausedEvents[type]) { // pause列表中查找并删除此事件
            (index = this._pausedEvents[type].indexOf(fn)) >= 0 && this._pausedEvents[type].splice(index, 1);
            this._pausedEvents[type].length === 0 && delete this._pausedEvents[type];
        }
        return this.removeEvent(type, fn);
    };
    // 触发一类监听事件
    EventEmmiter.prototype.trigger = function (type) {
        var _this = this;
        var args = [];
        for (var _i = 1; _i < arguments.length; _i++) {
            args[_i - 1] = arguments[_i];
        }
        if (!this._eventContainer[type])
            return this;
        var originEvents = __spreadArrays(this._eventContainer[type]);
        if (this._pausedEvents[type]) { // 此类监听事件中存在pause项时，另外记录完整的事件数组，并在现有的数组中删除pause项
            this._pausedEvents[type].forEach(function (fn) {
                _this.removeEvent(type, fn);
            });
        }
        this.fireEvent.apply(this, __spreadArrays([type], args));
        this._eventContainer[type] = originEvents; // 恢复完整的事件数组
        if (this._onceEvents[type]) { // 单次执行的事件，执行后从数组中删除
            this._onceEvents[type].forEach(function (fn) {
                (!_this._pausedEvents[type] || _this._pausedEvents[type].indexOf(fn) < 0) && _this.off(type, fn); // 排除once事件pause状态
            });
        }
        return this;
    };
    // 增加单次执行的事件
    EventEmmiter.prototype.once = function (type, fn) {
        this.on(type, fn);
        this._onceEvents[type] = this._onceEvents[type] || [];
        this._onceEvents[type].indexOf(fn) < 0 && this._onceEvents[type].push(fn);
        return this;
    };
    // 暂停事件
    EventEmmiter.prototype.pause = function (type, fn) {
        this._pausedEvents[type] = this._pausedEvents[type] || [];
        this._pausedEvents[type].indexOf(fn) < 0 && this._pausedEvents[type].push(fn);
        return this;
    };
    // 恢复事件
    EventEmmiter.prototype.resume = function (type, fn) {
        if (!this._pausedEvents[type])
            return this;
        var index = this._pausedEvents[type].indexOf(fn);
        index >= 0 && this._pausedEvents[type].splice(index, 1);
        this._pausedEvents[type].length === 0 && delete this._pausedEvents[type];
        return this;
    };
    EventEmmiter.prototype.reset = function () {
        this._onceEvents = {};
        this._pausedEvents = {};
        _super.prototype.reset.call(this);
    };
    return EventEmmiter;
}(EventManager));

// 此对象用于简化处理各类数据，包括判断数据类型、判断属性、及数据的各类其他操作等，待丰富
var DataUtil = {
    // 用于判断数据类型
    assertType: function (param, typeName) {
        var assert = function (fullTypeName) { return Object.prototype.toString.call(param) === "[object " + fullTypeName + "]"; };
        switch (typeName) {
            case 'obj':
            case 'Obj':
            case 'object':
            case 'Object':
                return assert('Object');
            case 'arr':
            case 'Arr':
            case 'array':
            case 'Array':
                return assert('Array');
            case 'num':
            case 'Num':
            case 'number':
            case 'Number':
                return assert('Number');
            case 'func':
            case 'Func':
            case 'function':
            case 'Function':
                return assert('Function');
            case 'str':
            case 'Str':
            case 'string':
            case 'String':
                return assert('String');
            default:
                return assert(typeName);
        }
    },
    // 判断多个参数是否都是某个数据类型（参数平铺传入，最后一个参数是类型名）
    assertParamsType: function () {
        var _this = this;
        var params = [];
        for (var _i = 0; _i < arguments.length; _i++) {
            params[_i] = arguments[_i];
        }
        if (params.length > 1) {
            var typeName_1 = params.splice(params.length - 1, 1)[0];
            var result_1 = true;
            params.every(function (param) {
                result_1 = _this.assertType(param, typeName_1);
                return result_1;
            });
            return result_1;
        }
    },
    // 判断对象是否包含某属性
    hasProperty: function (param, propertyName) {
        if (this.assertType(param, 'obj')) {
            return param[propertyName] !== undefined;
        }
    },
    // 颜色转换 hex to rgb
    hexToRgb: function (hex) {
        var result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
        return result ? {
            red: parseInt(result[1], 16),
            green: parseInt(result[2], 16),
            blue: parseInt(result[3], 16)
        } : null;
    },
    componentToHex: function (c) {
        var hex = c.toString(16);
        return hex.length == 1 ? "0" + hex : hex;
    },
    // 颜色转换 rgb to hex
    rgbToHex: function (r, g, b) {
        return "#" + this.componentToHex(r) + this.componentToHex(g) + this.componentToHex(b);
    }
};

// DomElement操作工具类
var Dom = /** @class */ (function () {
    function Dom(element) {
        this.eventMap = {};
        this.element = element;
    }
    // 创建DomElement对象（使用空的Dom对象初始化时调用）
    Dom.prototype.createElement = function (param) {
        var element = document.createElement(param.elementType);
        var className = param.className;
        if (className) {
            DataUtil.assertType(className, 'arr') && (className = className.join(' '));
            element.className = className;
        }
        param.id && (element.id = param.id);
        var domObject = new Dom(element);
        if (param.parent) {
            var parent_1 = param.parent instanceof Dom ? param.parent : new Dom(param.parent);
            parent_1.append(domObject);
        }
        domObject.visible = true;
        return domObject; // 返回一个新的以DomElement为参数创建的Dom对象以进行后续的链式操作
    };
    // 获取HTTPElement对象
    Dom.prototype.getElement = function () {
        return this.element;
    };
    // 获取父元素HTTPElement对象
    Dom.prototype.getParent = function () {
        return this.element.parentElement;
    };
    // 末尾附加一个子元素
    Dom.prototype.append = function (childElement) {
        this.element.appendChild(childElement.element);
        return this;
    };
    // 附加一个子元素到指定位置
    Dom.prototype.insert = function (childElement, index) {
        if (index !== undefined && this.element.childNodes.length > index) {
            this.element.insertBefore(childElement.element, this.element.childNodes[index]);
        }
        else {
            this.append(childElement);
        }
        return this;
    };
    // 删除一个子元素
    Dom.prototype.remove = function (childElement) {
        this.element.removeChild(childElement.element);
        return this;
    };
    // 删除所有子元素
    Dom.prototype.clear = function () {
        while (this.element.childNodes.length > 0) {
            this.element.removeChild(this.element.childNodes[0]);
        }
        return this;
    };
    // 替换元素
    Dom.prototype.replace = function (newElement, oldElement) {
        this.element.replaceChild(newElement.getElement(), oldElement.getElement());
        return this;
    };
    // 显示对象
    Dom.prototype.show = function () {
        if (this.visible)
            return this;
        this.element.style.display = this.displayType;
        this.visible = true;
        return this;
    };
    // 隐藏对象
    Dom.prototype.hide = function () {
        if (!this.visible)
            return this;
        if (this.element) {
            this.displayType = this.element.style.display;
            this.displayType === 'none' && (this.displayType = undefined);
            this.element.style.display = 'none';
            this.visible = false;
        }
        return this;
    };
    // 获取可见性
    Dom.prototype.isVisible = function () {
        return this.visible;
    };
    // 添加dom事件监听
    Dom.prototype.on = function (eventType, fn, option) {
        if (option === void 0) { option = null; }
        this.element.addEventListener(eventType, fn, option);
        this.eventMap[eventType] = this.eventMap[eventType] || [];
        this.eventMap[eventType].push(fn);
        return this;
    };
    // 删除dom事件监听
    Dom.prototype.off = function (eventType, fn) {
        this.element.removeEventListener(eventType, fn);
        if (this.eventMap[eventType]) {
            var index = this.eventMap[eventType].indexOf(fn);
            index >= 0 && this.eventMap[eventType].splice(index, 1);
        }
        return this;
    };
    // 销毁对象
    Dom.prototype.destroy = function () {
        var _this = this;
        var _loop_1 = function (eventType) {
            this_1.eventMap[eventType].forEach(function (fn) {
                _this.off(eventType, fn);
            });
        };
        var this_1 = this;
        for (var eventType in this.eventMap) {
            _loop_1(eventType);
        }
        this.element.parentElement && this.element.parentElement.removeChild(this.element);
        this.eventMap = undefined;
        this.displayType = undefined;
        this.themeType = undefined;
        this.element = undefined;
    };
    Dom.prototype.getClass = function () {
        return this.getElement().getAttribute('class');
    };
    Dom.prototype.hasClass = function (className) {
        var currentClassName = this.getClass();
        if (!currentClassName)
            return false;
        var arr = currentClassName && currentClassName.split(' ');
        if (arr.indexOf(className) > -1) {
            return true;
        }
        else {
            return false;
        }
    };
    // 添加一个或多个css class
    Dom.prototype.addClass = function () {
        var _a;
        var classes = [];
        for (var _i = 0; _i < arguments.length; _i++) {
            classes[_i] = arguments[_i];
        }
        (_a = this.element.classList).add.apply(_a, classes);
        return this;
    };
    // 删除一个或多个css class
    Dom.prototype.removeClass = function () {
        var _a;
        var classes = [];
        for (var _i = 0; _i < arguments.length; _i++) {
            classes[_i] = arguments[_i];
        }
        (_a = this.element.classList).remove.apply(_a, classes);
        return this;
    };
    Dom.prototype.toggleClass = function (className, enable) {
        this.getClass(); var hasClassName = this.hasClass(className);
        if (enable != undefined) {
            if (enable && !hasClassName) {
                this.addClass(className);
            }
            if (!enable) {
                this.removeClass(className);
            }
        }
        else {
            if (hasClassName) {
                this.removeClass(className);
            }
            else {
                this.addClass(className);
            }
        }
        return !hasClassName;
    };
    // toggle一个css class
    Dom.prototype.toggle = function (classname) {
        this.element.classList.toggle(classname);
        return this;
    };
    // 获取元素当前在页面中的位置
    Dom.prototype.getPosition = function () {
        var _a = this.element.getBoundingClientRect(), top = _a.top, left = _a.left, right = _a.right, bottom = _a.bottom;
        return { top: top, left: left, right: right, bottom: bottom };
    };
    // 获取元素的尺寸信息
    Dom.prototype.getDimensions = function () {
        var _a = this.element.getBoundingClientRect(), width = _a.width, height = _a.height;
        return { width: width, height: height };
    };
    // 获取元素宽度
    Dom.prototype.getWidth = function () {
        var width = this.element.getBoundingClientRect().width;
        return width;
    };
    // 获取元素高度
    Dom.prototype.getHeight = function () {
        var height = this.element.getBoundingClientRect().height;
        return height;
    };
    // 获取或设置元素的内容
    Dom.prototype.html = function (htmlContent) {
        if (htmlContent != undefined) {
            this.element.innerHTML = htmlContent;
            return this;
        }
        else {
            return this.element.innerHTML;
        }
    };
    // 获取单个css属性或设置单个/多个css属性
    Dom.prototype.css = function (key, value) {
        if (DataUtil.assertType(key, 'str')) {
            if (value) {
                this.element.style[key] = value;
                return this;
            }
            else {
                return this.element.style[key];
            }
        }
        else if (DataUtil.assertType(key, 'obj')) {
            var cssOptions = key;
            for (var cssKey in cssOptions) {
                this.element.style[cssKey] = cssOptions[cssKey];
            }
            return this;
        }
    };
    // 获取单个attribute属性或设置单个/多个attribute属性
    Dom.prototype.attribute = function (key, value) {
        if (DataUtil.assertType(key, 'str')) {
            if (value != undefined) {
                this.element[key] = value;
                return this;
            }
            else {
                return this.element[key];
            }
        }
        else if (DataUtil.assertType(key, 'obj')) {
            var attrOptions = key;
            for (var attrKey in attrOptions) {
                this.element[attrKey] = attrOptions[attrKey];
            }
            return this;
        }
    };
    // 获取或设置元素的主题
    Dom.prototype.theme = function (themeType) {
        if (themeType) {
            this.themeType && this.removeClass(this.themeType);
            this.themeType = themeType;
            this.addClass(themeType);
            return this;
        }
        else {
            return this.themeType;
        }
    };
    // 点击组件
    Dom.prototype.click = function () {
        this.element.click();
        return this;
    };
    return Dom;
}());

var BaseModule = /** @class */ (function () {
    function BaseModule(config) {
        this.data = {};
        this._config = config;
        this.domElement = new Dom().createElement({ elementType: 'div', className: 'bfui-app' });
        if (config.webApplication3D) {
            this.domElement.hide();
            var appDom = config.webApplication3D.getViewer()._opt.domElement;
            var parentDom = appDom.parentElement;
            this._originAppInfo = {
                replaced: false,
                parentDom: parentDom,
                app: config.webApplication3D,
            };
            config.domElement = config.domElement || parentDom;
        }
        config.domElement.appendChild(this.domElement.getElement());
        this._eventEmmiter = new EventEmmiter();
        this._moduleData = BaseModule.getCurrentModuleData();
    }
    BaseModule.prototype.getDomElement = function () {
        return this.domElement;
    };
    BaseModule.prototype.getConfig = function () {
        return this._config;
    };
    BaseModule.prototype.getModuleData = function () {
        return this._moduleData;
    };
    BaseModule.prototype.getApp3D = function () {
        return this._app3d;
    };
    BaseModule.prototype.getViewer3D = function () {
        return this._viewer3d;
    };
    BaseModule.prototype.getModelType = function () {
        return this._modelType;
    };
    BaseModule.prototype._initApp = function (options) {
        var _this = this;
        return new Promise(function (resolve, reject) {
            var app = _this.getConfig().webApplication3D;
            var successCb = function (viewMetaData) {
                viewMetaData.viewType = viewMetaData.viewType || (viewMetaData.renderType == "bimView" ? "3DView" : viewMetaData.renderType);
                var replaceAppDom = function () {
                    if (!_this._originAppInfo)
                        return;
                    _this.domElement.show();
                    _this._originAppInfo.replaced = true;
                    var currentApp = _this._originAppInfo.app;
                    currentApp.setDomElement(options.domElement);
                };
                if (viewMetaData.viewType === "drawingView") {
                    if (!app || app.getViewer().getViewerType() !== 'ViewerDrawing') {
                        var WebAppConfig = new Glodon.Bimface.Application.WebApplicationDrawingConfig();
                        WebAppConfig.domElement = options.domElement;
                        app = new Glodon.Bimface.Application.WebApplicationDrawing(WebAppConfig);
                    }
                    else {
                        replaceAppDom();
                    }
                    app.getViewer().loadDrawing({ viewMetaData: viewMetaData });
                    app.addEventListener(Glodon.Bimface.Application.WebApplicationDrawingEvent.ViewAdded, function () {
                        _this._eventEmmiter.trigger('ModuleInitialized');
                        resolve(app); // 返回具体的app对象，可在各个具体的子类中管理app
                    });
                }
                else if (viewMetaData.viewType === "3DView") {
                    if (!app || app.getViewer().getViewerType() !== 'Viewer3D') {
                        var WebAppConfig = new Glodon.Bimface.Application.WebApplication3DConfig();
                        WebAppConfig.domElement = options.domElement;
                        options.modelButtons && (WebAppConfig.Buttons = options.modelButtons);
                        app = new Glodon.Bimface.Application.WebApplication3D(WebAppConfig);
                    }
                    else {
                        replaceAppDom();
                    }
                    _this._app3d = app;
                    _this._viewer3d = app.getViewer();
                    _this._modelId = viewMetaData.modelId;
                    _this._modelType = viewMetaData.modelType;
                    var setConfigs_1 = function () {
                        // 配置信息，初始化保存在app下，便于后续统一维护
                        app.linkConfig = {
                            name: _this._viewer3d.getModels()[0]._data.name,
                            displayType: "Parallel",
                            showDrawingMaintoolbar: true,
                            showDrawingList: true,
                            showDrawingSearchTool: true,
                            showModelMaintoolbar: true,
                            showModelTreeToolbar: true,
                            showModelViewHouse: true,
                        };
                        // 链接的图纸信息
                        app.linkList = [];
                    };
                    // 预览时没有加载的模型，先加载模型
                    if (_this._viewer3d.getModels().length == 0) {
                        _this._viewer3d.loadModel({ viewMetaData: viewMetaData });
                        app.addEventListener("ModelAdded", function () {
                            setConfigs_1();
                        });
                    }
                    else {
                        setConfigs_1();
                    }
                    if (app.getViewer().getModels().filter(function (model) { return model.modelId == viewMetaData.modelId; }).length === 0) {
                        app.getViewer().loadModel({ viewMetaData: viewMetaData });
                        app.addEventListener(Glodon.Bimface.Application.WebApplication3DEvent.ModelAdded, function (modelId) {
                            if (viewMetaData.modelId == modelId) {
                                _this._eventEmmiter.trigger('ModuleInitialized');
                                _this._getFloors(modelId);
                                resolve(app);
                            }
                        });
                    }
                    else {
                        _this._getFloors(viewMetaData.modelId);
                        requestAnimationFrame(function () { return _this._eventEmmiter.trigger('ModuleInitialized'); });
                        resolve(app);
                    }
                }
            };
            var errorCb = function (e) {
                console.log(e);
                reject();
            };
            if (options.viewMetaData) {
                var metaData = __assign(__assign({}, options.viewMetaData), { viewToken: BaseModule._currentModuleData.viewToken });
                successCb(metaData);
            }
            else if (app) {
                successCb({
                    viewType: "3DView",
                    modelId: app.getViewer().getModel()._data.modelId,
                    modelType: app.getViewer().getModel()._data.modelType,
                });
            }
            else {
                var loaderConfig = new window.BimfaceSDKLoaderConfig();
                loaderConfig = __assign(__assign({}, loaderConfig), _this.getConfig());
                loaderConfig.viewToken = loaderConfig.viewToken || options.viewToken;
                window.BimfaceSDKLoader.load(loaderConfig).then(successCb).catch(errorCb);
            }
        });
    };
    BaseModule.prototype._getFloors = function (modelId) {
        var _this = this;
        this.getViewer3D().getModel(modelId).getFloors(function (data) {
            if (data) {
                _this.floors = data;
            }
        });
    };
    BaseModule.prototype.getFloors = function () {
        return this.floors;
    };
    BaseModule.prototype.addEventListener = function (type, fn) {
        this._eventEmmiter.on(type, fn);
    };
    BaseModule.prototype.removeEventListener = function (type, fn) {
        this._eventEmmiter.off(type, fn);
    };
    BaseModule.prototype.destroy = function () {
        if (this._destroyed)
            return;
        this._config = null;
        if (this._originAppInfo) {
            if (this._originAppInfo.replaced) {
                var _a = this._originAppInfo, parentDom = _a.parentDom, app = _a.app;
                app.setDomElement(parentDom);
            }
        }
        this.domElement.destroy();
        this._eventEmmiter.trigger('ModuleDestroyed');
        this._destroyed = true;
    };
    return BaseModule;
}());
BaseModule.setCurrentModuleData = function (moduleData) {
    BaseModule._currentModuleData = moduleData;
};
BaseModule.getCurrentModuleData = function () { return BaseModule._currentModuleData; };

/**
 * @namespace  Glodon.Bimface.Module.Linkage2D3D.EditorEvent
 * @classdesc EditorEvent的事件
 * @description  Glodon.Bimface.Module.Linkage2D3D.EditorEvent
 * @property {String} ModuleDestroyed 组件销毁完成事件
 * @property {String} ViewerDrawingInitialized ViewerDrawing初始化完成事件
*/
var EditorEvent;
(function (EditorEvent) {
    EditorEvent["InitModule"] = "InitModule";
    EditorEvent["LinkDrawing"] = "LinkDrawing";
    EditorEvent["AddResourcePanelShow"] = "AddResourcePanelShow";
    EditorEvent["AddResourceByViewToken"] = "AddResourceByViewToken";
    EditorEvent["RemoveResourceByViewToken"] = "RemoveResourceByViewToken";
    EditorEvent["LoadedDrawingChanged"] = "LoadedDrawingChanged";
    EditorEvent["ModuleInitialized"] = "ModuleInitialized";
    EditorEvent["ModuleDestroyed"] = "ModuleDestroyed";
    EditorEvent["ViewerDrawingInitialized"] = "ViewerDrawingAdded";
})(EditorEvent || (EditorEvent = {}));
var EditorEvent$1 = EditorEvent;

var LinkageModule = /** @class */ (function (_super) {
    __extends(LinkageModule, _super);
    function LinkageModule(config) {
        var _this = _super.call(this, config) || this;
        _this.data = {};
        // 小地图如果存在且打开，则切换图纸后先关闭再打开
        _this.reOpenMiniMap = function () {
            if (_this.getApp2D() && _this.getApp2D().UI.getToolbar('MainToolbar') && _this.getApp2D().UI.getToolbar('MainToolbar').getControl('Map')
                && _this.getApp2D().UI.getToolbar('MainToolbar').getControl('Map')._checked) {
                _this.getApp2D().UI.getToolbar('MainToolbar').getControl('Map').setCheckedState(false);
                var openMap_1 = function () {
                    _this.getApp2D().UI.getToolbar('MainToolbar').getControl('Map').setCheckedState(true);
                    _this.getViewerDrawing().removeEventListener('Loaded', openMap_1);
                };
                _this.getViewerDrawing().addEventListener('Loaded', openMap_1);
            }
        };
        return _this;
    }
    LinkageModule.prototype.getApp2D = function () {
        return this._app2d;
    };
    LinkageModule.prototype.getViewerDrawing = function () {
        return this._viewer2d;
    };
    LinkageModule.prototype.getCurrentData = function () {
        var allLinkedFiles = this._app3d.linkList;
        var allFiles = this._app3d.fileList;
        var linkedFiles = [{
                id: this._modelId.toString(),
                type: this.getModelType(),
            }], linkedFileData = [], unlinkedFileData = [], drawingConfig = [], modelConfig = [];
        allLinkedFiles.map(function (file) {
            linkedFiles.push({
                id: file.id,
                type: "singleModel"
            });
            linkedFileData.push({
                fileId: file.id,
                fileName: file.name,
                drawingType: "Plan",
                transformation: file.matrix.elements,
                viewId: file.viewId,
                elevation: {
                    type: file.isFloor ? 'Level' : 'Elevation',
                    value: file.isFloor ? file.selectedHeight : file.baseHeight,
                },
                alignPoint: {
                    drawing: file.addedDrawingPoints,
                    model: file.addedModelPoints
                },
                section: {
                    isEnabled: file.allowSection,
                    offset: file.sectionOffset,
                },
                order: allFiles.findIndex(function (previousFile) { return (previousFile.id || previousFile.modelId) == file.id; })
            });
        });
        // 获取添加后但未链接的图纸
        if (allFiles.length > allLinkedFiles.length) {
            allFiles.map(function (file, index) {
                var fileLinked = allLinkedFiles.find(function (linkedFile) {
                    return (linkedFile.id ? linkedFile.id : linkedFile.modelId) == (file.id ? file.id : file.modelId);
                });
                if (!fileLinked) {
                    linkedFiles.push({
                        id: file.id ? file.id : file.modelId,
                        type: "singleModel"
                    });
                    unlinkedFileData.push({
                        fileId: file.id ? file.id : file.modelId,
                        fileName: file.name,
                        order: index,
                    });
                }
            });
        }
        this._app3d.linkConfig.showDrawingMaintoolbar && drawingConfig.push('MainToolbar');
        this._app3d.linkConfig.showDrawingList && drawingConfig.push('DrawingList');
        this._app3d.linkConfig.showDrawingSearchTool && drawingConfig.push('SearchToolbar');
        this._app3d.linkConfig.showModelMaintoolbar && modelConfig.push('MainToolbar');
        this._app3d.linkConfig.showModelTreeToolbar && modelConfig.push('ModelTree');
        this._app3d.linkConfig.showModelViewHouse && modelConfig.push('ViewHouse');
        this._currentData = {
            moduleType: "Linkage2D3D",
            name: this._app3d.linkConfig.name ? this._app3d.linkConfig.name.split('.')[0] : this.getViewer3D().getModels()[0]._data.name.split('.')[0],
            sources: linkedFiles,
            data: {
                version: "1.0",
                model: {
                    id: this._modelId.toString(),
                    type: this.getModelType(),
                },
                setting: {
                    // 图纸类型，"2D"为二维图纸，"3D"为三维图纸 
                    viewType: "2D",
                    // 文件列表类型，二维图纸包括"dwg"、"rvtSheet"、"pdf"，三维图纸包括"dwg"（转换为三维浏览类型的图纸）
                    fileListType: ["dwg"]
                },
                drawings: [
                    {
                        fileType: "dwg",
                        list: linkedFileData,
                        unlinkedList: unlinkedFileData,
                        displayMode: {
                            mode: this._app3d.linkConfig.displayType,
                            toolbar: {
                                drawingToolbars: drawingConfig,
                                modelToolbars: modelConfig
                            }
                        }
                    }
                ],
            }
        };
        return this._currentData;
    };
    LinkageModule.prototype.setCurrentData = function (drawings) {
        this._app3d.originDrawings = drawings;
        var files = [];
        drawings[0].list.map(function (drawing) {
            var m = new window.THREE.Matrix4();
            m.fromArray(drawing.transformation);
            files.push({
                id: drawing.fileId,
                name: drawing.fileName,
                matrix: m,
                viewId: drawing.viewId,
                isFloor: drawing.elevation.type == 'Level',
                heightType: drawing.elevation.type == 'Level' ? 'BasedFloor' : 'BasedElevation',
                selectedHeight: drawing.elevation.type == 'Level' ? drawing.elevation.value : null,
                baseHeight: drawing.elevation.type == 'Level' ? null : drawing.elevation.value,
                addedDrawingPoints: drawing.alignPoint.drawing,
                addedModelPoints: drawing.alignPoint.model,
                allowSection: drawing.section.isEnabled,
                sectionOffset: drawing.section.offset
            });
        });
        this._app3d.linkList = __spreadArrays(files);
        this._app3d.linkConfig = {
            showDrawingMaintoolbar: drawings[0].displayMode.toolbar.drawingToolbars.indexOf('MainToolbar') > -1,
            showDrawingList: drawings[0].displayMode.toolbar.drawingToolbars.indexOf('DrawingList') > -1,
            showDrawingSearchTool: drawings[0].displayMode.toolbar.drawingToolbars.indexOf('SearchToolbar') > -1,
            showModelMaintoolbar: drawings[0].displayMode.toolbar.modelToolbars.indexOf('MainToolbar') > -1,
            showModelTreeToolbar: drawings[0].displayMode.toolbar.modelToolbars.indexOf('ModelTree') > -1,
            showModelViewHouse: drawings[0].displayMode.toolbar.modelToolbars.indexOf('ViewHouse') > -1,
            initialized: true,
        };
    };
    LinkageModule.prototype.getDrawingList = function () {
        this.getCurrentData();
        return this._currentData.data.drawings;
    };
    LinkageModule.prototype.isSupportAxisGrids = function (modelId) {
        var model = this.getViewer3D().getModel(modelId);
        return model && model._data.modelType == 'singleModel' && model._manifest.Features.HasAxisGrids == true;
    };
    LinkageModule.prototype.hideModelPlugins = function () {
        if (this.getApp3D().UI.getToolbar('MainToolbar')
            && this.getApp3D().linkConfig.showModelMaintoolbar == false) {
            this.getApp3D().UI.getToolbar('MainToolbar').hide();
        }
        if (this.getApp3D().UI.getToolbar('ModelTree')
            && this.getApp3D().linkConfig.showModelTreeToolbar == false) {
            this.getApp3D().UI.getToolbar('ModelTree').hide();
        }
        if (this.getApp3D().linkConfig.showModelViewHouse == false) {
            this.getViewer3D().hideViewHouse();
        }
    };
    LinkageModule.prototype._addDrawingApp = function (options) {
        var _this = this;
        return new Promise(function (resolve, reject) {
            var successCb = function (viewMetaData) {
                var WebAppConfig = new Glodon.Bimface.Application.WebApplicationDrawingConfig();
                WebAppConfig.domElement = options.domElement;
                options.drawingButtons && (WebAppConfig.Buttons = options.drawingButtons);
                _this._app2d = new Glodon.Bimface.Application.WebApplicationDrawing(WebAppConfig);
                _this._viewer2d = _this._app2d.getViewer();
                _this._eventEmmiter.trigger(EditorEvent$1.ViewerDrawingInitialized);
                _this._viewer2d.loadDrawing({ viewMetaData: viewMetaData });
                // 隐藏列表
                if (_this.getApp3D().linkConfig.showDrawingList == false) {
                    _this.getApp3D().UI.linkedList
                        && _this.getApp3D().UI.linkedList.getDomElement().addClass('hide');
                }
                _this._viewer2d.addEventListener(Glodon.Bimface.Application.WebApplicationDrawingEvent.Loaded, function () {
                    // 存在layout的工具栏则隐藏
                    requestAnimationFrame(function () {
                        return _this._app2d.UI.getToolbar('LeftSubToolbar') && _this._app2d.UI.getToolbar('LeftSubToolbar').hide();
                    });
                    _this._app2d.hideLeftSubToolbar = true;
                    setTimeout(function () {
                        if (_this.getApp3D().linkConfig.showDrawingMaintoolbar == false) { // 隐藏主工具栏
                            _this._app2d.UI.getToolbar('MainToolbar') && _this._app2d.UI.getToolbar('MainToolbar').hide();
                        }
                        else { // 居中主工具栏
                            _this._app2d.UI.getToolbar('MainToolbar') && _this._app2d.UI.getToolbar('MainToolbar').element.removeClass('bf-toolbar-bottom-float-right');
                        }
                        // 隐藏搜索栏
                        if (_this.getApp3D().linkConfig.showDrawingSearchTool == false) {
                            _this._app2d.UI.getToolbar('SearchToolbar') && _this._app2d.UI.getToolbar('SearchToolbar').hide();
                        }
                        delete _this._app2d.hideLeftSubToolbar;
                    }, 10);
                    resolve(_this._app2d);
                });
            };
            var errorCb = function (e) {
                console.log(e);
                reject();
            };
            if (options.viewMetaData) {
                successCb(options.viewMetaData);
            }
            else {
                var loaderConfig = new window.BimfaceSDKLoaderConfig();
                loaderConfig = __assign(__assign({}, loaderConfig), _this.getConfig());
                loaderConfig.viewToken = options.viewToken;
                loaderConfig.viewType = 'drawingView';
                window.BimfaceSDKLoader.load(loaderConfig).then(successCb).catch(errorCb);
            }
        });
    };
    LinkageModule.prototype.changeDrawing = function (option) {
        var _this = this;
        if (this._viewer2d) {
            this._viewer2d.removeAllDrawings();
            // 用于卸载加载图纸，防止有图纸加载中导致多个图纸被加载
            this._drawingModelId = option.id;
            // 编辑时使用token，预览使用viewMetaData
            if (option.viewToken) {
                this._viewer2d.loadDrawing({ viewToken: option.viewToken, modelId: this._drawingModelId });
            }
            else {
                this._viewer2d.loadDrawing({ viewMetaData: option.viewMetaData, modelId: this._drawingModelId });
            }
            this._viewer2d.addEventListener('Loaded', function () {
                // 卸载其余可能加载中未被卸载的图纸
                if (_this._drawingModelId && _this._viewer2d.loadedDrawings.length > 1) {
                    _this._viewer2d.loadedDrawings.forEach(function (drawing) {
                        if (drawing.modelId != _this._drawingModelId)
                            _this._viewer2d.removeDrawing(drawing.modelId, true);
                    });
                }
                setTimeout(function () {
                    _this._viewer2d.home();
                }, 100);
            });
        }
        else {
            this._addDrawingApp(option);
        }
    };
    LinkageModule.prototype.getModelId = function () {
        return this._modelId;
    };
    LinkageModule.prototype.getUnit = function () {
        return this._viewer3d.getUnit();
    };
    return LinkageModule;
}(BaseModule));

var UIEvents;
(function (UIEvents) {
    UIEvents["ValueChanged"] = "ValueChanged";
    UIEvents["Clicked"] = "Clicked";
    UIEvents["RightClicked"] = "RightClicked";
    UIEvents["MouseOver"] = "MouseOver";
    UIEvents["MouseOut"] = "MouseOut";
    UIEvents["CheckedChanged"] = "CheckedChanged";
    UIEvents["SelectionChanged"] = "SelectionChanged";
    UIEvents["ExpendChanged"] = "ExpendChanged";
    UIEvents["IsolateChanged"] = "IsolateChanged"; // 是否隔离
})(UIEvents || (UIEvents = {}));
var UIEvents$1 = UIEvents;

/**
 * @namespace Glodon.Bimface.Tiles.UI.ControlAnchor
 * @classdesc 常量：UI锚点（用以进行控件定位）
 * @description Glodon.Bimface.Tiles.UI.ControlAnchor
 * @property {String} TopLeft 视图左上角
 * @property {String} TopCenter 视图中上侧
 * @property {String} TopRight 视图右上角
 * @property {String} MiddleLeft 视图左中侧
 * @property {String} MiddleRight 视图右中侧
 * @property {String} BottomLeft 视图左下角
 * @property {String} BottomCenter 视图中下侧
 * @property {String} BottomRight 视图右下角
 * @property {String} MiddleCenter 视图中心
 */
var ControlAnchor;
(function (ControlAnchor) {
    ControlAnchor["TopLeft"] = "TopLeft";
    ControlAnchor["TopCenter"] = "TopCenter";
    ControlAnchor["TopRight"] = "TopRight";
    ControlAnchor["MiddleLeft"] = "MiddleLeft";
    ControlAnchor["MiddleRight"] = "MiddleRight";
    ControlAnchor["BottomLeft"] = "BottomLeft";
    ControlAnchor["BottomCenter"] = "BottomCenter";
    ControlAnchor["BottomRight"] = "BottomRight";
    ControlAnchor["MiddleCenter"] = "MiddleCenter";
})(ControlAnchor || (ControlAnchor = {}));
var ControlAnchor$1 = ControlAnchor;

// UI组件基类
var Control = /** @class */ (function (_super) {
    __extends(Control, _super);
    function Control(param) {
        var _this = _super.call(this) || this;
        _this._domElement = param.element || new Dom().createElement(param.elementParam);
        if (param.parent) {
            var parentElement = param.parent instanceof Control ? param.parent._domElement
                : param.parent instanceof Dom ? param.parent : new Dom(param.parent);
            parentElement.append(_this._domElement);
            param.parent instanceof Control && (_this._parent = param.parent);
        }
        _this.id = param.id; // || uuid
        _this.type = param.type;
        _this.position = { anchor: 'TopLeft', offset: { x: 0, y: 0 } };
        return _this;
    }
    //
    Control.prototype.getParent = function () {
        return this._parent;
    };
    // 设置本对象的父级Control对象
    Control.prototype.setParent = function (control) {
        this._parent = control;
    };
    // 获取DomElement对象
    Control.prototype.getDomElement = function () {
        return this._domElement;
    };
    // 设置点击事件
    Control.prototype.onClick = function (fn) {
        var _this = this;
        this._domElement.on('click', function (eventData) {
            fn(eventData);
            _this.trigger(UIEvents$1.Clicked, _this);
        });
        return this;
    };
    // 设置鼠标移入事件
    Control.prototype.onMouseOver = function (fn) {
        var _this = this;
        this._domElement.on('mouseover', function (eventData) {
            fn(eventData);
            _this.trigger(UIEvents$1.MouseOver, _this);
        });
        return this;
    };
    // 设置鼠标移出事件
    Control.prototype.onMouseOut = function (fn) {
        var _this = this;
        this._domElement.on('mouseout', function (eventData) {
            fn(eventData);
            _this.trigger(UIEvents$1.MouseOut, _this);
        });
        return this;
    };
    /**
     * 显示
     * @function Glodon.Bimface.Tiles.UI.Control.prototype.show
     */
    Control.prototype.show = function () {
        this._domElement.show();
        return this;
    };
    /**
     * 隐藏
     * @function Glodon.Bimface.Tiles.UI.Control.prototype.hide
     */
    Control.prototype.hide = function () {
        this._domElement.hide();
        return this;
    };
    /**
     * 获取可见性状态
     * @function Glodon.Bimface.Tiles.UI.Control.prototype.isVisible
     * @returns {Boolean} 可见性状态
     */
    Control.prototype.isVisible = function () {
        return this._domElement.isVisible();
    };
    // 销毁
    Control.prototype.destroy = function () {
        this._domElement.destroy();
    };
    /**
     * 对组件增加CSS样式
     * @function Glodon.Bimface.Tiles.UI.Control.prototype.addClass
     * @param {String} class CSS类名
     */
    Control.prototype.addClass = function () {
        var _a;
        var classes = [];
        for (var _i = 0; _i < arguments.length; _i++) {
            classes[_i] = arguments[_i];
        }
        (_a = this._domElement).addClass.apply(_a, classes);
        return this;
    };
    /**
     * 对组件移除CSS样式
     * @function Glodon.Bimface.Tiles.UI.Control.prototype.removeClass
     * @param {String} class CSS类名
     */
    Control.prototype.removeClass = function () {
        var _a;
        var classes = [];
        for (var _i = 0; _i < arguments.length; _i++) {
            classes[_i] = arguments[_i];
        }
        (_a = this._domElement).removeClass.apply(_a, classes);
        return this;
    };
    // 删除一个或多个css class
    Control.prototype.toggleClass = function (className) {
        this._domElement.toggleClass(className);
        return this;
    };
    /**
     * 设置组件的位置信息
     * @function Glodon.Bimface.Tiles.UI.Control.prototype.setPosition
     * @param {Object} position 组件的位置信息
     * @param {Glodon.Bimface.Tiles.UI.ControlAnchor} position.anchor 定位组件位置的锚点
     * @param {Object} position.offset 基于锚点的偏移值
     * @param {Number} position.offset.x X方向偏移值，单位为px
     * @param {Number} position.offset.y Y方向偏移值，单位为px
     */
    Control.prototype.setPosition = function (position) {
        if (position.anchor && ControlAnchor$1.hasOwnProperty(position.anchor)) {
            for (var key in ControlAnchor$1) {
                this.removeClass("bfui-anchor-" + key.toLowerCase());
            }
            var anchor = position.anchor.toLowerCase();
            this.addClass("bfui-anchor-" + anchor);
            this.position.anchor = position.anchor;
        }
        this.setOffset(position.offset);
        return this;
    };
    Control.prototype.setOffset = function (offset) {
        if (offset) {
            var offsetX = offset.x || 0;
            var offsetY = offset.y || 0;
            var anchor = this.position.anchor.toLowerCase();
            this.setStyle(anchor.indexOf('right') >= 0 ? { marginRight: -offsetX + "px" } : { marginLeft: offsetX + "px" })
                .setStyle(anchor.indexOf('bottom') >= 0 ? { marginBottom: -offsetY + "px" } : { marginTop: offsetY + "px" });
            this.position.offset.x = offsetX;
            this.position.offset.y = offsetY;
        }
        return this;
    };
    Control.prototype.getPositionParam = function () {
        return this.position;
    };
    /**
     * 获取ID
     * @function Glodon.Bimface.Tiles.UI.Control.prototype.getId
     * @returns {String} ID
     */
    Control.prototype.getId = function () {
        return this.id;
    };
    // 获取Control类型
    Control.prototype.getType = function () {
        return this.type;
    };
    /**
    * 获取组件的位置信息
    * @function Glodon.Bimface.Tiles.UI.Control.prototype.getPosition
    * @returns {Object} 组件的位置信息
    */
    Control.prototype.getPosition = function () {
        return this._domElement.getPosition();
    };
    /**
   * 获取组件的尺寸信息
   * @function Glodon.Bimface.Tiles.UI.Control.prototype.getDimensions
   * @returns {Object} 组件的尺寸信息
   */
    Control.prototype.getDimensions = function () {
        return this._domElement.getDimensions();
    };
    // 获取元素宽度
    Control.prototype.getWidth = function () {
        return this._domElement.getWidth();
    };
    // 获取元素高度
    Control.prototype.getHeight = function () {
        return this._domElement.getHeight();
    };
    // 设置css style（传入多个键值对组成的对象参数）
    Control.prototype.setStyle = function (style) {
        this._domElement.css(style);
        return this;
    };
    Control.prototype.getTooltip = function () {
        return this.tooltip;
    };
    Control.prototype.setTooltip = function (tooltip) {
        this.tooltip = tooltip;
        this.getDomElement().attribute('title', tooltip);
        return this;
    };
    /**
     * 通过HTML字符串初始化Control内容，支持在字符串中插入Control对象标记，配合controlMap实现html与Control混合结构的整体初始化
     * @param {string} html Control的内容html字符串。如需插入Control对象，则在插入的位置写入<Control id="abc"></Control>作为标记，id与controlMap中的key对应
     * @param {IControlMap} controlMap 通过key、value方式提供需插入html中的各个Control对象，key与html字符串中标记处的id对应
     */
    Control.prototype.setHTML = function (html, controlMap) {
        var controlReg = /<[C,c]ontrol id="(.*?)".*?>.*?<\/[C,c]ontrol>/g;
        var computedHtml = html.replace(controlReg, function (value) {
            var execResult = controlReg.exec(value);
            controlReg.lastIndex = 0;
            var id = execResult[1];
            var replaceHtml = "<div class=\"BFUI_Control_" + id + "\"></div>";
            return replaceHtml;
        });
        var dom = this.getDomElement();
        dom.html(computedHtml);
        for (var id in controlMap) {
            var elements = dom.getElement().getElementsByClassName("BFUI_Control_" + id);
            if (elements.length === 1) {
                var element = elements[0];
                var parent_1 = new Dom(element.parentElement);
                parent_1.replace(controlMap[id].getDomElement && controlMap[id].getDomElement() || controlMap[id], new Dom(element));
            }
        }
        return this;
    };
    /**
     * 点击组件
     */
    Control.prototype.click = function () {
        this.getDomElement().click();
        return this;
    };
    return Control;
}(EventEmmiter));

var SyleUtil = /** @class */ (function () {
    function SyleUtil() {
    }
    SyleUtil.formatElementParam = function (defaultParam, customParam) {
        if (typeof customParam == 'undefined')
            return defaultParam;
        var className = defaultParam.className, customClassName = customParam.className;
        // style = customParam.style;
        // 默认类名 自定义类名
        if (customClassName) {
            className = className + " " + customClassName;
        }
        // // 类型类名
        // if(style) {
        //   className = `${className} is-${customClassName}`;
        // }
        var elementParam = window.Object.assign(defaultParam, customParam);
        elementParam.className = className;
        return elementParam;
    };
    return SyleUtil;
}());

var _a, _b, _c, _d;
var SDM = (_d = (_c = (_b = (_a = window.Glodon) === null || _a === void 0 ? void 0 : _a.Bimface) === null || _b === void 0 ? void 0 : _b.Data) === null || _c === void 0 ? void 0 : _c.StatisticsDataManager) === null || _d === void 0 ? void 0 : _d.getInstance();
var UINamespace = 'Gldon.Bimface.Tiles.UI';
var getUINamespace = function (className) {
    if (DataUtil.assertType(className, 'str') && className.length > 0) {
        className[0] !== '.' && (className = "." + className);
        return "" + UINamespace + className;
    }
    else {
        return UINamespace;
    }
};

var SNS$h = getUINamespace('Button');
/**
 * @classdesc 类：Button组件类
 * @class Glodon.Bimface.Tiles.UI.Button
 * @constructs Glodon.Bimface.Tiles.UI.Button
 * @description 构造按钮组件对象
 * @extends Glodon.Bimface.Tiles.UI.Control
 * @param {Object} option 构造按钮对象的配置项
 * @param {String} option.id 按钮对象ID
 * @param {Boolean} option.selectable 按钮是否有选中状态
 * @param {String} option.iconClass 按钮图标样式
 * @param {String} option.text 按钮对象的文本内容
 */
var Button = /** @class */ (function (_super) {
    __extends(Button, _super);
    function Button(param) {
        var _this = this;
        SDM && SDM.send(SNS$h, "bf_c_UIButton_new");
        param = param || {};
        var elementParam = SyleUtil.formatElementParam({
            elementType: 'div',
            className: 'bfui-button'
        }, param.elementParam);
        var controlParam = {
            elementParam: elementParam,
            type: 'Button',
            id: param.id,
            parent: param.parent
        };
        _this = _super.call(this, controlParam) || this;
        param.text && _this.setText(param.text);
        _this.selectable = param.selectable === true;
        _this.selectable && (_this.select = false);
        param.iconClass && _this.setIcon(param.iconClass);
        param.tooltip && _this.setTooltip(param.tooltip);
        param.clickCallback instanceof Function && _this.onClick(param.clickCallback);
        return _this;
    }
    /**
     * 获取按钮文字内容
     * @function Glodon.Bimface.Tiles.UI.Button.prototype.getText
     * @returns {String} 按钮文字内容
     */
    Button.prototype.getText = function () {
        return this._domElement.html();
    };
    /**
     * 设置按钮文字内容
     * @function Glodon.Bimface.Tiles.UI.Button.prototype.setText
     * @param {String} test 按钮文字内容
     */
    Button.prototype.setText = function (text) {
        this._domElement.html(text);
        return this;
    };
    /*
     * 设置按钮的图标
     * @function Glodon.Bimface.Tiles.UI.Button.prototype.setIcon
     * @param {String} class 按钮图标样式类名
     */
    Button.prototype.setIcon = function (iconClass) {
        if (!iconClass)
            return this;
        if (!this.icon) {
            this.icon = new Dom().createElement({ elementType: 'div', className: 'bfui-button-icon' });
            this._domElement.append(this.icon);
        }
        else {
            this.icon.removeClass(this.iconClass);
        }
        this.iconClass = iconClass;
        this.icon.addClass(iconClass);
        return this;
    };
    /**
     * 获取按钮是否允许选中
     * @function Glodon.Bimface.Tiles.UI.Button.prototype.isSelectable
     * @returns {Boolean} 按钮是否允许选中
     */
    Button.prototype.isSelectable = function () {
        return this.selectable;
    };
    /**
     * 获取按钮当前的选中状态
     * @function Glodon.Bimface.Tiles.UI.Button.prototype.isSelected
     * @returns {Boolean} 按钮当前的选中状态
     */
    Button.prototype.isSelected = function () {
        return this.select;
    };
    /**
     * 设置按钮当前的选中状态
     * @function Glodon.Bimface.Tiles.UI.Button.prototype.setSelected
     * @param {Boolean} select 按钮当前的选中状态
     */
    Button.prototype.setSelected = function (select) {
        if (!this.selectable)
            return this;
        select = select === true;
        if (select) {
            this._domElement.addClass('bfui-button-select');
        }
        else {
            this._domElement.removeClass('bfui-button-select');
        }
        this.select = select;
        return this;
    };
    return Button;
}(Control));

var SNS$g = getUINamespace('Label');
/**
 * @classdesc 类：Label组件类
 * @class Glodon.Bimface.Tiles.UI.Label
 * @constructs Glodon.Bimface.Tiles.UI.Label
 * @description 构造标签组件
 * @extends Glodon.Bimface.Tiles.UI.Control
 * @param {Object} option 构造标签的配置项
 * @param {String} option.id 标签组件ID
 * @param {String} option.text 标签的文字内容
 */
var Label = /** @class */ (function (_super) {
    __extends(Label, _super);
    function Label(param) {
        var _this = this;
        SDM && SDM.send(SNS$g, "bf_c_UILabel_new");
        param = param || {};
        var elementParam = {
            elementType: 'div',
            className: 'bfui-label'
        };
        var controlParam = {
            elementParam: elementParam,
            type: 'Label',
            id: param.id,
            parent: param.parent
        };
        _this = _super.call(this, controlParam) || this;
        param.text && _this.setText(param.text);
        param.onClick && _this.onClick(param.onClick);
        param.className && _this.addClass(param.className);
        return _this;
    }
    /**
     * 获取文字内容
     * @function Glodon.Bimface.Tiles.UI.Label.prototype.getText
     * @returns {String} 文字内容
     */
    Label.prototype.getText = function () {
        return this._domElement.html();
    };
    /**
     * 设置文字内容
     * @function Glodon.Bimface.Tiles.UI.Label.prototype.setText
     * @param {String} text 文字内容
     */
    Label.prototype.setText = function (text) {
        this._domElement.html(text);
        return this;
    };
    return Label;
}(Control));

var SNS$f = getUINamespace('Link');
//Link组件类
var Link = /** @class */ (function (_super) {
    __extends(Link, _super);
    function Link(param) {
        var _this = this;
        SDM && SDM.send(SNS$f, "bf_c_UILink_new");
        param = param || {};
        var elementParam = {
            elementType: 'a',
            className: 'bfui-link'
        };
        var controlParam = {
            elementParam: elementParam,
            type: 'Link',
            id: param.id,
            parent: param.parent
        };
        _this = _super.call(this, controlParam) || this;
        param.text && _this.setText(param.text);
        _this.setHref(param.href);
        param.onClick && _this.onClick(param.onClick);
        return _this;
    }
    // 获取文本
    Link.prototype.getText = function () {
        return this._domElement.html();
    };
    // 设置文本
    Link.prototype.setText = function (text) {
        this._domElement.html(text);
        return this;
    };
    Link.prototype.getHref = function () {
        return this.href;
    };
    Link.prototype.setHref = function (href) {
        this.href = href || 'javascript:void(0)';
        this._domElement.attribute('href', this.href);
        return this;
    };
    return Link;
}(Control));

/**
 * @classdesc 类：Tooltip组件类
 * @class Glodon.Bimface.Tiles.UI.Tooltip
 * @constructs Glodon.Bimface.Tiles.UI.Tooltip
 * @description 构造提示框组件
 * @extends Glodon.Bimface.Tiles.UI.Control
 * @param {Object} option 构造提示框的配置项
 * @param {String} option.id 提示框组件ID
 * @param {String} option.content 提示框的文字内容
 */
var Tooltip = /** @class */ (function (_super) {
    __extends(Tooltip, _super);
    function Tooltip(param) {
        var _this = this;
        var elementParam = {
            elementType: 'div',
            className: 'bfui-tooltip'
        };
        var controlParam = {
            elementParam: elementParam,
            type: 'Tooltip',
            id: param.id,
            parent: param.parent
        };
        _this = _super.call(this, controlParam) || this;
        _this.hostDom = param.hostDom;
        _this.placement = param.placement || 'right';
        _this.isFixed = param.isFixed || false;
        _this.addClass("bfui-tooltip-" + _this.placement).setHTML(param.content).hide();
        if (param.theme === 'dark') {
            _this.addClass('bfui-tooltip-dark');
        }
        param.className && _this.addClass(param.className);
        param.width && _this.getDomElement().css({ width: param.width + "px" });
        var onHover = false;
        var duration = 500;
        var hideTimeout = null;
        var hideWhenLeave = function () {
            // dom元素被删除后取消监听
            if (_this._domElement.displayType == undefined) {
                param.hostDom.off('mouseleave', hideWhenLeave);
                return;
            }
            onHover = false;
            if (_this.enableAlwaysShow)
                return;
            if (hideTimeout) {
                clearTimeout(hideTimeout);
                hideTimeout = null;
            }
            hideTimeout = setTimeout(function () {
                onHover || _this.hide();
            }, duration);
        };
        var mouseEnterFn = function () {
            // dom元素被删除后取消监听
            if (_this._domElement.displayType == undefined) {
                param.hostDom.off('mouseenter', mouseEnterFn);
                return;
            }
            onHover = true;
            _this.show();
            requestAnimationFrame(function () {
                !_this.isFixed && _this.updatePosition();
            });
        };
        param.hostDom.on('mouseenter', mouseEnterFn).on('mouseleave', hideWhenLeave);
        _this.getDomElement().on('mouseenter', function () {
            onHover = true;
        }).on('mouseleave', hideWhenLeave);
        document.body.appendChild(_this.getDomElement().getElement());
        return _this;
    }
    Tooltip.prototype.setEnableAlwaysShow = function (enable) {
        this.enableAlwaysShow = enable;
        if (enable) {
            this.show();
        }
        else {
            this.hide();
        }
        this.updatePosition();
    };
    Tooltip.prototype.setToolTipPosition = function (position) {
        this.newLeft = position.left;
        this.newTop = position.top;
        this.getDomElement().css({ left: this.hostDom.getPosition().left + this.newLeft + "px",
            top: this.hostDom.getPosition().top + position.top + "px" });
    };
    Tooltip.prototype.updatePosition = function () {
        var _this = this;
        var _a = this.hostDom.getPosition(), top = _a.top, right = _a.right; _a.bottom; var left = _a.left;
        var _b = this.hostDom.getDimensions(), parentHeight = _b.height, parentWidth = _b.width;
        var _c = this.getDimensions(), width = _c.width, height = _c.height;
        switch (this.placement) {
            case 'right':
                (function () {
                    var computedTop = top + parentHeight / 2 - height / 2;
                    var computedLeft = right + 10;
                    _this.getDomElement().css({ left: computedLeft + "px", top: computedTop + "px" });
                })();
                break;
            case 'top':
                (function () {
                    var computedTop = top - height - 10;
                    var computedLeft = left + parentWidth / 2 - width / 2;
                    _this.getDomElement().css({ left: computedLeft + "px", top: computedTop + "px" });
                })();
                break;
            case 'bottom':
                (function () {
                    var computedTop = top + (typeof _this.newTop !== 'undefined' ? _this.newTop : 30);
                    var computedLeft = left - (typeof _this.newTop !== 'undefined' ? _this.newLeft : parentWidth - 10);
                    _this.getDomElement().css({ left: computedLeft + "px", top: computedTop + "px" });
                })();
        }
    };
    return Tooltip;
}(Control));

var SNS$e = getUINamespace('Input');
/**
 * @classdesc 类：Input组件类
 * @class Glodon.Bimface.Tiles.UI.Input
 * @constructs Glodon.Bimface.Tiles.UI.Input
 * @description 构造输入框组件
 * @extends Glodon.Bimface.Tiles.UI.Control
 * @param {Object} option 构造输入框的配置项
 * @param {String} option.id 输入框ID
 */
var Input = /** @class */ (function (_super) {
    __extends(Input, _super);
    function Input(param) {
        var _this = this;
        SDM && SDM.send(SNS$e, "bf_c_UIInput_new");
        param = param || {};
        var elementParam = {
            elementType: 'input',
            className: 'bfui-input'
        };
        var controlParam = {
            elementParam: elementParam,
            type: 'Input',
            id: param.id,
            parent: param.parent
        };
        _this = _super.call(this, controlParam) || this;
        _this._domElement.attribute('type', 'text');
        param.onClick && _this.onClick(param.onClick);
        param.onBlur && _this.onBlur(param.onBlur);
        return _this;
    }
    /**
     * 获取输入框的值
     * @function Glodon.Bimface.Tiles.UI.Input.prototype.getValue
     * @returns {String} 输入框的值
     */
    Input.prototype.getValue = function () {
        return this._domElement.attribute('value');
    };
    /**
     * 设置输入框的值
     * @function Glodon.Bimface.Tiles.UI.Input.prototype.setValue
     * @param {String} value 输入框的值
     */
    Input.prototype.setValue = function (value) {
        return this._domElement.attribute('value', value);
    };
    /**
     * 输入框的值发生变化的事件
     * @function Glodon.Bimface.Tiles.UI.Input.prototype.onChange
     * @param {Function} event 输入框值发生变化时执行的函数
     */
    Input.prototype.onChange = function (fn) {
        var _this = this;
        this.onChangeFn && this._domElement.off('change', this.onChangeFn);
        this._domElement.on('change', function (evt) {
            _this.onChangeFn(evt.target.value);
        });
        this.onChangeFn = fn;
        return this;
    };
    Input.prototype.onBlur = function (fn) {
        this.onBlurFn && this._domElement.off('blur', this.onBlurFn);
        this._domElement.on('blur', fn);
        this.onBlurFn = fn;
        return this;
    };
    Input.prototype.onFocus = function (fn) {
        this._domElement.on('focus', fn);
        return this;
    };
    Input.prototype.onInput = function (fn) {
        this._domElement.on('input', fn);
        return this;
    };
    Input.prototype.enabled = function (isEnabled) {
        this._domElement.attribute('disabled', isEnabled ? '' : true);
        return this;
    };
    Input.prototype.setPlaceholder = function (placeholder) {
        this._domElement.attribute('placeholder', placeholder);
    };
    // 开启状态组件
    Input.prototype.enableStatus = function (isEnable) {
        if (isEnable === void 0) { isEnable = true; }
        if (isEnable) {
            this._domElement.addClass('bfui-input-status');
        }
        else {
            this._domElement.removeClass('bfui-input-status');
        }
    };
    // 组件出错状态
    Input.prototype.enableErrorStatus = function (isEnable, errorMessage, width, left, top) {
        if (isEnable === void 0) { isEnable = true; }
        if (width === void 0) { width = 220; }
        if (left === void 0) { left = 0; }
        if (top === void 0) { top = 40; }
        if (isEnable) {
            this._domElement.addClass('bfui-input-error');
            if (!this._toolTip && errorMessage) {
                this._toolTip = new Tooltip({
                    hostDom: this._domElement,
                    placement: 'bottom',
                    width: width,
                    content: errorMessage,
                    isFixed: false,
                });
                this._toolTip.setToolTipPosition({ left: left, top: top });
                this._toolTip.show();
            }
        }
        else {
            this._domElement.removeClass('bfui-input-error');
            if (this._toolTip) {
                this._toolTip.destroy();
                this._toolTip = null;
            }
        }
    };
    return Input;
}(Control));

// 组件Group
var ControlGroup = /** @class */ (function (_super) {
    __extends(ControlGroup, _super);
    function ControlGroup(param) {
        var _this = _super.call(this, param) || this;
        _this._children = [];
        return _this;
    }
    /**
     * 添加组件
     * @function Glodon.Bimface.Tiles.UI.ControlGroup.prototype.addControl
     * @param {Object} control UI控件，对应Glodon.Bimface.Tiles.UI下的组件相关类
     * @param {Object} option 添加组件的选项
     * @param {Object} option.position 组件添加的位置
     * @param {Glodon.Bimface.Tiles.UI.ControlAnchor} option.position.anchor 组件添加的锚点位置
     * @param {Object} option.position.offset 基于锚点的偏移值
     * @param {Number} option.position.offset.x X方向偏移值，单位为px
     * @param {Number} option.position.offset.y Y方向偏移值，单位为px
     */
    ControlGroup.prototype.addControl = function (control, option) {
        if (!option || isNaN(option.index) || option.index === -1) {
            this._children.push(control);
            control.setParent(this);
            this._domElement.append(control.getDomElement());
        }
        else {
            var index = option.index;
            if (index < -1) {
                index = this.getControlCount() + index + 1;
            }
            this.insertControl(control, index);
        }
        return this;
    };
    // 插入子组件到指定index
    ControlGroup.prototype.insertControl = function (control, index) {
        if (!isNaN(index) && index < this._children.length) {
            this._children.splice(index, 0, control);
            control.setParent(this);
            this._domElement.insert(control.getDomElement(), index);
        }
        else {
            this.addControl(control);
        }
        return this;
    };
    /**
     * 移除指定组件
     * @function Glodon.Bimface.Tiles.UI.ControlGroup.prototype.removeControl
     * @param {Object} control UI控件，对应Glodon.Bimface.Tiles.UI下的组件相关类
     */
    ControlGroup.prototype.removeControl = function (control) {
        var index = this.indexOf(control);
        if (index >= 0) {
            this._children.splice(index, 1);
            control.setParent(undefined);
            this._domElement.remove(control.getDomElement());
        }
        return this;
    };
    ControlGroup.prototype.clearControls = function (destroy) {
        if (destroy === void 0) { destroy = false; }
        while (this.getControlCount() > 0) {
            var control = this.getControl({ index: 0 });
            this.removeControl(control);
            destroy && control.destroy();
        }
        return this;
    };
    /**
     * 根据筛选条件获取组件对象
     * @function Glodon.Bimface.Tiles.UI.ControlGroup.prototype.getControl
     * @param {Object} condition 筛选条件
     * @param {String} condition.id 组件ID
     * @returns {Object} UI控件，对应Glodon.Bimface.Tiles.UI下的组件相关类
     */
    ControlGroup.prototype.getControl = function (condition) {
        var resultControl;
        if (!condition)
            return;
        if (condition.id) {
            this.getAllControls().some(function (control) {
                if (control.getId() === condition.id) {
                    resultControl = control;
                    return true;
                }
            });
        }
        else if (!isNaN(condition.index) && condition.index < this.getControlCount()) {
            resultControl = this._children[condition.index];
        }
        return resultControl;
    };
    /**
     * 获取所有组件
     * @function Glodon.Bimface.Tiles.UI.ControlGroup.prototype.getAllControls
     * @returns {Array} 组件对象列表
     */
    ControlGroup.prototype.getAllControls = function () {
        return this._children;
    };
    // 根据子组件获取index
    ControlGroup.prototype.indexOf = function (control) {
        return this._children.indexOf(control);
    };
    // 获取当前已有子组件数量
    ControlGroup.prototype.getControlCount = function () {
        return this._children.length;
    };
    return ControlGroup;
}(Control));

var SNS$d = getUINamespace('Panel');
/**
 * @classdesc 类：Panel组件类
 * @class Glodon.Bimface.Tiles.UI.Panel
 * @constructs Glodon.Bimface.Tiles.UI.Panel
 * @description 构造面板组件
 * @extends Glodon.Bimface.Tiles.UI.Control
 * @param {Object} option 构造标签的配置项
 * @param {String} option.id 标签组件ID
 * @param {String} option.title 标签标题的文字内容
 * @param {Boolean} option.sizeFixed 面板大小是否固定，默认为false
 * @param {Number} option.width 面板宽度，单位为px，默认为300
 * @param {Number} option.height 面板宽度，单位为px，默认为200
 * @param {Boolean} option.draggable 面板是否可拖动，默认为true
 * @param {Object} option.position 面板位置
 * @param {Glodon.Bimface.Tiles.UI.ControlAnchor} option.position.anchor 描述面板位置的锚点
 * @param {Object} option.position.offset 面板基于锚点的偏移量，单位为px，默认值为{x: 0, y: 0}
 */
//Panel组件类
var Panel = /** @class */ (function (_super) {
    __extends(Panel, _super);
    function Panel(param) {
        var _this = this;
        SDM && SDM.send(SNS$d, "bf_c_UIPanel_new");
        param = param || {};
        var elementParam = {
            elementType: 'div',
            className: 'bfui-panel'
        };
        var controlParam = {
            elementParam: elementParam,
            type: 'Panel',
            id: param.id,
            parent: param.parent
        };
        _this = _super.call(this, controlParam) || this;
        param.onClick && _this.onClick(param.onClick);
        var header = _this.header = new Dom().createElement({ elementType: 'div', className: 'bfui-panel-header' });
        var contentElementParam = {
            elementType: 'div',
            className: 'bfui-panel-content'
        };
        var contentControlParam = {
            elementParam: contentElementParam,
            type: 'ControlGroup',
        };
        var content = _this.content = new ControlGroup(contentControlParam);
        _this._domElement.append(header).append(content.getDomElement());
        var title = _this.title = new Dom().createElement({ elementType: 'div', className: 'bfui-panel-title' });
        var headerCloseButton = _this.headerCloseButton = new Dom().createElement({ elementType: 'div', className: 'bfui-button bfui-panel-headerclosebutton gld-bf-close-sm' });
        header.append(title).append(headerCloseButton);
        _this.onCloseFn = function () { _this.hide(); };
        headerCloseButton.on('click', _this.onCloseFn);
        param.title && _this.setTitle(param.title);
        _this.sizeFixed = param.sizeFixed === true;
        _this.draggable = param.draggable !== false;
        _this.footerDisabled = param.footerDisabled === true;
        param.position && _this.setPosition(param.position);
        if (!isNaN(param.width)) {
            _this.width = param.width;
            if (!isNaN(param.minWidth) && param.minWidth > _this.width) {
                param.minWidth = _this.width;
            }
        }
        else {
            _this.width = 300;
        }
        param.minWidth = param.minWidth || 100;
        _this.getDomElement().css({ 'width': _this.width + "px" });
        if (!isNaN(param.height)) {
            _this.height = param.height;
            if (!isNaN(param.minHeight) && param.minHeight > _this.height) {
                param.minHeight = _this.height;
            }
            _this.getDomElement().css('height', _this.height + "px");
        }
        else { // 根据产品定义，用户未定义高度时，默认最小高度200px，最大高度420px，中间根据内容浮动
            _this.height = 200;
            _this.getDomElement().css({ 'minHeight': "200px", 'maxHeight': "420px" });
        }
        param.minHeight = param.minHeight || 80;
        // 鼠标按下时此panel移到最上层
        _this.getDomElement().on('mousedown', function () {
            _this.topShow();
        });
        // 设置拖动位置事件
        if (_this.draggable) {
            var dragging_1 = false, startX_1, startY_1, startOffsetX_1, startOffsetY_1, parentDom_1, parentDimensions_1, selfDimensions_1, selfAnchor_1;
            var mousemove_1 = function (event) {
                if (!dragging_1)
                    return;
                var x = event.clientX - startX_1 + startOffsetX_1;
                var y = event.clientY - startY_1 + startOffsetY_1;
                // 控制panel不超出父级div范围
                var parentWidth = parentDimensions_1.width, parentHeight = parentDimensions_1.height;
                var selfWidth = selfDimensions_1.width, selfHeight = selfDimensions_1.height;
                var minX, minY, maxX, maxY;
                if (selfAnchor_1.indexOf('left') >= 0) {
                    minX = 0;
                    maxX = parentWidth - selfWidth;
                }
                else if (selfAnchor_1.indexOf('right') >= 0) {
                    minX = -parentWidth + selfWidth;
                    maxX = 0;
                }
                else if (selfAnchor_1.indexOf('center') >= 0) {
                    minX = -(parentWidth - selfWidth) / 2;
                    maxX = (parentWidth - selfWidth) / 2;
                }
                if (selfAnchor_1.indexOf('top') >= 0) {
                    minY = 0;
                    maxY = parentHeight - selfHeight;
                }
                else if (selfAnchor_1.indexOf('bottom') >= 0) {
                    minY = -parentHeight + selfHeight;
                    maxY = 0;
                }
                else if (selfAnchor_1.indexOf('middle') >= 0) {
                    minY = -(parentHeight - selfHeight) / 2;
                    maxY = (parentHeight - selfHeight) / 2;
                }
                x = Math.max(x, minX);
                x = Math.min(x, maxX);
                y = Math.max(y, minY);
                y = Math.min(y, maxY);
                _this.setOffset({ x: x, y: y });
            };
            var mouseup_1 = function (event) {
                dragging_1 = false;
                parentDom_1.off('mousemove', mousemove_1).off('mouseup', mouseup_1);
            };
            header.on('mousedown', function (event) {
                dragging_1 = true;
                startX_1 = event.clientX;
                startY_1 = event.clientY;
                var _a = _this.getPositionParam(), anchor = _a.anchor, _b = _a.offset, x = _b.x, y = _b.y;
                selfAnchor_1 = anchor.toLowerCase();
                startOffsetX_1 = x;
                startOffsetY_1 = y;
                parentDom_1 = parentDom_1 || new Dom(_this.getDomElement().getElement().parentElement);
                parentDom_1.on('mousemove', mousemove_1).on('mouseup', mouseup_1);
                parentDimensions_1 = parentDom_1.getDimensions();
                selfDimensions_1 = _this.getDimensions();
            }).on('mousemove', mousemove_1).on('mouseup', mouseup_1).addClass('bfui-panel-header-dragable');
        }
        // 设置拖动大小事件
        if (!_this.sizeFixed) {
            var minWidth_1 = param.minWidth, minHeight_1 = param.minHeight;
            _this.sizeFixedDom = new Dom().createElement({ elementType: 'div', className: 'bfui-panel-sizefixed' });
            _this.getDomElement().append(_this.sizeFixedDom);
            var dragging_2 = false, startX_2, startY_2, startWidth_1, startHeight_1, parentDom_2;
            var mousemove_2 = function (event) {
                if (!dragging_2)
                    return;
                var formerWidth = _this.width, formerHeight = _this.height;
                _this.width = event.clientX - startX_2 + startWidth_1;
                _this.height = event.clientY - startY_2 + startHeight_1;
                _this.width < minWidth_1 && (_this.width = minWidth_1);
                _this.height < minHeight_1 && (_this.height = minHeight_1);
                _this.getDomElement().css({ width: _this.width + "px", height: _this.height + "px" });
                var _a = _this.getPositionParam(), _b = _a.offset, x = _b.x, y = _b.y, anchor = _a.anchor;
                var deltaX = _this.width - formerWidth, deltaY = _this.height - formerHeight;
                if (anchor.indexOf('Center') >= 0) {
                    x += deltaX / 2;
                }
                if (anchor.indexOf('Middle') >= 0) {
                    y += deltaY / 2;
                }
                if (anchor.indexOf('Right') >= 0) {
                    x += deltaX;
                }
                if (anchor.indexOf('Bottom') >= 0) {
                    y += deltaY;
                }
                _this.setOffset({ x: x, y: y });
            };
            var mouseup_2 = function (event) {
                dragging_2 = false;
                parentDom_2.off('mousemove', mousemove_2).off('mouseup', mouseup_2);
            };
            _this.sizeFixedDom.on('mousedown', function (event) {
                _this.getDomElement().css({ height: _this.getHeight() + "px" }).css({ minWidth: minWidth_1 + "px", minHeight: minHeight_1 + "px", maxHeight: '' });
                dragging_2 = true;
                startX_2 = event.clientX;
                startY_2 = event.clientY;
                startWidth_1 = _this.getWidth();
                startHeight_1 = _this.getHeight();
                parentDom_2 = parentDom_2 || new Dom(_this.getDomElement().getElement().parentElement);
                parentDom_2.on('mousemove', mousemove_2).on('mouseup', mouseup_2);
            }).on('mousemove', mousemove_2).on('mouseup', mouseup_2);
        }
        // 设置footer
        if (_this.footerDisabled) {
            var footerElementParam = {
                elementType: 'div',
                className: 'bfui-panel-footer'
            };
            var footerControlParam = {
                elementParam: footerElementParam,
                type: 'ControlGroup',
            };
            _this.footer = new ControlGroup(footerControlParam);
            _this._domElement.append(_this.footer.getDomElement());
        }
        return _this;
    }
    Panel.prototype.setTips = function (tips, type) {
        if (this.tipsElement) {
            this.tipsElement.destroy();
        }
        var classMap = {
            default: "bfui-panel-tips",
            loading: "bfui-panel-loading"
        };
        var className = classMap[type] || classMap.default;
        var tipsElement = new Dom().createElement({ elementType: 'div', className: className });
        tipsElement.html(tips);
        this.tipsElement = tipsElement;
        this.showTips();
    };
    Panel.prototype.showTips = function () {
        if (this.tipsElement) {
            this.content.getDomElement().html('');
            this.content.getDomElement().append(this.tipsElement);
            return this;
        }
    };
    Panel.prototype.getContent = function () {
        return this.content;
    };
    Panel.prototype.clearContent = function () {
        this.content.getDomElement().html('');
        return this;
    };
    Panel.prototype.getFooter = function () {
        return this.footer;
    };
    Panel.prototype.clearFooter = function () {
        this.footerDisabled && this.footer.getDomElement().html('');
        return this;
    };
    /**
   * 设置面板标题文字内容
   * @function Glodon.Bimface.Tiles.UI.Panel.prototype.setTitle
   * @param {String} text 面板标题文字内容
   */
    Panel.prototype.setTitle = function (text) {
        this.title.html(text);
        return this;
    };
    /**
     * 获取面板标题文字内容
     * @function Glodon.Bimface.Tiles.UI.Panel.prototype.getTitle
     * @returns {String} 面板标题文字内容
     */
    Panel.prototype.getTitle = function () {
        return this.title.html();
    };
    /**
   * 添加控件
   * @function Glodon.Bimface.Tiles.UI.Panel.prototype.addControl
   * @param {Object} control UI控件，对应Glodon.Bimface.Tiles.UI下的组件相关类
   * @param {Object} option 添加控件的选项（选填）
   * @param {Number} index 控件插入位置，index为0时控件插入至面板第一个位置，1为第二个位置，以此类推，为-1时则将该控件放至面板最后
   */
    Panel.prototype.addControl = function (control, option) {
        this.content.addControl(control, option);
        return this;
    };
    /**
   * 移除控件
   * @function Glodon.Bimface.Tiles.UI.Panel.prototype.removeControl
   * @param {Object} control UI控件，对应Glodon.Bimface.Tiles.UI下的组件相关类
   */
    Panel.prototype.removeControl = function (control) {
        this.content.removeControl(control);
        return this;
    };
    /**
     * 获取指定控件的序号
     * @function Glodon.Bimface.Tiles.UI.Panel.prototype.indexOf
     * @param {Object} control UI控件，对应Glodon.Bimface.Tiles.UI下的组件相关类
     * @returns {Number} 控件在面板中的序号
     */
    Panel.prototype.indexOf = function (control) {
        return this.content.indexOf(control);
    };
    /**
     * 根据筛选条件获取控件对象
     * @function Glodon.Bimface.Tiles.UI.Panel.prototype.getControl
     * @param {Object} condition 筛选条件
     * @param {String} condition.id 控件ID
     * @param {Number} condition.index 控件在面板中的序号
     * @returns {Object} UI控件，对应Glodon.Bimface.Tiles.UI下的组件相关类
     */
    Panel.prototype.getControl = function (condition) {
        return this.content.getControl(condition);
    };
    /**
     * 根据筛选条件获取控件对象
     * @function Glodon.Bimface.Tiles.UI.Panel.prototype.getAllControls
     * @returns {Array} 由控件对象组成的列表
     */
    Panel.prototype.getAllControls = function () {
        return this.content.getAllControls();
    };
    /**
     * 面板关闭的事件
     * @function Glodon.Bimface.Tiles.UI.Panel.prototype.onClose
     * @param {Function} event 面板关闭时执行的函数
     */
    Panel.prototype.onClose = function (fn, hide) {
        var _this = this;
        if (hide === void 0) { hide = true; }
        this.headerCloseButton.off('click', this.onCloseFn);
        this.onCloseFn = function (data) {
            fn(data);
            hide && _this.hide();
        };
        this.headerCloseButton.on('click', this.onCloseFn);
        return this;
    };
    Panel.prototype.topShow = function () {
        var list = document.querySelectorAll('.bfui-panel');
        list.forEach(function (element) {
            var dom = new Dom(element);
            dom.removeClass('bfui-panel-topshow');
        });
        this.getDomElement().addClass('bfui-panel-topshow');
    };
    return Panel;
}(Control));

/**
 * SelectOption组件类，作为Select组件的单条option
 */
var SelectOption = /** @class */ (function (_super) {
    __extends(SelectOption, _super);
    function SelectOption(param) {
        var _this = this;
        param = param || {};
        var elementParam = {
            elementType: 'div',
            className: 'bfui-select-option'
        };
        var controlParam = {
            elementParam: elementParam,
            type: 'SelectOption',
        };
        _this = _super.call(this, controlParam) || this;
        _this.setTooltip(param.text).getDomElement().html(param.text);
        _this.disabled = param.disabled === true;
        _this.selected = param.selected === true;
        _this.value = param.value;
        _this.setDisabled(_this.disabled).setSelected(_this.selected);
        param.visible === false && _this.hide();
        // 点击本条option时，调用SelectOptionGroup的setSelected事件，将本条option设置为选中，其他option取消选中
        _this.onClick(function () {
            if (!_this.selected && !_this.disabled) {
                _this._parent.setSelected(_this);
            }
            _this.trigger(UIEvents$1.Clicked);
        });
        return _this;
    }
    /**
     * 设置选中状态
     * @param {Boolean} selected 选中状态
     */
    SelectOption.prototype.setSelected = function (selected) {
        if (!this.disabled && this.selected !== selected) {
            this.selected = selected;
            selected ? this.addClass('bfui-select-option-selected') : this.removeClass('bfui-select-option-selected');
        }
        return this;
    };
    /**
     * 设置不可用状态
     * @param {Boolean} disabled 不可用状态
     */
    SelectOption.prototype.setDisabled = function (disabled) {
        if (this.disabled !== disabled) {
            this.setSelected(false);
            this.disabled = disabled;
            disabled ? this.addClass('bfui-select-option-disabled') : this.removeClass('bfui-select-option-disabled');
        }
        return this;
    };
    /**
     * 获取option的文本内容
     */
    SelectOption.prototype.getText = function () {
        return this.getDomElement().html();
    };
    /**
     * 获取option的实际value，如未设置value则返回option的text文本内容
     */
    SelectOption.prototype.getValue = function () {
        return this.value === undefined ? this.getText() : this.value;
    };
    return SelectOption;
}(Control));

/**
 * SelectOptionGroup组件类，管理多条SelectOption
 */
var SelectOptionGroup = /** @class */ (function (_super) {
    __extends(SelectOptionGroup, _super);
    function SelectOptionGroup(config) {
        var _this = this;
        var contentElementParam = {
            elementType: 'div',
            className: 'bfui-select-content'
        };
        var contentControlParam = {
            // parent: document.getElementsByTagName('body')[0],
            elementParam: contentElementParam,
            type: 'ControlGroup',
        };
        _this = _super.call(this, contentControlParam) || this;
        _this.enableAnimation = config.enableAnimation !== false;
        _this.enableAnimation && _this.addClass('bfui-select-animation');
        _this._maxHeight = config.maxHeight || 125;
        _this._showTop = false;
        _this.getDomElement().css('maxHeight', _this._maxHeight + "px");
        config.values && _this.setValues(config.values);
        _this.hide();
        return _this;
    }
    /**
     * 设置option内容
     * @param {Array<string | IOptionParam>} values option内容
     */
    SelectOptionGroup.prototype.setValues = function (values) {
        var _this = this;
        this.clearControls();
        this.values = [];
        values.forEach(function (value) {
            var param;
            if (!DataUtil.assertType(value, 'obj')) {
                param = { text: value.toString() };
            }
            else {
                param = value;
            }
            _this.values.push(param);
            var option = new SelectOption(param);
            _this.addControl(option);
            option.on(UIEvents$1.Clicked, function () {
                _this.trigger(UIEvents$1.Clicked, option);
            });
        });
        this._height = 0;
        return this;
    };
    /**
     * 获取当前显示/隐藏状态
     */
    SelectOptionGroup.prototype.isVisible = function () {
        return this.enableAnimation ? this._visible : _super.prototype.isVisible.call(this);
    };
    SelectOptionGroup.prototype.getOptionsHeight = function () {
        if (!this._height) {
            var height_1 = 2;
            this._children && this._children.forEach(function (child) {
                if (child.isVisible()) {
                    height_1 += child.getHeight();
                }
            });
            this._height = height_1;
        }
        return this._height;
    };
    SelectOptionGroup.prototype.setFixedPosition = function (position) {
        var pageHeight = window.innerHeight, maxHeight = this._maxHeight, dom = this.getDomElement().getElement(), minHeight = Math.min(maxHeight, this.getOptionsHeight());
        if (position.bottom + minHeight > pageHeight) {
            dom.style.top = '';
            dom.style.bottom = pageHeight - position.top + 'px';
        }
        else {
            dom.style.top = position.bottom + 'px';
            dom.style.bottom = '';
        }
        dom.style.left = position.left + 'px';
        dom.style.width = position.right - position.left + 'px';
    };
    SelectOptionGroup.prototype.setTop = function (value) {
        this._showTop = value;
        if (value) {
            this.removeClass('bfui-select-animation');
        }
        else {
            this.addClass('bfui-select-animation');
        }
    };
    /**
     * 显示组件
     */
    SelectOptionGroup.prototype.show = function () {
        var _this = this;
        if (this.enableAnimation) { // 支持动画状态时，通过height变化显示组件
            this._visible = true;
            var transitionEndFn_1 = function () {
                if (_this._inTransition) {
                    _this.getDomElement().removeClass('bfui-select-overflow-hidden').off('transitionend', transitionEndFn_1);
                    _this._inTransition = false;
                }
            };
            this._inTransition = true;
            this.getDomElement().addClass('bfui-select-overflow-hidden').css({ 'border-width': '1px', 'height': this.getOptionsHeight() + 'px' }).on('transitionend', transitionEndFn_1);
            if (this._showTop) {
                var topMargin = 27 + this.getOptionsHeight();
                this.getDomElement().css({ 'margin-top': '-' + topMargin + 'px' });
            }
        }
        else {
            _super.prototype.show.call(this);
        }
        return this;
    };
    /**
     * 隐藏组件
     */
    SelectOptionGroup.prototype.hide = function () {
        if (this.enableAnimation) { // 支持动画状态时，通过height变化隐藏组件
            this._visible = false;
            this.getDomElement().addClass('bfui-select-overflow-hidden').css({ 'border-width': '0px', 'height': '0px' });
        }
        else {
            _super.prototype.hide.call(this);
        }
        return this;
    };
    /**
     * 设置某条option为选中状态，其他option取消选中
     * @param {SelectOption | string} option 选中的内容
     */
    SelectOptionGroup.prototype.setSelected = function (option) {
        if (option === undefined)
            return;
        var isValidOption = false;
        if (!(option instanceof SelectOption)) {
            this.getAllControls().some(function (cControl) {
                if (cControl.getText() === option.toString()) {
                    option = cControl;
                    isValidOption = true;
                    return true;
                }
            });
        }
        else if (this.indexOf(option) >= 0) {
            isValidOption = true;
        }
        if (isValidOption && this.selected !== option) {
            this.getAllControls().forEach(function (cControl) {
                cControl.setSelected(cControl === option ? true : false);
            });
            this.selected = option;
            var text = this.selected.getText();
            var index = this.indexOf(this.selected);
            var value = this.selected.getValue();
            this.trigger(UIEvents$1.ValueChanged, { text: text, value: value, index: index });
        }
        return this;
    };
    /**
     * 根据序号设置Option选中状态
     * @param {Number} index Option
     */
    SelectOptionGroup.prototype.setSelectedByIndex = function (index) {
        var option = this.getControl({ index: index });
        if (option) {
            this.setSelected(option);
        }
        return this;
    };
    /**
     * 根据Value设置Option选中状态
     * @param {String | Number} value Option的Value值
     */
    SelectOptionGroup.prototype.setSelectedByValue = function (value) {
        var result;
        this.getAllControls().some(function (option) {
            option = option;
            if (option.getValue() === value) {
                result = option;
                return true;
            }
        });
        return this.setSelected(result);
    };
    /**
     * 获取当前选中的Option
     */
    SelectOptionGroup.prototype.getSelected = function () {
        return this.selected;
    };
    /**
     * 获取所有Option的信息数组
     */
    SelectOptionGroup.prototype.getValues = function () {
        return this.values;
    };
    SelectOptionGroup.prototype.clearSelection = function () {
        this.selected = null;
        this.trigger(UIEvents$1.ValueChanged);
    };
    SelectOptionGroup.prototype.setMaxHeight = function (maxHeight) {
        this._maxHeight = maxHeight;
        this.getDomElement().css('maxHeight', this._maxHeight + "px");
    };
    return SelectOptionGroup;
}(ControlGroup));

var SNS$c = getUINamespace('Select');
/**
 * Select组件类
 */
var Select = /** @class */ (function (_super) {
    __extends(Select, _super);
    function Select(param) {
        var _this = this;
        SDM && SDM.send(SNS$c, "bf_c_UISelect_new");
        param = param || {};
        var elementParam = {
            elementType: 'div',
            className: 'bfui-select'
        };
        var controlParam = {
            elementParam: elementParam,
            type: 'Select',
            id: param.id,
            parent: param.parent
        };
        _this = _super.call(this, controlParam) || this;
        _this.enableAnimation = param.enableAnimation !== false;
        // Select的header部分，由显示的选中Text及下拉button组成
        var header = _this.header = new Dom().createElement({ elementType: 'div', className: 'bfui-select-header' });
        var title = _this.title = new Dom().createElement({ elementType: 'div', className: 'bfui-select-title' });
        title.html('请选择');
        var toggleButton = _this.toggleButton = new Dom().createElement({ elementType: 'div', className: 'bfui-button bfui-select-togglebutton gld-bf-arrow-down-sm' });
        _this.enableAnimation && toggleButton.addClass('bfui-select-animation');
        header.append(title).append(toggleButton).on('click', function (evt) {
            if (_this.disabled)
                return;
            _this.content.isVisible() ? _this.hideContent() : _this.showContent();
        });
        // Select的content部分，基于ControlGroup建立了SelectOptionGroup子类，用于管理多个SelectOption
        var content = _this.content = new SelectOptionGroup({ enableAnimation: _this.enableAnimation, values: param.values, maxHeight: param.maxHeight });
        _this.getDomElement().append(header);
        new Dom(document.body).append(content.getDomElement());
        _this.onChange(param.onChange);
        // 选中项发生变化时，更新header文字，隐藏content，并触发onChange事件
        content.on(UIEvents$1.ValueChanged, function (data) {
            data && _this.title.html(data.text);
            _this.onChangeCallback instanceof Function && _this.onChangeCallback(data);
        });
        content.on(UIEvents$1.Clicked, function (clickedOption) {
            _this.hideContent();
        });
        // this.getDomElement().on('mouseleave', ()=> this.hideContent());  // 鼠标离开Select组件时隐藏content
        param.defaultIndex !== undefined && _this.setSelected(param.defaultIndex); // 根据配置设置初始选中序号
        param.width && (_this.getDomElement().css('width', param.width + "px")); // 根据配置设置Select组件的宽度
        document.addEventListener('mousedown', function (evt) {
            var headerDom = header.getElement(), targetParent = evt.target.parentElement;
            if (evt.target == headerDom || targetParent == headerDom || targetParent == content.getDomElement().getElement() || evt.target == content.getDomElement().getElement()) {
                return;
            }
            _this.hideContent();
        }, false);
        window.addEventListener('resize', function () {
            _this._resize();
        });
        _this.scrollFunc = _this._scrollFunc.bind(_this);
        return _this;
    }
    Select.prototype._resize = function () {
        var domRect = this.header.getPosition();
        this.content.setFixedPosition(domRect);
    };
    /**
     * 设置option内容
     * @param {Array<string | IOptionParam>} values 由string或IOptionParam组成的数组
     */
    Select.prototype.setValues = function (values) {
        this.content.setValues(values);
        return this;
    };
    /**
     * 设置选中项
     * @param {Number} index 选中序号
     */
    Select.prototype.setSelected = function (index) {
        this.content.setSelectedByIndex(index);
        this.title.html(this.content.getSelected().getText());
        this.hideContent();
        return this;
    };
    /**
     * 根据Value设置Option选中状态
     * @param {String | Number} value Option的Value值
     */
    Select.prototype.setSelectedByValue = function (value) {
        this.content.setSelectedByValue(value);
        return this;
    };
    /**
     * 设置Select组件不可用状态
     * @param {Boolean} disabled 组件不可用状态，默认为true
     */
    Select.prototype.setDisabled = function (disabled) {
        if (disabled === void 0) { disabled = true; }
        this.disabled = disabled;
        disabled ? this.addClass('bfui-select-disabled') : this.removeClass('bfui-select-disabled');
        return this;
    };
    /**
     * 设置onChange回调函数
     * @param {Function} fn onChange回调函数
     */
    Select.prototype.onChange = function (fn) {
        fn instanceof Function && (this.onChangeCallback = fn);
        return this;
    };
    Select.prototype._scrollFunc = function () {
        this.hideContent();
        //移除监听容器的滚动事件
        this.getDomElement().getParent().removeEventListener('scroll', this.scrollFunc, false);
    };
    /**
     * 显示options
     */
    Select.prototype.showContent = function () {
        this.toggleButton.addClass('bfui-select-togglebutton-opened');
        this._resize();
        this.content.show();
        //监听容器的滚动事件
        this.getDomElement().getParent().addEventListener('scroll', this.scrollFunc, false);
        return this;
    };
    /**
     * 隐藏options
     */
    Select.prototype.hideContent = function () {
        this.toggleButton.removeClass('bfui-select-togglebutton-opened');
        this.content.hide();
        return this;
    };
    Select.prototype.clearSelection = function (defaultText) {
        this.content.clearSelection();
        this.setText(defaultText);
        return this;
    };
    Select.prototype.setText = function (text) {
        this.title.html(text);
        return this;
    };
    Select.prototype.setMaxHeight = function (maxHeight) {
        this.content.setMaxHeight(maxHeight);
    };
    Select.prototype.setId = function (id) {
        this.id = id;
    };
    Select.prototype.getId = function () {
        return this.id;
    };
    return Select;
}(Control));

var SNS$b = getUINamespace('Switch');
//Switch组件类
var Switch = /** @class */ (function (_super) {
    __extends(Switch, _super);
    function Switch(param) {
        var _this = this;
        SDM && SDM.send(SNS$b, "bf_c_UISwitch_new");
        param = param || {};
        var elementParam = {
            elementType: 'div',
            className: 'bfui-switch'
        };
        var controlParam = {
            elementParam: elementParam,
            type: 'Switch',
            id: param.id,
            parent: param.parent
        };
        _this = _super.call(this, controlParam) || this;
        param.onClick && _this.onClick(param.onClick);
        var switchPart = _this.switchPart = new Dom().createElement({ elementType: 'input', className: 'bfui-switch-switch' });
        switchPart.attribute('type', 'checkbox');
        switchPart.on('click', function () {
            _this.value = switchPart.attribute('checked');
            _this.onChangeFn && _this.onChangeFn(_this.value);
        });
        var textPart = _this.textPart = new Dom().createElement({ elementType: 'div', className: 'bfui-switch-text' });
        _this._domElement.append(switchPart).append(textPart);
        param.text && _this.setText(param.text);
        param.value && _this.setValue(param.value);
        return _this;
    }
    // 获取文本
    Switch.prototype.getText = function () {
        return this.textPart.html();
    };
    // 设置文本
    Switch.prototype.setText = function (text) {
        this.textPart.html(text);
        return this;
    };
    // 获取当前switch状态
    Switch.prototype.getValue = function () {
        return this.value;
    };
    // 设置switch状态
    Switch.prototype.setValue = function (value) {
        this.value = value;
        this.switchPart.attribute('checked', value);
        this.trigger(UIEvents$1.ValueChanged, this, value);
        return this;
    };
    Switch.prototype.onChange = function (fn) {
        fn instanceof Function && (this.onChangeFn = fn);
        return this;
    };
    return Switch;
}(Control));

var CheckState = /** @class */ (function (_super) {
    __extends(CheckState, _super);
    function CheckState(param) {
        var _this = _super.call(this, param) || this;
        var inputPart = _this.inputPart = new Dom().createElement({ elementType: 'input', className: param.className });
        inputPart.attribute('type', param.name);
        _this._domElement.on('click', function (evt) {
            if (!evt.target.hasClass(param.className)) {
                _this.inputPart.getElement().click();
            }
        });
        inputPart.on('change', function () {
            _this.trigger(UIEvents$1.ValueChanged, _this, _this.getValue(), _this.isChecked());
        });
        var textPart = _this.textPart = new Dom().createElement({ elementType: 'label', className: "bfui-checkState-text" });
        _this._domElement.append(inputPart).append(textPart);
        _this.setValue(param.value === undefined ? true : param.value);
        param.label && _this.setLabel(param.label);
        param.enabled === false && _this.setEnabled(param.enabled);
        param.checked === true && _this.setChecked(param.checked);
        return _this;
    }
    /**
     * 多选框选中状态发生变化的事件
     * @function Glodon.Bimface.Tiles.UI.CheckState.prototype.onChange
     * @param {Function} event 多选框选中状态发生变化时执行的函数
     */
    CheckState.prototype.onChange = function (fn) {
        this.on(UIEvents$1.ValueChanged, fn);
        return this;
    };
    /**
     * 获取多选框文字内容
     * @function Glodon.Bimface.Tiles.UI.CheckState.prototype.getLabel
     * @returns {String} 多选框文字内容
     */
    CheckState.prototype.getLabel = function () {
        return this.textPart.html();
    };
    /**
     * 设置多选框文字内容
     * @function Glodon.Bimface.Tiles.UI.CheckState.prototype.setLabel
     * @param {String} text 多选框文字内容
     */
    CheckState.prototype.setLabel = function (text) {
        this.textPart.html(text);
        return this;
    };
    /**
     * 获取多选框的启用状态
     * @function Glodon.Bimface.Tiles.UI.CheckState.prototype.isEnabled
     * @returns {Boolean} 多选框的启用状态
     */
    CheckState.prototype.isEnabled = function () {
        return !this.inputPart.attribute('disabled');
    };
    /**
     * 设置多选框的启用状态
     * @function Glodon.Bimface.Tiles.UI.CheckState.prototype.setEnabled
     * @param {Boolean} isEnabled 多选框的启用状态
     */
    CheckState.prototype.setEnabled = function (isEnabled) {
        if (isEnabled != this.isEnabled()) {
            this.textPart.toggle('bfui-checkState-text-disabled');
            this.inputPart.attribute('disabled', !isEnabled);
        }
        return this;
    };
    /**
     * 获取多选框的选中状态
     * @function Glodon.Bimface.Tiles.UI.CheckState.prototype.isChecked
     * @returns {Boolean} 多选框的选中状态
     */
    CheckState.prototype.isChecked = function () {
        return this.inputPart.attribute('checked');
    };
    /**
     * 设置多选框的选中状态
     * @function Glodon.Bimface.Tiles.UI.CheckState.prototype.setChecked
     * @param {Boolean} checked 多选框的选中状态
     */
    CheckState.prototype.setChecked = function (checked) {
        if (checked != this.isChecked()) {
            this.inputPart.attribute('checked', checked);
            this.trigger(UIEvents$1.ValueChanged, this, this.getValue(), checked);
        }
        return this;
    };
    /**
    * 获取多选框的返回值
    * @function Glodon.Bimface.Tiles.UI.CheckState.prototype.getValue
    * @returns {String} 多选框的返回值
    */
    CheckState.prototype.getValue = function () {
        return this.value;
    };
    /**
     * 设置多选框的返回值
     * @function Glodon.Bimface.Tiles.UI.CheckState.prototype.setValue
     * @param {String} value 多选框的返回值
     */
    CheckState.prototype.setValue = function (value) {
        this.value = value;
        return this;
    };
    return CheckState;
}(Control));

var SNS$a = getUINamespace('Checkbox');
/**
 * @classdesc 类：Checkbox组件类
 * @class Glodon.Bimface.Tiles.UI.Checkbox
 * @constructs Glodon.Bimface.Tiles.UI.Checkbox
 * @extends Glodon.Bimface.Tiles.UI.CheckState
 * @extends Glodon.Bimface.Tiles.UI.Control
 * @description 构造Checkbox组件对象
 * @param {Object} option 构造多选框的配置项
 * @param {String} option.id 多选框ID
 * @param {String} option.label 多选框文字内容
 * @param {String} option.value 多选框状态变化时的返回值
 * @param {Boolean} option.checked 多选框的选中状态，缺省值为false
 * @param {Boolean} option.enabled 多选框是否可用，缺省值为true
 */
var Checkbox = /** @class */ (function (_super) {
    __extends(Checkbox, _super);
    function Checkbox(param) {
        var _this = this;
        SDM && SDM.send(SNS$a, "bf_c_UICheckBox_new");
        var elementParam = {
            elementType: 'div',
            className: 'bfui-checkbox'
        };
        var controlParam = {
            elementParam: elementParam,
            type: 'Checkbox',
            name: 'checkbox',
            id: param.id,
            className: 'bfui-checkbox-checkbox',
            parent: param.parent
        };
        param = Object.assign({}, controlParam, param);
        _this = _super.call(this, param) || this;
        return _this;
    }
    return Checkbox;
}(CheckState));

var SNS$9 = getUINamespace('Collapse');
//Collapse组件类
var Collapse = /** @class */ (function (_super) {
    __extends(Collapse, _super);
    function Collapse(param) {
        var _this = this;
        SDM && SDM.send(SNS$9, "bf_c_UICollapse_new");
        var elementParam = {
            elementType: 'tbody',
            className: 'bfui-collapse-tbody'
        };
        var controlParam = {
            elementParam: elementParam,
            type: 'Collapse',
            parent: param.parent
        };
        _this = _super.call(this, controlParam) || this;
        // let tbodyPart = this.tbodyPart = new Dom().createElement({ elementType: 'tbody', className :param.isCollapse?'bf-collapse':'' });
        var tbodyPart = _this._domElement;
        var titlePart = new Dom().createElement({ elementType: 'tr', className: 'bfui-collapse-title bfui-transition' });
        if (param.group === '基本属性') {
            param.group = window.BimfaceLanguage.bf_panel_basic_attribute;
        }
        titlePart.html("<td colspan=\"2\"><i class=\"bfui-collapse-icon\"></i>" + param.group + "</td>");
        tbodyPart.append(titlePart);
        if (param.items && param.items.length > 0) {
            param.items.forEach(function (item) {
                var tr = new Dom().createElement({ elementType: 'tr', className: 'bfui-collapse-content' });
                tr.html("<td class=\"bfui-collapse-content-key\"><div  class=\"bfui-collapse-content-padding\"></div><div   class=\"bfui-collapse-content-name\">" + item.key + "</div></td><td class=\"bfui-collapse-content-value\">" + item.value + "</td>");
                tbodyPart.append(tr);
            });
        }
        titlePart.on('click', function () {
            tbodyPart.toggleClass('bfui-collapse-collapse');
        });
        return _this;
    }
    return Collapse;
}(Control));

var SNS$8 = getUINamespace('Radio');
//Radio组件类
var Radio = /** @class */ (function (_super) {
    __extends(Radio, _super);
    function Radio(param) {
        var _this = this;
        SDM && SDM.send(SNS$8, "bf_c_UIRadio_new");
        var elementParam = {
            elementType: 'div',
            className: 'bfui-radio'
        };
        var controlParam = {
            elementParam: elementParam,
            type: 'Radio',
            name: 'radio',
            id: param.id,
            className: 'bfui-radio-radio',
            parent: param.parent
        };
        param = Object.assign({}, controlParam, param);
        _this = _super.call(this, param) || this;
        return _this;
    }
    return Radio;
}(CheckState));

var SNS$7 = getUINamespace('Slider');
//Slider组件类
var Slider = /** @class */ (function (_super) {
    __extends(Slider, _super);
    function Slider(param) {
        var _this = this;
        SDM && SDM.send(SNS$7, "bf_c_UISlider_new");
        param = param || {};
        var elementParam = {
            elementType: 'div',
            className: 'bfui-slider'
        };
        var controlParam = {
            elementParam: elementParam,
            type: 'Slider',
            id: param.id,
            parent: param.parent
        };
        _this = _super.call(this, controlParam) || this;
        _this.minValue = param.minValue || 0;
        _this.maxValue = param.maxValue || 100;
        param.onClick && _this.onClick(param.onClick);
        _this.onChangeCallback = param.onChangeCallback;
        var sliderLinePart = _this.sliderLinePart = new Dom().createElement({ elementType: 'div', className: 'bfui-slider-line' });
        var sliderLeftPart = _this.sliderLeftPart = new Dom().createElement({ elementType: 'div', className: 'bfui-slider-left' });
        var sliderValuePart = _this.sliderValuePart = new Dom().createElement({ elementType: 'div', className: 'bfui-slider-value' });
        _this._domElement.append(sliderLinePart).append(sliderLeftPart).append(sliderValuePart);
        _this.setValue(param.value || 0);
        _this.setEnableTooltip(param.enableTooltip);
        _this.on(UIEvents$1.ValueChanged, function (data) {
            _this.onChangeCallback instanceof Function && _this.onChangeCallback(Math.round(data));
        });
        var dragging = false;
        var startX, startValue;
        sliderValuePart.on('mousedown', function (data) {
            dragging = true;
            startX = data.clientX;
            startValue = _this.getValue();
            if (_this.enableTooltip && _this.tooltipControl) {
                _this.tooltipControl.setEnableAlwaysShow(true);
            }
        });
        var setValue = function (data) {
            if (!dragging)
                return;
            var cX = data.clientX;
            var cValue = startValue + ((cX - startX) * (_this.maxValue - _this.minValue) / (_this.sliderLinePart.getWidth() - 10));
            cValue > _this.maxValue && (cValue = _this.maxValue);
            cValue < _this.minValue && (cValue = _this.minValue);
            _this.setValue(cValue);
            startX = cX;
            startValue = cValue;
            if (_this.enableTooltip && _this.tooltipControl) {
                _this.tooltipControl.updatePosition();
            }
            data.preventDefault && data.preventDefault();
        };
        var body = new Dom(document.body);
        body.on('mousemove', setValue);
        body.on('mouseup', function (data) {
            setValue(data);
            if (_this.enableTooltip && _this.tooltipControl) {
                _this.tooltipControl.setEnableAlwaysShow(false);
            }
            dragging = false;
        });
        return _this;
        // this._domElement.on('mouseout', (data: any)=> {setValue(data); dragging = false});
    }
    Slider.prototype.setValueRange = function (minValue, maxValue) {
        this.minValue = minValue;
        this.maxValue = maxValue;
    };
    // 获取当前slider的值
    Slider.prototype.getValue = function () {
        return this.value;
    };
    // 设置slider的值
    Slider.prototype.setValue = function (value) {
        this.value = Math.round(value);
        var cssValue = (this.sliderLinePart.getWidth() - 10) * this.value / (this.maxValue - this.minValue);
        this.sliderValuePart.css('left', cssValue + "px");
        this.sliderLeftPart.css('width', cssValue + 3 + "px");
        this.trigger(UIEvents$1.ValueChanged, this.value);
        if (this.enableTooltip) {
            this.setTooltipValue();
        }
        return this;
    };
    /**
     * 设置onChange回调函数
     * @param {Function} fn onChange回调函数
     */
    Slider.prototype.onChange = function (fn) {
        fn instanceof Function && (this.onChangeCallback = fn);
        return this;
    };
    Slider.prototype.setEnableTooltip = function (enable) {
        this.enableTooltip = enable;
        if (enable && !this.tooltipControl) {
            this.tooltipControl = new Tooltip({
                hostDom: this.sliderValuePart,
                content: '',
                placement: 'top',
                theme: 'dark',
                className: 'bfui-slider-tooltip'
            });
            this.setTooltipValue();
        }
    };
    Slider.prototype.setTooltipFormater = function (fn) {
        this.tooltipFormater = fn;
        this.setTooltipValue();
    };
    Slider.prototype.setTooltipValue = function () {
        if (!this.enableTooltip || !this.tooltipControl)
            return;
        this.tooltipControl.setHTML(this.tooltipFormater ? this.tooltipFormater(this.getValue()) : this.getValue().toString());
    };
    Slider.prototype.getTooltipControl = function () {
        return this.tooltipControl;
    };
    return Slider;
}(Control));

var SNS$6 = getUINamespace('Tree');
//Tree组件类
var Tree = /** @class */ (function (_super) {
    __extends(Tree, _super);
    function Tree(param) {
        var _this = this;
        SDM && SDM.send(SNS$6, "bf_c_UITree_new");
        param = param || {};
        var elementParam = {
            elementType: 'div',
            className: 'bfui-tree'
        };
        var controlParam = {
            elementParam: elementParam,
            type: 'Tree',
            id: param.id,
            parent: param.parent
        };
        _this = _super.call(this, controlParam) || this;
        var contentElementParam = {
            elementType: 'div',
            className: 'bfui-tree-content'
        };
        var contentControlParam = {
            elementParam: contentElementParam,
            type: 'ControlGroup',
        };
        var content = _this.content = new ControlGroup(contentControlParam);
        _this.header = new Dom().createElement({ elementType: 'div', className: 'bfui-tree-header' });
        _this.footer = new Dom().createElement({ elementType: 'div', className: 'bfui-tree-footer' });
        _this.title = new Dom().createElement({ elementType: 'div', className: 'bfui-tree-title' });
        _this._domElement.append(content.getDomElement());
        // this._domElement.append(header).append(content.getDomElement()).append(footer);
        // header.append(title);
        param.title && _this.setTitle(param.title);
        _this.sizeFixed = param.sizeFixed;
        return _this;
    }
    Tree.prototype.addTreeNode = function (control, option) {
        var _this = this;
        this.content.addControl(control, option);
        control.on(UIEvents$1.RightClicked, function (params) {
            _this.trigger(UIEvents$1.RightClicked, params);
        });
    };
    Tree.prototype.getControl = function (condition) {
        return this.content.getControl(condition);
    };
    Tree.prototype.getAllControls = function () {
        return this.content.getAllControls();
    };
    /******* public for client *********/
    // 设置Header文字
    Tree.prototype.setTitle = function (text) {
        this.title.html(text);
        return this;
    };
    Tree.prototype.getTitle = function () {
        return this.title.html();
    };
    // 获取当前选择项
    Tree.prototype.getSelection = function () {
        // 交互细节待补充
    };
    // 清空当前选择
    Tree.prototype.clearSelection = function () {
        var selection = function (control) {
            if (control.length > 0) {
                control.find(function (subChild) {
                    if (subChild.getSelectState()) {
                        subChild.setSelectState(false);
                    }
                    else {
                        selection(subChild.getAllControls());
                    }
                });
            }
        };
        selection(this.getAllControls());
    };
    return Tree;
}(Control));

/**
 * @namespace Glodon.Bimface.Tiles.UI.ControlAlignOption
 * @classdesc 常量：UI对齐样式（控件对齐样式的选项）
 * @description Glodon.Bimface.Tiles.UI.ControlAlignOption
 * @property {String} Left 左侧对齐
 * @property {String} Center 居中对齐
 * @property {String} Right 右侧对齐
 * @property {String} Top 顶部对齐
 * @property {String} Middle 中部对齐
 * @property {String} Bottom 底部对齐
 */
var ControlAlignOption;
(function (ControlAlignOption) {
    ControlAlignOption["Left"] = "Left";
    ControlAlignOption["Center"] = "Center";
    ControlAlignOption["Right"] = "Right";
    ControlAlignOption["Top"] = "Top";
    ControlAlignOption["Middle"] = "Middle";
    ControlAlignOption["Bottom"] = "Bottom";
})(ControlAlignOption || (ControlAlignOption = {}));
var ControlAlignOption$1 = ControlAlignOption;

/**
 * @classdesc 类：工具条组件类
 * @class Glodon.Bimface.Tiles.UI.Toolbar
 * @constructs Glodon.Bimface.Tiles.UI.Toolbar
 * @extends Glodon.Bimface.Tiles.UI.Control
 * @extends Glodon.Bimface.Tiles.UI.ControlGroup
 * @description 构造工具条组件对象
 * @param {Object} option 构造工具条对象的配置项
 * @param {String} option.id 工具条对象ID
 * @param {Boolean} option.isHorizontal 工具条是否为水平放置，默认为true
 * @param {Boolean} option.isFullLength 工具条长度是否与视图DOM容器保持一致，默认为false
 * @param {Glodon.Bimface.Tiles.UI.ControlAlignOption} option.controlAlign 组件的对齐方式，当水平放置时默认为Left，垂直放置时默认为Top
 */
var Toolbar = /** @class */ (function (_super) {
    __extends(Toolbar, _super);
    function Toolbar(param) {
        var _this = this;
        param = param || {};
        var elementParam = SyleUtil.formatElementParam({
            elementType: 'div',
            className: 'bfui-toolbar'
        }, param.elementParam);
        var controlParam = {
            elementParam: elementParam,
            type: 'Toolbar',
            id: param.id,
            parent: param.parent
        };
        _this = _super.call(this, controlParam) || this;
        _this.isHorizontal = param.isHorizontal !== false;
        _this.isFullLength = param.isFullLength === true;
        _this.controlAlign = param.controlAlign || (_this.isHorizontal ? ControlAlignOption$1.Left : ControlAlignOption$1.Top);
        var fullClass = _this.isFullLength ? '-full' : '';
        _this.isHorizontal ? _this.addClass("bfui-toolbar-horizontal" + fullClass) : _this.addClass("bfui-toolbar-vertical" + fullClass);
        _this.addClass("bfui-align-" + _this.controlAlign.toLowerCase());
        return _this;
    }
    return Toolbar;
}(ControlGroup));

var JustifyContent;
(function (JustifyContent) {
    JustifyContent["Start"] = "flex-start";
    JustifyContent["End"] = "flex-end";
    JustifyContent["Center"] = "center";
    JustifyContent["Between"] = "space-between";
    JustifyContent["Around"] = "space-around";
})(JustifyContent || (JustifyContent = {}));
/**
 * @classdesc 类：行元素组件类
 * @class Glodon.Bimface.Tiles.UI.Row
 * @constructs Glodon.Bimface.Tiles.UI.Row
 * @extends Glodon.Bimface.Tiles.UI.Control
 * @extends Glodon.Bimface.Tiles.UI.ControlGroup
 * @description 构造行元素组件对象
 * @param {Object} option 构造行元素对象的配置项
 * @param {String} option.id 行元素对象ID
 */
var Row = /** @class */ (function (_super) {
    __extends(Row, _super);
    function Row(param) {
        var _this = this;
        param = param || {};
        var elementParam = {
            elementType: 'div',
            className: 'bfui-row'
        };
        var controlParam = {
            elementParam: elementParam,
            type: 'Row',
            id: param.id,
            parent: param.parent
        };
        _this = _super.call(this, controlParam) || this;
        _this.setJustifyContent(param.justifyContent);
        param.contents && param.contents.forEach(function (control) {
            _this.addControl(control);
        });
        return _this;
    }
    Row.prototype.setJustifyContent = function (justifyContentKeyParam) {
        var justifyContentKey = Object.keys(JustifyContent).indexOf(justifyContentKeyParam) >= 0 ? justifyContentKeyParam : 'Between';
        var justifyContent = JustifyContent[justifyContentKey];
        this.getDomElement().css('justifyContent', justifyContent);
    };
    return Row;
}(ControlGroup));

var SNS$5 = getUINamespace('RadioGroup');
/**
 * @classdesc 类：RadioGroup组件类
 * @class Glodon.Bimface.Tiles.UI.RadioGroup
 * @constructs Glodon.Bimface.Tiles.UI.RadioGroup
 * @description 构造单选组件对象
 * @extends Glodon.Bimface.Tiles.UI.Control
 * @param {Object} opt 构造单选组件的配置项
 * @param {String} opt.id 单选对象ID
 * @param {Array} opt.options 单选框数组
 * @param {String} opt.options.value 单选框的返回值
 * @param {String} opt.options.label 单选框显示的文本信息
 * @param {Boolean} opt.options.checked	单选框的选中状态
 * @param {Boolean} opt.options.enabled	单选框的可用状态，缺省值为true
 */
var RadioGroup = /** @class */ (function (_super) {
    __extends(RadioGroup, _super);
    function RadioGroup(param) {
        var _this = this;
        SDM.send(SNS$5, "bf_c_UIRadioGroup_new");
        param = param || {};
        var elementParam = {
            elementType: 'div',
            className: 'bfui-radioGroup'
        };
        var controlParam = {
            elementParam: elementParam,
            type: 'RadioGroup',
            id: param.id,
            parent: param.parent
        };
        _this = _super.call(this, controlParam) || this;
        _this.radioArray = [];
        param.options && _this.setOptions(param.options);
        _this.on(UIEvents$1.ValueChanged, function (radio, value, checked) {
            checked && _this.setCheckedByValue(value);
        });
        return _this;
    }
    // 创建单个radio
    RadioGroup.prototype.addRadio = function (param) {
        var _this = this;
        param.parent = this.getDomElement();
        var radio = new Radio(param);
        radio.onChange(function (radio, value) {
            radio.isChecked() && _this.trigger(UIEvents$1.ValueChanged, radio, value, radio.isChecked());
        });
        this.radioArray.push(radio);
        return radio;
    };
    // 创建多个radio
    RadioGroup.prototype.setOptions = function (options) {
        var _this = this;
        var checkedIndex;
        this.clearOptions();
        options.forEach(function (label, index) {
            if (checkedIndex == undefined && label.checked) {
                checkedIndex = index;
            }
            _this.addRadio(label);
        });
        this.setChecked(checkedIndex == undefined ? 0 : checkedIndex);
    };
    // 获取单选框数值内容列表
    RadioGroup.prototype.getOptions = function () {
        var options = [];
        this.radioArray.forEach(function (radio) {
            options.push({ value: radio.getValue(), enabled: radio.isEnabled(), checked: radio.isChecked(), label: radio.getLabel() });
        });
        return options;
    };
    /**
     * 设置选中的单选框
     * @function Glodon.Bimface.Tiles.UI.RadioGroup.prototype.setChecked
     * @param {Number} index 选中选项的序号
     */
    RadioGroup.prototype.setChecked = function (index) {
        this.radioArray.forEach(function (radio, idx) {
            radio.setChecked(idx === index);
        });
    };
    //设置选中的单选框
    RadioGroup.prototype.setCheckedByValue = function (value) {
        this.radioArray.forEach(function (radio) {
            radio.setChecked(radio.getValue() == value);
        });
    };
    /**
    * 获取当前选中选项的序号
    * @function Glodon.Bimface.Tiles.UI.RadioGroup.prototype.getChecked
    * @returns {Number} 选中选项的序号
    */
    RadioGroup.prototype.getChecked = function () {
        var index;
        this.radioArray.forEach(function (radio, idx) {
            radio.isChecked() && (index = idx);
        });
        return index;
    };
    // 清空radio
    RadioGroup.prototype.clearOptions = function () {
        this.radioArray = [];
        this._domElement.html('');
    };
    /**
     * 设置可用的单选框选项
     * @function Glodon.Bimface.Tiles.UI.RadioGroup.prototype.setEnabled
     * @param {Array} index 可用选项的序号列表
     */
    RadioGroup.prototype.setEnabled = function (indexs) {
        this.radioArray.forEach(function (radio, idx) {
            radio.setEnabled(indexs.indexOf(idx) > -1);
        });
    };
    /**
     * 获取可用的单选框选项
     * @function Glodon.Bimface.Tiles.UI.RadioGroup.prototype.getEnabled
     * @returns {Array} 可用选项的序号列表
     */
    RadioGroup.prototype.getEnabled = function () {
        var indexs = [];
        this.radioArray.forEach(function (radio, idx) {
            radio.isEnabled() && indexs.push(idx);
        });
        return indexs;
    };
    /**
     * 单选框值发生变化的事件
     * @function Glodon.Bimface.Tiles.UI.RadioGroup.prototype.onChange
     * @param {Function} event 单选框值发生变化时执行的函数
     */
    RadioGroup.prototype.onChange = function (fn) {
        this.on(UIEvents$1.ValueChanged, fn);
        return this;
    };
    return RadioGroup;
}(Control));

//PropertyPanel组件类
var PropertyControl = /** @class */ (function (_super) {
    __extends(PropertyControl, _super);
    function PropertyControl(param) {
        var _this = this;
        param = param || {};
        var elementParam = {
            elementType: 'table',
            className: 'bfui-property-panel'
        };
        var controlParam = {
            elementParam: elementParam,
            type: 'PropertyPanel',
        };
        _this = _super.call(this, controlParam) || this;
        _this.createCollapse(param.propertyData);
        return _this;
    }
    // 清空radio
    PropertyControl.prototype.clear = function () {
        this._domElement.html('');
    };
    PropertyControl.prototype.createCollapse = function (properties) {
        var _this = this;
        this.clear();
        if (properties) {
            properties.forEach(function (item) {
                item.parent = _this.getDomElement();
                new Collapse(item);
            });
        }
    };
    return PropertyControl;
}(Control));

/**
 * TabOption组件类，作为Tab组件的单条option
 */
var Tab = /** @class */ (function (_super) {
    __extends(Tab, _super);
    function Tab(param) {
        var _this = this;
        var elementParam = {
            elementType: 'div',
            className: 'bfui-tab'
        };
        var controlParam = {
            elementParam: elementParam,
            type: 'Tab',
            name: 'tab',
            className: 'bfui-tab-tab',
            parent: param.parent
        };
        param = Object.assign({}, controlParam, param);
        param.extraClass && (elementParam.className = elementParam.className + ' ' + param.extraClass);
        _this = _super.call(this, param) || this;
        _this.param = param;
        _this._domElement.on('click', function (evt) {
            if (!_this.isActived() && _this.isEnabled()) {
                _this.trigger(UIEvents$1.SelectionChanged, _this);
            }
        });
        param.label && _this.setLabel(param.label);
        param.enabled === false && _this.setEnabled(param.enabled);
        return _this;
    }
    Tab.prototype.isEnabled = function () {
        return !this._domElement.hasClass('disabled');
    };
    Tab.prototype.setEnabled = function (isEnabled) {
        if (isEnabled != this.isEnabled()) {
            this._domElement.toggle('disabled');
        }
        return this;
    };
    Tab.prototype.setLabel = function (text) {
        this.setHTML(text);
        return this;
    };
    Tab.prototype.setActive = function () {
        if (!this.isActived()) {
            this._domElement.addClass('is-active');
        }
        return this;
    };
    Tab.prototype.isActived = function () {
        return this._domElement.hasClass('is-active');
    };
    Tab.prototype.getValue = function () {
        return this.param.value;
    };
    return Tab;
}(Control));

var SNS$4 = getUINamespace('Tabs');
/**
 * Tabs组件类
 */
var Tabs = /** @class */ (function (_super) {
    __extends(Tabs, _super);
    function Tabs(param) {
        var _this = this;
        SDM && SDM.send(SNS$4, "bf_c_UITabs_new");
        param = param || {};
        var elementParam = {
            elementType: 'div',
            className: 'bfui-tabs'
        };
        var controlParam = {
            elementParam: elementParam,
            type: 'Tabs',
            id: param.id,
            parent: param.parent
        };
        _this = _super.call(this, controlParam) || this;
        var header = _this.header = new Dom().createElement({ elementType: 'div', className: 'bfui-tabs-header' });
        var content = _this.content = new Dom().createElement({ elementType: 'div', className: 'bfui-tabs-content' });
        _this.getDomElement().append(header).append(content);
        _this.tabLeft = param.tabLeft != null ? param.tabLeft : null;
        _this.tabsArray = [];
        param.options && _this.setOptions(param.options);
        return _this;
    }
    // 创建单个tab
    Tabs.prototype.addTab = function (param) {
        var _this = this;
        param.parent = this.header;
        var tab = new Tab(param);
        var pane = new Dom().createElement({ elementType: 'div', className: 'bfui-tabs-pane', id: "pane-" + param.name });
        this.content.append(pane);
        if (typeof param.content === 'string') {
            pane.html(param.content);
        }
        else {
            pane.append(param.content);
        }
        tab.on(UIEvents$1.SelectionChanged, function (tab) {
            var idx = _this.getIndex(tab);
            _this.setActive(idx);
            _this.trigger(UIEvents$1.SelectionChanged, tab.param);
        });
        this.tabsArray.push(tab);
        return tab;
    };
    Tabs.prototype.getIndex = function (tab) {
        for (var i = 0; i < this.tabsArray.length; i++) {
            if (tab.param.name === this.tabsArray[i].param.name) {
                return i;
            }
        }
    };
    // 创建多个tab
    Tabs.prototype.setOptions = function (options) {
        var _this = this;
        var activedIndex;
        this.clearOptions();
        this.header.html("<div class=\"bfui-tabs-active-bar\"></div>");
        options.forEach(function (label, index) {
            if (activedIndex == undefined && label.actived) {
                activedIndex = index;
            }
            _this.addTab(label);
        });
        this.setActive(activedIndex == undefined ? 0 : activedIndex);
    };
    // 获取tab数值内容列表
    Tabs.prototype.getOptions = function () {
        var options = [];
        this.tabsArray.forEach(function (tab) {
            options.push(tab.param);
        });
        return options;
    };
    /**
     * 设置选中的tab
     * @function Glodon.Bimface.Tiles.UI.Tabs.prototype.setActive
     * @param {Number} index 选中选项的序号
     */
    Tabs.prototype.setActive = function (index) {
        var _this = this;
        var left = 0;
        this.tabsArray.forEach(function (tab, idx) {
            if (idx === index) {
                _this.activeValue = tab.getValue();
                var header_active = _this.header.getElement().querySelector('.is-active');
                var content_active = _this.content.getElement().querySelector('.is-active');
                header_active && header_active.removeClass('is-active');
                content_active && content_active.removeClass('is-active');
                tab.setActive();
                var pane = _this.content.getElement().querySelector("#pane-" + tab.param.name);
                pane.addClass('is-active');
                var header_bar = _this.header.getElement().querySelector('.bfui-tabs-active-bar');
                if (index == 1)
                    left = left - 5;
                if (index == 2)
                    left = left - 10;
                header_bar.style.transform = "translateX(" + left + "px)";
                header_bar.style.width = tab.getDomElement().getWidth() + 'px';
            }
            else {
                var marginLeft = _this.tabLeft ? _this.tabLeft : 20;
                left = left + tab.getDomElement().getWidth() + marginLeft;
            }
        });
        this.selectHandler && this.selectHandler(this.activeValue);
    };
    Tabs.prototype.getActiveValue = function () {
        return this.activeValue;
    };
    // 清空
    Tabs.prototype.clearOptions = function () {
        this.tabsArray = [];
        this.header.html('');
        this.content.html('');
    };
    /**
     * 设置可用的单选框选项
     * @function Glodon.Bimface.Tiles.UI.Tabs.prototype.setEnabled
     * @param {Array} index 可用选项的序号列表
     */
    Tabs.prototype.setEnabled = function (indexs) {
        this.tabsArray.forEach(function (tab, idx) {
            tab.setEnabled(indexs.indexOf(idx) > -1);
        });
    };
    Tabs.prototype.onSelect = function (fn) {
        this.selectHandler = fn;
    };
    return Tabs;
}(Control));

var SNS$3 = getUINamespace('Input');
/**
 * @classdesc 类：Crumb组件类
 * @class Glodon.Bimface.Tiles.UI.Crumb
 * @constructs Glodon.Bimface.Tiles.UI.Crumb
 * @description 构造面包屑组件
 * @extends Glodon.Bimface.Tiles.UI.Control
 * @param {Object} option 构造面包屑的配置项
 * @param {String} option.id 面包屑ID
 */
var Crumb = /** @class */ (function (_super) {
    __extends(Crumb, _super);
    function Crumb(param) {
        var _this = this;
        SDM && SDM.send(SNS$3, "bf_c_UICrumb_new");
        param = param || {};
        var elementParam = {
            elementType: 'div',
            className: 'bfui-crumb'
        };
        var controlParam = {
            elementParam: elementParam,
            type: 'Crumb',
            id: param.id,
            parent: param.parent
        };
        _this = _super.call(this, controlParam) || this;
        _this.value = [];
        _this.getDomElement().on('click', function (event) {
            var target = new Dom(event.target);
            var selectedId = target.attribute('data-id');
            if (selectedId) {
                _this._selectHandler && _this._selectHandler(selectedId);
            }
        });
        return _this;
    }
    /**
     * 获取面包屑的信息数组
     * @function Glodon.Bimface.Tiles.UI.Crumb.prototype.getValue
     * @returns {Array<ICrumbData>} 面包屑的信息数组
     */
    Crumb.prototype.getValue = function () {
        return this.value;
    };
    /**
     * 设置面包屑的信息数组
     * @function Glodon.Bimface.Tiles.UI.Crumb.prototype.setValue
     * @param {Array<ICrumbData>} value 面包屑的信息数组
     */
    Crumb.prototype.setValue = function (value) {
        var _this = this;
        this.value = value;
        this.getDomElement().clear();
        value.forEach(function (data, index) {
            var item = new Dom().createElement({ elementType: 'a', className: 'bfui-crumb-item' });
            item.html(data.text);
            item.attribute('title', data.text);
            item.attribute('data-id', data.id);
            _this.getDomElement().append(item);
            if (index !== value.length - 1) {
                var array = new Dom().createElement({ elementType: 'i', className: 'bfui-crumb-array gld-bf-arrow-up-sm' });
                _this.getDomElement().append(array);
            }
        });
        return this;
    };
    Crumb.prototype.onSelect = function (handler) {
        this._selectHandler = handler;
        return this;
    };
    return Crumb;
}(Control));

var SNS$2 = getUINamespace('Input');
/**
 * @classdesc 类：List组件类
 * @class Glodon.Bimface.Tiles.UI.List
 * @constructs Glodon.Bimface.Tiles.UI.List
 * @description 构造列表组件
 * @extends Glodon.Bimface.Tiles.UI.Control
 * @param {Object} option 构造列表的配置项
 * @param {String} option.id 列表ID
 */
var List = /** @class */ (function (_super) {
    __extends(List, _super);
    function List(param) {
        var _this = this;
        SDM && SDM.send(SNS$2, "bf_c_UIList_new");
        param = param || {};
        var elementParam = {
            elementType: 'div',
            className: 'bfui-list'
        };
        var controlParam = {
            elementParam: elementParam,
            type: 'List',
            id: param.id,
            parent: param.parent
        };
        _this = _super.call(this, controlParam) || this;
        _this.value = [];
        _this._domElements = [];
        _this._checkedList = [];
        _this._singleCheckedMode = false;
        _this._checkBoxMap = new Map();
        return _this;
    }
    /**
     * 获取列表的信息数组
     * @function Glodon.Bimface.Tiles.UI.List.prototype.getValue
     * @returns {Array<IListItemData>} 列表的信息数组
     */
    List.prototype.getValue = function () {
        return this.value;
    };
    /**
     * 设置列表的信息数组
     * @function Glodon.Bimface.Tiles.UI.List.prototype.setValue
     * @param {Array<IListItemData>} value 列表的信息数组
     */
    List.prototype.setValue = function (value) {
        var _this = this;
        this.value = value;
        this._domElements.forEach(function (dom) {
            dom.destroy();
        });
        this._domElements = [];
        this._checkedList = [];
        this._checkBoxMap.clear();
        this.getDomElement().clear();
        value.forEach(function (data) {
            var row = new Row({ justifyContent: 'Start' });
            if (!!data.hasCheckBox) {
                var checkBox_1 = new Checkbox({ value: data.id });
                if (data.isCheckBoxDisabled) {
                    checkBox_1.setEnabled(false);
                }
                else {
                    checkBox_1.onChange(function () {
                        if (checkBox_1.isChecked()) {
                            if (_this._singleCheckedMode) {
                                _this._checkedList.forEach(function (value) {
                                    _this._checkBoxMap.get(value.id).setChecked(false);
                                });
                                _this._checkedList = [];
                            }
                            _this._checkedList.push(data);
                        }
                        else {
                            var index = _this._checkedList.indexOf(data);
                            if (index >= 0) {
                                _this._checkedList.splice(index, 1);
                            }
                        }
                        _this._checkedHandler && _this._checkedHandler(__spreadArrays(_this._checkedList));
                    });
                }
                _this._domElements.push(checkBox_1.getDomElement());
                _this._checkBoxMap.set(data.id, checkBox_1);
                row.addControl(checkBox_1);
                if (data.checked) {
                    checkBox_1.setChecked(true);
                }
            }
            if (!!data.hasIcon) {
                var icon = void 0;
                if (data.iconType === 'svg') {
                    icon = new Dom().createElement({ elementType: 'div', className: "bfui-list-icon" });
                    icon.html("<svg class=\"bimface-svgicon svg-icon\" aria-hidden=\"true\">\n            <use xlink:href=\"#" + data.iconClass + "\"></use>\n          </svg>");
                }
                else {
                    icon = new Dom().createElement({ elementType: 'i', className: "bfui-list-icon " + (data.iconClass ? data.iconClass : '') });
                }
                row.getDomElement().append(icon);
            }
            var text = new Dom().createElement({ elementType: 'div', className: 'bfui-list-text' });
            text.html(data.text);
            text.on('click', function () {
                _this._itemClickedHandler && _this._itemClickedHandler(data);
            });
            _this._domElements.push(text);
            row.getDomElement().append(text);
            _this.getDomElement().append(row.getDomElement());
        });
        return this;
    };
    List.prototype.onChecked = function (handler) {
        this._checkedHandler = handler;
        return this;
    };
    List.prototype.onItemClicked = function (handler) {
        this._itemClickedHandler = handler;
        return this;
    };
    List.prototype.getCheckedIds = function () {
        return this._checkedList.map(function (item) { return item.id; });
    };
    List.prototype.getCheckedValue = function () {
        return this._checkedList;
    };
    List.prototype.setSingleCheckedMode = function (singleCheckedMode) {
        this._singleCheckedMode = singleCheckedMode;
    };
    return List;
}(Control));

/**
 * SearchResultOption组件类，作为Search组件的单条option
 */
var SearchResultOption = /** @class */ (function (_super) {
    __extends(SearchResultOption, _super);
    function SearchResultOption(param) {
        var _this = this;
        param = param || {};
        var elementParam = {
            elementType: 'div',
            className: 'bfui-search-option'
        };
        var controlParam = {
            elementParam: elementParam,
            type: 'SearchResultOption',
        };
        _this = _super.call(this, controlParam) || this;
        _this.getDomElement().html(param.html || param.text || '');
        _this.param = param;
        _this.disabled = param.disabled === true;
        _this.selected = param.selected === true;
        _this.value = param.value;
        _this.text = param.text;
        _this.setTooltip(_this.text);
        _this.setDisabled(_this.disabled).setSelected(_this.selected);
        param.class && _this.addClass(param.class);
        // 点击本条option时，调用SearchResultOptionGroup的setSelected事件，将本条option设置为选中，其他option取消选中
        _this.onClick(function () {
            if (!_this.selected && !_this.disabled) {
                _this._parent.setSelected(_this);
            }
            _this.trigger(UIEvents$1.Clicked);
        });
        return _this;
    }
    /**
     * 设置选中状态
     * @param {Boolean} selected 选中状态
     */
    SearchResultOption.prototype.setSelected = function (selected) {
        if (!this.disabled && this.selected !== selected) {
            this.selected = selected;
            selected ? this.addClass('bfui-search-option-selected') : this.removeClass('bfui-search-option-selected');
        }
        return this;
    };
    /**
     * 设置不可用状态
     * @param {Boolean} disabled 不可用状态
     */
    SearchResultOption.prototype.setDisabled = function (disabled) {
        if (this.disabled !== disabled) {
            this.setSelected(false);
            this.disabled = disabled;
            disabled ? this.addClass('bfui-search-option-disabled') : this.removeClass('bfui-search-option-disabled');
        }
        return this;
    };
    /**
     * 获取option的文本内容
     */
    SearchResultOption.prototype.getText = function () {
        return this.text;
    };
    /**
     * 获取option的实际value，如未设置value则返回option的text文本内容
     */
    SearchResultOption.prototype.getValue = function () {
        return this.value === undefined ? this.getText() : this.value;
    };
    SearchResultOption.prototype.getParam = function () {
        return this.param;
    };
    return SearchResultOption;
}(Control));

/**
 * SearchResultOptionGroup组件类，管理多条SearchResultOption
 */
var SearchResultOptionGroup = /** @class */ (function (_super) {
    __extends(SearchResultOptionGroup, _super);
    function SearchResultOptionGroup(config) {
        var _this = this;
        var contentElementParam = {
            elementType: 'div',
            className: 'bfui-search-content'
        };
        var contentControlParam = {
            // parent: document.getElementsByTagName('body')[0],
            elementParam: contentElementParam,
            type: 'ControlGroup',
        };
        _this = _super.call(this, contentControlParam) || this;
        _this.enableAnimation = config.enableAnimation !== false;
        _this.enableAnimation && _this.addClass('bfui-search-animation');
        config.values && _this.setValues(config.values);
        _this.hide();
        return _this;
    }
    /**
     * 设置option内容
     * @param {Array<string | IOptionParam>} values option内容
     */
    SearchResultOptionGroup.prototype.setValues = function (values) {
        var _this = this;
        this.clearControls(true);
        this.values = [];
        values.forEach(function (value) {
            var param;
            if (!DataUtil.assertType(value, 'obj')) {
                param = { html: value.toString() };
            }
            else {
                param = value;
            }
            _this.values.push(param);
            var option = new SearchResultOption(param);
            _this.addControl(option);
            option.on(UIEvents$1.Clicked, function () {
                _this.trigger(UIEvents$1.Clicked, option);
            });
        });
        return this;
    };
    /**
     * 获取当前显示/隐藏状态
     */
    SearchResultOptionGroup.prototype.isVisible = function () {
        return this.enableAnimation ? this._visible : _super.prototype.isVisible.call(this);
    };
    SearchResultOptionGroup.prototype.setFixedPosition = function (position) {
        var pageHeight = window.innerHeight, maxHeight = 125, dom = this.getDomElement().getElement(), minHeight = Math.min(maxHeight, this._height);
        if (position.bottom + minHeight > pageHeight) {
            dom.style.top = '';
            dom.style.bottom = pageHeight - position.top + 'px';
        }
        else {
            dom.style.top = position.bottom + 'px';
            dom.style.bottom = '';
        }
        dom.style.left = position.left + 'px';
        dom.style.width = position.right - position.left + 'px';
    };
    /**
     * 显示组件
     */
    SearchResultOptionGroup.prototype.show = function () {
        var _this = this;
        if (this.enableAnimation) { // 支持动画状态时，通过height变化显示组件
            this._visible = true;
            var transitionEndFn_1 = function () {
                if (_this._inTransition) {
                    _this.getDomElement().removeClass('bfui-search-overflow-hidden').off('transitionend', transitionEndFn_1);
                    _this._inTransition = false;
                }
            };
            this._inTransition = true;
            if (this._children.length > 0) {
                var itemHeight = this._children[0].getDimensions().height;
                this._height = this._children.length * itemHeight + 2;
            }
            this.getDomElement().addClass('bfui-search-overflow-hidden').css({ 'border-width': '1px', 'height': this._height + 'px' }).on('transitionend', transitionEndFn_1);
        }
        else {
            _super.prototype.show.call(this);
        }
        return this;
    };
    /**
     * 隐藏组件
     */
    SearchResultOptionGroup.prototype.hide = function () {
        if (this.enableAnimation) { // 支持动画状态时，通过height变化隐藏组件
            this._visible = false;
            this.getDomElement().addClass('bfui-search-overflow-hidden').css({ 'border-width': '0px', 'height': '0px' });
        }
        else {
            _super.prototype.hide.call(this);
        }
        return this;
    };
    /**
     * 设置某条option为选中状态，其他option取消选中
     * @param {SearchResultOption | string} option 选中的内容
     */
    SearchResultOptionGroup.prototype.setSelected = function (option) {
        if (option === undefined)
            return;
        var isValidOption = false;
        if (!(option instanceof SearchResultOption)) {
            this.getAllControls().some(function (cControl) {
                if (cControl.getText() === option.toString()) {
                    option = cControl;
                    isValidOption = true;
                    return true;
                }
            });
        }
        else if (this.indexOf(option) >= 0) {
            isValidOption = true;
        }
        if (isValidOption && this.selected !== option) {
            this.getAllControls().forEach(function (cControl) {
                cControl.setSelected(cControl === option ? true : false);
            });
            this.selected = option;
            var index = this.indexOf(this.selected);
            var value = this.selected.getValue();
            var text = this.selected.getText();
            var data = this.selected.getParam();
            this.trigger(UIEvents$1.ValueChanged, { text: text, value: value, index: index, data: data });
        }
        return this;
    };
    /**
     * 根据序号设置Option选中状态
     * @param {Number} index Option
     */
    SearchResultOptionGroup.prototype.setSelectedByIndex = function (index) {
        var option = this.getControl({ index: index });
        if (option) {
            this.setSelected(option);
        }
        return this;
    };
    /**
     * 根据Value设置Option选中状态
     * @param {String | Number} value Option的Value值
     */
    SearchResultOptionGroup.prototype.setSelectedByValue = function (value) {
        var result;
        this.getAllControls().some(function (option) {
            option = option;
            if (option.getValue() === value) {
                result = option;
                return true;
            }
        });
        return this.setSelected(result);
    };
    /**
     * 获取当前选中的Option
     */
    SearchResultOptionGroup.prototype.getSelected = function () {
        return this.selected;
    };
    /**
     * 获取所有Option的信息数组
     */
    SearchResultOptionGroup.prototype.getValues = function () {
        return this.values;
    };
    SearchResultOptionGroup.prototype.clearSelection = function () {
        this.selected = null;
        this.trigger(UIEvents$1.ValueChanged);
    };
    return SearchResultOptionGroup;
}(ControlGroup));

getUINamespace('Search');
/**
 * Search组件类
 */
var Search = /** @class */ (function (_super) {
    __extends(Search, _super);
    function Search(param) {
        var _this = this;
        // SDM && SDM.send(SNS, "bf_c_UISearch_new");
        param = param || {};
        var elementParam = {
            elementType: 'div',
            className: 'bfui-search'
        };
        var controlParam = {
            elementParam: elementParam,
            type: 'Search',
            id: param.id,
            parent: param.parent
        };
        _this = _super.call(this, controlParam) || this;
        _this.enableAnimation = param.enableAnimation !== false;
        new Dom().createElement({ elementType: 'i', className: 'bfui-search-icon gld-bf-search-md', parent: _this.getDomElement() });
        _this.input = new Input({ parent: _this });
        _this.input.addClass('bfui-search-input')
            .onBlur(function () { return window.setTimeout(function () { return _this.searchResult.hide(); }, 100); })
            .onFocus(function () {
            if (_this.input.getValue()) {
                _this.onSearch();
            }
        });
        param.placeholder && _this.setPlaceholder(param.placeholder);
        _this.noResultText = param.noResultText || '没有找到对应内容';
        _this.setSearchHandler(param.searchHandler || (function () { return []; }));
        _this.setSelectHandler(param.selectHandler || (function () { return null; }));
        _this.searchResult = new SearchResultOptionGroup({ enableAnimation: _this.enableAnimation });
        new Dom(document.body).append(_this.searchResult.getDomElement());
        _this.searchResult.on(UIEvents$1.ValueChanged, function (data) {
            data && _this.input.setValue(data.text);
            _this.searchResult.hide();
            _this.selectHandler instanceof Function && _this.selectHandler(data);
        });
        return _this;
    }
    // input变化时搜索符合条件的结果
    Search.prototype.onSearch = function () {
        var _this = this;
        var value = this.input.getValue();
        if (!value) {
            this.searchResult.hide();
            return;
        }
        var searchResultValue = this.searchResultValue = this.searchHandler(value);
        if (searchResultValue && searchResultValue.length > 0) {
            this.searchResult.setValues(searchResultValue);
        }
        else { // 未搜索到结果
            this.searchResult.setValues([
                {
                    html: this.noResultText,
                    class: 'bfui-search-noresult',
                },
                {
                    html: '',
                    class: 'bfui-search-noresult',
                }
            ]);
        }
        var domRect = this.getPosition();
        this.searchResult.setFixedPosition(domRect);
        requestAnimationFrame(function () { return _this.searchResult.show(); });
    };
    // 设置搜索回调
    Search.prototype.setSearchHandler = function (handler) {
        var _this = this;
        if (handler instanceof Function) {
            this.searchHandler = handler;
            this.input.onInput(function () {
                _this.onSearch();
            });
        }
    };
    // 设置选中回调
    Search.prototype.setSelectHandler = function (handler) {
        if (handler instanceof Function) {
            this.selectHandler = handler;
        }
    };
    // 设置input中的placeholder
    Search.prototype.setPlaceholder = function (value) {
        this.input.getDomElement().attribute('placeholder', value);
    };
    // 设置未匹配到结果时的文本
    Search.prototype.setNoResultText = function (value) {
        this.noResultText = value;
    };
    Search.prototype.getSearchResultGroup = function () {
        return this.searchResult;
    };
    return Search;
}(Control));

/**
 * @classdesc 类：ColorPicker组件类
 * @class Glodon.Bimface.Tiles.UI.ColorPicker
 * @constructs Glodon.Bimface.Tiles.UI.ColorPicker
 * @description 构造颜色选择器组件
 * @extends Glodon.Bimface.Tiles.UI.Control
 * @param {Object} option 构造输入框的配置项
 * @param {String} option.id 输入框ID
 */
var ColorPicker = /** @class */ (function (_super) {
    __extends(ColorPicker, _super);
    function ColorPicker(param) {
        var _this = this;
        param = param || {};
        var elementParam = {
            elementType: 'div',
            className: 'bfui-colorpicker'
        };
        var controlParam = {
            elementParam: elementParam,
            type: 'ColorPicker',
            id: param.id,
            parent: param.parent
        };
        _this = _super.call(this, controlParam) || this;
        _this.inputColor = new Dom().createElement({ elementType: 'input', className: 'bfui-colorpicker-input' });
        _this.inputColor.attribute('type', 'color');
        _this._domElement.append(_this.inputColor);
        _this.onClick(function () {
            _this.inputColor.click();
        });
        _this.onInput(function () { });
        _this.setValue('');
        return _this;
    }
    /**
     * 获取输入框的值
     * @function Glodon.Bimface.Tiles.UI.Input.prototype.getValue
     * @returns {String} 输入框的值
     */
    ColorPicker.prototype.getValue = function () {
        return this.value;
    };
    /**
     * 设置输入框的值
     * @function Glodon.Bimface.Tiles.UI.Input.prototype.setValue
     * @param {String} value 输入框的值
     */
    ColorPicker.prototype.setValue = function (value) {
        this.value = value;
        if (value) {
            this.inputColor.attribute('value', value);
            this.getDomElement().css('background', value);
            this.getDomElement().removeClass('no-color');
        }
        else {
            this.getDomElement().css('background', 'none');
            this.getDomElement().addClass('no-color');
        }
        return this;
    };
    ColorPicker.prototype.onChange = function (fn) {
        var _this = this;
        this.onChangeFn && this._domElement.off('change', this.onChangeFn);
        this.onChangeFn = function (evt) {
            var value = evt.target.value;
            _this.value = value;
            _this.getDomElement().css('background', value);
            _this.getDomElement().removeClass('no-color');
            fn(value);
        };
        this._domElement.on('change', this.onChangeFn);
        return this;
    };
    ColorPicker.prototype.onInput = function (fn) {
        var _this = this;
        this.onInputFn && this._domElement.off('input', this.onInputFn);
        this.onInputFn = function (evt) {
            var value = evt.target.value;
            _this.value = value;
            _this.getDomElement().css('background', value);
            _this.getDomElement().removeClass('no-color');
            fn(value);
        };
        this._domElement.on('input', this.onInputFn);
        return this;
    };
    return ColorPicker;
}(Control));

var PropertyPanel = PropertyControl;

var UIMap = /*#__PURE__*/Object.freeze({
    __proto__: null,
    Button: Button,
    Toolbar: Toolbar,
    Row: Row,
    RadioGroup: RadioGroup,
    PropertyPanel: PropertyPanel,
    UIEvents: UIEvents$1,
    Label: Label,
    Link: Link,
    Input: Input,
    Panel: Panel,
    Select: Select,
    Switch: Switch,
    Checkbox: Checkbox,
    Collapse: Collapse,
    Radio: Radio,
    Slider: Slider,
    Tree: Tree,
    Tabs: Tabs,
    ControlAnchor: ControlAnchor$1,
    ControlAlignOption: ControlAlignOption$1,
    Crumb: Crumb,
    List: List,
    Search: Search,
    ColorPicker: ColorPicker
});

var sign_enum = {
    SIGN_END: "SIGN_END",
    SIGN_END_OK: "SIGN_EN_OK",
    SIGN_START: "SIGN_START",
    SIGN_START_OK: "SIGN_START_OK",
};
var htmlStrParser = function (htmlStr) {
    var str = htmlStr.replace(/\n/g, "");
    var result = { nodeName: "root", children: [] };
    var use_line = [0];
    var current_index = 0;
    var node = result;
    var sign = "";
    var status = "";
    for (var i = 0; i < str.length; i++) {
        var current = str.charAt(i);
        var next = str.charAt(i + 1);
        if (current === "<") {
            if (sign && status === sign_enum.SIGN_START_OK) {
                node.text = sign;
                sign = "";
            }
            if (next === "/") {
                status = sign_enum.SIGN_END;
            }
            else {
                status = sign_enum.SIGN_START;
            }
        }
        else if (current === ">") {
            if (status === sign_enum.SIGN_START) {
                node = result;
                use_line.map(function (_, index) {
                    if (!node.children)
                        node.children = [];
                    if (index === use_line.length - 1) {
                        sign = sign.replace(/^\s*/g, "");
                        var mark = sign.match(/^[a-zA-Z0-9]*\s*/)[0].replace(/\s/g, "");
                        var attributeStr = sign.replace(mark, ' ').match((/(^|\s)(\S+?=".+?")/g));
                        var attrbuteObj_1 = {};
                        var style_1 = {};
                        attributeStr && attributeStr.map(function (attr) {
                            if (attr) {
                                var value = /="(.+?)"/.exec(attr)[1];
                                var key = attr.split("=")[0].replace(/\s/g, "");
                                if (key === "style") {
                                    value.split(";").map(function (s) {
                                        if (s) {
                                            style_1[s.split(":")[0]] = s.split(":")[1];
                                        }
                                    });
                                    return attrbuteObj_1[key] = style_1;
                                }
                                attrbuteObj_1[key] = value;
                            }
                        });
                        node.children.push(__assign({ nodeName: mark, children: [] }, attrbuteObj_1));
                    }
                    current_index = node.children.length - 1;
                    node = node.children[current_index];
                });
                use_line.push(current_index);
                sign = "";
                status = sign_enum.SIGN_START_OK;
            }
            if (status === sign_enum.SIGN_END) {
                use_line.pop();
                node = result;
                use_line.map(function (i) {
                    node.children[i] && (node = node.children[i]);
                });
                sign = "";
                status = sign_enum.SIGN_END_OK;
            }
        }
        else {
            sign = sign + current;
        }
    }
    return result;
};
var attributeKeysList = ['title']; // 后续扩展
var render = function (html, container, scope) {
    var parseResult = htmlStrParser(html);
    var renderItemMap = {};
    var createItem = function (propsMap, parentItem) {
        var nodeName = propsMap.nodeName, ref = propsMap.ref, text = propsMap.text, style = propsMap.style;
        var className = propsMap.class;
        var item;
        var dom;
        if (UIMap[nodeName]) {
            var config = ref !== undefined ? { id: ref } : {};
            if (propsMap['bf-config'] && scope && Object.prototype.toString.call(scope[propsMap['bf-config']]) === '[object Object]') {
                config = __assign(__assign({}, config), scope[propsMap['bf-config']]);
            }
            item = new UIMap[propsMap.nodeName](config);
            dom = item.getDomElement();
            className && item.addClass.apply(item, className.split(' '));
        }
        else {
            item = dom = new Dom().createElement({ elementType: nodeName, className: className });
        }
        text !== undefined && dom.html(text);
        style && dom.css(style);
        if (propsMap.hasOwnProperty('disabled') && propsMap.disabled !== 'false') {
            dom.attribute('disabled', true);
        }
        attributeKeysList.forEach(function (attributeKey) {
            if (propsMap.hasOwnProperty(attributeKey)) {
                dom.attribute(attributeKey, propsMap[attributeKey]);
            }
        });
        parentItem.append(dom);
        if (scope) {
            var expressionValue_1 = {};
            var listen_1 = function (key, execute) {
                if (propsMap[key].indexOf('scope.data') >= 0) { // 存在scope，认为是包含data某字段的表达式，当data任一字段更新时响应
                    var update_1 = function () {
                        var lastValue = expressionValue_1[key];
                        var value;
                        try {
                            value = eval(propsMap[key]);
                            expressionValue_1[key] = value;
                        }
                        catch (err) {
                            console.log(err);
                            eventEmmiter_1.off('ValueChanged', update_1);
                        }
                        execute(value, lastValue);
                    };
                    eventEmmiter_1.on('ValueChanged', update_1);
                    update_1();
                }
                else if (scope.data.hasOwnProperty(propsMap[key])) { // scope.data中有对应字段，直接采用data的字段做响应化监控
                    execute(scope.data[propsMap[key]]);
                    eventEmmiter_1.on(propsMap[key], execute);
                }
            };
            var eventEmmiter_1 = new EventEmmiter();
            var proxy = new Proxy(scope.data, {
                set: function (obj, prop, value) {
                    var formerValue = obj[prop];
                    obj[prop] = value;
                    eventEmmiter_1.trigger(prop, value, formerValue);
                    eventEmmiter_1.trigger('ValueChanged', prop, value, formerValue);
                    return true;
                }
            });
            scope.data = proxy;
            var _loop_1 = function (key) {
                if (key.indexOf('@') === 0) {
                    Object.prototype.toString.call(scope[propsMap[key]]) === '[object Function]' && dom.on(key.slice(1), function () { return scope[propsMap[key]](); });
                }
                if (key === 'bf-disabled') {
                    listen_1('bf-disabled', function (value) {
                        if (Object.prototype.toString.call(value) === '[object Boolean]') {
                            dom.attribute('disabled', value);
                        }
                    });
                }
                if (key === 'bf-class') {
                    listen_1('bf-class', function (value, formerValue) {
                        if (Object.prototype.toString.call(value) === '[object String]') {
                            formerValue && dom.removeClass(formerValue);
                            value && dom.addClass(value);
                        }
                    });
                }
                if (key === 'bf-show') {
                    listen_1('bf-show', function (value) {
                        if (Object.prototype.toString.call(value) === '[object Boolean]') {
                            value ? dom.show() : dom.hide();
                        }
                    });
                }
                attributeKeysList.forEach(function (attributeKey) {
                    if (key === "bf-" + attributeKey) {
                        listen_1("bf-" + attributeKey, function (value) {
                            if (Object.prototype.toString.call(value) === '[object String]') {
                                dom.attribute(attributeKey, value);
                            }
                        });
                    }
                });
            };
            for (var key in propsMap) {
                _loop_1(key);
            }
        }
        if (propsMap.children.length > 0) {
            propsMap.children.forEach(function (child) {
                createItem(child, item);
            });
        }
        ref && (renderItemMap[ref] = item);
    };
    parseResult.children.forEach(function (child) {
        createItem(child, container);
    });
    return renderItemMap;
};

/**
 * @namespace  Glodon.Bimface.Module.Linkage2D3D.WebApplicationEvent
 * @classdesc WebApplicationEvent的事件
 * @description  Glodon.Bimface.Module.Linkage2D3D.WebApplicationEvent
 * @property {String} ModuleDestroyed 组件销毁完成事件
 * @property {String} ViewerDrawingInitialized ViewerDrawing初始化完成事件
 */
var WebApplicationEvent;
(function (WebApplicationEvent) {
    WebApplicationEvent["ModuleInitialized"] = "ModuleInitialized";
    WebApplicationEvent["ModuleDestroyed"] = "ModuleDestroyed";
    WebApplicationEvent["ViewerDrawingInitialized"] = "ViewerDrawingAdded";
})(WebApplicationEvent || (WebApplicationEvent = {}));
var WebApplicationEvent$1 = WebApplicationEvent;

var eventBus = new EventEmmiter();

var DrawableItem = /** @class */ (function () {
    function DrawableItem() {
        this.xmlns = "http://www.w3.org/2000/svg";
        this.material = null;
        this.svgNode = null;
        this.children = [];
        this.glodonColor = '#11DAB7';
        this.position = new THREE.Vector2();
    }
    DrawableItem.prototype.add = function (item) {
        this.children.push(item);
    };
    DrawableItem.prototype.getSvgNode = function () {
        return this.svgNode;
    };
    DrawableItem.prototype.abtainRenderables = function (container) {
        container.appendChild(this.svgNode);
    };
    DrawableItem.prototype.isMatch = function (name) {
        return this.name == name;
    };
    DrawableItem.prototype.highlight = function () {
        this.svgNode.setAttribute('style', 'stroke:' + this.glodonColor);
    };
    DrawableItem.prototype.cancelHighlight = function () {
        this.svgNode.setAttribute('style', 'stroke:' + this.material.color.getStyle());
    };
    DrawableItem.prototype.locate = function (posX, posY) {
        this.position.set(posX, posY);
        this.move(0, 0);
    };
    DrawableItem.prototype.move = function (deltaX, deltaY) {
        var offsetX = this.position.x + deltaX;
        var offsetY = this.position.y + deltaY;
        this.svgNode.setAttribute("transform", "translate(" + offsetX + "," + offsetY + ")");
    };
    return DrawableItem;
}());

var EditorName;
(function (EditorName) {
    EditorName["Default"] = "Editor";
    EditorName["PICK_Editor"] = "Pick";
    EditorName["RECTPICK_Editor"] = "RectPick";
    EditorName["PAN_Editor"] = "Pan";
    EditorName["Zoom_Editor"] = "Zoom";
})(EditorName || (EditorName = {}));
var Editor$1 = /** @class */ (function () {
    function Editor() {
        this.bIsMouseDown = false;
        this.name = EditorName.Default;
    }
    Editor.prototype.onMouseDown = function (event) {
    };
    Editor.prototype.onMouseMove = function (event) {
    };
    Editor.prototype.onMouseUp = function (event) {
    };
    Editor.prototype.onMouseWheel = function (event) {
    };
    Editor.prototype.getName = function () {
        return this.name;
    };
    return Editor;
}());

var EventType;
(function (EventType) {
    EventType[EventType["RECTPICK_MOUSE_DOWN"] = 1000] = "RECTPICK_MOUSE_DOWN";
    EventType[EventType["RECTPICK_MOUSE_MOVE"] = 1001] = "RECTPICK_MOUSE_MOVE";
    EventType[EventType["RECTPICK_MOUSE_UP"] = 1002] = "RECTPICK_MOUSE_UP";
    EventType[EventType["PICK_MOUSE_DOWN"] = 2000] = "PICK_MOUSE_DOWN";
    EventType[EventType["PICK_MOUSE_MOVE"] = 2001] = "PICK_MOUSE_MOVE";
    EventType[EventType["PICK_MOUSE_UP"] = 2002] = "PICK_MOUSE_UP";
    EventType[EventType["Floor_Plane_Changed"] = 3000] = "Floor_Plane_Changed";
    EventType[EventType["Floor_Plane_Changed_For_Panel"] = 3001] = "Floor_Plane_Changed_For_Panel";
    EventType[EventType["Resize"] = 4000] = "Resize";
    EventType[EventType["Camera_Height_Changed"] = 5000] = "Camera_Height_Changed";
    EventType[EventType["ZOOM_MOUSE_WHEEL"] = 6000] = "ZOOM_MOUSE_WHEEL";
    EventType[EventType["PAN_MOUSE_MOVE"] = 7000] = "PAN_MOUSE_MOVE";
    // 小地图框选
    EventType[EventType["Minimap_Rect_Changed"] = 8000] = "Minimap_Rect_Changed";
    EventType[EventType["Minimap_Rect_Destroyed"] = 8001] = "Minimap_Rect_Destroyed";
})(EventType || (EventType = {}));
var MouseEventType;
(function (MouseEventType) {
    MouseEventType[MouseEventType["Left"] = 0] = "Left";
    MouseEventType[MouseEventType["Middle"] = 1] = "Middle";
    MouseEventType[MouseEventType["Right"] = 2] = "Right";
})(MouseEventType || (MouseEventType = {}));

var VFSizeMode;
(function (VFSizeMode) {
    VFSizeMode["Min"] = "Min";
    VFSizeMode["Max"] = "Max";
})(VFSizeMode || (VFSizeMode = {}));
//按鼠标进行缩放
var ZoomEditor = /** @class */ (function (_super) {
    __extends(ZoomEditor, _super);
    function ZoomEditor(vfViewer, eventManager) {
        var _this = _super.call(this) || this;
        _this.totalZoomFactors = [];
        _this.zoomFactors = [];
        _this.lastZoomFactor = 1;
        _this.currentIdx = 0;
        _this.name = EditorName.Zoom_Editor;
        _this.eventManager = eventManager;
        _this.vfData = vfViewer.getData();
        _this.vfViewer = vfViewer;
        return _this;
    }
    ZoomEditor.prototype.onMouseWheel = function (event) {
        var deltaY = event.deltaY || -event.wheelDelta || event.detail;
        var bIsZoomIn = (deltaY < 0) ? true : false;
        var multiplyZoomFactor = -1;
        if (bIsZoomIn) {
            if (this.currentIdx < this.zoomFactors.length - 1) {
                this.currentIdx++;
                multiplyZoomFactor = this.zoomFactors[this.currentIdx];
            }
        }
        else {
            if (this.currentIdx > 0) {
                this.currentIdx--;
                multiplyZoomFactor = this.zoomFactors[this.currentIdx];
            }
        }
        if (multiplyZoomFactor < 0 || this.lastZoomFactor == multiplyZoomFactor) {
            return;
        }
        this.lastZoomFactor = multiplyZoomFactor;
        if (multiplyZoomFactor == 1) {
            PanEditor.clear();
        }
        var originPanelSize = this.vfData.getOriginSize();
        var mouseWheelOffset = new THREE.Vector2(event.offsetX - originPanelSize[0] / 2, event.offsetY - originPanelSize[1] / 2);
        var curZoomFactor = this.zoomFactors[this.currentIdx];
        var panDeltaX = 0, panDeltaY = 0;
        // zoom in 
        if (bIsZoomIn) {
            var lastZoomFactor = this.zoomFactors[this.currentIdx - 1];
            var multiplier = curZoomFactor / lastZoomFactor;
            var currentZoomOffset = [];
            var restoreZoomOffset = this.vfData.getCorner('Virtual', 'LB');
            restoreZoomOffset[0] -= mouseWheelOffset.x;
            restoreZoomOffset[1] -= mouseWheelOffset.y;
            currentZoomOffset.push(restoreZoomOffset[0] * multiplier + mouseWheelOffset.x);
            currentZoomOffset.push(restoreZoomOffset[1] * multiplier + mouseWheelOffset.y);
            this.vfData.setZoomFactor(curZoomFactor);
            var panelCornerLB = this.vfData.getCorner('Virtual', 'LB');
            this.vfData.setZoomFactor(lastZoomFactor);
            panDeltaX = currentZoomOffset[0] - panelCornerLB[0];
            panDeltaY = currentZoomOffset[1] - panelCornerLB[1];
        }
        // zoom out , need bounds checking 
        else {
            var lastZoomFactor = this.zoomFactors[this.currentIdx + 1];
            var additionOffset = this.boundsChecking(curZoomFactor, lastZoomFactor, mouseWheelOffset);
            panDeltaX = additionOffset[0];
            panDeltaY += additionOffset[1];
        }
        PanEditor.panOffsetX += panDeltaX;
        PanEditor.panOffsetY += panDeltaY;
        PanEditor.panOffsetXForCamera += panDeltaX;
        PanEditor.panOffsetYForCamera += panDeltaY;
        this.vfData.setZoomFactor(multiplyZoomFactor);
        this.vfViewer.destroy();
        this.vfData.destroy();
        this.vfData.build();
        this.vfViewer.update();
        this.vfData.updateMovement();
        this.updateZoomAndPan();
        this.eventManager.dispatchEvent({
            type: EventType.ZOOM_MOUSE_WHEEL,
            data: {
                offsetX: PanEditor.panOffsetX,
                offsetY: PanEditor.panOffsetY,
                zoomFactor: multiplyZoomFactor,
            }
        });
    };
    //checking when zoom out 
    ZoomEditor.prototype.boundsChecking = function (curZoomFactor, lastZoomFactor, mouseWheelOffset) {
        var multiplier = curZoomFactor / lastZoomFactor;
        var cornerLB = this.vfData.getCorner('Virtual', 'LB');
        var cornerLT = this.vfData.getCorner('Virtual', 'LT');
        var cornerRT = this.vfData.getCorner('Virtual', 'RT');
        var cornerRB = this.vfData.getCorner('Virtual', 'RB');
        var additionOffset = [0, 0];
        var offsetX = (cornerLB[0] - mouseWheelOffset.x) * multiplier + mouseWheelOffset.x, offsetY = (cornerLB[1] - mouseWheelOffset.y) * multiplier + mouseWheelOffset.y;
        this.vfData.setZoomFactor(curZoomFactor);
        var panelCornerLB = this.vfData.getCorner('Virtual', 'LB');
        this.vfData.setZoomFactor(lastZoomFactor);
        additionOffset[0] = (offsetX - panelCornerLB[0]);
        additionOffset[1] = (offsetY - panelCornerLB[1]);
        var xCounts = 0, yCounts = 0;
        //corner left bottom
        var originCornerLB = this.vfData.getCorner('Origin', 'LB');
        if (offsetX > originCornerLB[0]) {
            additionOffset[0] += (originCornerLB[0] - offsetX);
            xCounts++;
        }
        if (offsetY < originCornerLB[1]) {
            additionOffset[1] += (originCornerLB[1] - offsetY);
            yCounts++;
        }
        //corner left top
        offsetX = (cornerLT[0] - mouseWheelOffset.x) * multiplier + mouseWheelOffset.x;
        offsetY = (cornerLT[1] - mouseWheelOffset.y) * multiplier + mouseWheelOffset.y;
        var originCornerLT = this.vfData.getCorner('Origin', 'LT');
        if (offsetX > originCornerLT[0]) {
            if (xCounts == 0) {
                additionOffset[0] += (originCornerLT[0] - offsetX);
                xCounts++;
            }
        }
        if (offsetY > originCornerLT[1]) {
            if (yCounts == 0) {
                additionOffset[1] += (originCornerLT[1] - offsetY);
                yCounts++;
            }
        }
        //corner right top
        offsetX = (cornerRT[0] - mouseWheelOffset.x) * multiplier + mouseWheelOffset.x;
        offsetY = (cornerRT[1] - mouseWheelOffset.y) * multiplier + mouseWheelOffset.y;
        var originCornerRT = this.vfData.getCorner('Origin', 'RT');
        if (offsetX < originCornerRT[0]) {
            if (xCounts == 0) {
                additionOffset[0] += (originCornerRT[0] - offsetX);
                xCounts++;
            }
        }
        if (offsetY > originCornerRT[1]) {
            if (yCounts == 0) {
                additionOffset[1] += (originCornerRT[1] - offsetY);
                yCounts++;
            }
        }
        //corner right bottom
        offsetX = (cornerRB[0] - mouseWheelOffset.x) * multiplier + mouseWheelOffset.x;
        offsetY = (cornerRB[1] - mouseWheelOffset.y) * multiplier + mouseWheelOffset.y;
        var origincornerRB = this.vfData.getCorner('Origin', 'RB');
        if (offsetX < origincornerRB[0]) {
            if (xCounts == 0) {
                additionOffset[0] += (origincornerRB[0] - offsetX);
                xCounts++;
            }
        }
        if (offsetY < origincornerRB[1]) {
            if (yCounts == 0) {
                additionOffset[1] += (origincornerRB[1] - offsetY);
                yCounts++;
            }
        }
        return additionOffset;
    };
    ZoomEditor.prototype.enableMode = function (mode) {
        if (mode == VFSizeMode.Min) {
            this.zoomFactors = this.totalZoomFactors;
        }
        else if (mode == VFSizeMode.Max) {
            this.zoomFactors = this.totalZoomFactors.slice(1, this.totalZoomFactors.length);
            for (var i = 1; i < this.zoomFactors.length; i++) {
                this.zoomFactors[i] /= this.zoomFactors[0];
            }
            this.zoomFactors[0] = 1;
        }
        this.currentIdx = 0;
    };
    ZoomEditor.prototype.setZoomFactors = function (zoomFactors) {
        this.totalZoomFactors = zoomFactors;
        this.zoomFactors = zoomFactors;
    };
    ZoomEditor.prototype.setZoonIndex = function (index) {
        this.currentIdx = index;
    };
    ZoomEditor.prototype.getZoonIndex = function () {
        return this.currentIdx;
    };
    ZoomEditor.prototype.updateZoomAndPan = function () {
        var originCornerLT = this.vfData.getCorner('Origin', 'LT');
        var virtualCornerLT = this.vfData.getCorner('Virtual', 'LT');
        ZoomEditor.offsetXForZoomAndPan = originCornerLT[0] - virtualCornerLT[0];
        ZoomEditor.offsetYForZoomAndPan = originCornerLT[1] - virtualCornerLT[1];
    };
    ZoomEditor.clear = function () {
        ZoomEditor.offsetXForZoomAndPan = 0;
        ZoomEditor.offsetYForZoomAndPan = 0;
    };
    ZoomEditor.offsetXForZoomAndPan = 0;
    ZoomEditor.offsetYForZoomAndPan = 0;
    return ZoomEditor;
}(Editor$1));

var PanEditor = /** @class */ (function (_super) {
    __extends(PanEditor, _super);
    function PanEditor(vfViewer, eventManager) {
        var _this = _super.call(this) || this;
        _this.name = EditorName.PAN_Editor;
        _this.eventManager = eventManager;
        _this.vfData = vfViewer.getData();
        _this.bIsMouseDown = false;
        _this.mouseDownPos = new THREE.Vector2();
        PanEditor.panOffsetX = 0;
        PanEditor.panOffsetY = 0;
        return _this;
    }
    PanEditor.prototype.onMouseDown = function (event) {
        this.bIsMouseDown = true;
        this.mouseDownPos.setX(event.clientX);
        this.mouseDownPos.setY(event.clientY);
    };
    PanEditor.prototype.onMouseMove = function (event) {
        if (this.bIsMouseDown == false) {
            return;
        }
        if (this.vfData.getZoomFactor() == 1) {
            return;
        }
        // 当右键或中键在进行拖动时，根据鼠标位置进行实时更新定位
        var deltaX = (event.clientX - this.mouseDownPos.x);
        var deltaY = (event.clientY - this.mouseDownPos.y);
        var boundsOffset = this.boundsChecking(deltaX, deltaY);
        deltaX = boundsOffset[0];
        deltaY = boundsOffset[1];
        PanEditor.panOffsetX += deltaX;
        PanEditor.panOffsetY += deltaY;
        PanEditor.panOffsetXForCamera += deltaX;
        PanEditor.panOffsetYForCamera += deltaY;
        this.vfData.updateMovement();
        this.updateZoomAndPan();
        this.mouseDownPos.setX(event.clientX);
        this.mouseDownPos.setY(event.clientY);
    };
    PanEditor.prototype.onMouseUp = function (event) {
        if (this.bIsMouseDown == false) {
            return;
        }
        if (this.vfData.getZoomFactor() == 1) {
            return;
        }
        var deltaX = (event.clientX - this.mouseDownPos.x);
        var deltaY = (event.clientY - this.mouseDownPos.y);
        //对新的offset做bounds check
        var boundsOffset = this.boundsChecking(deltaX, deltaY);
        deltaX = boundsOffset[0];
        deltaY = boundsOffset[1];
        PanEditor.panOffsetX += deltaX;
        PanEditor.panOffsetY += deltaY;
        PanEditor.panOffsetXForCamera += deltaX;
        PanEditor.panOffsetYForCamera += deltaY;
        this.vfData.updateMovement();
        this.updateZoomAndPan();
        this.eventManager.dispatchEvent({
            type: EventType.PAN_MOUSE_MOVE,
            data: {
                offsetX: PanEditor.panOffsetX,
                offsetY: PanEditor.panOffsetY,
                zoomFactor: this.vfData.getZoomFactor()
            }
        });
        this.bIsMouseDown = false;
    };
    //deltaX 大于0 ---> 检查LB.x
    //       小于0 ---> 检查RT.x
    //deltaY 大于0 ---> 检查LB.y
    //       小于0 ---> 检查RT.y
    PanEditor.prototype.boundsChecking = function (deltaX, deltaY) {
        var originCornerLB = this.vfData.getCorner('Origin', 'LB');
        var virtualCornerLB = this.vfData.getCorner('Virtual', 'LB');
        var originCornerRT = this.vfData.getCorner('Origin', 'RT');
        var virtualCornerRT = this.vfData.getCorner('Virtual', 'RT');
        //横向检查
        if (deltaX > 0) {
            var maxOffsetX = originCornerLB[0] - virtualCornerLB[0];
            if (maxOffsetX <= deltaX) {
                deltaX = maxOffsetX;
            }
        }
        else if (deltaX < 0) {
            var maxOffsetX = originCornerRT[0] - virtualCornerRT[0];
            if (maxOffsetX >= deltaX) {
                deltaX = maxOffsetX;
            }
        }
        //纵向检查
        if (deltaY < 0) {
            var maxOffsetY = originCornerLB[1] - virtualCornerLB[1];
            if (maxOffsetY >= deltaY) {
                deltaY = maxOffsetY;
            }
        }
        else if (deltaY > 0) {
            var maxOffsetY = originCornerRT[1] - virtualCornerRT[1];
            if (maxOffsetY <= deltaY) {
                deltaY = maxOffsetY;
            }
        }
        return [deltaX, deltaY];
    };
    PanEditor.prototype.updateZoomAndPan = function () {
        var originCornerLT = this.vfData.getCorner('Origin', 'LT');
        var virtualCornerLT = this.vfData.getCorner('Virtual', 'LT');
        ZoomEditor.offsetXForZoomAndPan = originCornerLT[0] - virtualCornerLT[0];
        ZoomEditor.offsetYForZoomAndPan = originCornerLT[1] - virtualCornerLT[1];
    };
    PanEditor.clear = function () {
        PanEditor.panOffsetX = 0;
        PanEditor.panOffsetY = 0;
        PanEditor.panOffsetXForCamera = 0;
        PanEditor.panOffsetYForCamera = 0;
    };
    return PanEditor;
}(Editor$1));

var CameraNode = /** @class */ (function (_super) {
    __extends(CameraNode, _super);
    function CameraNode() {
        var _this = _super.call(this) || this;
        _this.cameraCircleNode = null;
        _this.cameraArrowNode = null;
        _this.rotateAngle = 0;
        _this.panelSize = [298, 198];
        _this.build();
        return _this;
    }
    CameraNode.prototype.build = function () {
        var svgNode = document.createElementNS(this.xmlns, 'g');
        svgNode.setAttribute('fill', 'none');
        svgNode.setAttribute('fill-rule', 'evenodd');
        svgNode.setAttribute('stroke-width', '1');
        var circle = document.createElementNS(this.xmlns, 'circle');
        circle.setAttribute('r', '4.5');
        circle.setAttribute('stroke', '#FFFFFF');
        circle.setAttribute('fill', '#32D3A6');
        this.cameraCircleNode = circle;
        var path = document.createElementNS(this.xmlns, 'path');
        //path.setAttribute('d', 'M 7 6 Q 10 0, 7 -6 L 19 0 Z');
        path.setAttribute('d', 'M5.94925387,0 C18.4132389,0 28.6581001,9.50119823 29.8362478,21.6560048 L5.94925387,25 Z');
        path.setAttribute('fill', 'url(#radialGradient-1)');
        path.setAttribute('transform', 'translate(13,-21)rotate(50)');
        this.cameraArrowNode = path;
        // 渐变
        var defs = document.createElementNS(this.xmlns, 'defs');
        var radialGradient = document.createElementNS(this.xmlns, 'radialGradient');
        var stop1 = document.createElementNS(this.xmlns, 'stop');
        var stop2 = document.createElementNS(this.xmlns, 'stop');
        radialGradient.setAttribute('cx', '0%');
        radialGradient.setAttribute('cy', '100%');
        radialGradient.setAttribute('fx', '0%');
        radialGradient.setAttribute('fy', '100%');
        radialGradient.setAttribute('r', '104.321936%');
        radialGradient.setAttribute('id', 'radialGradient-1');
        stop1.setAttribute('stop-color', '#36D4A8');
        stop1.setAttribute('offset', '0%');
        stop2.setAttribute('stop-color', '#36D4A8');
        stop2.setAttribute('offset', '100%');
        stop2.setAttribute('stop-opacity', '0');
        defs.append(radialGradient);
        radialGradient.append(stop1);
        radialGradient.append(stop2);
        svgNode.appendChild(defs);
        svgNode.appendChild(circle);
        svgNode.appendChild(path);
        this.svgNode = svgNode;
    };
    CameraNode.prototype.setCircleAttribute = function (key, value) {
        this.cameraCircleNode.setAttribute(key, value);
    };
    CameraNode.prototype.setArrowAttribute = function (key, value) {
        this.cameraArrowNode.setAttribute(key, value);
    };
    CameraNode.prototype.rotate = function (angle) {
        if (angle) {
            this.rotateAngle = angle;
        }
        var transform = this.svgNode.getAttribute('transform');
        transform += "rotate" + "(" + this.rotateAngle + ")";
        this.svgNode.setAttribute("transform", transform);
    };
    CameraNode.prototype.setOffsetAndRotate = function (offsetX, offsetY, angle) {
        var offset = this.setOffsetBoundary(offsetX, offsetY);
        this.locate(offset.X, offset.Y);
        this.rotate(angle);
        PanEditor.panOffsetXForCamera = 0;
        PanEditor.panOffsetYForCamera = 0;
    };
    CameraNode.prototype.move = function (deltaX, deltaY) {
        var offsetX = this.position.x + deltaX;
        var offsetY = this.position.y + deltaY;
        var offset = this.setOffsetBoundary(offsetX, offsetY);
        this.svgNode.setAttribute("transform", "translate(" + offset.X + "," + offset.Y + ")");
    };
    CameraNode.prototype.setOpacity = function (opacity) {
        this.svgNode.setAttribute('opacity', opacity);
    };
    CameraNode.prototype.setCameraArrowOpacity = function (opacity) {
        this.cameraArrowNode.setAttribute('opacity', opacity);
    };
    CameraNode.prototype.setCameraCircleOpacity = function (opacity) {
        this.cameraCircleNode.setAttribute('opacity', opacity);
    };
    CameraNode.prototype.setBigCamera = function () {
        this.cameraCircleNode.setAttribute('r', '4.5');
        this.cameraArrowNode.setAttribute('transform', 'translate(13,-21)rotate(50)');
    };
    CameraNode.prototype.setSmallCamera = function () {
        this.cameraCircleNode.setAttribute('r', '2');
        this.cameraArrowNode.setAttribute('transform', 'translate(20,-21)rotate(50)');
    };
    CameraNode.prototype.setPanelSize = function (panelSize) {
        this.panelSize = panelSize;
    };
    CameraNode.prototype.setOffsetBoundary = function (offsetX, offsetY) {
        var offsetXmax = this.panelSize[0] / 2 - 6;
        var offsetXmin = -this.panelSize[0] / 2 + 6;
        var offsetYmax = this.panelSize[1] / 2 - 6;
        var offsetYmin = -this.panelSize[1] / 2 + 6;
        if (offsetX >= offsetXmax || offsetX <= offsetXmin || offsetY >= offsetYmax || offsetY <= offsetYmin) {
            this.setSmallCamera();
        }
        else {
            this.setBigCamera();
        }
        offsetX > offsetXmax ? offsetX = offsetXmax : '';
        offsetX < offsetXmin ? offsetX = offsetXmin : '';
        offsetY > offsetYmax ? offsetY = offsetYmax : '';
        offsetY < offsetYmin ? offsetY = offsetYmin : '';
        return { X: offsetX, Y: offsetY };
    };
    return CameraNode;
}(DrawableItem));

var getDrawableContainer = function (viewer) {
    if (!viewer.drawableContainer || viewer.drawableContainer._viewer._isDestroyed) {
        var drawableConfig = new window.Glodon.Bimface.Plugins.Drawable.DrawableContainerConfig();
        drawableConfig.viewer = viewer;
        drawableConfig.affectedBySection = false;
        viewer.drawableContainer = new window.Glodon.Bimface.Plugins.Drawable.DrawableContainer(drawableConfig);
    }
    return viewer.drawableContainer;
};
var addPickTag = function (viewer, position, z, text) {
    var pickIcon = document.createElement('canvas');
    pickIcon.width = 90;
    pickIcon.height = 90;
    var context = pickIcon.getContext('2d');
    context.fillStyle = '#FF9D0B';
    context.fillRect(35, 43.5, 20, 3);
    context.fillRect(43.5, 35, 3, 20);
    context.beginPath();
    context.arc(70, 25, 13, 0, 2 * Math.PI);
    context.fill();
    context.fillStyle = '#FFFFFF';
    var font = '16px -apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Helvetica,"PingFang SC","Hiragino Sans GB","Microsoft YaHei",SimSun,sans-serif';
    context.font = font;
    context.fillText(text, 65, 30);
    var pickIconSrc = pickIcon.toDataURL();
    var imageConfig = new window.Glodon.Bimface.Plugins.Drawable.ImageConfig();
    imageConfig.worldPosition = position;
    imageConfig.src = pickIconSrc;
    imageConfig.width = 90;
    imageConfig.height = 90;
    imageConfig.opacity = 1;
    var image = new window.Glodon.Bimface.Plugins.Drawable.Image(imageConfig);
    getDrawableContainer(viewer).addItem(image);
};
var clearIcon = function (viewer) {
    getDrawableContainer(viewer).exit();
    delete viewer.drawableContainer;
};
var addCameraNode = function (viewer, app3D) {
    if (!viewer)
        return false;
    var cameraNode = new CameraNode();
    var drawableContainer = document.createElementNS("http://www.w3.org/2000/svg", "svg");
    drawableContainer.style.position = 'absolute';
    drawableContainer.style.top = '0';
    cameraNode.abtainRenderables(drawableContainer);
    var divContainer = document.createElement("div");
    divContainer.style.left = '0px';
    divContainer.style.bottom = '0px';
    divContainer.style.position = "relative";
    divContainer.style.zIndex = '1';
    divContainer.appendChild(drawableContainer);
    var oldPositionX = null, oldPositionY = null;
    drawableContainer.addEventListener('mousedown', function (e) {
        oldPositionX = e.clientX;
        oldPositionY = e.clientY;
        // 框选状态下不允许拖动
        if (viewer.getViewer().mouseEditorMgr.editors.find((function (editor) { return editor.name == 'zoom'; }))) {
            viewer.getViewer().mouseEditorMgr.enableDrag(false);
        }
        else {
            viewer.getViewer().mouseEditorMgr.enableDrag(true);
        }
    });
    drawableContainer.addEventListener('mouseup', function (e) {
        // 通过判断是否为相同位置决定应为点选或移动操作
        if (Math.abs(oldPositionX - e.clientX) <= 1 && Math.abs(oldPositionY - e.clientY) <= 1) {
            var width = viewer.getDomElement().clientWidth;
            var height = viewer.getDomElement().clientHeight;
            var wrapRect = viewer.getDomElement().getBoundingClientRect();
            var clickX = e.clientX - wrapRect.left;
            var clickY = e.clientY - wrapRect.top;
            viewer.cameraWorldPosition = viewer.getViewer().toWorldPoint([clickX, clickY]);
            // 联动三维相机位置
            var camera = app3D.getViewer().getCamera().getStatus();
            var translatedPoint = new window.THREE.Vector3(viewer.cameraWorldPosition[0], viewer.cameraWorldPosition[1], viewer.cameraWorldPosition[2]);
            var translatedPosition = translatedPoint.applyMatrix4(drawableContainer.matrix);
            var cameraOffsetX = translatedPosition.x - camera.position.x;
            var cameraOffsetY = translatedPosition.y - camera.position.y;
            camera.position.x = translatedPosition.x;
            camera.position.y = translatedPosition.y;
            camera.position.z = app3D.getViewer().getUnit() == window.Glodon.Bimface.Common.Units.LengthUnits.Meter ? viewer.cameraNode.savedHeight / 1000 : viewer.cameraNode.savedHeight;
            camera.target.x += cameraOffsetX;
            camera.target.y += cameraOffsetY;
            app3D.getViewer().getCamera().setStatus(camera);
            cameraNode.move(clickX - width / 2, clickY - height / 2);
            // 重新绑定mouseClicked事件从而正确触发，防止因为增加相机图层导致div层级并列而无法监听事件
            var pointObj = {
                worldPosition: { x: viewer.getViewer().toWorldPoint([clickX, clickY])[0], y: viewer.getViewer().toWorldPoint([clickX, clickY])[1] },
                clientPosition: {
                    x: e.clientX - viewer._opt.domElement.getBoundingClientRect().left,
                    y: e.clientY - viewer._opt.domElement.getBoundingClientRect().top
                },
                clientX: e.clientX,
                clientY: e.clientY,
                event: e,
                eventType: e.button === 0 ? 'Click' : 'RightClick',
                objectId: viewer.getViewer().getHighlightIds(viewer.loadedDrawings[0].drawing.drawingIndex)[0] || null,
            };
            if (pointObj.objectId) {
                pointObj.layerId = viewer.getDrawing().getLayerIdFromElementId() || 0;
                pointObj.layerName = viewer.getDrawing().getLayerById(pointObj.layerId).name || null;
                pointObj.modelId = viewer._data.modelId;
            }
            else {
                pointObj.layer = null;
            }
            if (viewer.grids) {
                pointObj.grid = viewer.getNearestGrid({ x: clickX, y: clickY });
            }
            if (pointObj.objectId && viewer._drawingViewer.viewer.IsViewportElement && viewer._drawingViewer.viewer.IsViewportElement(pointObj.objectId)) {
                pointObj.objectType = 'Viewport';
            }
            viewer.getEventManager().removeEvent('MouseClicked');
            viewer.getEventManager().fireEvent('MouseClicked', pointObj);
        }
    });
    // 三维变换时同步变换相机位置
    var cameraMoveFn = function (status) {
        if (!viewer.getViewer())
            return false;
        // 获取逆矩阵计算相机对应图纸位置并移动
        var cloneMatrix = drawableContainer.matrix.clone();
        cloneMatrix.elements[10] = 1;
        var inverseMatrix = cloneMatrix.invert();
        var drawingWorldPosition = new window.THREE.Vector3(status.position.x, status.position.y, status.position.z).applyMatrix4(inverseMatrix);
        var drawingClientPosition = viewer.worldToClient(drawingWorldPosition);
        var width = viewer.getDomElement().clientWidth;
        var height = viewer.getDomElement().clientHeight;
        // 保持相机移动的边界值，防止图标变化剧烈
        var moveX = drawingClientPosition.x - width / 2 < -width / 2 + 20
            ? -width / 2 + 20 : drawingClientPosition.x - width / 2 > width / 2 - 20
            ? width / 2 - 20 : drawingClientPosition.x - width / 2;
        var moveY = drawingClientPosition.y - height / 2 < -height / 2 + 20
            ? -height / 2 + 20 : drawingClientPosition.y - height / 2 > height / 2 - 20
            ? height / 2 - 20 : drawingClientPosition.y - height / 2;
        cameraNode.move(moveX, moveY);
        var x = status.target.x - status.position.x;
        var y = status.target.y - status.position.y;
        var rotateValue = (drawableContainer.rotation - Math.atan2(y, x)) * 180 / Math.PI;
        cameraNode.rotate(rotateValue);
    };
    var resizeElements = function () {
        var width = viewer.getDomElement().clientWidth;
        var height = viewer.getDomElement().clientHeight;
        cameraNode.setPanelSize([width, height]);
        drawableContainer.setAttribute('width', width + '');
        drawableContainer.setAttribute('height', height + '');
        drawableContainer.setAttribute('viewBox', (-width / 2) + ' ' + (-height / 2) + ' ' + width + ' ' + height);
        if (viewer.cameraWorldPosition) {
            var clientPosition = viewer.worldToClient({ x: viewer.cameraWorldPosition[0], y: viewer.cameraWorldPosition[1] });
            cameraNode.move(clientPosition.x - width / 2, clientPosition.y - height / 2);
        }
        else {
            requestAnimationFrame(function () {
                cameraMoveFn(app3D.getViewer().getCamera().getStatus());
            });
        }
    };
    resizeElements();
    cameraNode.move(-viewer.getDomElement().clientWidth / 2 + 20, viewer.getDomElement().clientHeight / 2 - 20);
    cameraNode.rotate(-45);
    viewer.getDomElement().getElementsByClassName('bf-drawing-container')[0].append(divContainer);
    window.onresize = function () {
        resizeElements();
    };
    viewer.addEventListener('ZoomEnd', resizeElements);
    viewer.addEventListener('MouseDragged', resizeElements);
    app3D.getViewer().addEventListener('CameraPositionChanged', cameraMoveFn);
    return drawableContainer;
};
var destroyCameraNode = function (viewer) {
    if (viewer) {
        viewer.cameraNode && viewer.cameraNode.parentElement.remove(viewer.cameraNode);
        delete viewer.cameraNode;
    }
};
var drawDashedLine = function (startPoint, endPoint, viewer) {
    var xmlns = "http://www.w3.org/2000/svg";
    var dashedLineContainer = document.createElementNS(xmlns, "svg");
    dashedLineContainer.setAttributeNS(null, "class", 'bfmodule-linkage-editor-dashed-line-container bf-measure-svg');
    var dashedLine = document.createElementNS(xmlns, "line");
    dashedLine.setAttribute('stroke', '#FF9D00');
    dashedLine.setAttribute('class', 'bf-measure-svg');
    dashedLine.setAttribute('stroke-width', '2');
    dashedLine.setAttribute('stroke-dasharray', '6,4');
    dashedLine.setAttribute('x1', startPoint.x);
    dashedLine.setAttribute('y1', startPoint.y);
    dashedLine.setAttribute('x2', endPoint.x);
    dashedLine.setAttribute('y2', endPoint.y);
    dashedLine.setAttribute('style', 'stroke-width: 2; display:block;');
    dashedLineContainer.appendChild(dashedLine);
    // 图纸or模型
    viewer instanceof window.Glodon.Bimface.Viewer.ViewerDrawing
        ? viewer.getDomElement().getElementsByClassName('bf-drawing-container')[0].appendChild(dashedLineContainer)
        : viewer.getDomElement().appendChild(dashedLineContainer);
    return dashedLine;
};
var updateDashedLine = function (dashedLine, startPoint, endPoint, viewer) {
    dashedLine.setAttribute('x1', startPoint.x);
    dashedLine.setAttribute('y1', startPoint.y);
    dashedLine.setAttribute('x2', endPoint.x);
    dashedLine.setAttribute('y2', endPoint.y);
};
var calculateLinkageMatrix = function (drawingPoints, modelPoints) {
    var THREE = window.THREE;
    var drawingPoint0 = new THREE.Vector3(drawingPoints[0].x, drawingPoints[0].y, 0);
    var drawingPoint1 = new THREE.Vector3(drawingPoints[1].x, drawingPoints[1].y, 0);
    var modelPoint0 = new THREE.Vector3(modelPoints[0].x, modelPoints[0].y, 0);
    var modelPoint1 = new THREE.Vector3(modelPoints[1].x, modelPoints[1].y, 0);
    //旋转角度
    var drawingDirection = drawingPoint1.clone().sub(drawingPoint0);
    var modelDirection = modelPoint1.clone().sub(modelPoint0);
    var drawingDirection2D = new THREE.Vector2(drawingDirection.x, drawingDirection.y);
    var modelDirection2D = new THREE.Vector2(modelDirection.x, modelDirection.y);
    var angle = modelDirection2D.angle() - drawingDirection2D.angle();
    // 缩放比例,通过点距离获取
    var x3d = Math.abs(modelPoint1.x - modelPoint0.x);
    var y3d = Math.abs(modelPoint1.y - modelPoint0.y);
    var zoom3d = Math.sqrt(Math.pow(x3d, 2) + Math.pow(y3d, 2));
    var x2d = Math.abs(drawingPoint1.x - drawingPoint0.x);
    var y2d = Math.abs(drawingPoint1.y - drawingPoint0.y);
    var zoom2d = Math.sqrt(Math.pow(x2d, 2) + Math.pow(y2d, 2));
    var zoomValue = zoom3d / zoom2d;
    var translationMatrix = new THREE.Matrix4().makeTranslation(modelPoint0.x, modelPoint0.y, 0);
    var rotationMatrix = new THREE.Matrix4().makeRotationZ(angle);
    var scaleMatrix = new THREE.Matrix4().makeScale(zoomValue, zoomValue, 1);
    var translationBackMatrix = new THREE.Matrix4().makeTranslation(-drawingPoint0.x, -drawingPoint0.y, 0);
    return {
        matrix: translationMatrix.multiply(scaleMatrix).multiply(rotationMatrix).multiply(translationBackMatrix),
        rotation: angle,
    };
};
// 获取剖切百分比
var getSectionProgress = function (unit, sectionOffset, height, modelMaxHeight, modelMinHeight) {
    height = unit == "Meter" ? height / 1000 : height;
    sectionOffset = unit == "Meter" ? sectionOffset / 1000 : sectionOffset;
    return { sectionOffset: sectionOffset, height: height };
};
// 设置剖切
var setModelSectionProgress = function (viewer3d, sectProgress) {
    if (!viewer3d._sectionPlane)
        initSectionPlane(viewer3d);
    // 计算法线和偏移的投影
    var direction = new window.THREE.Vector3(0, 1, 0);
    var offset = new window.THREE.Vector3(0, 0, sectProgress.height + sectProgress.sectionOffset);
    var centerToPosition = viewer3d.worldToScene(offset).sub(viewer3d._sectionPlane._sectionTool.center).projectOnVector(direction);
    var distance = centerToPosition.length();
    if (Math.abs(centerToPosition.angleTo(direction)) > Math.PI / 2) { // 判断距离符号
        distance *= -1;
    }
    viewer3d._sectionPlane._sectionTool.setOffset(distance);
    viewer3d._sectionPlane._sectionTool.calculateClippingIds();
    viewer3d.render();
};
// 初始化剖切
var initSectionPlane = function (viewer3D) {
    delete viewer3D._sectionPlane;
    var config = new window.Glodon.Bimface.Plugins.Section.SectionPlaneConfig();
    config.plane = window.Glodon.Bimface.Plugins.Section.SectionPlanePlane.Z;
    config.viewer = viewer3D;
    config.id = "SectionPlane";
    config.exitSectionBox = false;
    config.enableSnap = true;
    var sectionPlane = new window.Glodon.Bimface.Plugins.Section.SectionPlane(config);
    sectionPlane.hidePlane();
    viewer3D._sectionPlane = sectionPlane;
};
// 右键菜单展示开启、关闭剖切按钮
var menuManagement = function (app3D, app2D) {
    var closeSectionConfig = new window.Glodon.Bimface.UI.Menu.MenuItemConfig();
    closeSectionConfig.id = "closeSection";
    var closeSection = new window.Glodon.Bimface.UI.Menu.MenuItem(closeSectionConfig);
    closeSection.setText('关闭剖切');
    var openSectionConfig = new window.Glodon.Bimface.UI.Menu.MenuItemConfig();
    openSectionConfig.id = "openSection";
    var openSection = new window.Glodon.Bimface.UI.Menu.MenuItem(openSectionConfig);
    openSection.setText('开启剖切');
    var addItem = function () {
        requestAnimationFrame(function () {
            var linkedFile = app3D.linkList.find(function (file) { return file.id == app2D.getViewer().getDrawing().modelId; });
            if (app3D.getViewer()._sectionPlane) {
                !app3D.getViewer().contextMenu.getControl('closeSection') && app3D.getViewer().contextMenu.addControl(closeSection);
            }
            else if (linkedFile) {
                !app3D.getViewer().contextMenu.getControl('openSection') && app3D.getViewer().contextMenu.addControl(openSection);
            }
        });
    };
    app3D.addEventListener('ContextMenu', addItem);
    closeSection.addEventListener('click', function () {
        if (app3D && app3D.getViewer()._sectionPlane) {
            app3D.getViewer()._sectionPlane.exit();
            app3D.getViewer().contextMenu.destroy();
        }
    });
    openSection.addEventListener('click', function () {
        var linkedFile = app3D.linkList.find(function (file) { return file.id == app2D.getViewer().getDrawing().modelId; });
        var progress = getSectionProgress(app3D.getViewer().getUnit(), linkedFile.sectionOffset, linkedFile.isFloor ? linkedFile.selectedHeight : linkedFile.baseHeight, app3D.modelMaxHeight, app3D.modelMinHeight);
        setModelSectionProgress(app3D.getViewer(), progress);
        app3D.getViewer().contextMenu.destroy();
    });
};

/**
 * @classdesc 类：MessageBox组件类
 * @class Glodon.Bimface.Tiles.UI.MessageBox
 * @constructs Glodon.Bimface.Tiles.UI.MessageBox
 * @description 构造消息框组件
 * @extends Glodon.Bimface.Tiles.UI.Control
 * @param {Object} option 构造消息框的配置项
 * @param {String} option.id 消息框组件ID
 * @param {String} option.content 消息框的文字内容
 */
var MessageBox = /** @class */ (function (_super) {
    __extends(MessageBox, _super);
    function MessageBox(param) {
        var _this = this;
        param = param || {};
        var elementParam = {
            elementType: 'div',
            className: 'bfui-messagebox'
        };
        var controlParam = {
            elementParam: elementParam,
            type: 'MessageBox',
            id: param.id,
            parent: param.parent
        };
        _this = _super.call(this, controlParam) || this;
        _this.data = __assign({}, param);
        render("\n      <div class=\"bfui-messagebox-main\">\n        <div ref=\"content\" class=\"bfui-messagebox-content\">" + param.content + "</div>\n        <div class=\"bfui-messagebox-footer\">\n          <div class=\"bfui-messagebox-button button-primary\" @click=\"confirm\">" + (param.confirmText || '确认') + "</div>\n          <div class=\"bfui-messagebox-button\" @click=\"cancel\">" + (param.cancelText || '取消') + "</div>\n        </div>\n      </div>\n    ", _this.getDomElement(), _this);
        param.className && _this.addClass(param.className);
        if (param.parent) {
            if (param.parent instanceof Control) {
                param.parent.getDomElement().append(_this.getDomElement());
            }
            else if (param.parent instanceof Dom) {
                param.parent.append(_this.getDomElement());
            }
            else if (param.parent instanceof HTMLElement) {
                param.parent.appendChild(_this.getDomElement().element);
            }
        }
        else {
            document.body.appendChild(_this.getDomElement().element);
        }
        requestAnimationFrame(function () { return _this.addClass('bfui-messagebox-show'); });
        return _this;
    }
    MessageBox.prototype.confirm = function () {
        var _this = this;
        this.data.confirmHandler && this.data.confirmHandler();
        this.removeClass('bfui-messagebox-show');
        this.getDomElement().on('transitionend', function () { return _this.destroy(); });
    };
    MessageBox.prototype.cancel = function () {
        var _this = this;
        this.data.cancelHandler && this.data.cancelHandler();
        this.removeClass('bfui-messagebox-show');
        this.getDomElement().on('transitionend', function () { return _this.destroy(); });
    };
    return MessageBox;
}(Control));

var DrawingList = /** @class */ (function () {
    function DrawingList(app) {
        var _this = this;
        this.app = app;
        this.domElement = new Dom().createElement({ elementType: 'div' }).css('height', '100%');
        this.data = {
            fileList: []
        };
        this.checkedRows = [];
        this.rows = [];
        this.checkBoxs = [];
        this.linkdedFiles = [];
        this.highlightedDom = null;
        this.deletable = false;
        this.renderItemMap = render("\n      <hr></hr>\n      <div class=\"bfmodule-linkage-editor-section-container one-line-content width220 height44\">\n        <label class=\"bfmodule-linkage-editor-label black-text\">dwg\u56FE\u7EB8</label>\n        <div class=\"bfmodule-linkage-editor-button-container\">\n          <Button ref=\"addResource\" class=\"bfmodule-linkage-editor-add-btn bf-add-resource\"></Button>\n          <Button ref=\"removeResource\" class=\"bfmodule-linkage-editor-remove-btn bf-delete bfmodule-linkage-btn-disbale\"></Button>\n        </div>\n      </div>\n      <hr class=\"margin-left20 width220 margin-bottom5\"></hr>\n      <div ref=\"fileList\" class=\"bfmodule-linkage-editor-list width240 margin-left20\"></div>\n    ", this.domElement, this);
        var addResource = this.renderItemMap.addResource;
        addResource.setTooltip('添加');
        addResource.onClick(function () {
            eventBus.trigger(EditorEvent$1.AddResourcePanelShow);
        });
        var self = this;
        var fileList = this.renderItemMap.fileList;
        // 设置列表高度使其可以拉伸
        fileList.getElement().style.height = document.getElementsByClassName('bfmodule-linkage-editor-leftside')[0].clientHeight - 85 + "px";
        fileList.getElement().style.overflowY = 'auto';
        eventBus.on(EditorEvent$1.AddResourceByViewToken, function (data) {
            var firstLoaded = false;
            // 从零添加时添加全部图纸选项
            if (_this.data.fileList.length == 0 && data.length > 0) {
                firstLoaded = true;
                var row = new Row({ id: 'allDrawings', justifyContent: 'Start' });
                row.getDomElement().getElement().style.position = 'relative';
                var checkBoxAll_1 = new Checkbox({ value: 'allDrawings' });
                checkBoxAll_1.onChange(function () {
                    if (checkBoxAll_1.isChecked()) {
                        _this.checkBoxs.forEach(function (cb) { return cb.setChecked(true); });
                        _this.checkedRows = __spreadArrays(_this.rows);
                    }
                    else {
                        _this.checkBoxs.forEach(function (cb) { return cb.setChecked(false); });
                        _this.checkedRows = [];
                    }
                });
                row.addControl(checkBoxAll_1);
                var text = new Dom().createElement({ elementType: 'p', className: 'bfmodule-list-text' });
                text.html('全部图纸');
                row.getDomElement().append(text);
                _this.rows.push(row);
                _this.checkBoxs.push(checkBoxAll_1);
                fileList.append(row.getDomElement());
            }
            // array without duplication
            var allFiles = __spreadArrays(_this.data.fileList, data);
            _this.data.fileList = allFiles.filter(function (value, index, self) {
                return index === self.findIndex(function (t) { return ((t.id ? t.id : t.modelId) === (value.id ? value.id : value.modelId)); });
            });
            data.forEach(function (item) {
                // detect if the data has been added
                var index = _this.rows.findIndex(function (row) { return row.getId() == item.id; });
                if (index > -1) {
                    return;
                }
                // 链接的token初始链接没有id，通过文件获取
                var row = new Row({
                    id: item.id || item.modelId,
                    justifyContent: 'Start'
                });
                row.getDomElement().getElement().style.position = 'relative';
                // add checkbox
                var checkBox = new Checkbox({ value: item.id });
                checkBox.onChange(function () {
                    if (checkBox.isChecked()) {
                        // enable delete button
                        self.deletable = true;
                        self.renderItemMap.removeResource.removeClass('bfmodule-linkage-btn-disbale');
                        _this.checkedRows.push(row);
                        // detect if half check for all files checkbox is needed
                        var includeAll = _this.checkedRows.findIndex(function (row) { return row.id == 'allDrawings'; }) > -1;
                        if (_this.checkedRows.length >= (includeAll ? _this.rows.length : _this.rows.length - 1)) {
                            if (_this.rows[0].getAllControls()[0].getDomElement().element.children[0].hasClass('bfui-half')) {
                                _this.rows[0].getAllControls()[0].getDomElement().element.children[0].removeClass('bfui-half');
                            }
                            _this.rows[0].getAllControls()[0].getDomElement().element.children[0].checked = true;
                        }
                        else if (_this.checkedRows.length == 1
                            && _this.checkedRows.length < _this.rows.length - 1
                            && !_this.rows[0].getAllControls()[0].getDomElement().element.children[0].hasClass('bfui-half')) {
                            _this.rows[0].getAllControls()[0].getDomElement().element.children[0].checked = false;
                            _this.rows[0].getAllControls()[0].getDomElement().element.children[0].addClass('bfui-half');
                        }
                        else if (!_this.rows[0].getAllControls()[0].getDomElement().element.children[0].hasClass('bfui-half')) {
                            _this.rows[0].getAllControls()[0].getDomElement().element.children[0].addClass('bfui-half');
                        }
                    }
                    else {
                        var index_1 = _this.checkedRows.indexOf(row);
                        if (index_1 > -1) {
                            _this.checkedRows.splice(index_1, 1);
                            if (_this.checkedRows.length < _this.rows.length
                                && _this.checkedRows.length > 0
                                && !_this.rows[0].getAllControls()[0].getDomElement().element.children[0].hasClass('bfui-half')
                                && !(_this.checkedRows.length == 1 && _this.checkedRows[0].id == 'allDrawings')) {
                                _this.rows[0].getAllControls()[0].getDomElement().element.children[0].addClass('bfui-half');
                                _this.rows[0].getAllControls()[0].getDomElement().element.children[0].checked = false;
                            }
                            else if ((_this.checkedRows.length == 0 || (_this.checkedRows.length == 1 && _this.checkedRows[0].id == "allDrawings"))) {
                                // disbale delete button
                                self.deletable = false;
                                self.renderItemMap.removeResource.addClass('bfmodule-linkage-btn-disbale');
                                if (_this.rows[0].getAllControls()[0].getDomElement().element.children[0].hasClass('bfui-half')) {
                                    _this.rows[0].getAllControls()[0].getDomElement().element.children[0].removeClass('bfui-half');
                                }
                                _this.rows[0].getAllControls()[0].getDomElement().element.children[0].checked = false;
                            }
                        }
                    }
                });
                row.addControl(checkBox);
                // add text
                var text = new Dom().createElement({ elementType: 'p', className: 'bfmodule-list-text' });
                text.html(item.name);
                text.attribute('title', item.name);
                row.getDomElement().append(text);
                // 没有高亮时高亮的一个
                if (!_this.highlightedDom) {
                    text.getElement().style.color = '#32D3A6';
                    _this.highlightedDom = text.getElement();
                }
                text.on('click', function () {
                    if (_this.highlightedDom) {
                        _this.highlightedDom.style.color = '#333333';
                    }
                    text.getElement().style.color = '#32D3A6';
                    _this.highlightedDom = text.getElement();
                    eventBus.trigger(EditorEvent$1.LoadedDrawingChanged, [item]);
                });
                if (_this.checkedRows.length > 0
                    && !_this.rows[0].getAllControls()[0].getDomElement().element.children[0].hasClass('bfui-half')) {
                    _this.rows[0].getAllControls()[0].getDomElement().element.children[0].addClass('bfui-half');
                    _this.rows[0].getAllControls()[0].getDomElement().element.children[0].checked = false;
                }
                // button container
                var buttonContainer = new Dom().createElement({ elementType: 'div', className: 'bfmodule-list-buttons-container' });
                // link button
                var linkButton = new Button();
                linkButton.addClass('bfmodule-link-button');
                linkButton.addClass('bf-connect-16');
                linkButton.addClass('hide');
                linkButton.setTooltip('位置关联');
                buttonContainer.append(linkButton.getDomElement());
                // linked button
                var linkedButton = new Button();
                linkedButton.addClass('bfmodule-linked-button');
                linkedButton.addClass('bf-connectted-16');
                linkedButton.addClass('hide');
                linkedButton.setTooltip('位置已关联');
                buttonContainer.append(linkedButton.getDomElement());
                row.getDomElement().append(buttonContainer);
                linkButton.onClick(function () {
                    text.click();
                    self.app.app._viewer2d.cameraNode && destroyCameraNode(self.app.app._viewer2d);
                    eventBus.trigger(EditorEvent$1.LinkDrawing, item);
                });
                linkedButton.onClick(function () {
                    text.click();
                    self.app.app._viewer2d.cameraNode && destroyCameraNode(self.app.app._viewer2d);
                    // 链接token传入的数据没有id
                    var linkedFile = _this.linkdedFiles.find(function (file) { return file.id === (item.id ? item.id : item.modelId) || (file.name == item.name && typeof item.viewId != 'undefined'); });
                    linkedFile.linked = true;
                    eventBus.trigger(EditorEvent$1.LinkDrawing, linkedFile);
                });
                // 首次加载并且存在链接列表，则传入为metadata
                if (firstLoaded && self.app.app.getApp3D().linkList.length > 0) {
                    _this.linkdedFiles = __spreadArrays(self.app.app.getApp3D().linkList);
                    var initSection = function () {
                        // 是否为已经链接的图纸，据此判断链接按钮等信息是否展示
                        if (self.app.app.getApp3D().linkList.findIndex(function (i) { return i.id == item.modelId; }) < 0)
                            return;
                        linkedButton.removeClass('hide');
                        // 相机点
                        if (self.app.app.getApp3D().linkList.findIndex(function (i) { return i.id == data[0].modelId; }) < 0)
                            return;
                        destroyCameraNode(self.app.app._viewer2d);
                        var updateCameraNodeStatus = function () {
                            destroyCameraNode(self.app.app._viewer2d);
                            self.app.app._viewer2d.cameraNode = addCameraNode(self.app.app._viewer2d, self.app.app._app3d);
                            var savedHeight = self.app.app.getApp3D().linkList[0].isFloor
                                ? Number(self.app.app.getApp3D().linkList[0].selectedHeight) + Number(self.app.app.getApp3D().linkList[0].sectionOffset)
                                : Number(self.app.app.getApp3D().linkList[0].baseHeight) + Number(self.app.app.getApp3D().linkList[0].sectionOffset);
                            self.app.app._viewer2d.cameraNode.savedHeight = savedHeight;
                            // 计算矩阵并保存矩阵值
                            var _a = calculateLinkageMatrix(self.app.app.getApp3D().linkList[0].addedDrawingPoints, self.app.app.getApp3D().linkList[0].addedModelPoints), matrix = _a.matrix, rotation = _a.rotation;
                            self.app.app._viewer2d.cameraNode.matrix = matrix;
                            self.app.app._viewer2d.cameraNode.rotation = rotation;
                            // 剖切
                            self.app.app.getViewer3D().getModel(self.app.app.getModelId()).getBoundingBox(self.app.app.getModelId(), function (data) {
                                self.app.app.getApp3D().modelMaxHeight = data.currentBoundingBox.max.z;
                                self.app.app.getApp3D().modelMinHeight = data.currentBoundingBox.min.z;
                                if (self.app.app.getApp3D().linkList[0].allowSection) {
                                    var progress = getSectionProgress(self.app.app.getViewer3D().getUnit(), self.app.app.getApp3D().linkList[0].sectionOffset, self.app.app.getApp3D().linkList[0].isFloor ? self.app.app.getApp3D().linkList[0].selectedHeight : self.app.app.getApp3D().linkList[0].baseHeight, self.app.app.getApp3D().modelMaxHeight, self.app.app.getApp3D().modelMinHeight);
                                    setModelSectionProgress(self.app.app.getViewer3D(), progress);
                                }
                            });
                            self.app.app._viewer2d.removeEventListener('Loaded', updateCameraNodeStatus);
                        };
                        self.app.app._viewer2d.addEventListener('Loaded', updateCameraNodeStatus);
                    };
                    self.app.app._eventEmmiter.on(EditorEvent$1.ViewerDrawingInitialized, initSection);
                }
                eventBus.on(EditorEvent$1.InitModule, function (savedData) {
                    if (savedData && (savedData.id === (item.id ? item.id : item.modelId) || (savedData.name === item.name && typeof item.viewId != 'undefined'))) {
                        if (savedData.linked) {
                            linkedButton.removeClass('hide');
                            destroyCameraNode(self.app.app._viewer2d);
                            self.app.app._viewer2d.cameraNode = addCameraNode(self.app.app._viewer2d, self.app.app._app3d);
                            var savedHeight = savedData.isFloor ? Number(savedData.selectedHeight) + Number(savedData.sectionOffset) : Number(savedData.baseHeight) + Number(savedData.sectionOffset);
                            self.app.app._viewer2d.cameraNode.savedHeight = savedHeight;
                            var linkedFile = _this.linkdedFiles.find(function (file) {
                                return file.id === savedData.id;
                            });
                            // 计算矩阵并保存矩阵值
                            var _a = calculateLinkageMatrix(savedData.addedDrawingPoints, savedData.addedModelPoints), matrix = _a.matrix, rotation = _a.rotation;
                            self.app.app._viewer2d.cameraNode.matrix = matrix;
                            self.app.app._viewer2d.cameraNode.rotation = rotation;
                            if (linkedFile) {
                                var index_2 = _this.linkdedFiles.indexOf(linkedFile);
                                _this.linkdedFiles[index_2] = linkedFile = __assign(__assign({}, savedData), { matrix: matrix });
                            }
                            else {
                                _this.linkdedFiles.push(__assign(__assign({}, savedData), { matrix: matrix }));
                            }
                            // 保存至app下
                            self.app.app._app3d.linkList = __spreadArrays(_this.linkdedFiles);
                            // 保存切換的layout
                            item.viewId = savedData.viewId;
                        }
                    }
                });
                // dynamic display link button
                row.getDomElement().on('mouseenter', function () {
                    var linkExist = __spreadArrays(_this.linkdedFiles).filter(function (file) { return file.id == row.getId(); });
                    linkExist.length === 0 && linkButton.removeClass('hide');
                });
                row.getDomElement().on('mouseleave', function () {
                    linkButton.addClass('hide');
                });
                _this.rows.push(row);
                _this.checkBoxs.push(checkBox);
                fileList.append(row.getDomElement());
            });
            if (firstLoaded) {
                window.requestAnimationFrame(function () { return firstLoaded = false; });
            }
            self.app.app.getApp3D().fileList = _this.data.fileList;
        });
        eventBus.on(EditorEvent$1.LoadedDrawingChanged, function (data) {
            if (data.length == 0 || (data.length == 1 && data[0] == undefined))
                return;
            var seletedId = data[0].id;
            // 若不存在则为metadata传入数据
            if (!seletedId) {
                seletedId = data[0].modelId;
            }
            if (_this.highlightedDom) {
                _this.highlightedDom.style.color = '#333333';
            }
            var selectedRow = _this.rows.find(function (item) { return item.id == seletedId; });
            if (selectedRow) {
                _this.highlightedDom = selectedRow.getDomElement().element.childNodes[1];
                _this.highlightedDom.style.color = '#32D3A6';
            }
            // 每次切换都根据当前是否关联确认是否展示相机点，并将matrix关联至相机点下
            var linkedFile = _this.linkdedFiles.find(function (file) {
                return file.id === seletedId;
            });
            // 链接存在且非初始化，确保viewer存在
            if (linkedFile && linkedFile.id == seletedId && self.app.app._viewer2d) {
                destroyCameraNode(self.app.app._viewer2d);
                self.app.app._viewer2d.cameraNode = addCameraNode(self.app.app._viewer2d, self.app.app._app3d);
                var _a = calculateLinkageMatrix(linkedFile.addedDrawingPoints, linkedFile.addedModelPoints), matrix = _a.matrix, rotation = _a.rotation;
                self.app.app._viewer2d.cameraNode.matrix = matrix;
                self.app.app._viewer2d.cameraNode.rotation = rotation;
                var savedHeight = linkedFile.isFloor ? linkedFile.selectedHeight + Number(linkedFile.sectionOffset) : (linkedFile.baseHeight) + Number(linkedFile.sectionOffset);
                self.app.app._viewer2d.cameraNode.savedHeight = savedHeight;
                // 已经链接的模型则切换至对应切面高度
                var sectProgress = getSectionProgress(self.app.app._viewer3d.getUnit(), linkedFile.sectionOffset, linkedFile.isFloor ? linkedFile.selectedHeight : Number(linkedFile.baseHeight), self.app.app._app3d.modelMaxHeight, self.app.app._app3d.modelMinHeight);
                linkedFile.allowSection && setModelSectionProgress(self.app.app._viewer3d, sectProgress);
            }
            // 兼容图纸可能未加载导致没有viewer的情况
            if (!linkedFile && self.app.app._viewer2d && self.app.app._viewer2d.cameraNode) {
                destroyCameraNode(self.app.app._viewer2d);
            }
        });
        var confirmClicked = function () {
            removeResource.addClass('bfmodule-linkage-btn-disbale');
            _this.deletable = false;
            var needRemovedFile = [];
            // 删除半选
            if (_this.rows[0].getAllControls()[0].getDomElement().element.children[0].hasClass('bfui-half')
                && _this.checkedRows.length < _this.rows.length) {
                _this.rows[0].getAllControls()[0].getDomElement().element.children[0].removeClass('bfui-half');
            }
            _this.checkedRows.forEach(function (row) {
                if ((row.getId() != 'allDrawings')) {
                    var index_3 = 0;
                    _this.rows = _this.rows.filter(function (item, idx) {
                        item.id === row.getId() && (index_3 = idx);
                        return item.id !== row.getId();
                    });
                    index_3 > 0 && needRemovedFile.push(index_3 - 1);
                    fileList.remove(row.getDomElement());
                }
            });
            var _loop_1 = function (i) {
                if (_this.data.fileList.length > 0) {
                    // 删除已关联的图纸
                    var linkedIndex = _this.linkdedFiles.findIndex(function (file) {
                        if (_this.data.fileList[needRemovedFile[i]]) {
                            return file.id === (_this.data.fileList[needRemovedFile[i]].id || _this.data.fileList[needRemovedFile[i]].modelId);
                        }
                    });
                    if (linkedIndex > -1) {
                        _this.linkdedFiles.splice(linkedIndex, 1);
                        self.app.app._app3d.linkList = __spreadArrays(_this.linkdedFiles);
                    }
                    _this.data.fileList.splice(needRemovedFile[i], 1);
                }
                else {
                    self.app.app._app3d.linkList = [];
                }
            };
            for (var i = 0; i <= needRemovedFile.length - 1; i++) {
                _loop_1(i);
            }
            // 触发要删除的index序列通知引擎中顶部的图纸列表
            eventBus.trigger(EditorEvent$1.RemoveResourceByViewToken, needRemovedFile);
            // 删除顶部全部图纸提示语
            if (_this.data.fileList.length == 0) {
                if (fileList.getElement().childNodes.length == 1) {
                    fileList.getElement().removeChild(fileList.getElement().firstChild);
                }
                _this.rows = [];
            }
            else if (!_this.highlightedDom.parentElement.parentElement) { // 删除了当前的高亮图纸
                eventBus.trigger(EditorEvent$1.LoadedDrawingChanged, [_this.data.fileList[0]]);
            }
            _this.checkedRows = [];
            // 删除全部半勾选
            if (_this.rows.length > 0
                && _this.rows[0].getAllControls()[0].getDomElement().element.children[0].hasClass('bfui-half')) {
                _this.rows[0].getAllControls()[0].getDomElement().element.children[0].removeClass('bfui-half');
                _this.rows[0].getAllControls()[0].getDomElement().element.children[0].checked = false;
            }
            // 删除了第一张图纸则加载的内容需要发生变化
            if (needRemovedFile.indexOf(0) > -1) {
                eventBus.trigger(EditorEvent$1.LoadedDrawingChanged, _this.data.fileList);
            }
        };
        var removeResource = this.renderItemMap.removeResource;
        removeResource.setTooltip('移除');
        removeResource.onClick(function () {
            if (!_this.deletable)
                return;
            // 二次确认弹窗
            new MessageBox({
                content: '确认移除所选图纸？',
                hasCancelButton: true,
                confirmText: '确认',
                cancelText: '取消',
                confirmHandler: confirmClicked,
            });
        });
    }
    DrawingList.prototype.getDomElement = function () {
        return this.domElement;
    };
    return DrawingList;
}());

// 图纸类型
var displayType = [
    { text: '平行展示', value: 'Parallel' },
];
var heightRelation = [
    { text: '按楼层', value: 'BasedFloor' },
    { text: '按标高', value: 'BasedElevation' }
];

var ViewerConfig = /** @class */ (function () {
    function ViewerConfig(app) {
        this.app = app;
        this.domElement = new Dom().createElement({ elementType: 'div' }).css('height', '100%');
        this.data = {};
        this.renderItemMap = render("\n      <hr></hr>\n      <div class=\"bfmodule-linkage-editor-title-container\">\n        <div class=\"bfmodule-linkage-editor-rect\"></div>\n        <label class=\"bfmodule-linkage-editor-title\">\u57FA\u7840\u4FE1\u606F</label>\n      </div>\n      <div class=\"bfmodule-linkage-editor-content-container\">\n        <div class=\"bfmodule-linkage-editor-section-container\">\n          <label class=\"bfmodule-linkage-editor-label\">\u540D\u79F0</label>\n          <Input ref=\"moduleName\" class=\"bfmodule-linkage-editor-input margin-top5\"></Input>\n        </div>\n        <div class=\"bfmodule-linkage-editor-section-container margin-top10 margin-bottom12\">\n          <label class=\"bfmodule-linkage-editor-label\">\u5C55\u793A\u6A21\u5F0F</label>\n          <Select ref=\"displayType\" class=\"bfmodule-linkage-editor-select margin-top5\"></Select>\n        </div>\n      </div>\n\n      <div class=\"bfmodule-linkage-editor-title-container direction-row\">\n        <div class=\"bfmodule-linkage-editor-rect\"></div>\n        <label class=\"bfmodule-linkage-editor-title\">\u56FE\u7EB8\u5DE5\u5177\u6761</label>\n      </div>\n      <div class=\"bfmodule-linkage-editor-content margin-left20\">\n        <div ref=\"configList\" class=\"bfmodule-linkage-editor-list margin-top10\">\n          <div class=\"bfmodule-linkage-editor-section-container direction-row align-center\">\n            <Checkbox ref=\"showDrawingMaintoolbar\"></Checkbox>\n            <label class=\"label-text\">\u4E3B\u5DE5\u5177\u6761</label>\n          </div>\n          <div class=\"bfmodule-linkage-editor-section-container direction-row margin-top10 align-center\">\n            <Checkbox ref=\"showDrawingList\"></Checkbox>\n            <label class=\"label-text\">\u56FE\u7EB8\u5217\u8868</label>\n          </div>\n          <div class=\"bfmodule-linkage-editor-section-container direction-row margin-top10 align-center\">\n            <Checkbox ref=\"showDrawingSearchTool\"></Checkbox>\n            <label class=\"label-text\">\u641C\u7D22\u5DE5\u5177</label>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"bfmodule-linkage-editor-title-container margin-top10\">\n        <div class=\"bfmodule-linkage-editor-rect\"></div>\n        <label class=\"bfmodule-linkage-editor-title\">\u6A21\u578B\u5DE5\u5177\u6761</label>\n      </div>\n      <div class=\"bfmodule-linkage-editor-content margin-left20\">\n        <div ref=\"configList\" class=\"bfmodule-linkage-editor-list margin-top10\">\n          <div class=\"bfmodule-linkage-editor-section-container direction-row align-center\">\n            <Checkbox ref=\"showModelMaintoolbar\"></Checkbox>\n            <label class=\"label-text\">\u4E3B\u5DE5\u5177\u6761</label>\n          </div>\n          <div class=\"bfmodule-linkage-editor-section-container direction-row margin-top10 align-center\">\n            <Checkbox ref=\"showModelTreeToolbar\"></Checkbox>\n            <label class=\"label-text\">\u76EE\u5F55\u6811</label>\n          </div>\n          <div class=\"bfmodule-linkage-editor-section-container direction-row margin-top10 align-center\">\n            <Checkbox ref=\"showModelViewHouse\"></Checkbox>\n            <label class=\"label-text\">viewHouse</label>\n          </div>\n        </div>\n      </div>\n    ", this.domElement, this);
        var moduleName = this.renderItemMap.moduleName;
        moduleName.setValue(app.app.getViewer3D().getModels()[0]._data.name.split('.')[0]);
        moduleName.onChange(function (value) {
            app.app.getApp3D().linkConfig.name = value;
        });
        var displayType$1 = this.renderItemMap.displayType;
        displayType$1.setValues(displayType);
        displayType$1.setSelectedByValue(displayType[0].value);
        // 除图纸viewer内select组件的选项中均加入控制样式的class
        setTimeout(function () {
            [].forEach.call(document.getElementsByClassName('bfui-select-content'), function (ele) {
                !ele.classList.contains('bfmodule-linkage-editor-linked-select-options') && ele.classList.add('bfmodule-linkage-editor-select');
            });
        }, 0);
        displayType$1.onChange(function (item) {
            app.app.getApp3D().linkConfig.displayType = item.value;
        });
        var showDrawingMaintoolbar = this.renderItemMap.showDrawingMaintoolbar;
        showDrawingMaintoolbar.onChange(function () {
            if (showDrawingMaintoolbar.isChecked()) {
                app.app.getApp3D().linkConfig.showDrawingMaintoolbar = true;
                app.app.getApp2D() && app.app.getApp2D().UI.getToolbar('MainToolbar').show();
            }
            else {
                app.app.getApp3D().linkConfig.showDrawingMaintoolbar = false;
                app.app.getApp2D() && app.app.getApp2D().UI.getToolbar('MainToolbar').hide();
            }
        });
        var showDrawingSearchTool = this.renderItemMap.showDrawingSearchTool;
        showDrawingSearchTool.onChange(function () {
            if (showDrawingSearchTool.isChecked()) {
                app.app.getApp3D().linkConfig.showDrawingSearchTool = true;
                app.app.getApp2D() && app.app.getApp2D().UI.getToolbar("SearchToolbar").show();
            }
            else {
                app.app.getApp3D().linkConfig.showDrawingSearchTool = false;
                app.app.getApp2D() && app.app.getApp2D().UI.getToolbar("SearchToolbar").hide();
            }
        });
        var showModelMaintoolbar = this.renderItemMap.showModelMaintoolbar;
        showModelMaintoolbar.onChange(function () {
            if (showModelMaintoolbar.isChecked()) {
                app.app.getApp3D().linkConfig.showModelMaintoolbar = true;
                app.app.getApp3D().UI.getToolbar('MainToolbar').show();
            }
            else {
                app.app.getApp3D().linkConfig.showModelMaintoolbar = false;
                app.app.getApp3D().UI.getToolbar('MainToolbar').hide();
            }
        });
        var showModelTreeToolbar = this.renderItemMap.showModelTreeToolbar;
        showModelTreeToolbar.onChange(function () {
            if (showModelTreeToolbar.isChecked()) {
                app.app.getApp3D().linkConfig.showModelTreeToolbar = true;
                app.app.getApp3D().UI.getToolbar('ModelTree').show();
            }
            else {
                app.app.getApp3D().linkConfig.showModelTreeToolbar = false;
                app.app.getApp3D().UI.getToolbar('ModelTree').hide();
            }
        });
        var showModelViewHouse = this.renderItemMap.showModelViewHouse;
        showModelViewHouse.onChange(function () {
            if (showModelViewHouse.isChecked()) {
                app.app.getApp3D().linkConfig.showModelViewHouse = true;
                app.app.getViewer3D().showViewHouse();
            }
            else {
                app.app.getApp3D().linkConfig.showModelViewHouse = false;
                app.app.getViewer3D().hideViewHouse();
            }
        });
        var showDrawingList = this.renderItemMap.showDrawingList;
        showDrawingList.onChange(function () {
            if (showDrawingList.isChecked()) {
                app.app.getApp3D().linkConfig.showDrawingList = true;
                app.app.getApp3D().UI.linkedList
                    && app.app.getApp3D().UI.linkedList.getDomElement().removeClass('hide');
            }
            else {
                app.app.getApp3D().linkConfig.showDrawingList = false;
                app.app.getApp3D().UI.linkedList
                    && app.app.getApp3D().UI.linkedList.getDomElement().addClass('hide');
            }
        });
        var setToolbars = function () {
            showModelTreeToolbar.setChecked(app.app.getApp3D().linkConfig.showModelTreeToolbar);
            showModelMaintoolbar.setChecked(app.app.getApp3D().linkConfig.showModelMaintoolbar);
            showModelViewHouse.setChecked(app.app.getApp3D().linkConfig.showModelViewHouse);
            showDrawingList.setChecked(app.app.getApp3D().linkConfig.showDrawingList);
            showDrawingMaintoolbar.setChecked(app.app.getApp3D().linkConfig.showDrawingMaintoolbar);
            showDrawingSearchTool.setChecked(app.app.getApp3D().linkConfig.showDrawingSearchTool);
        };
        var setUntilInited = function () {
            if (!app.app.getApp3D().linkConfig || !app.app.getApp3D().linkConfig.initialized) {
                setTimeout(setUntilInited, 100);
            }
            else {
                if (app.app._app3d.linkList.length == 0) {
                    requestAnimationFrame(function () {
                        setToolbars();
                    });
                }
                else {
                    app.app._eventEmmiter.on('ViewerDrawingAdded', function () {
                        app.app.getViewerDrawing().addEventListener('Loaded', function () {
                            setToolbars();
                        });
                    });
                }
            }
        };
        setUntilInited();
    }
    ViewerConfig.prototype.getDomElement = function () {
        return this.domElement;
    };
    return ViewerConfig;
}());

var InitModule = /** @class */ (function () {
    function InitModule(app) {
        this.app = app;
        this.domElement = new Dom().createElement({ elementType: 'div' }).css('height', '100%');
        this.data = {};
        this.renderItemMap = render("\n      <div ref=\"contents\" class=\"bfmodule-linkage-editor-content\"></div>\n    ", this.domElement, this);
        var contents = this.renderItemMap.contents;
        var drawingList = new DrawingList(this);
        var viewerConfig = new ViewerConfig(this);
        var tabs = new Tabs({
            parent: contents,
            options: [
                {
                    content: drawingList.getDomElement(),
                    label: '图纸',
                    name: 'drawingList',
                    extraClass: 'margin-change',
                }, {
                    content: viewerConfig.getDomElement(),
                    label: '设置',
                    name: 'viewerConfig',
                    extraClass: 'margin-change',
                }
            ],
            tabLeft: 10,
        });
        requestAnimationFrame(function () {
            tabs.setActive(0);
        });
    }
    InitModule.prototype.getDomElement = function () {
        return this.domElement;
    };
    return InitModule;
}());

/**
 * @classdesc 类：Message组件类
 * @class Glodon.Bimface.Tiles.UI.Message
 * @constructs Glodon.Bimface.Tiles.UI.Message
 * @description 构造消息组件
 * @extends Glodon.Bimface.Tiles.UI.Control
 * @param {Object} option 构造消息的配置项
 * @param {String} option.id 消息组件ID
 * @param {String} option.text 消息的文字内容
 */
var Message = /** @class */ (function (_super) {
    __extends(Message, _super);
    function Message(param) {
        var _this = this;
        param = param || {};
        var elementParam = {
            elementType: 'div',
            className: 'bfui-message'
        };
        var controlParam = {
            elementParam: elementParam,
            type: 'Message',
            id: param.id,
            parent: param.parent
        };
        _this = _super.call(this, controlParam) || this;
        param.text && _this.setText(param.text);
        param.className && _this.addClass(param.className);
        setTimeout(function () {
            _this.getDomElement().addClass('hide');
            _this.getDomElement().on('transitionend', function () { return _this.destroy(); });
        }, param.duration || 3000);
        return _this;
    }
    /**
     * 获取文字内容
     * @function Glodon.Bimface.Tiles.UI.Message.prototype.getText
     * @returns {String} 文字内容
     */
    Message.prototype.getText = function () {
        return this._domElement.html();
    };
    /**
     * 设置文字内容
     * @function Glodon.Bimface.Tiles.UI.Message.prototype.setText
     * @param {String} text 文字内容
     */
    Message.prototype.setText = function (text) {
        this._domElement.html(text);
        return this;
    };
    return Message;
}(Control));

var LinkModule = /** @class */ (function () {
    function LinkModule(app) {
        var _this = this;
        this.app = app;
        this.domElement = new Dom().createElement({ elementType: 'div' }).css('height', '100%');
        this.data = {
            id: null,
            name: null,
            modelType: null,
            viewToken: null,
            heightType: 'BasedFloor',
            isFloor: true,
            allowSection: true,
            allowAxisGrids: true,
            openAxisGrids: true,
            addedDrawingPoints: [],
            addedModelPoints: [],
            sectionOffset: 1200,
            baseHeight: 0,
            selectedHeight: null,
            linked: false
        };
        var self = this;
        this.renderItemMap = render("\n      <div class=\"relative-position height-all\">\n        <div class=\"bfmodule-linkage-editor-header-container\">\n          <Button ref=\"backInitBtn\" class=\"bfmodule-linkage-editor-back-btn bf-arrow-left\"></Button>\n          <label ref=\"drawingName\" class=\"bfmodule-linkage-editor-title\"></label>\n        </div>\n        <hr></hr>\n\n        <div class=\"bfmodule-linkage-editor-content-container margin0\">\n          <div class=\"bfmodule-linkage-editor-title-container\">\n            <div class=\"bfmodule-linkage-editor-rect\"></div>\n            <label class=\"bfmodule-linkage-editor-title\">\u9AD8\u5EA6\u5173\u8054</label>\n          </div>\n          <div class=\"bfmodule-linkage-editor-section-container margin-top10 margin-left20\">\n            <label class=\"bfmodule-linkage-editor-label\">\u5173\u8054\u65B9\u5F0F</label>\n            <Select ref=\"heightType\" class=\"bfmodule-linkage-editor-select margin-top5\"></Select>\n          </div>\n          <div bf-show=\"scope.data.isFloor\" class=\"bfmodule-linkage-editor-section-container margin-top10 margin-bottom20 margin-left20\">\n            <label class=\"bfmodule-linkage-editor-label\">\u697C\u5C42</label>\n            <Select ref=\"floorsInfo\" class=\"bfmodule-linkage-editor-select margin-top5\"></Select>\n          </div>\n          <div bf-show=\"!scope.data.isFloor\" class=\"bfmodule-linkage-editor-section-container margin-top10 margin-bottom20 margin-left20\">\n            <label class=\"bfmodule-linkage-editor-label\">\u6807\u9AD8</label>\n            <div class=\"bfmodule-linkage-editor-content margin-top5 align-center\">\n              <div class=\"bfmodule-linkage-editor-content relative-position width186 align-center\">\n                <Input ref=\"pickHeightInput\" class=\"bfmodule-linkage-editor-name-input width186\"></Input>\n                <span class=\"unit\">m</span>\n              </div>\n              <Button ref=\"pickHeightButton\" class=\"bfmodule-linkage-editor-pick-point-icon bf-absorb-24 margin-left15\"></Button>\n            </div>\n          </div>\n          \n          <div class=\"bfmodule-linkage-editor-title-container\">\n            <div class=\"bfmodule-linkage-editor-rect\"></div>\n            <label class=\"bfmodule-linkage-editor-title\">\n              \u5E73\u9762\u5173\u8054\n              <i ref=\"linkageNotice\" class=\"bf-warning-sm\"></i>\n            </label>\n          </div>\n          <div bf-show=\"scope.data.allowAxisGrids\" class=\"bfmodule-linkage-editor-section-container margin-top5 margin-left20 margin-right20\">\n            <div class=\"bfmodule-linkage-editor-content margin-top5 space-between\">\n              <label class=\"bfmodule-linkage-editor-label\">\u6A21\u578B\u8F74\u7F51</label>\n              <Switch ref=\"allowAxisGrids\"></Switch>\n            </div>\n          </div>\n          <div class=\"bfmodule-linkage-editor-content-container margin-top5\">\n            <div class=\"bfmodule-linkage-editor-content margin-bottom12\">\n              <button ref=\"pickDrawingButton\" class=\"bfmodule-linkage-editor-pick-point-button\">\n                <span class=\"bfmodule-linkage-editor-pick-point-button-text\">\u56FE\u7EB8\u5BF9\u9F50\u70B9</span>\n                <span class=\"bfmodule-linkage-editor-pick-point-icon-sm bf-absorb-24\"></span>\n              </bitton>\n              <button ref=\"pickModelButton\" class=\"bfmodule-linkage-editor-pick-point-button margin-left10\">\n                <span class=\"bfmodule-linkage-editor-pick-point-button-text\">\u6A21\u578B\u5BF9\u9F50\u70B9</span>\n                <span class=\"bfmodule-linkage-editor-pick-point-icon-sm bf-absorb-24\"></span>\n              </button>\n            </div>\n          </div>\n\n          <div class=\"bfmodule-linkage-editor-title-container\">\n            <div class=\"bfmodule-linkage-editor-rect\"></div>\n            <label class=\"bfmodule-linkage-editor-title\">\u5256\u5207\u8BBE\u7F6E</label>\n          </div>\n          <div class=\"bfmodule-linkage-editor-section-container margin-top5 margin-left20 margin-right20\">\n            <div class=\"bfmodule-linkage-editor-content margin-top10 space-between height30\">\n              <label class=\"bfmodule-linkage-editor-label\">\u5256\u5207</label>\n              <Switch ref=\"allowSection\"></Switch>\n            </div>\n            <div bf-show=\"scope.data.allowSection\" class=\"bfmodule-linkage-editor-section-container margin-top5\">\n              <label class=\"bfmodule-linkage-editor-label\">\u5256\u9762\u504F\u79FB</label>\n              <div class=\"bfmodule-linkage-editor-content relative-position margin-top5 align-center\">\n                <Input ref=\"sectionOffset\" class=\"bfmodule-linkage-editor-name-input width220\"></Input>\n                <span class=\"unit\">mm</span>\n              </div>\n            </div>\n          </div>\n\n        </div>\n        <div class=\"bfmodule-linkage-editor-fixed-bottom\">\n          <Button ref=\"cancelButton\" class=\"bfmodule-linkage-editor-cancel-button\">\u53D6\u6D88</Button>\n          <Button ref=\"saveButton\" class=\"bfmodule-linkage-editor-submit-button\">\u786E\u8BA4</Button>\n        </div>\n      </div>\n    ", this.domElement, this);
        var setModelLayout = function () {
            if (app._app2d.UI.getToolbar('LeftSubToolbar')) {
                var layoutList = app._app2d.UI.getToolbar('LeftSubToolbar').getControl('LayoutList');
                layoutList._subToolbar._controls.map(function (control) {
                    control.setCheckedState(false);
                });
                layoutList.setSelectedControlById('Model');
            }
        };
        var updateInfo = function () {
            requestAnimationFrame(function () {
                app._app2d.UI.getToolbar('LeftSubToolbar') && app._app2d.UI.getToolbar('LeftSubToolbar').show();
                if (!_this.data.linked) {
                    setModelLayout();
                }
            });
        };
        var backInitBtn = this.renderItemMap.backInitBtn;
        backInitBtn.onClick(function () {
            clearEditStatus(true);
            _this.data.linked = false;
            _this.data.allowAxisGrids && _this.data.openAxisGrids && app.getViewer3D().getModel().removeAllAxisGrids();
            eventBus.trigger(EditorEvent$1.InitModule);
            if (_this.stateBeforeTop)
                app.getViewer3D().clearIsolation();
            app._app2d.UI.getToolbar('LeftSubToolbar') && app._app2d.UI.getToolbar('LeftSubToolbar').hide();
            app.getViewerDrawing().removeEventListener(window.Glodon.Bimface.Application.WebApplicationDrawingEvent.Loaded, updateInfo);
            app.outLinkModulePage = true;
        });
        var drawingName = this.renderItemMap.drawingName;
        var heightType = this.renderItemMap.heightType;
        heightType.setValues(heightRelation);
        heightType.setSelectedByValue(heightRelation[0].value);
        heightType.onChange(function (data) {
            if (data.value === 'BasedFloor') {
                _this.data.isFloor = true;
                _this.data.allowSection && setSectionProgress(_this.data.selectedHeight);
            }
            else {
                _this.data.isFloor = false;
                _this.data.baseHeight = 0;
                pickHeightInput.setValue(0);
                _this.data.allowSection && setSectionProgress(_this.data.baseHeight);
            }
            _this.data.heightType = data.value;
            if (_this.data.allowAxisGrids && _this.data.openAxisGrids) {
                app.getViewer3D().getModel().removeAllAxisGrids();
                _this.showAxisGridsByElevation();
            }
        });
        var floorsInfo = this.renderItemMap.floorsInfo;
        var setSectionProgress = function (height) {
            // 明确单位后确认偏移量
            var sectProgress = getSectionProgress(app._viewer3d.getUnit(), _this.data.sectionOffset, height, _this.modelMaxHeight, _this.modelMinHeight);
            setModelSectionProgress(app._app3d.getViewer(), sectProgress);
        };
        floorsInfo.onChange(function (data) {
            _this.data.selectedHeight = app._viewer3d.getUnit() == window.Glodon.Bimface.Common.Units.LengthUnits.Meter ? Number(data.value) * 1000 : Number(data.value);
            if (_this.data.allowSection) {
                if (!app._app3d.getViewer()._sectionPlane) {
                    initSectionPlane(app._app3d.getViewer());
                }
                setSectionProgress(_this.data.selectedHeight);
            }
            if (_this.data.allowAxisGrids && _this.data.openAxisGrids) {
                app.getViewer3D().getModel().removeAllAxisGrids();
                _this.showAxisGridsByElevation();
            }
        });
        var pickHeightInput = this.renderItemMap.pickHeightInput;
        pickHeightInput.setValue(0);
        pickHeightInput.getDomElement().attribute('type', 'number');
        pickHeightInput.getDomElement().attribute('max-length', '{20}');
        pickHeightInput.getDomElement().on('keypress', function (e) {
            if (e.target.value.length > 20) {
                e.preventDefault();
                return false;
            }
        });
        pickHeightInput.onChange(function (value) {
            if (isNaN(value)) {
                value = _this.data.baseHeight;
                return;
            }
            _this.data.baseHeight = Number(value) * 1000;
            _this.data.allowSection && setSectionProgress(_this.data.baseHeight);
            if (_this.data.allowAxisGrids && _this.data.openAxisGrids) {
                app.getViewer3D().getModel().removeAllAxisGrids();
                _this.showAxisGridsByElevation();
            }
        });
        var pickHeight = function (e) {
            if (!_this.data.isFloor) {
                pickHeightInput.setValue((app._viewer3d.getUnit() == window.Glodon.Bimface.Common.Units.LengthUnits.Meter ? e.worldPosition.z : e.worldPosition.z / 1000).toFixed(3));
            }
            if (_this.pickingHeight) {
                _this.pickingHeight = false;
                _this.data.baseHeight = app._viewer3d.getUnit() == window.Glodon.Bimface.Common.Units.LengthUnits.Meter ?
                    e.worldPosition.z * 1000 : e.worldPosition.z;
                app.getViewer3D().enableSnap(false);
                _this.data.allowSection && setSectionProgress(_this.data.baseHeight);
            }
            app._app3d.getViewer().removeEventListener('MouseClicked', pickHeight);
            // 清除选中
            app._app3d.getViewer().clearSelectedComponents();
            app._app3d.getViewer().render();
            if (_this.data.allowAxisGrids && _this.data.openAxisGrids) {
                app.getViewer3D().getModel().removeAllAxisGrids();
                _this.showAxisGridsByElevation();
            }
        };
        // 轴网开关
        var allowAxisGrids = this.renderItemMap.allowAxisGrids;
        allowAxisGrids.setValue(true);
        allowAxisGrids.onChange(function (openAxisGrids) {
            _this.data.openAxisGrids = openAxisGrids;
            if (openAxisGrids) {
                _this.showAxisGridsByElevation();
                // 如果正在选点，开启隔离
                if (_this.stateBeforeTop)
                    app.getViewer3D().getModel().isolateComponentsById([undefined], "MakeOthersTranslucent");
            }
            else {
                app.getViewer3D().getModel().removeAllAxisGrids();
                if (_this.stateBeforeTop)
                    app.getViewer3D().clearIsolation();
            }
        });
        //标高选择按钮
        var pickHeightButton = this.renderItemMap.pickHeightButton;
        pickHeightButton.setTooltip('拾取标高');
        pickHeightButton.onClick(function () {
            app.getViewer3D().getViewer().setHomeView(3);
            new Message({
                parent: app._app3d.getViewer().getDomElement(),
                className: 'bfmodule-linkage-editor-warning',
                text: "<div class=\"bfmodule-linkage-editor-message-content\"><i class=\"bf-warning-fill-sm\"></i><span>\u8BF7\u5728\u6A21\u578B\u4E2D\u70B9\u9009\u62FE\u53D6\u6807\u9AD8</span></div>",
                duration: 3000,
            });
            app.getViewer3D().enableSnap(true);
            _this.pickingHeight = true;
            app._app3d.getViewer().addEventListener('MouseClicked', pickHeight);
            app._app3d.getViewer()._sectionPlane && app._app3d.getViewer()._sectionPlane.exit();
        });
        var pickDrawingButton = this.renderItemMap.pickDrawingButton;
        var pickModelButton = this.renderItemMap.pickModelButton;
        // 鼠标点击选点
        var pickPoints = function (e) {
            var selectedPoint = e.snapPoint ? e.snapPoint.worldPosition : e.worldPosition ? e.worldPosition : null;
            if (!selectedPoint)
                return false;
            //模型选点
            if (e.click && e.click == 1) {
                selectedPoint.z = Number(_this.data.isFloor ? _this.data.selectedHeight : Number(_this.data.baseHeight))
                    + Number(_this.data.sectionOffset);
                app._viewer3d.getUnit() == window.Glodon.Bimface.Common.Units.LengthUnits.Meter && (selectedPoint.z = selectedPoint.z / 1000);
                if (_this.data.addedModelPoints.length == 0) {
                    _this.data.addedModelPoints.push(selectedPoint);
                    addPickTag(app.getViewer3D(), selectedPoint, 0, '1');
                }
                else if (_this.data.addedModelPoints.length == 1) {
                    _this.data.addedModelPoints.push(selectedPoint);
                    addPickTag(app.getViewer3D(), selectedPoint, 0, '2');
                    app.getViewer3D().enableSnap(false);
                    app.getViewer3D().getDomElement().removeClass('crosshair');
                    app.getViewer3D().removeEventListener('MouseClicked', pickPoints);
                    app.getViewer3D().clearIsolation();
                    // 恢复状态
                    if (_this.stateBeforeTop) {
                        // 轴网关闭状态则关闭轴网，防止状态重置
                        if (_this.data.allowAxisGrids && !_this.data.openAxisGrids) {
                            delete _this.stateBeforeTop.axisGrid;
                        }
                        // 剖切关闭
                        if (!_this.data.allowSection) {
                            delete _this.stateBeforeTop.sectionPlaneState;
                        }
                        else { // 剖切开启重新赋值
                            if (!_this.stateBeforeTop.sectionPlaneState) {
                                _this.stateBeforeTop.sectionPlaneState = app.getViewer3D().getCurrentState().sectionPlaneState;
                            }
                        }
                        app.getViewer3D().setState(_this.stateBeforeTop);
                        delete _this.stateBeforeTop;
                    }
                }
                app.getViewer3D().clearSelectedComponents();
            }
            else {
                //图纸选点
                if (_this.data.addedDrawingPoints.length == 0) {
                    _this.data.addedDrawingPoints.push(selectedPoint);
                    addPickTag(app.getViewerDrawing(), selectedPoint, 0, '1');
                }
                else if (_this.data.addedDrawingPoints.length == 1) {
                    _this.data.addedDrawingPoints.push(selectedPoint);
                    addPickTag(app.getViewerDrawing(), selectedPoint, 0, '2');
                    app.getViewerDrawing().enableSnap(false);
                    app.getViewerDrawing().enablePickEffect(true);
                    app.getViewerDrawing().getDomElement().removeClass('crosshair');
                    app.getViewerDrawing().removeEventListener('MouseClicked', pickPoints);
                    //清除图纸上可能存在的捕捉点
                    if (app.getViewerDrawing().getViewer().mouseEditorMgr && app.getViewerDrawing().getViewer().mouseEditorMgr.editors) {
                        var pickEditor = app.getViewerDrawing().getViewer().mouseEditorMgr.editors.find(function (editor) {
                            return editor.name == 'pick';
                        });
                        pickEditor.tmpPoint = null;
                        pickEditor.finalScreenPoint = [];
                        pickEditor.tmpPointScreenPoint = [];
                        if (app.getViewerDrawing().getViewer().dc && app.getViewerDrawing().getViewer().dc.ctxOverlay) {
                            var width = app.getViewerDrawing()._opt.domElement.clientWidth;
                            var height = app.getViewerDrawing()._opt.domElement.clientHeight;
                            app.getViewerDrawing().getViewer().dc.ctxOverlay.clearRect(0, 0, width, height);
                        }
                    }
                }
                setTimeout(function () {
                    app.getViewerDrawing().clearSelection();
                }, 0);
            }
        };
        // 绘制虚线
        var createDashedLine = function (e, isDrawing) {
            if (isDrawing) {
                var boundingBox = app.getViewerDrawing().getDomElement().getBoundingClientRect();
                var screenPoint = _this.data.addedDrawingPoints[0] && app.getViewerDrawing().worldToClient(_this.data.addedDrawingPoints[0]);
                if (_this.data.addedDrawingPoints.length == 1) {
                    if (!_this.drawingDashedLine) {
                        var screenPoint_1 = app.getViewerDrawing().worldToClient(_this.data.addedDrawingPoints[0]);
                        _this.drawingDashedLine = drawDashedLine(new window.THREE.Vector3(screenPoint_1.x, screenPoint_1.y, 0), new window.THREE.Vector3(e.clientX - boundingBox.x, e.clientY - boundingBox.y, 0), app.getViewerDrawing());
                    }
                    else {
                        var endX = e.snapPoint ? e.snapPoint.worldPosition.x : e.clientX - boundingBox.x;
                        var endY = e.snapPoint ? e.snapPoint.worldPosition.y : e.clientY - boundingBox.y;
                        updateDashedLine(_this.drawingDashedLine, screenPoint, new window.THREE.Vector3(endX, endY, 0), app.getViewerDrawing());
                    }
                }
                if (_this.data.addedDrawingPoints.length == 2) {
                    resizeDashedLine();
                }
            }
            else {
                var boundingBox = app.getViewer3D().getDomElement().getBoundingClientRect();
                var screenPoint = _this.data.addedModelPoints[0] && app.getViewer3D().worldToClient(_this.data.addedModelPoints[0]);
                if (_this.data.addedModelPoints.length == 1) {
                    if (!_this.modelDashedLine) {
                        var screenPoint_2 = app.getViewer3D().worldToClient(_this.data.addedModelPoints[0]);
                        _this.modelDashedLine = drawDashedLine(new window.THREE.Vector3(screenPoint_2.x, screenPoint_2.y, screenPoint_2.z), new window.THREE.Vector3(e.clientX - boundingBox.x, e.clientY - boundingBox.y, screenPoint_2.z), app.getViewer3D());
                    }
                    else {
                        var endX = e.snapPoint ? e.snapPoint.worldPosition.x : e.clientX - boundingBox.x;
                        var endY = e.snapPoint ? e.snapPoint.worldPosition.y : e.clientY - boundingBox.y;
                        updateDashedLine(_this.modelDashedLine, screenPoint, new window.THREE.Vector3(endX, endY, screenPoint.z), app.getViewer3D());
                    }
                }
                if (_this.data.addedModelPoints.length == 2) {
                    resizeDashedLine();
                }
            }
        };
        // 跟随模型变换更新虚线位置
        var resizeDashedLine = function () {
            if (_this.drawingDashedLine) { //图纸中虚线
                var startPoint = app.getViewerDrawing().worldToClient(_this.data.addedDrawingPoints[0]);
                var endPoint = _this.data.addedDrawingPoints[1] && app.getViewerDrawing().worldToClient(_this.data.addedDrawingPoints[1]);
                //防止移出当前模块报错
                startPoint && endPoint && updateDashedLine(_this.drawingDashedLine, startPoint, endPoint, app.getViewerDrawing());
            }
            if (_this.modelDashedLine) { //模型中虚线
                var startPoint = app.getViewer3D().worldToClient(_this.data.addedModelPoints[0]);
                var endPoint = _this.data.addedModelPoints[1] && app.getViewer3D().worldToClient(_this.data.addedModelPoints[1]);
                //防止移出当前模块报错
                startPoint && endPoint && updateDashedLine(_this.modelDashedLine, startPoint, endPoint, app.getViewer3D());
            }
        };
        // 图纸选点点击
        pickDrawingButton.on('click', function () {
            new Message({
                parent: app.getViewerDrawing().getDomElement(),
                className: 'bfmodule-linkage-editor-warning',
                text: "<div class=\"bfmodule-linkage-editor-message-content\"><i class=\"bf-warning-fill-sm\"></i><span>\u8BF7\u5728\u56FE\u7EB8\u4E2D\u70B9\u9009\u62FE\u53D6\u4E24\u4E2A\u5BF9\u9F50\u70B9</span></div>",
                duration: 3000,
            });
            // 还原设置
            _this.data.addedDrawingPoints = [];
            app.getViewerDrawing().removeEventListener('MouseClicked', pickPoints);
            if (_this.drawingDashedLine) {
                _this.drawingDashedLine.parentElement.remove();
                _this.drawingDashedLine = null;
            }
            app.getViewerDrawing().getDomElement().addClass('crosshair');
            app.getViewerDrawing().enablePickEffect(false);
            app.getViewerDrawing().enableSnap(true);
            clearIcon(app.getViewerDrawing());
            app.getViewerDrawing().addEventListener('MouseClicked', pickPoints);
            app.getViewerDrawing().getDomElement().addEventListener('mousemove', function (e) {
                createDashedLine(e, true);
            });
            app.getViewerDrawing().getDomElement().addEventListener('mousewheel', resizeDashedLine);
            app.getViewerDrawing().addEventListener('MouseDragged', resizeDashedLine);
        });
        // 模型选点
        pickModelButton.on('click', function () {
            new Message({
                parent: app.getViewer3D().getDomElement(),
                className: 'bfmodule-linkage-editor-warning',
                text: "<div class=\"bfmodule-linkage-editor-message-content\"><i class=\"bf-warning-fill-sm\"></i><span>\u8BF7\u5728\u6A21\u578B\u4E2D\u70B9\u9009\u62FE\u53D6\u4E24\u4E2A\u5BF9\u9F50\u70B9</span></div>",
                duration: 3000,
            });
            // 还原设置
            _this.data.addedModelPoints = [];
            app.getViewer3D().removeEventListener('MouseClicked', pickPoints);
            if (_this.modelDashedLine) {
                _this.modelDashedLine.parentElement.remove();
                _this.modelDashedLine = null;
            }
            // 仅当模型选点点击时, 并且防止重复设置，导致点击多次后状态变化
            !_this.stateBeforeTop && (_this.stateBeforeTop = app.getViewer3D().getCurrentState());
            app.getViewer3D().getViewer().setHomeView(1);
            app.getViewer3D().getDomElement().addClass('crosshair');
            app.getViewer3D().enableSnap(true);
            if (_this.data.allowAxisGrids && _this.data.openAxisGrids) {
                app.getViewer3D().getModel().removeAllAxisGrids();
                _this.showAxisGridsByElevation();
                app.getViewer3D().getModel().isolateComponentsById([undefined], "MakeOthersTranslucent");
            }
            clearIcon(app.getViewer3D());
            app.getViewer3D().addEventListener('MouseClicked', pickPoints);
            app.getViewer3D().getDomElement().addEventListener('mousemove', function (e) {
                createDashedLine(e, false);
            });
            app.getViewer3D().addEventListener('CameraPositionChanged', resizeDashedLine);
        });
        // 浏览器大小变化时跟随变化
        window.addEventListener('resize', function () {
            resizeDashedLine();
            if (app.getApp2D() && app.getApp2D().UI && app.getApp2D().UI.getToolbar('MainToolbar')) {
                setTimeout(function () {
                    app.getApp2D().UI.getToolbar('MainToolbar').element.removeClass('bf-toolbar-bottom-float-right');
                }, 10);
            }
        });
        var linkageNotice = this.renderItemMap.linkageNotice;
        new Tooltip({
            hostDom: linkageNotice,
            placement: 'right',
            width: 175,
            content: '仅支持对一个视图进行关联',
        });
        var allowSection = this.renderItemMap.allowSection;
        allowSection.setValue(true);
        allowSection.onChange(function (allowSection) {
            _this.data.allowSection = allowSection;
            if (allowSection) {
                if (_this.data.isFloor) {
                    setSectionProgress(_this.data.selectedHeight);
                }
                else {
                    setSectionProgress(Number(_this.data.baseHeight));
                }
            }
            if (!allowSection && app._app3d.getViewer()._sectionPlane)
                app._app3d.getViewer()._sectionPlane.exit();
        });
        var sectionOffset = this.renderItemMap.sectionOffset;
        sectionOffset.setValue(1200);
        sectionOffset.getDomElement().attribute('type', 'number');
        sectionOffset.getDomElement().on('keypress', function (e) {
            if (e.target.value.length > 20) {
                e.preventDefault();
                return false;
            }
        });
        sectionOffset.onChange(function (value) {
            if (isNaN(value)) {
                value = _this.data.sectionOffset;
                return;
            }
            _this.data.sectionOffset = Number(value);
            if (_this.data.isFloor) {
                _this.data.allowSection && setSectionProgress(_this.data.selectedHeight);
            }
            else {
                _this.data.allowSection && setSectionProgress(_this.data.baseHeight);
            }
            if (_this.data.allowAxisGrids && _this.data.openAxisGrids) {
                app.getViewer3D().getModel().removeAllAxisGrids();
                _this.showAxisGridsByElevation();
            }
        });
        var clearEditStatus = function (exitSection) {
            app.getViewerDrawing().getDomElement().removeClass('crosshair');
            app.getViewer3D().getDomElement().removeClass('crosshair');
            exitSection && app._app3d.getViewer()._sectionPlane && app._app3d.getViewer()._sectionPlane.exit();
            clearIcon(app.getViewerDrawing());
            clearIcon(app.getViewer3D());
            if (_this.drawingDashedLine) {
                _this.drawingDashedLine.parentElement.remove();
                _this.drawingDashedLine = null;
            }
            if (_this.modelDashedLine) {
                _this.modelDashedLine.parentElement.remove();
                _this.modelDashedLine = null;
            }
        };
        var saveButton = this.renderItemMap.saveButton;
        saveButton.onClick(function () {
            // 未满足条件校验
            if (_this.data.addedDrawingPoints.length != 2) {
                pickDrawingButton.click();
                return false;
            }
            if (_this.data.addedModelPoints.length != 2) {
                pickModelButton.click();
                return false;
            }
            clearEditStatus(false);
            _this.data.linked = true;
            _this.data.viewId = app.getViewerDrawing().getCurrentViewId();
            eventBus.trigger(EditorEvent$1.InitModule, __assign({}, _this.data));
            _this.data.allowAxisGrids && _this.data.openAxisGrids && app.getViewer3D().getModel().removeAllAxisGrids();
            app._app2d.UI.getToolbar('LeftSubToolbar') && app._app2d.UI.getToolbar('LeftSubToolbar').hide();
            app.getViewerDrawing().removeEventListener(window.Glodon.Bimface.Application.WebApplicationDrawingEvent.Loaded, updateInfo);
            app.outLinkModulePage = true;
        });
        var cancelBtn = this.renderItemMap.cancelButton;
        cancelBtn.onClick(function () {
            clearEditStatus(true);
            _this.data.linked = false;
            _this.data.addedDrawingPoints = [];
            _this.data.addedModelPoints = [];
            eventBus.trigger(EditorEvent$1.InitModule);
            _this.data.allowAxisGrids && _this.data.openAxisGrids && app.getViewer3D().getModel().removeAllAxisGrids();
            if (_this.stateBeforeTop)
                app.getViewer3D().clearIsolation();
            app._app2d.UI.getToolbar('LeftSubToolbar') && app._app2d.UI.getToolbar('LeftSubToolbar').hide();
            app.getViewerDrawing().removeEventListener(window.Glodon.Bimface.Application.WebApplicationDrawingEvent.Loaded, updateInfo);
            app.outLinkModulePage = true;
        });
        eventBus.on(EditorEvent$1.LinkDrawing, function (data) {
            drawingName.getElement().innerHTML = data.name;
            drawingName.attribute('title', data.name);
            _this.data.id = data.id ? data.id : data.modelId;
            _this.data.name = data.name;
            _this.data.modelType = data.modelType;
            _this.data.viewToken = data.viewToken;
            // 楼层信息是否存在判断页面展示
            _this.floors = app.getFloors();
            var sectionHeight = 0;
            //设置剖切选项和偏移
            allowSection.setValue(data.allowSection);
            sectionOffset.setValue(data.sectionOffset);
            // 是否展示轴网
            _this.data.allowAxisGrids = app.isSupportAxisGrids();
            _this.data.openAxisGrids = true;
            allowAxisGrids.setValue(true);
            if (_this.floors && _this.floors.length > 0) {
                _this.data.isFloor = true;
                var floors_1 = [];
                _this.floors.map(function (floor) {
                    floors_1.push({
                        text: floor.name, value: floor.structElev
                    });
                });
                floorsInfo.setValues(floors_1);
                if (!data.isFloor) {
                    sectionHeight = floors_1[0].value;
                    setTimeout(function () {
                        floorsInfo.setSelectedByValue(sectionHeight);
                        _this.data.selectedHeight = sectionHeight;
                    }, 100); // 加入该延迟防止引擎渲染报错，具体错误参见https://geek.glodon.com/projects/WEB3D/repos/jsapi/pull-requests/862/overview 评论
                }
                heightType.setSelectedByValue('BasedFloor');
            }
            else {
                heightType.setSelectedByValue('BasedElevation');
                heightType.setDisabled(true);
                _this.data.isFloor = false;
                _this.data.baseHeight = sectionHeight;
                pickHeightInput.setValue(sectionHeight);
                _this.data.allowSection && setSectionProgress(sectionHeight);
            }
            // 获取模型高度
            _this.modelId = app.getModelId();
            app.getViewer3D().getModel(_this.modelId).getBoundingBox(_this.modelId, function (data) {
                _this.modelMaxHeight = app.getApp3D().modelMaxHeight = data.currentBoundingBox.max.z;
                _this.modelMinHeight = app.getApp3D().modelMinHeight = data.currentBoundingBox.min.z;
            });
            // 是否已经链接,初始化参数
            if (data.linked) {
                _this.data.linked = true;
                _this.data.heightType = data.heightType;
                heightType.setSelectedByValue(data.heightType);
                _this.data.isFloor = data.isFloor;
                _this.data.baseHeight = data.baseHeight;
                _this.data.allowSection = data.allowSection;
                _this.data.selectedHeight = data.selectedHeight;
                _this.data.sectionOffset = data.sectionOffset;
                _this.data.addedDrawingPoints = data.addedDrawingPoints;
                _this.data.addedModelPoints = data.addedModelPoints;
                //设置高度及三维剖切
                if (!data.isFloor) {
                    pickHeightInput.setValue(data.baseHeight / 1000);
                    if (_this.data.allowSection) {
                        setTimeout(function () {
                            setSectionProgress(data.baseHeight);
                        }, 300);
                    }
                    else {
                        app._app3d.getViewer()._sectionPlane && app._app3d.getViewer()._sectionPlane.exit();
                    }
                }
                else {
                    floorsInfo.setSelectedByValue(data.selectedHeight);
                    if (_this.data.allowSection) {
                        setTimeout(function () {
                            setSectionProgress(data.selectedHeight);
                        }, 300);
                    }
                    else {
                        app._app3d.getViewer()._sectionPlane && app._app3d.getViewer()._sectionPlane.exit();
                    }
                }
                // 绘制选点， 绘制虚线
                setTimeout(function () {
                    addPickTag(app.getViewer3D(), data.addedModelPoints[0], 0, '1');
                    addPickTag(app.getViewer3D(), data.addedModelPoints[1], 0, '2');
                    self.modelDashedLine = drawDashedLine(new window.THREE.Vector3(app.getViewer3D().worldToClient(_this.data.addedModelPoints[0]).x, app.getViewer3D().worldToClient(_this.data.addedModelPoints[0]).y, app.getViewer3D().worldToClient(_this.data.addedModelPoints[0]).z), new window.THREE.Vector3(app.getViewer3D().worldToClient(_this.data.addedModelPoints[1]).x, app.getViewer3D().worldToClient(_this.data.addedModelPoints[1]).y, app.getViewer3D().worldToClient(_this.data.addedModelPoints[1]).z), app.getViewer3D());
                    app.getViewer3D().addEventListener('CameraPositionChanged', resizeDashedLine);
                    // 切换图纸时，点位信息在更换home视角后更新
                    app.getViewerDrawing().addEventListener(window.Glodon.Bimface.Application.WebApplicationDrawingEvent.Loaded, function () {
                        setTimeout(function () {
                            resizeDashedLine();
                        }, 101);
                    });
                }, 310);
                // 防止被设置为none
                setTimeout(function () {
                    [].forEach.call(app.getViewer3D().getDomElement().getElementsByClassName('bf-drawable-image'), function (imgDom) {
                        imgDom.style.display = 'block';
                    });
                }, 300);
                addPickTag(app.getViewerDrawing(), data.addedDrawingPoints[0], 0, '1');
                addPickTag(app.getViewerDrawing(), data.addedDrawingPoints[1], 0, '2');
                _this.drawingDashedLine = drawDashedLine(new window.THREE.Vector3(app.getViewerDrawing().worldToClient(_this.data.addedDrawingPoints[0]).x, app.getViewerDrawing().worldToClient(_this.data.addedDrawingPoints[0]).y, 0), new window.THREE.Vector3(app.getViewerDrawing().worldToClient(_this.data.addedDrawingPoints[1]).x, app.getViewerDrawing().worldToClient(_this.data.addedDrawingPoints[1]).y, 0), app.getViewerDrawing());
                app.getViewerDrawing().getDomElement().addEventListener('mousewheel', resizeDashedLine);
                app.getViewerDrawing().addEventListener('MouseDragged', resizeDashedLine);
            }
            else {
                _this.data.linked = false;
                _this.data.addedDrawingPoints = [];
                _this.data.addedModelPoints = [];
                requestAnimationFrame(function () {
                    setModelLayout();
                });
            }
            // 根据判断条件开启轴网
            if (_this.data.allowAxisGrids && _this.data.openAxisGrids) {
                setTimeout(function () {
                    _this.showAxisGridsByElevation();
                }, 101); // 加入延迟保证在selectedHeight获取值后执行
            }
            app.getViewerDrawing().addEventListener(window.Glodon.Bimface.Application.WebApplicationDrawingEvent.Loaded, updateInfo);
            app._app2d.UI.getToolbar('LeftSubToolbar') && app._app2d.UI.getToolbar('LeftSubToolbar').show();
            delete app.outLinkModulePage;
        });
    }
    LinkModule.prototype.showAxisGridsByElevation = function () {
        var _this = this;
        var gridsElevation;
        if (this.data.isFloor) {
            gridsElevation = this.app._viewer3d.getUnit() == window.Glodon.Bimface.Common.Units.LengthUnits.Meter
                ? (this.data.selectedHeight / 1000 + Number(this.data.allowSection && this.data.sectionOffset) / 1000 - 0.0004)
                : (this.data.selectedHeight + Number(this.data.allowSection && this.data.sectionOffset) - 0.4);
        }
        else {
            gridsElevation = this.app._viewer3d.getUnit() == window.Glodon.Bimface.Common.Units.LengthUnits.Meter
                ? (this.data.baseHeight / 1000 + Number(this.data.allowSection && this.data.sectionOffset) / 1000 - 0.0004)
                : (this.data.baseHeight + Number(this.data.allowSection && this.data.sectionOffset) - 0.4);
        }
        this.app.getViewer3D().getModel().removeAllAxisGrids();
        this.app.getViewer3D().getModel().showAxisGridsByElevation('', gridsElevation, function () {
            _this.app.getViewer3D().getModel().setGridLinesColor(new window.Glodon.Web.Graphics.Color(255, 0, 0, 1));
            _this.app.getViewer3D().getModel().setGridBubblesColor(new window.Glodon.Web.Graphics.Color(255, 0, 0, 1));
        });
    };
    LinkModule.prototype.getDomElement = function () {
        return this.domElement;
    };
    return LinkModule;
}());

var ResourceSearch = /** @class */ (function (_super) {
    __extends(ResourceSearch, _super);
    function ResourceSearch() {
        var _this = this;
        var elementParam = {
            elementType: 'div',
            className: 'bfui-sceneeditor-search'
        };
        var controlParam = {
            elementParam: elementParam,
            type: 'ResourceSearch',
        };
        _this = _super.call(this, controlParam) || this;
        _this.isSearching = false;
        _this.data = {
            buttonCancelVisible: false,
        };
        _this.renderItemMap = render("\n      <Input ref=\"input\" class=\"bfui-sceneeditor-search-input\"></input>\n      <i ref=\"buttonCancel\" bf-show=\"buttonCancelVisible\" class=\"bfui-sceneeditor-search-icon gld-bf-close-sm\" @click=\"cancel\"></i>\n      <i ref=\"buttonSearch\" class=\"bfui-sceneeditor-search-icon gld-bf-search-md\" @click=\"search\"></i>\n    ", _this.getDomElement(), _this);
        var input = _this.renderItemMap.input;
        input.getDomElement().attribute('placeholder', '请输入要搜索的文件名或ID');
        input.onInput(function () {
            _this.data.buttonCancelVisible = input.getValue() !== '';
        });
        return _this;
    }
    ResourceSearch.prototype.search = function () {
        var input = this.renderItemMap.input;
        var value = input.getValue();
        value && this.searchHandler && this.searchHandler(input.getValue());
        this.isSearching = true;
    };
    ResourceSearch.prototype.cancel = function () {
        var input = this.renderItemMap.input;
        input.setValue('');
        this.cancelHandler && this.cancelHandler();
        this.data.buttonCancelVisible = false;
        this.isSearching = false;
    };
    ResourceSearch.prototype.onSearch = function (fn) {
        this.searchHandler = fn;
    };
    ResourceSearch.prototype.onCancel = function (fn) {
        this.cancelHandler = fn;
    };
    return ResourceSearch;
}(Control));

var Loading = /** @class */ (function (_super) {
    __extends(Loading, _super);
    function Loading() {
        var _this = this;
        var elementParam = {
            elementType: 'div',
            className: 'bfui-sceneeditor-loading'
        };
        var controlParam = {
            elementParam: elementParam,
            type: 'Loading',
        };
        _this = _super.call(this, controlParam) || this;
        render("\n      <div class=\"bf-loading\">\n        <div class=\"bf-loading-gif\"></div>\n      </div>\n    ", _this.getDomElement());
        return _this;
    }
    Loading.prototype.show = function () {
        _super.prototype.show.call(this);
        return this;
    };
    return Loading;
}(Control));

var suffixMap = {
    folder: 'folder-md',
};
[
    'zip',
    'dwf',
    'dwg',
    'dgn',
    'rvm',
    'osgb',
    'igms',
    'catia',
    'ifc',
    'rvt',
    'nwd',
    'skp',
    'model',
    'map',
    'terrain',
].forEach(function (name) {
    suffixMap[name] = name;
});
var setSuffixMap = function (suffixArray, name) {
    suffixArray.forEach(function (suffix) {
        suffixMap[suffix] = name;
    });
};
setSuffixMap(['dwf', 'dwfx'], 'dwf');
setSuffixMap(['nwd', 'nwc', 'nwf'], 'nwd');
setSuffixMap(['dwg', 'dxf'], 'dwg');
setSuffixMap(['rvt', 'rfa', 'rte'], 'rvt');
setSuffixMap(['catpart', 'catproduct'], 'catia');
setSuffixMap(['igms', 'gbg', 'gbq', 'gcl', 'gdq', 'ggj', 'gjg', 'gmp', 'gpb', 'gpv', 'gqi', 'gsc', 'gsh', 'gtb', 'gtj', 'gzb'], 'igms');
setSuffixMap(['sat', 'stl', '3dm', 'obj', 'dae', 'ply', 'fbx', 'stp', 'step'], 'model');
setSuffixMap(['osgb', 'shp', 'shpzip', 'osgbzip', 'kml', 'laszip'], 'osgb');
setSuffixMap(['3ds', 'max'], 'max');
var AddResource = /** @class */ (function () {
    function AddResource(app) {
        var _this = this;
        this.app = app;
        this.arrayCheckedData = [];
        this.panel = new Panel({
            title: '添加图纸',
            width: 680,
            height: 480,
            draggable: false,
            sizeFixed: true,
            position: { anchor: 'TopCenter', offset: { x: 0, y: 20 } },
            footerDisabled: true,
        });
        this.panel.onClose(function () { return _this.cancel(); });
        this.panel.addClass('bfui-sceneeditor-panel');
        var modal = new Dom().createElement({ elementType: 'div', className: 'bfui-sceneeditor-modal' });
        modal.append(this.panel.getDomElement()).hide();
        this.domElement = modal;
        var dwgDom = new Dom().createElement({ elementType: 'div', className: 'bfui-sceneeditor-resource-container' });
        var fileSearch = this.fileSearch = new ResourceSearch();
        fileSearch.onSearch(function (keyword) {
            _this.fileCrumb.hide();
            _this.fileList.addClass('filelist-search');
            if (_this.fileCrumb.getValue().length > 1) {
                _this.getResourceList({ searchKeyword: keyword, type: _this.tabs.getActiveValue(), parentId: _this.fileCrumb.getValue()[_this.fileCrumb.getValue().length - 1].id });
            }
            else {
                _this.getResourceList({ searchKeyword: keyword, type: _this.tabs.getActiveValue() });
            }
        });
        fileSearch.onCancel(function () {
            _this.fileCrumb.show();
            _this.fileList.removeClass('filelist-search');
            var param = { type: _this.tabs.getActiveValue() };
            if (_this.crumbData.length > 1) {
                param.parentId = _this.crumbData[_this.crumbData.length - 1].id;
            }
            _this.getResourceList(param);
        });
        this.fileCrumb = new Crumb().addClass('bfui-sceneeditor-resource-crumb');
        this.crumbData = [{ id: 'root', text: '全部' }];
        this.fileCrumb.setValue(this.crumbData).onSelect(function (id) {
            var data = _this.crumbData.find(function (item) { return item.id === id; });
            if (data) {
                var index = _this.crumbData.indexOf(data);
                _this.crumbData = _this.crumbData.slice(0, index + 1);
                _this.fileCrumb.setValue(_this.crumbData);
                var param = { type: _this.tabs.getActiveValue() };
                if (data.id !== 'root') {
                    param.parentId = data.id;
                }
                _this.getResourceList(param);
            }
        });
        this.fileList = new List().addClass('bfui-sceneeditor-resource-filelist');
        this.fileList.onChecked(function (checkedData) {
            _this.arrayCheckedData = checkedData.map(function (item) { return item.data; });
            _this.updateAddResourceButton();
        }).onItemClicked(function (data) {
            if (data.data.isFolder) {
                _this.getResourceList({ parentId: data.id, type: _this.tabs.getActiveValue() });
                _this.crumbData.push({ id: data.id, text: data.text });
                _this.fileCrumb.setValue(_this.crumbData);
            }
        });
        dwgDom.append(fileSearch.getDomElement()).append(this.fileCrumb.getDomElement()).append(this.fileList.getDomElement());
        var tabs = this.tabs = new Tabs({
            parent: this.panel.getDomElement(),
            options: [
                {
                    content: dwgDom,
                    label: 'dwg',
                    name: 'dwg',
                    value: 'dwg',
                }
            ]
        });
        tabs.addClass('bfui-sceneeditor-resource-tabs');
        tabs.onSelect(function (value) {
            if (value === 'dwg') {
                fileSearch.cancel();
            }
        });
        requestAnimationFrame(function () {
            tabs.setActive(0);
        });
        var buttonConfirm = this.buttonConfirm = new Button({ text: '添加' }).addClass('bfui-sceneeditor-button', 'footer-button', 'button-primary');
        buttonConfirm.onClick(function () { return _this.confirm(); });
        var buttonCancel = new Button({ text: '取消' }).addClass('bfui-sceneeditor-button', 'footer-button', 'button-default').onClick(function () { return _this.cancel(); });
        var footerRow = new Row({ justifyContent: 'End' }).addClass('bfui-sceneeditor-panel-footer');
        var info = new Dom().createElement({ elementType: 'div', className: 'footer-info' });
        footerRow.addControl(buttonConfirm).addControl(buttonCancel);
        var loading = this.loading = new Loading();
        loading.hide();
        this.panel.addControl(loading).addControl(tabs).getFooter().getDomElement().append(info);
        this.panel.addControl(loading).addControl(tabs).getFooter().addControl(footerRow);
        this.emptyListLabel = new Dom().createElement({ elementType: 'div', className: 'bfui-sceneeditor-emptylabel' });
        this.emptyListLabel.hide().html('没有找到对应内容');
        this.panel.getDomElement().append(this.emptyListLabel);
        eventBus.on(EditorEvent$1.AddResourcePanelShow, function (mode) {
            _this.show();
            _this.crumbData = [{ id: 'root', text: '全部' }];
            _this.fileCrumb.setValue(_this.crumbData);
            requestAnimationFrame(function () {
                tabs.setActive(0);
            });
            _this.getResourceList({ type: _this.tabs.getActiveValue() });
        });
    }
    AddResource.prototype.getDomElement = function () {
        return this.domElement;
    };
    AddResource.prototype.getResourceList = function (params) {
        var _this = this;
        this.arrayCheckedData = [];
        this.updateAddResourceButton();
        this.emptyListLabel.hide();
        this.loading.show();
        params || (params = {});
        this.tabs.getActiveValue() === 'dwg';
        params.type = 'dwg';
        var getIconClass = function (item) {
            var isFolder = item.isFolder, name = item.name;
            if (isFolder) {
                return 'bf-folder-md';
            }
            var suffix = item.suffix || name.slice(name.lastIndexOf('.') + 1);
            if (suffix) {
                var iconClass = suffixMap[suffix.toLowerCase()];
                if (iconClass) {
                    return "bf-" + iconClass;
                }
            }
            return 'bf-file';
        };
        this.app.getConfig().getResourceListHandler(params).then(function (data) {
            _this.loading.hide();
            var list = _this.fileList;
            var listData = data.map(function (item) {
                return {
                    id: item.id,
                    text: item.name,
                    hasCheckBox: true,
                    hasIcon: true,
                    iconType: 'svg',
                    iconClass: getIconClass(item),
                    data: item,
                };
            });
            list.setValue(listData);
            if (listData.length === 0) {
                _this.emptyListLabel.show();
            }
        }).catch(function (err) { return console.log("getResourceListHandler error: " + err); });
    };
    AddResource.prototype.show = function () {
        var _this = this;
        this.domElement.show();
        this.panel.show();
        requestAnimationFrame(function () { return _this.domElement.addClass('bfui-sceneeditor-modal-show'); });
    };
    AddResource.prototype.hide = function () {
        var _this = this;
        this.domElement.removeClass('bfui-sceneeditor-modal-show');
        var hide = function () {
            _this.domElement.hide();
            _this.getDomElement().off('transitionend', hide);
        };
        this.getDomElement().on('transitionend', hide);
    };
    AddResource.prototype.confirm = function () {
        var _this = this;
        if (this.arrayCheckedData.length === 0) {
            // this.cancel();
            // new Message({ text: '未选择任何资源', parent: this.app.getConfig().domElement });
            return;
        }
        this.arrayCheckedData.forEach(function (item) {
            item.type = _this.tabs.getActiveValue();
        });
        this.app.getConfig().getViewTokenHandler(this.arrayCheckedData).then(function (viewTokens) {
            eventBus.trigger(EditorEvent$1.AddResourceByViewToken, viewTokens.map(function (item) {
                if (!item.name) {
                    var data = _this.arrayCheckedData.filter(function (itemData) { return itemData.id === item.id; })[0];
                    if (data) {
                        item.name = data.name;
                    }
                }
                item.modelType = 'dwg';
                return item;
            }));
            _this.cancel();
        }).catch(function (err) {
            console.log("getViewTokenHandler error: " + err);
            _this.cancel();
        });
    };
    AddResource.prototype.cancel = function () {
        this.arrayCheckedData = [];
        this.hide();
    };
    AddResource.prototype.updateAddResourceButton = function () {
        this.buttonConfirm.setHTML('添加');
        this.arrayCheckedData.length === 0 ? this.buttonConfirm.addClass('button-disabled') : this.buttonConfirm.removeClass('button-disabled');
    };
    return AddResource;
}());

var LinkedFileListToolbar = /** @class */ (function () {
    function LinkedFileListToolbar(app) {
        var _this = this;
        this.app = app;
        this.domElement = new Dom().createElement({ elementType: 'div' }).css('height', '100%');
        this.data = {
            allDrawings: []
        };
        this.selectComponent = null;
        this.renderItemMap = render("\n      <Select ref=\"linkedList\" class=\"bfmodule-linkage-editor-linked-select hide\"></Select>\n    ", this.domElement, this);
        var linkedList = this.renderItemMap.linkedList;
        linkedList.content.getDomElement().addClass('bfmodule-linkage-editor-linked-select-options');
        this.setOptionList(this.data.allDrawings);
        this.selectComponent = linkedList;
        linkedList.getDomElement().addClass('hide');
        linkedList.onChange(function (data) {
            var selectedItem;
            selectedItem = _this.data.allDrawings.find(function (drawing) {
                return drawing.viewToken == data.value;
            });
            // 兼容预览传入viewMetaData没有token的情况
            if (!selectedItem) {
                selectedItem = _this.data.allDrawings.find(function (drawing) {
                    return drawing.name == data.text;
                });
            }
            eventBus.trigger(EditorEvent$1.LoadedDrawingChanged, [selectedItem]);
        });
        eventBus.on(EditorEvent$1.AddResourceByViewToken, function (data) {
            var allFiles = __spreadArrays(_this.data.allDrawings);
            data.forEach(function (file) {
                if (_this.data.allDrawings && (_this.data.allDrawings.findIndex(function (item) { return item.name == file.name; }) < 0)) {
                    allFiles.push(file);
                }
            });
            // 兼容已经链接的文件，传入的值为metadata，包含databagId
            if (data[0].databagId) {
                _this.data.allDrawings = allFiles;
            }
            else {
                // 去重
                _this.data.allDrawings = allFiles.filter(function (value, index, self) {
                    return index === self.findIndex(function (t) {
                        if (t.id) {
                            return value.id ? t.id == value.id : t.id == value.databagId;
                        }
                        else {
                            return value.id ? t.databagId == value.id : t.databagId == value.databagId;
                        }
                    });
                });
            }
            _this.setOptionList(_this.data.allDrawings);
        });
        eventBus.on(EditorEvent$1.RemoveResourceByViewToken, function (data) {
            for (var i = 0; i <= data.length - 1; i++) {
                _this.data.allDrawings.splice(data[i], 1);
            }
            _this.setOptionList(_this.data.allDrawings);
        });
        eventBus.on(EditorEvent$1.LoadedDrawingChanged, function (data) {
            if (data.length > 0 && data[0]) {
                // 判断是否为新加载图纸（包含viewToken）还是以前编辑过的图纸
                data[0].viewToken
                    ? _this.renderItemMap.linkedList.setSelectedByValue(data[0].viewToken)
                    : _this.renderItemMap.linkedList.setSelectedByValue(data[0]);
            }
        });
        // mount to the UI
        app.getApp3D().UI.linkedList = linkedList;
    }
    LinkedFileListToolbar.prototype.getDomElement = function () {
        return this.domElement;
    };
    LinkedFileListToolbar.prototype.getSelectComponent = function () {
        return this.selectComponent;
    };
    LinkedFileListToolbar.prototype.setAllDrawings = function (drawings) {
        this.data.allDrawings = drawings;
    };
    LinkedFileListToolbar.prototype.setOptionList = function (options) {
        var _this = this;
        if (options.length > 0) {
            var selectionList_1 = [];
            options.map(function (file) {
                selectionList_1.push({
                    value: file.viewToken || file,
                    text: file.name,
                });
            });
            this.renderItemMap.linkedList.setValues(selectionList_1);
            // 仅当没有选项时设置选中值
            if (!this.renderItemMap.linkedList.content.getSelected()) {
                this.renderItemMap.linkedList.setSelectedByValue(selectionList_1[0].value);
            }
            else {
                // 判断选项是否包含已经选中的值
                var exitOption = options.find(function (option) { return option.name == _this.renderItemMap.linkedList.content.getSelected().tooltip; });
                if (!exitOption) {
                    this.renderItemMap.linkedList.setSelectedByValue(selectionList_1[0].value);
                }
            }
            // 根据配置项决定是否展示
            if (this.app.getApp3D().linkConfig) {
                if (this.app.getApp3D().linkConfig.showDrawingList) {
                    var setUntil_1 = function () {
                        if (!_this.app.getViewerDrawing() || (_this.app.getViewerDrawing().loadedDrawings.length == 1 && _this.app.getViewerDrawing().loadedDrawings[0].initRemoved)) {
                            setTimeout(setUntil_1, 500);
                        }
                        else {
                            _this.renderItemMap.linkedList.getDomElement().removeClass('hide');
                        }
                    };
                    setUntil_1();
                }
                else {
                    this.renderItemMap.linkedList.getDomElement().addClass('hide');
                }
            }
        }
        else {
            this.renderItemMap.linkedList.content.clearSelection();
            this.renderItemMap.linkedList.getDomElement().addClass('hide');
        }
    };
    LinkedFileListToolbar.prototype.setTop = function () {
        var optionsDom = this.renderItemMap.linkedList.content;
        optionsDom.setTop(true);
    };
    return LinkedFileListToolbar;
}());

var InitDrawingDom = /** @class */ (function () {
    function InitDrawingDom(app) {
        this.app = app;
        this.domElement = new Dom().createElement({ elementType: 'div' }).css('height', '100%');
        this.data = {};
        this.renderItemMap = render("\n      <div class=\"bfmodule-linkage-editor-init-drawing-dom\">\n        <div class=\"bfmodule-linkage-editor-init-tip-img\"></div>\n        <div class=\"bfmodule-linkage-editor-init-drawing-text\">\n          \u8FD8\u6CA1\u6709\u56FE\u7EB8\uFF0C\u8BF7\u5148\n          <span ref=\"addResources\" class=\"bfmodule-linkage-editor-init-drawing-link\">\n            \u6DFB\u52A0\u56FE\u7EB8\n          </span>\n        </div>\n      </div>\n    ", this.domElement, this);
        this.renderItemMap.addResources.on('click', function () {
            eventBus.trigger(EditorEvent$1.AddResourcePanelShow);
        });
    }
    InitDrawingDom.prototype.getDomElement = function () {
        return this.domElement;
    };
    return InitDrawingDom;
}());

!function(l){var a,c,h,t,p,i,d='<svg><symbol id="bf-map1" viewBox="0 0 1024 1024"><path d="M64 64h896v896H64z" fill="#EEEEEE" ></path><path d="M960 64v896H64V64h896z m-64 64H128v768h768V128z" fill="#999999" ></path><path d="M112.448 388.544l800 480-32.896 54.912-800-480z" fill="#979797" ></path><path d="M514.944 82.56l58.112 26.88-384 832-58.112-26.88 384-832zM704 256a128 128 0 1 1 0 256 128 128 0 0 1 0-256z m0 64a64 64 0 1 0 0 128 64 64 0 0 0 0-128z" fill="#979797" ></path></symbol><symbol id="bf-terrain" viewBox="0 0 1024 1024"><path d="M960 871.04l-194.816-633.28a64 64 0 0 0-122.368 0L448 871.04h512z" fill="#EEEEEE" ></path><path d="M685.184 195.456a64 64 0 0 1 77.44 35.328l2.56 7.04L960 871.04H448l194.816-633.216a64 64 0 0 1 42.368-42.24zM704 256.64l-169.408 550.4h338.752L704 256.64z" fill="#999999" ></path><path d="M704 871.04L438.272 445.76a64 64 0 0 0-108.544 0L64 871.04h640z" fill="#EEEEEE" ></path><path d="M350.08 425.472a64 64 0 0 1 83.84 14.208l4.352 6.144L704 871.04H64l265.728-425.152a64 64 0 0 1 20.352-20.352z m33.92 54.272L179.392 807.04h409.152L384 479.744z" fill="#999999" ></path></symbol><symbol id="bf-file" viewBox="0 0 1024 1024"><path d="M128 64h512l256 256v640H128z" fill="#EEEEEE" ></path><path d="M640 64l256 256v640H128V64h512z m-26.56 64H192v768h640V346.56L613.44 128z" fill="#999999" ></path><path d="M640 64l256 256v64H576V64h64z m0 90.56V320h165.44L640 154.56z" fill="#979797" ></path></symbol><symbol id="bf-file-md-c" viewBox="0 0 1024 1024"><path d="M690.752 0a32 32 0 0 1 22.624 9.376l173.248 173.248A32 32 0 0 1 896 205.248V992a32 32 0 0 1-32 32H160a32 32 0 0 1-32-32V32a32 32 0 0 1 32-32h530.752z" fill="#FFFFFF" ></path><path d="M690.752 0a32 32 0 0 1 22.624 9.376l173.248 173.248A32 32 0 0 1 896 205.248V992a32 32 0 0 1-32 32H160a32 32 0 0 1-32-32V32a32 32 0 0 1 32-32h530.752zM672 32H160v960h704V224H704a32 32 0 0 1-32-32V32z" fill="#C8C8C8" ></path></symbol><symbol id="bf-folder-open-sm" viewBox="0 0 1024 1024"><path d="M384 128l64 64 482.112 4.928C945.6 196.928 960 209.088 960 224v580.928A27.52 27.52 0 0 1 931.968 832H92.032A27.52 27.52 0 0 1 64 804.928V155.072A27.52 27.52 0 0 1 92.032 128H384z m64 128l-64-64H128v576h768V256H448z" fill="#FACB66" ></path><path d="M448 256l-64-64H128v576h768V256z" fill="#FFFFFF" ></path><path d="M482.24 679.808h64v64h-64z" fill="#FFFFFF" ></path><path d="M72.512 384h878.976a64 64 0 0 1 63.488 71.936l-48 384A64 64 0 0 1 903.488 896H120.512a64 64 0 0 1-63.488-56.064l-48-384A64 64 0 0 1 72.512 384z" fill="#FACB66" ></path></symbol><symbol id="bf-folder-close-sm" viewBox="0 0 1024 1024"><path d="M384 128l64 64 482.112 4.928C945.6 196.928 960 209.088 960 224v580.928A27.52 27.52 0 0 1 931.968 832H92.032A27.52 27.52 0 0 1 64 804.928V155.072A27.52 27.52 0 0 1 92.032 128H384z m64 128l-64-64H128v576h768V256H448z" fill="#FACB66" ></path><path d="M448 256l-64-64H128v576h768V256z" fill="#FFFFFF" ></path><path d="M128 320h768q64 0 64 64v448q0 64-64 64H128q-64 0-64-64V384q0-64 64-64z" fill="#FACB66" ></path></symbol><symbol id="bf-folder-md" viewBox="0 0 1024 1024"><path d="M345.856 96L448 160h512c16.576 0 32 14.336 32 32v704c0 17.664-13.44 32-30.016 32H62.016C45.44 928 32 913.664 32 896V128c0-17.664 13.44-32 30.016-32h283.84z" fill="#FFFFFF" ></path><path d="M345.856 96L448 160h512c16.576 0 32 14.336 32 32v704c0 17.664-13.44 32-30.016 32H62.016C45.44 928 32 913.664 32 896V128c0-17.664 13.44-32 30.016-32h283.84zM448 192l-110.592-64H64v768h896V192H448z" fill="#FACB66" ></path><path d="M482.272 679.776h60v34.976h-60z" fill="#FFFFFF" ></path><path d="M64 320h896v576H64z" fill="#FACB66" ></path></symbol><symbol id="bf-model" viewBox="0 0 1024 1024"><path d="M690.752 0a32 32 0 0 1 22.624 9.376l173.248 173.248A32 32 0 0 1 896 205.248V992a32 32 0 0 1-32 32H160a32 32 0 0 1-32-32V32a32 32 0 0 1 32-32h530.752z" fill="#FFFFFF" ></path><path d="M751.968 463.488v258.656L528 851.424V592.832z m-479.968 0l224 129.344v258.592l-224-129.28z m240-157.024l223.968 129.312L512 565.088 288 435.776z" fill="#00B0DC" ></path><path d="M690.752 0a32 32 0 0 1 22.624 9.376l173.248 173.248A32 32 0 0 1 896 205.248V992a32 32 0 0 1-32 32H160a32 32 0 0 1-32-32V32a32 32 0 0 1 32-32h530.752zM672 32H160v960h704V224H704a32 32 0 0 1-32-32V32z" fill="#00B0DC" ></path></symbol><symbol id="bf-zip" viewBox="0 0 1024 1024"><path d="M864 0a32 32 0 0 1 32 32v960a32 32 0 0 1-32 32H160a32 32 0 0 1-32-32V32a32 32 0 0 1 32-32h704z" fill="#FFFFFF" ></path><path d="M864 0a32 32 0 0 1 32 32v960a32 32 0 0 1-32 32H160a32 32 0 0 1-32-32V32a32 32 0 0 1 32-32h704z m0 32H160v960h704V32z" fill="#7C00AC" ></path><path d="M448 32h64v96h-64zM512 128h64v96h-64zM448 224h64v96h-64zM512 320h64v96h-64zM448 416h64v96h-64z" fill="#C18EC9" ></path><path d="M448 512h128v128a32 32 0 0 1-32 32h-64a32 32 0 0 1-32-32v-128z" fill="#7C00AC" ></path></symbol><symbol id="bf-dwf" viewBox="0 0 1024 1024"><path d="M690.752 0a32 32 0 0 1 22.624 9.376l173.248 173.248a32 32 0 0 1 9.376 22.624V992a32 32 0 0 1-32 32H160a32 32 0 0 1-32-32V32a32 32 0 0 1 32-32h530.752z" fill="#FFFFFF" ></path><path d="M690.752 0a32 32 0 0 1 22.624 9.376l173.248 173.248a32 32 0 0 1 9.376 22.624V992a32 32 0 0 1-32 32H160a32 32 0 0 1-32-32V32a32 32 0 0 1 32-32h530.752zM672 32H160v960h704V224h-160a32 32 0 0 1-32-32V32z" fill="#0092D0" ></path><path d="M765.568 410.752l0.704 1.888 25.504 105.6a8 8 0 0 1-8.224 9.856l-1.984-0.384-22.272-7.072-5.888 11.2-5.76 10.592c-1.792 3.2-3.456 6.08-5.12 8.832l-4.896 7.744a210.816 210.816 0 0 1-5.216 7.456l-15.136 22.08-17.6 21.248a155.744 155.744 0 0 1-6.432 7.808l-7.616 8.192-10.752 10.688-11.424 11.072-6.88 6.784-22.208 17.952c-11.744 9.472-19.84 15.36-28.8 20.8l-6.944 4.8-7.232 4.608-8.192 4.8-4.672 2.56-31.328 16.64-20.8 8.736-13.44 5.408a235.776 235.776 0 0 1-27.072 8.96c-6.08 2.144-12.416 4-19.488 5.76l-16.64 3.712-26.912 5.376-9.92 0.96-26.24 2.88-8.512 0.64-7.232 0.256h-3.456l-3.52-0.128-27.648 0.064-16.768-1.536a1125.792 1125.792 0 0 1-7.936-0.8 187.648 187.648 0 0 1-14.88-1.344l-10.304-1.6-37.952-7.456-42.88-13.152a8 8 0 0 1-5.6-7.584l0.256-2.048 1.184-4.672a8 8 0 0 1 9.472-5.792l27.712 6.24 13.792 3.072 22.336 2.688c12.864 1.6 21.568 2.432 30.56 2.528h5.44l23.104 0.384 17.664-1.44 14.272-0.928 6.24-0.704c1.12-0.128 2.272-0.32 3.52-0.512l8.416-1.44 22.624-4.256 5.376-0.832 5.696-1.6 31.04-7.36c4.224-1.024 8.064-1.92 11.552-2.848l9.6-2.624c5.76-1.664 10.464-3.296 14.4-5.024l3.68-1.28 7.232-2.912 7.84-3.488 14.56-7.104 19.328-9.664 23.936-15.232 8.48-5.632 7.104-5.024 6.4-4.928 3.168-2.56 3.456-2.56 6.912-5.44a247.04 247.04 0 0 0 7.424-6.528l22.144-20.8a42.24 42.24 0 0 1 1.44-1.28l5.088-5.952 13.088-14.784a161.28 161.28 0 0 0 15.488-20.032l13.856-19.296 11.616-20.224c3.456-5.44 6.496-11.072 9.536-17.536l5.696-12.64-22.272-5.248a8 8 0 0 1-5.056-11.904l1.28-1.6 77.216-76.032a8 8 0 0 1 12.672 1.952z" fill="#FFE600" ></path><path d="M564 249.376l1.536 1.376 3.2 3.616a8 8 0 0 1 0.704 9.6l-1.376 1.6-30.08 27.52-1.28 1.152-24.64 27.808-1.184 1.408a199.68 199.68 0 0 0-8.128 10.4l-3.68 5.28-13.536 18.752-10.08 17.216-3.104 5.184a125.696 125.696 0 0 0-7.552 13.44l-4.224 8.96-9.6 21.344-1.856 3.712-2.24 6.496-14.496 36.832-3.872 10.688c-2.24 6.496-3.776 11.84-4.704 16.512a168.512 168.512 0 0 0-2.08 8l-1.824 8.64a282.656 282.656 0 0 0-0.832 4.8l-3.2 21.504-1.76 12.128a161.088 161.088 0 0 1-0.32 2.016l-1.344 33.536a350.72 350.72 0 0 0-0.096 4.928l0.064 8.736 0.16 4.032 0.576 8.096 0.256 8 0.288 4.064 0.96 8.992 1.504 11.008 2.976 19.712 0.352 3.2 8.48 33.984 2.144 7.328 2.24 6.4 1.28 3.104 8 22.464 9.888 21.056c1.728 4.096 3.648 8.064 5.824 12.16l4.832 8.704 6.048 10.08 20.288-13.056a8 8 0 0 1 11.68 3.52l0.576 2.016 15.296 102.592a8 8 0 0 1-8.896 9.12l-1.92-0.48-101.152-39.424a8 8 0 0 1-3.52-12.224l1.568-1.6 18.496-14.176-8.992-17.568a265.568 265.568 0 0 1-11.136-24.896l-2.048-5.792-9.472-25.024-7.328-26.624-2.08-7.072a157.376 157.376 0 0 1-0.928-3.648l-1.728-7.936-1.728-9.472-5.44-32.608-2.624-36.544-0.416-10.112a209.216 209.216 0 0 1-0.064-4.512l0.192-8.576 0.256-4.288a217.408 217.408 0 0 1 0.288-14.656l0.8-11.296 0.704-7.04 4.16-34.016 7.36-33.44 2.464-9.92 1.216-4.416 2.432-8.032c0.832-2.592 1.728-5.12 2.688-7.712l1.312-4.544 2.976-9.088 1.728-4.704 4.16-10.432 13.664-31.456 17.536-31.808 2.592-4.48 4.896-7.808c0.8-1.216 1.6-2.368 2.432-3.52l2.464-3.328 15.744-22.72 16.032-19.04c3.68-4.672 7.648-9.216 11.936-13.76l4.48-4.704 4.8-4.736 9.792-9.28 13.6-13.184 35.232-27.776a8 8 0 0 1 9.408-0.32z" fill="#DC232D" ></path><path d="M343.04 368.32l0.512 1.92 2.944 23.136 5.472 0.32c11.936 0.32 23.872 1.28 35.648 2.944l11.744 1.856 26.464 4.48 26.688 7.04c3.04 0.704 6.176 1.504 9.44 2.464l8.224 2.56 5.984 2.112 34.848 13.184 24 11.744c13.952 6.816 23.744 12.192 32.64 18.24 5.6 3.136 10.624 6.272 16.256 10.112l8.96 6.368 5.12 3.744 24.512 18.656 22.592 20.8 8.32 7.968 6.912 7.072c3.2 3.424 6.08 6.72 8.896 10.144 6.848 6.944 12.896 14.176 20.416 24.16l19.648 26.752 5.6 9.6 11.84 19.488c3.744 6.4 6.752 11.968 9.184 17.216l2.272 5.12 11.712 25.12 8.416 23.584c5.28 14.08 9.472 28.576 12.416 42.976l4.608 18.4 2.24 15.456 3.968 28.992a8 8 0 0 1-4.448 8.32l-1.952 0.64-4.8 0.896a8 8 0 0 1-9.312-6.144l-5.92-27.712-1.568-6.72-1.44-7.104-10.88-33.12-2.88-8.032a168.32 168.32 0 0 0-1.44-3.552l-3.008-6.88-1.696-3.584-9.376-21.184-10.464-18.56-2.24-4.032a143.2 143.2 0 0 0-9.76-16.288l-16.192-22.72-3.328-4.96-4.8-5.6-23.008-29.216c-8.352-10.4-14.72-17.6-20.416-22.592l-5.44-5.44-5.952-5.44-7.04-5.952-25.824-20.608-26.144-16.768-4.608-2.816-8.384-4.864a189.984 189.984 0 0 0-11.84-6.016 182.08 182.08 0 0 0-19.392-9.504l-10.24-4.224-18.816-7.488a94.56 94.56 0 0 1-2.144-0.96l-10.944-3.104-20.608-6.144-5.152-1.408-4.608-1.056-5.952-1.12-3.808-0.576-23.424-4.416-23.168-2.016-8.32-0.864-8.512-0.416a244.672 244.672 0 0 0-4.8-0.032l-12.96 0.16 2.368 18.432a8 8 0 0 1-8.64 8.992l-1.888-0.416-98.336-33.472a8 8 0 0 1-3.84-12.384l1.44-1.44 85.024-67.52a8 8 0 0 1 12.416 3.296z" fill="#F9AE17" ></path></symbol><symbol id="bf-dwg" viewBox="0 0 1024 1024"><path d="M690.752 0a32 32 0 0 1 22.624 9.376l173.248 173.248a32 32 0 0 1 9.376 22.624V992a32 32 0 0 1-32 32H160a32 32 0 0 1-32-32V32a32 32 0 0 1 32-32h530.752z" fill="#FFFFFF" ></path><path d="M416 32l-0.032 512H512v96h352v32H512v96h-96v224h-32v-224H288v-96H160v-32h128v-96h95.968L384 32h32z m32 576h-96v96h96v-96z" fill="#3D69B0" ></path><path d="M690.752 0a32 32 0 0 1 22.624 9.376l173.248 173.248a32 32 0 0 1 9.376 22.624V992a32 32 0 0 1-32 32H160a32 32 0 0 1-32-32V32a32 32 0 0 1 32-32h530.752zM672 32H160v960h704V224h-160a32 32 0 0 1-32-32V32z" fill="#3D69B0" ></path></symbol><symbol id="bf-dgn" viewBox="0 0 1024 1024"><path d="M690.752 0a32 32 0 0 1 22.624 9.376l173.248 173.248a32 32 0 0 1 9.376 22.624V992a32 32 0 0 1-32 32H160a32 32 0 0 1-32-32V32a32 32 0 0 1 32-32h530.752z" fill="#FFFFFF" ></path><path d="M697.472 738.976c-5.76 23.456-91.488 50.112-94.048-26.112v-43.2c-88.896 104.864-136.576 117.536-206.88 74.848l-39.84 151.52H269.216l143.232-576h87.552L412.48 683.84c36.64 43.712 106.56 27.808 191.04-60.608L667.2 320h79.552l-73.824 378.944c0.896 14.688 14.688 16.256 34.144 5.664" fill="#49BDCA" ></path><path d="M690.752 0a32 32 0 0 1 22.624 9.376l173.248 173.248a32 32 0 0 1 9.376 22.624V992a32 32 0 0 1-32 32H160a32 32 0 0 1-32-32V32a32 32 0 0 1 32-32h530.752zM672 32H160v960h704V224h-160a32 32 0 0 1-32-32V32z" fill="#49BDCA" ></path></symbol><symbol id="bf-rvm" viewBox="0 0 1024 1024"><path d="M690.752 0a32 32 0 0 1 22.624 9.376l173.248 173.248a32 32 0 0 1 9.376 22.624V992a32 32 0 0 1-32 32H160a32 32 0 0 1-32-32V32a32 32 0 0 1 32-32h530.752z" fill="#FFFFFF" ></path><path d="M468.288 638.4l64.416-149.056a6.112 6.112 0 0 1 11.2 0l65.184 149.088h-140.8z m306.432 185.184L543.904 291.872a6.144 6.144 0 0 0-11.2 0L383.36 638.4H229.44a5.44 5.44 0 0 0-4.992 7.584l28.736 66.464c0.768 1.792 2.56 2.944 4.48 2.944h92.48l-46.528 108.096a6.144 6.144 0 0 0 5.568 8.736h71.36a6.112 6.112 0 0 0 5.6-3.648l48.864-113.184h207.68l49.472 113.12c0.96 2.208 3.2 3.648 5.6 3.648h71.296a6.144 6.144 0 0 0 5.6-8.64" fill="#3D1152" ></path><path d="M690.752 0a32 32 0 0 1 22.624 9.376l173.248 173.248a32 32 0 0 1 9.376 22.624V992a32 32 0 0 1-32 32H160a32 32 0 0 1-32-32V32a32 32 0 0 1 32-32h530.752zM672 32H160v960h704V224h-160a32 32 0 0 1-32-32V32z" fill="#3D1152" ></path></symbol><symbol id="bf-osgb" viewBox="0 0 1024 1024"><path d="M690.752 0a32 32 0 0 1 22.624 9.376l173.248 173.248a32 32 0 0 1 9.376 22.624V992a32 32 0 0 1-32 32H160a32 32 0 0 1-32-32V32a32 32 0 0 1 32-32h530.752z" fill="#FFFFFF" ></path><path d="M512 288a288 288 0 1 1 0 576 288 288 0 0 1 0-576z m0 32a256 256 0 1 0 0 512 256 256 0 0 0 0-512z" fill="#3A8EC9" ></path><path d="M512 288c88.352 0 160 128.96 160 288s-71.648 288-160 288-160-128.96-160-288 71.648-288 160-288z m0 32c-65.632 0-128 112.256-128 256s62.368 256 128 256 128-112.256 128-256-62.368-256-128-256z" fill="#3A8EC9" ></path><path d="M735.488 394.336c6.656 8.192 12.896 16.768 18.624 25.664-51.264 39.712-140.544 66.016-242.112 66.016-101.568 0-190.848-26.304-242.144-66.016a288.96 288.96 0 0 1 18.656-25.664c43.392 33.92 126.272 59.68 223.488 59.68 95.776 0 177.6-24.96 221.568-58.176l1.92-1.504zM289.184 744.096a288.96 288.96 0 0 1-18.656-25.632c51.296-39.744 140.576-66.016 242.144-66.016 101.568 0 190.848 26.272 242.144 65.984a288.96 288.96 0 0 1-18.656 25.664c-43.392-33.92-126.272-59.648-223.488-59.648-95.776 0-177.6 24.96-221.568 58.144l-1.92 1.504z" fill="#3A8EC9" ></path><path d="M528 290.016v571.968h-32V290.016z" fill="#3A8EC9" ></path><path d="M784 560v32h-544v-32z" fill="#3A8EC9" ></path><path d="M690.752 0a32 32 0 0 1 22.624 9.376l173.248 173.248a32 32 0 0 1 9.376 22.624V992a32 32 0 0 1-32 32H160a32 32 0 0 1-32-32V32a32 32 0 0 1 32-32h530.752zM672 32H160v960h704V224h-160a32 32 0 0 1-32-32V32z" fill="#3A8EC9" ></path></symbol><symbol id="bf-igms" viewBox="0 0 1024 1024"><path d="M690.752 0a32 32 0 0 1 22.624 9.376l173.248 173.248a32 32 0 0 1 9.376 22.624V992a32 32 0 0 1-32 32H160a32 32 0 0 1-32-32V32a32 32 0 0 1 32-32h530.752z" fill="#FFFFFF" ></path><path d="M491.712 352c-121.856 0-198.432 37.664-225.76 154.784l-21.312 92.224c-26.56 114.656 65.216 164.352 155.392 164.352h288.736c20.16 0 37.632-13.824 42.24-33.472l34.816-148.512c6.4-27.232-14.272-53.28-42.24-53.28h-73.856v0.192H556.48l-11.744 22.144a32.544 32.544 0 0 0 28.768 47.744h59.52l-21.056 90.848h-117.12c-83.04 0-107.584-26.56-94.848-81.152l23.488-102.624c11.776-49.952 54.432-78.304 146.432-78.304h194.272c20.16 0 37.664-13.92 42.24-33.6l9.568-41.184L491.712 352z" fill="#0082DC" ></path><path d="M690.752 0a32 32 0 0 1 22.624 9.376l173.248 173.248a32 32 0 0 1 9.376 22.624V992a32 32 0 0 1-32 32H160a32 32 0 0 1-32-32V32a32 32 0 0 1 32-32h530.752zM672 32H160v960h704V224h-160a32 32 0 0 1-32-32V32z" fill="#0082DC" ></path></symbol><symbol id="bf-catia" viewBox="0 0 1024 1024"><path d="M690.752 0a32 32 0 0 1 22.624 9.376l173.248 173.248a32 32 0 0 1 9.376 22.624V992a32 32 0 0 1-32 32H160a32 32 0 0 1-32-32V32a32 32 0 0 1 32-32h530.752z" fill="#FFFFFF" ></path><path d="M690.752 0a32 32 0 0 1 22.624 9.376l173.248 173.248a32 32 0 0 1 9.376 22.624V992a32 32 0 0 1-32 32H160a32 32 0 0 1-32-32V32a32 32 0 0 1 32-32h530.752zM672 32H160v960h704V224h-160a32 32 0 0 1-32-32V32z" fill="#005386" ></path><path d="M508.992 288.32c46.656-2.24 101.888 7.68 112.16 40.64 16.864 53.952-68.576 132.608-143.04 171.232-18.24 9.472-29.312 12.064-33.92 11.424-4.416-0.608-5.088-4.992-3.648-8 2.656-5.696 14.656-17.248 30.72-29.824 83.84-65.44 111.072-113.472 95.264-132.928-10.176-12.48-52.64-21.12-96.768-21.12-11.968 0-43.456 2.752-46.688-8.32-2.624-9.088 39.616-20.928 85.92-23.136M410.304 535.68c40-2.336 88.64 4.672 115.904 25.536 13.568 10.368 23.104 29.472 17.6 51.36-15.776 62.144-74.56 137.28-205.216 195.04-41.536 18.368-74.24 25.408-81.056 20.48-7.36-5.312 14.048-46.304 20.48-59.904 20.864-44.064 46.912-86.976 72.32-126.272 9.376-14.432 23.808-39.52 36.896-37.504 11.712 1.792-2.016 28.448-11.04 45.376-15.136 28.448-56.896 107.424-43.136 112.128 30.464 10.464 173.312-98.24 152.256-158.752-9.76-28.096-71.808-30.752-110.848-30.752-17.792 0-56.576 4.16-58.752-9.408-2.304-12.576 58.976-25.216 94.592-27.328" fill="#005386" ></path><path d="M719.296 469.12c24 4.352 43.584 7.712 48.064 17.856 5.056 11.488-21.12 12.16-34.272 12.48-63.168 1.184-101.28 2.432-103.168 23.904-2.496 27.616 26.976 55.456 56.096 94.432 26.56 35.616 56.8 70.72 48.096 103.52-10.88 40.8-60.896 49.44-108.512 49.44a581.696 581.696 0 0 1-107.04-10.88c-22.624-4.48-19.264-15.648-13.216-19.232 6.72-4.032 45.184-4.128 71.904-5.696 22.016-1.28 85.888-1.28 95.84-17.632 12.224-20.032-17.696-54.336-45.12-90.24-34.272-44.8-71.936-91.072-52-124.832 25.76-43.712 101.184-40.736 143.328-33.088" fill="#005386" ></path></symbol><symbol id="bf-ifc" viewBox="0 0 1024 1024"><path d="M690.752 0a32 32 0 0 1 22.624 9.376l173.248 173.248a32 32 0 0 1 9.376 22.624V992a32 32 0 0 1-32 32H160a32 32 0 0 1-32-32V32a32 32 0 0 1 32-32h530.752z" fill="#FFFFFF" ></path><path d="M568.384 611.488l138.56-138.56c-38.464-38.4-76.736-76.704-115.104-115.072l-35.328 35.84-45.536-45.568c0.16-0.192 0.992-1.152 1.92-2.048 13.792-13.856 27.648-27.712 41.504-41.536 21.984-21.952 54.208-22.08 76.224-0.16a73285.12 73285.12 0 0 1 130.112 130.112c21.632 21.696 21.504 54.08-0.064 75.648l-109.504 109.504a52.48 52.48 0 0 1-73.952 0.288c-2.816-2.752-5.696-5.44-8.832-8.448" fill="#DD0224" ></path><path d="M455.744 509.28l-138.4 138.368 114.816 114.816 35.968-36.032 45.536 45.536c-0.512 0.512-1.344 1.472-2.24 2.368-14.304 14.304-28.64 28.576-42.88 42.944a50.24 50.24 0 0 1-36.384 15.168 48.864 48.864 0 0 1-35.936-14.272c-44.832-44.704-89.6-89.44-134.304-134.272-19.424-19.488-18.912-52.576 1.12-72.736 36.576-36.8 73.344-73.408 110.016-110.08a51.52 51.52 0 0 1 35.488-15.584c13.696-0.48 26.048 3.968 36.416 13.12 3.744 3.296 7.104 7.008 10.784 10.656" fill="#AB007C" ></path><path d="M563.008 504.064l-138.56-138.592-114.784 114.784 36.032 36-45.312 45.344c0.192 0.128-0.192-0.032-0.448-0.32-15.68-15.712-31.776-31.04-46.848-47.296-18.208-19.616-17.216-51.232 1.728-70.272 44.288-44.512 88.704-88.896 133.184-133.216a52.032 52.032 0 0 1 72.448 0 28848.32 28848.32 0 0 1 111.776 111.68c19.968 20.128 19.392 52.384-1.024 73.728-2.432 2.56-5.024 4.992-8.192 8.16" fill="#004D95" ></path><path d="M679.52 603.712l44.352-44.384c0.8 0.736 1.856 1.664 2.848 2.656 13.76 13.76 27.52 27.488 41.216 41.248 21.376 21.408 21.44 53.344 0.096 74.688l-130.72 130.688c-21.184 21.216-53.12 21.184-74.336-0.032l-109.792-109.792a51.456 51.456 0 0 1 0.032-73.28c2.56-2.56 5.472-6.976 8.032-6.848 2.944 0.16 5.824 4.288 8.48 6.944l127.104 127.04c1.088 1.12 2.272 2.144 3.68 3.424 1.408-1.28 2.688-2.336 3.872-3.52 36.16-36.096 72.256-72.288 108.48-108.32 2.848-2.816 2.848-4.416 0-7.168-10.272-9.984-20.32-20.192-30.432-30.336-1.024-0.992-1.984-2.048-2.912-3.008" fill="#0093A8" ></path><path d="M357.6 619.872l103.712-103.712 44.704 44.8c-0.192 0.192-0.768 0.896-1.408 1.536-21.056 21.056-42.144 42.112-63.168 63.2-10.432 10.496-22.848 16.64-37.696 16.864a51.552 51.552 0 0 1-37.888-15.264c-2.72-2.624-5.632-5.056-8.256-7.424" fill="#004D95" ></path><path d="M452.896 405.888l103.616 103.616-45.664 45.664c-0.32-0.64-0.704-2.24-1.696-3.2-21.024-21.12-42.112-42.176-63.168-63.264-21.664-21.696-21.6-54.144 0.128-75.904l6.784-6.912" fill="#DD0224" ></path><path d="M512.416 566.08l1.344 1.152c22.08 22.112 44.288 44.096 66.208 66.368a51.84 51.84 0 0 1-0.704 72.896c-2.656 2.688-5.248 5.44-7.648 7.904l-103.776-103.808 44.576-44.512" fill="#AB007C" ></path><path d="M665.92 500.512l-103.04 102.976-44.576-44.576c1.12-0.736 2.912-1.504 4.192-2.784 21.056-20.928 42.016-41.92 63.008-62.912a51.744 51.744 0 0 1 71.84-0.608c2.912 2.816 5.92 5.472 8.576 7.904" fill="#0093A8" ></path><path d="M690.752 0a32 32 0 0 1 22.624 9.376l173.248 173.248a32 32 0 0 1 9.376 22.624V992a32 32 0 0 1-32 32H160a32 32 0 0 1-32-32V32a32 32 0 0 1 32-32h530.752zM672 32H160v960h704V224h-160a32 32 0 0 1-32-32V32z" fill="#004D95" ></path></symbol><symbol id="bf-rvt" viewBox="0 0 1024 1024"><path d="M690.752 0a32 32 0 0 1 22.624 9.376l173.248 173.248a32 32 0 0 1 9.376 22.624V992a32 32 0 0 1-32 32H160a32 32 0 0 1-32-32V32a32 32 0 0 1 32-32h530.752z" fill="#FFFFFF" ></path><path d="M765.12 841.824l-0.736-0.544c-0.224-0.192-0.512-0.32-0.768-0.576-0.576-0.448-1.216-0.832-1.792-1.312l-2.048-1.472a734.976 734.976 0 0 1-22.624-16.224l-0.96-0.736c-2.624-1.888-5.248-3.84-7.904-5.824l-0.096-0.064-0.512-0.384a20.864 20.864 0 0 0-1.28-0.928l-7.168-5.44c-15.936-12.288-31.552-24.96-46.848-38.016-30.4-26.24-60.256-53.312-87.232-83.04-13.056-15.104-25.344-31.264-39.456-45.76-14.048-14.56-29.44-28.16-47.36-38.016a137.92 137.92 0 0 0-28.608-10.976 325.28 325.28 0 0 0-29.6-6.72c-12.096-2.24-24.256-4-36.48-5.44a606.848 606.848 0 0 0 61.312-10.72c27.904-6.496 55.488-15.648 80.768-29.632 12.576-7.04 24.544-15.616 34.24-26.624a80.064 80.064 0 0 0 19.104-39.04c2.56-14.464 1.6-29.12-0.48-43.328a154.592 154.592 0 0 0-12.672-41.408c-6.272-13.024-14.816-25.28-26.368-34.4-3.2-2.56-6.624-4.928-10.112-7.04a115.744 115.744 0 0 0-28.64-12.064 169.376 169.376 0 0 0-42.72-5.76c-14.4-0.224-28.64 0.608-42.976 2.4-7.104 1.088-14.304 2.88-20.704 6.592-6.4 3.648-11.584 9.536-14.4 16.256a66.528 66.528 0 0 0-4.544 21.12c-0.64 7.2-1.056 14.304-1.184 21.44-0.224 14.336 0.736 28.704 3.424 42.752 1.376 7.008 2.912 14.08 5.888 20.576a43.008 43.008 0 0 0 10.88 15.104l-0.736-0.64a41.6 41.6 0 0 1-9.28-13.472c-0.224-0.384-0.416-0.736-0.576-1.12l-0.032-0.16a94.592 94.592 0 0 1-5.44-20.448 207.168 207.168 0 0 1-2.688-42.528c0.192-7.104 0.768-14.208 1.44-21.28 0.736-7.04 1.92-14.144 4.736-20.48a31.616 31.616 0 0 1 13.76-14.976c5.952-3.456 12.896-5.184 19.84-6.08 28.064-2.976 57.152-3.84 84.32 4.032 5.856 1.696 11.616 3.744 17.12 6.336 7.232 3.264 14.112 7.392 20.256 12.416 10.88 8.768 18.88 20.608 24.96 33.216 5.952 12.704 9.952 26.368 12.192 40.288 2.048 13.888 2.944 28.16 0.384 41.824-0.64 3.584-1.536 7.072-2.72 10.432 0.064 0.064 0 0.096 0 0.096a75.264 75.264 0 0 1-15.36 26.368l-1.632 1.696c-8.96 9.632-19.936 17.408-31.552 23.808-24.768 13.664-52 22.528-79.552 28.992-27.328 6.24-55.136 11.008-83.072 13.056 0.192 0.192 0.416 0.32 0.608 0.544l1.024 0.864-0.096 0.736-0.064 0.384v0.16l-0.256 2.176c19.2 1.536 38.4 3.904 57.28 7.264 9.856 1.792 19.648 3.904 29.28 6.528 9.568 2.72 19.104 5.856 27.68 10.528 17.376 9.6 32.64 22.784 46.656 37.024 14.08 14.272 26.4 30.048 39.744 45.28 11.264 12.352 23.136 24 35.296 35.424l0.416 0.448 2.112 2.144 1.408 1.408 2.528 2.432c3.648 3.456 10.272 9.344 18.08 16.128l2.336 2.048 2.432 2.08c4.096 3.52 8.32 7.168 12.576 10.72l2.528 2.208 2.464 2.08 2.496 2.112 3.744 3.168 3.36 2.752 2.4 1.952 14.464 11.616c0.928 0.704 1.92 1.44 2.816 2.24l0.48 0.384c6.016 4.672 12.032 9.28 18.144 13.824l1.28 0.96c1.504 1.248 3.04 2.368 4.608 3.52 15.648 11.584 31.616 22.656 47.68 33.6l-1.216-0.864z" fill="#3DBBD8" ></path><path d="M768 843.872h-149.504c-11.04 0-21.376-5.664-27.328-15.008l-37.76-59.2-7.36-11.296a347.296 347.296 0 0 1-3.616-5.408l-3.36-5.056v-0.032a854.08 854.08 0 0 0-30.496-42.336c-26.944-34.784-46.336-52.48-56.64-56.704-0.16-0.096-0.32-0.192-0.448-0.192l-0.704-0.32h-0.128c-18.56-6.368-18.144 5.632-19.936 22.272l-0.032 0.704c-0.32 2.976-0.544 8.64-0.704 16.224a86.656 86.656 0 0 0-0.096 3.584l-0.096 3.36-0.096 5.024-0.032 3.072a7539.808 7539.808 0 0 0-0.64 73.696c-0.096 28.32-0.096 49.408-0.096 49.408l-80.032 28.192c6.176-45.504 11.776-91.008 17.344-136.544l6.944-58.432v-0.224l1.12-9.664 7.744-66.176c19.2 1.536 38.4 3.904 57.28 7.264 9.856 1.792 19.648 3.904 29.28 6.528 9.568 2.72 19.104 5.856 27.68 10.528 17.376 9.6 32.64 22.784 46.656 37.024 14.08 14.272 26.4 30.048 39.744 45.312 11.264 12.32 23.168 24 35.296 35.392l0.416 0.448 2.112 2.144 1.408 1.408 2.528 2.432c3.648 3.456 10.272 9.344 18.08 16.128l2.336 2.048 2.432 2.08c4.096 3.52 8.32 7.168 12.576 10.72l2.528 2.208 2.464 2.08 2.496 2.112 3.744 3.168 3.36 2.752 2.4 1.952c3.552 3.04 6.816 5.664 9.504 7.84a162.624 162.624 0 0 0 4.16 3.328l0.064 0.032c0.256 0.096 0.48 0.256 0.736 0.416 0.928 0.704 1.92 1.44 2.816 2.24l0.512 0.384c5.984 4.672 12.032 9.28 18.112 13.824l1.28 0.96c1.504 1.248 3.04 2.368 4.608 3.52 15.648 11.584 31.616 22.656 47.68 33.6 0.064 0 0.064 0.064 0.096 0.096 0.512 0.352 0.96 0.704 1.472 0.96l0.032 0.064 0.064 0.064z" fill="#2390AD" ></path><path d="M768 843.872h-149.504c-11.04 0-21.376-5.664-27.328-15.008l-37.76-59.2-7.36-11.296a347.296 347.296 0 0 1-3.616-5.408 196.064 196.064 0 0 0-3.584-5.184 0.384 0.384 0 0 0 0.224 0.096v0.032l0.96 0.64 0.064 0.032c65.696 43.808 121.12 25.44 150.464 39.136l0.032 0.032c0.256 0.096 0.48 0.256 0.736 0.416 0.928 0.704 1.92 1.44 2.816 2.24l0.512 0.384c5.984 4.672 12.032 9.28 18.112 13.824l1.28 0.96c1.504 1.248 3.04 2.368 4.608 3.52 15.648 11.584 31.616 22.656 47.68 33.6 0.064 0 0.064 0.064 0.096 0.096 0.512 0.352 0.96 0.704 1.472 0.96l0.032 0.064 0.064 0.064z" fill="#3AB9DB" ></path><path d="M609.28 350.752c-18.784 7.968-44.224 2.4-59.84-2.56a115.744 115.744 0 0 0-28.64-12.096 169.376 169.376 0 0 0-42.72-5.76c-14.4-0.224-28.64 0.608-42.976 2.4-7.104 1.088-14.304 2.88-20.704 6.592-6.4 3.648-11.584 9.536-14.4 16.256a66.528 66.528 0 0 0-4.544 21.12c-0.64 7.2-1.056 14.304-1.152 21.44-0.256 14.336 0.704 28.704 3.392 42.752 1.376 7.008 2.912 14.08 5.888 20.576a43.008 43.008 0 0 0 11.104 15.424c1.76 1.792 3.68 3.456 5.76 5.056 13.408 10.176 30.08 14.752 46.656 15.776 28.224 1.664 61.472-2.624 81.344-25.088 3.904-4.384 6.688-9.152 8.832-14.208 0.672-1.024 11.04-17.12 19.68-16.288 13.92 1.472 17.536 21.056 15.04 41.792 0.064 0.064 0 0.096 0 0.096a75.264 75.264 0 0 1-15.36 26.368l-1.632 1.696c-1.504 0.928-3.008 1.76-4.48 2.56a140.704 140.704 0 0 1-4.448 2.496 181.888 181.888 0 0 1-10.528 5.504c-3.104 1.6-6.176 3.072-9.248 4.48-1.536 0.704-3.104 1.44-4.672 2.048l-1.12 0.512-3.296 1.408-5.216 2.08a139.52 139.52 0 0 1-6.688 2.432c-1.696 0.64-3.392 1.216-5.12 1.76-0.864 0.32-1.792 0.576-2.624 0.896l-5.152 1.536a204.288 204.288 0 0 1-52.8 8.32 181.76 181.76 0 0 1-59.488-8.64c-34.336-10.112-59.36-39.264-76.224-67.2a287.36 287.36 0 0 1-23.712-49.824c0-0.032-0.064-0.032-0.064-0.096a588.16 588.16 0 0 1-5.248-29.12 605.984 605.984 0 0 1-5.184-41.984l-0.032-0.64c0-0.256 0-0.544-0.064-0.768-0.416-5.376-1.12-18.24-1.12-18.24-0.192-2.656-0.32-5.344-0.48-8v-65.28c0.64 0 1.312-0.064 1.92-0.128l2.368-0.128 2.08-0.096c1.088-0.064 2.208-0.16 3.264-0.16l2.24-0.096a891.104 891.104 0 0 1 6.624-0.32 1133.76 1133.76 0 0 1 80.384-1.184c12.096 0.224 24.16 0.384 36.192 0.672 6.72 0.192 13.44 0.384 20.16 0.672 45.504 2.016 92.992 6.944 134.592 26.88 0 0 31.296 27.936 38.016 48.192l0.032 0.064c2.56 7.872 1.504 14.592-6.624 18.048" fill="#0070B7" ></path><path d="M767.904 843.776a25.376 25.376 0 0 1-1.472-0.96c-0.032-0.064-0.032-0.128-0.096-0.128l-1.216-0.864-0.736-0.544c-0.224-0.192-0.512-0.32-0.768-0.576-0.576-0.448-1.216-0.832-1.792-1.312l-2.048-1.472a734.976 734.976 0 0 1-22.624-16.224l-0.96-0.736c-2.624-1.888-5.248-3.84-7.904-5.824l-0.096-0.064-0.512-0.384a20.864 20.864 0 0 0-1.28-0.928l-7.168-5.44c-15.936-12.288-31.552-24.96-46.848-38.016-30.4-26.24-60.256-53.312-87.232-83.04-13.056-15.104-25.344-31.264-39.456-45.76-14.048-14.56-29.44-28.16-47.36-38.016a137.92 137.92 0 0 0-28.608-10.976 325.28 325.28 0 0 0-29.6-6.72c-12.096-2.24-24.256-4-36.48-5.44a606.848 606.848 0 0 0 61.312-10.72c27.904-6.496 55.488-15.648 80.768-29.632 12.576-7.04 24.544-15.616 34.24-26.624 7.808-8.672 13.696-19.168 17.056-30.368 7.936-12.48 20.864-30.4 36.864-43.456 10.144-8.256 16.416-4.512 20.32 3.424v0.064c6.272 12.736 6.4 36.288 6.4 38.56a210.368 210.368 0 0 1-22.08 55.136c-5.76 9.92-11.776 19.648-18.176 29.12-5.696 8.48-11.232 15.072-13.248 25.28-3.296 16.832 3.936 26.88 11.52 40.416 13.472 24 51.84 77.888 75.776 110.912 1.152 1.696 2.336 3.296 3.52 4.864 2.08 2.88 3.968 5.536 5.76 7.936l1.248 1.664c0.736 1.024 1.44 2.048 2.24 3.072l3.744 5.152 1.312 1.792 2.24 3.072 23.2 31.968 5.824 8.032 2.784 3.808 5.12 7.04 2.336 3.264 2.976 4.096 2.688 3.712 2.336 3.2 1.984 2.784 4.192 5.856z" fill="#0070B7" ></path><path d="M594.72 473.536c-0.64 3.52-1.536 7.04-2.72 10.4 2.496-20.736-1.12-40.32-15.04-41.792-8.64-0.832-19.008 15.264-19.68 16.288 2.08-5.024 3.552-10.4 4.672-16 3.072-14.944 4.544-31.552 2.496-46.72a86.304 86.304 0 0 0-2.112-10.56c-3.84-14.72-12.704-30.048-25.408-39.392 7.232 3.264 14.112 7.392 20.256 12.416 10.88 8.768 18.88 20.608 24.96 33.216 5.952 12.704 9.952 26.368 12.192 40.288 2.048 13.888 2.944 28.16 0.384 41.856" fill="#1D4D78" ></path><path d="M575.008 512.096c-8.96 9.664-19.936 17.408-31.552 23.808-24.768 13.664-52 22.528-79.552 28.992-27.328 6.24-55.136 11.008-83.072 13.056a234.24 234.24 0 0 1-42.752-53.12l-0.96-1.6a270.048 270.048 0 0 1-24.704-54.752c-0.064-0.224-0.096-0.48-0.192-0.704a258.368 258.368 0 0 1-2.08-6.4c-3.968-12.832-7.232-25.792-9.952-38.912a308.896 308.896 0 0 0 20.48 44.32l3.2 5.504c16.896 27.936 41.92 57.088 76.256 67.2a230.528 230.528 0 0 0 25.28 6.112c9.664 1.6 21.12 2.848 34.208 2.56l3.2-0.096a187.712 187.712 0 0 0 33.536-4.192c5.184-1.088 10.56-2.464 16.064-4.064l5.152-1.536c0.832-0.32 1.76-0.544 2.624-0.896 1.728-0.544 3.424-1.12 5.12-1.76a139.52 139.52 0 0 0 6.688-2.432l5.216-2.08 3.296-1.408 1.12-0.512a228.672 228.672 0 0 0 13.92-6.528c2.048-0.96 4.096-2.048 6.144-3.104 1.472-0.768 2.944-1.568 4.384-2.4 1.472-0.768 2.944-1.6 4.448-2.464l4.48-2.56" fill="#0086C9" ></path><path d="M429.152 755.712l-0.128 20.544c-0.096 28.32-0.096 49.408-0.096 49.408l-80.032 28.192c6.176-45.504 11.776-91.008 17.344-136.544l6.944-58.432c1.824 16.64 7.168 48.96 21.76 71.68 9.44 14.656 26.56 27.392 33.984 25.184l0.224-0.032" fill="#35ABCD" ></path><path d="M666.432 433.76a283.008 283.008 0 0 1-0.32 11.264c-0.704 12.288-2.592 24.544-5.536 36.576 0-2.272-0.096-25.824-6.368-38.56v-0.064c-3.904-7.936-10.176-11.68-20.32-3.424-16 13.056-28.928 30.944-36.864 43.456 0.832-2.88 1.504-5.76 2.048-8.64 2.56-14.496 1.6-29.12-0.48-43.36a154.592 154.592 0 0 0-12.672-41.408c-6.272-13.024-14.816-25.28-26.368-34.4-3.2-2.56-6.624-4.928-10.112-7.04 15.616 4.992 41.056 10.56 59.84 2.592 8.096-3.456 9.184-10.176 6.592-18.048l-0.032-0.064c-6.72-20.256-38.016-48.224-38.016-48.224 37.856 18.176 65.92 53.344 79.488 93.056 0.8 2.272 1.536 4.512 2.144 6.816a89.536 89.536 0 0 1 2.24 8.512c0.64 2.496 1.184 5.024 1.6 7.52 0.32 1.44 0.608 2.944 0.8 4.48 0.096 0.224 0.096 0.512 0.16 0.768 0.32 2.24 0.672 4.544 0.928 6.816 0.032 0.256 0.096 0.48 0.096 0.768a118.688 118.688 0 0 1 0.704 7.808 173.184 173.184 0 0 1 0.448 12.8" fill="#0079BC" ></path><path d="M459.2 492.384c-13.024 0.512-28.96-1.824-35.68-14.496a52.704 52.704 0 0 1-4.8-17.472c-2.88-21.568-9.024-68.64 23.552-71.264 22.976-1.856 68.544-11.68 78.816 26.304 10.336 37.984 2.816 74.368-61.92 76.928m105.28-96.64a86.304 86.304 0 0 0-2.112-10.56c-3.84-14.72-12.704-30.08-25.408-39.424a117.984 117.984 0 0 0-17.12-6.336c-27.2-7.84-56.256-7.04-84.32-4.064a53.696 53.696 0 0 0-19.84 6.08 31.616 31.616 0 0 0-13.76 15.04 67.04 67.04 0 0 0-4.736 20.448c-0.672 7.04-1.28 14.176-1.44 21.28-0.512 14.24 0.224 28.48 2.656 42.528 1.28 6.976 2.688 13.984 5.44 20.48l0.064 0.128 0.544 1.12c2.336 4.8 5.376 9.344 9.28 13.44l0.768 0.672 0.128 0.192a52.704 52.704 0 0 0 5.856 5.152c13.408 10.208 30.08 14.784 46.656 15.808 28.224 1.664 61.472-2.624 81.344-25.088 3.904-4.384 6.688-9.152 8.832-14.208 2.08-5.024 3.552-10.4 4.672-16 3.072-14.944 4.544-31.552 2.496-46.72" fill="#283558" ></path><path d="M377.344 581.28l-7.424 67.2-7.392 68.384a10769.6 10769.6 0 0 0-13.632 136.992L288 830.24V327.008c0 6.4 0.16 12.8 0.448 19.2v3.296c0.736 14.816 2.144 29.376 4.032 44.064 3.104 23.2 7.584 46.24 14.016 68.832 6.496 22.56 15.648 44.48 27.744 64.704 11.84 19.84 25.792 38.4 43.104 54.176" fill="#18426F" ></path><path d="M382.464 579.36l-0.096 0.736-0.064 0.384v0.16l-0.256 2.176-7.744 66.176-1.12 9.664v0.224l-6.944 58.432a9954.464 9954.464 0 0 1-17.344 136.544c4.16-45.696 8.864-91.328 13.632-136.96l7.392-68.416 7.424-67.2c-17.28-15.808-31.264-34.368-43.104-54.176a273.12 273.12 0 0 1-27.744-64.704c-6.4-22.592-10.88-45.632-14.016-68.8-1.92-14.72-3.296-29.28-4.032-44.096v-3.264c-0.32-6.432-0.448-12.8-0.448-19.232v-3.36c0.16 2.624 1.184 20.832 1.6 26.208 0.064 0.224 0.064 0.512 0.064 0.768l0.032 0.64c1.216 14.08 2.976 28.064 5.184 41.984 1.504 9.728 3.264 19.488 5.248 29.12 0 0.064 0.064 0.064 0.064 0.096a428.736 428.736 0 0 0 11.776 44.576c0.032 0.256 0.16 0.48 0.256 0.736l0.224 0.736a270.048 270.048 0 0 0 24.704 54.72l1.696 3.04c-0.224-0.48-0.544-0.96-0.768-1.472a234.24 234.24 0 0 0 42.752 53.152c0.192 0.192 0.416 0.32 0.608 0.544l1.024 0.864z" fill="#3DBBD8" ></path><path d="M690.752 0a32 32 0 0 1 22.624 9.376l173.248 173.248a32 32 0 0 1 9.376 22.624V992a32 32 0 0 1-32 32H160a32 32 0 0 1-32-32V32a32 32 0 0 1 32-32h530.752zM672 32H160v960h704V224h-160a32 32 0 0 1-32-32V32z" fill="#0070B7" ></path></symbol><symbol id="bf-nwd" viewBox="0 0 1024 1024"><path d="M690.752 0a32 32 0 0 1 22.624 9.376l173.248 173.248a32 32 0 0 1 9.376 22.624V992a32 32 0 0 1-32 32H160a32 32 0 0 1-32-32V32a32 32 0 0 1 32-32h530.752z" fill="#FFFFFF" ></path><path d="M630.912 826.464h-5.952s-52.704-63.808-100.544-126.848c-38.816-51.2-72.832-110.208-93.76-102.016-12.576 4.992-12.8 47.456-12.64 89.6 0.224 55.68 0.768 113.888 0.768 113.888l-27.744 25.376H288V310.4l0.192 1.504c0 0.224 0 0.416 0.064 0.64 0.032 0.512 0.064 1.12 0.16 1.632l0.224 2.112c0 0.384 0.032 0.8 0.128 1.184l0.128 1.152 0.16 1.216 0.064 0.896c0.16 0.896 0.224 1.76 0.32 2.784 1.728 17.504 3.744 34.976 5.888 52.416 2.72 21.76 5.632 43.584 8.8 65.312 6.272 43.52 13.12 86.944 21.024 130.176 8.224 43.2 16.928 86.304 27.616 128.96 8.128 32.768 17.184 65.28 28.128 97.28l0.128 0.416 2.88 8.576c0.992 2.912 2.016 5.824 3.072 8.704l3.136 8.544-0.064-0.256v-0.256a3046.432 3046.432 0 0 1-9.088-97.152 2405.504 2405.504 0 0 1-6.08-97.984 1060.704 1060.704 0 0 1 0.096-98.144c0.96-16.32 2.496-32.576 5.92-48.48 1.824-7.872 4.032-15.808 8-22.752a26.368 26.368 0 0 1 7.68-8.736 15.36 15.36 0 0 1 10.88-2.304c7.616 1.28 14.4 6.368 20.416 11.68 5.952 5.44 11.392 11.52 16.512 17.824 10.208 12.736 19.264 26.368 27.968 40.224 17.216 27.776 32.704 56.64 47.712 85.696 29.952 58.176 56.96 117.888 86.592 176.32a748.736 748.736 0 0 1 4.544 9.024l2.272 4.48 8.544 16.544 1.824 3.456c0.288 0.512 0.544 0.96 0.768 1.472l0.768 1.44c1.984 3.776 3.52 6.752 4.512 8.544l0.384 0.64 0.48 0.96 0.16 0.32M736 555.008v271.456l-20.96-31.392-0.32-0.448-1.12-1.664-0.224-0.32-0.64-0.96-0.096-0.192-0.16-0.16-0.384-0.64-0.96-1.44-0.32-0.416-0.096-0.192-0.128-0.224-0.224-0.32-0.448-0.704-0.128-0.16-0.448-0.672-0.288-0.384-0.32-0.416-0.256-0.448-0.8-1.248-23.36-35.968-27.168-40.864c0.416-0.096 0.8-0.128 1.216-0.352 0.32-0.16 0.736-0.288 1.152-0.512l0.16-0.032a15.584 15.584 0 0 0 6.56-5.344c2.432-3.424 4.096-7.392 5.248-11.392 2.24-8.096 3.2-16.48 3.872-24.768 1.12-16.64 0.96-33.344 0.32-49.92-0.32-8.32-0.704-16.544-1.28-24.832v-0.128l0.16-2.24 0.256-4.96 0.32-5.344 0.544-8.768 0.32-4.64 0.32-4.768 0.48-5.92v-0.128l0.416-5.888 0.512-6.176 0.384-4.608 0.32-3.36 0.32-3.424 0.64-6.88c0.16-1.088 0.256-2.24 0.384-3.424a349.248 349.248 0 0 1 1.504-13.568 1186625.472 1186625.472 0 0 1 1.248-9.92l0.64-4.864 0.736-4.768 0.48-3.072c0.384-2.048 0.672-4.064 1.024-6.016a44.48 44.48 0 0 1 0.448-2.56l0.064-0.32c0.224-1.088 0.384-2.112 0.64-3.168 0.096-0.832 0.288-1.632 0.48-2.464 0.32-1.664 0.704-3.328 1.056-4.928 0.576-2.368 1.152-4.64 1.728-6.752l0.576-2.112c0.192-0.64 0.416-1.344 0.576-2.048l0.64-1.824c0.224-0.576 0.384-1.152 0.64-1.728l0.384-1.088 0.64-1.664a18.048 18.048 0 0 1 3.744-6.496l0.64-0.736c0.352-0.32 0.672-0.64 1.024-0.832a4.16 4.16 0 0 1 0.8-0.544 4.8 4.8 0 0 1 1.696-0.576C722.88 434.752 736 555.008 736 555.008" fill="#08723A" ></path><path d="M736 826.464h-105.056l-0.192-0.32-0.48-0.96-0.352-0.64c-0.992-1.792-2.56-4.768-4.544-8.544l-0.768-1.44-0.768-1.472-1.824-3.456-8.544-16.576-2.24-4.448a748.8 748.8 0 0 0-4.544-9.024c-14.624-29.312-28.128-59.136-42.112-88.8-13.952-29.536-28.16-59.072-43.008-88.288a910.304 910.304 0 0 0-7.552-14.624c-2.56-4.896-5.088-9.76-7.68-14.624-1.184-2.112-2.304-4.32-3.52-6.432a871.648 871.648 0 0 0-28.704-50.368c-8.64-13.92-17.664-27.648-27.904-40.512a157.888 157.888 0 0 0-16.736-18.24c-6.144-5.408-12.992-10.848-21.696-12.32a17.408 17.408 0 0 0-12.544 2.72 28.832 28.832 0 0 0-8.416 9.6c-4.16 7.328-6.368 15.36-8.16 23.456a322.624 322.624 0 0 0-5.76 48.864c-1.696 32.768-0.992 65.536 0.32 98.272a2337.28 2337.28 0 0 0 12.896 159.744l0.128 1.152v0.224l0.448 4.064 0.256 2.56 0.352 3.36c0.096 1.184 0.224 2.368 0.416 3.552l0.032 0.416 0.416 3.584c0.032 0.576 0.064 1.152 0.16 1.76 0.224 1.856 0.448 3.52 0.576 5.12 0.096 1.088 0.224 2.176 0.416 3.296l0.16 1.376 0.544 4.896-9.472-28.16c-3.424-10.4-6.24-21.056-9.408-31.552-3.232-10.496-5.856-21.12-8.672-31.776-2.816-10.624-5.664-21.216-8.064-31.936a2308.64 2308.64 0 0 1-26.784-128.96 3578.88 3578.88 0 0 1-21.824-129.984c-3.296-21.728-6.336-43.52-9.28-65.28-2.912-21.28-5.76-42.496-8.32-63.84L288.032 310.4h103.2l0.256 0.448 72.48 108.48-20.576-30.08 33.856 51.648 34.144 51.456 68.32 102.848 69.024 102.4 34.56 51.168 25.472 36.992 0.32 0.416 0.256 0.384 0.448 0.64 0.128 0.224 0.448 0.64 0.224 0.32v0.064l0.128 0.192 0.128 0.192 0.256 0.416 0.96 1.44 0.416 0.64 0.16 0.192 0.128 0.16 0.64 0.96 0.192 0.32 1.12 1.664 0.32 0.448 20.96 31.36z" fill="#65B184" ></path><path d="M673.344 664.704a129.344 129.344 0 0 1-3.104 24.544 38.4 38.4 0 0 1-4.544 11.392 14.304 14.304 0 0 1-8 6.176c0.224-0.128 0.384-0.32 0.576-0.48a4.704 4.704 0 0 0 1.024-1.664 5.248 5.248 0 0 0 0.32-0.864 8.064 8.064 0 0 0 0.416-1.92 2.88 2.88 0 0 0 0.16-0.896l0.128-1.568a31.808 31.808 0 0 0-0.064-4.096c-0.032-0.576-0.064-1.184-0.16-1.856a46.784 46.784 0 0 0-1.12-7.296 65.152 65.152 0 0 0-3.488-12.608 83.072 83.072 0 0 0-1.824-4.896c-13.248-33.088-27.36-63.84-47.232-82.112l-0.64-0.512 1.152-0.224c7.424-2.4 8.224-25.6 7.776-55.808 0 0 0.96-5.76 2.624-14.016v-0.096c3.872-19.712 11.52-53.6 18.72-58.24 7.584-4.96 18.112 11.84 21.568 17.952 2.272 13.408 4.416 26.816 6.4 40.256 2.336 16.416 4.64 32.896 6.208 49.408 1.728 16.48 2.496 33.088 2.976 49.696 0.544 16.608 0.96 33.216 0.128 49.728" fill="#24713C" ></path><path d="M660.32 699.424l-0.128 1.568a2.88 2.88 0 0 1-0.16 0.896 8.064 8.064 0 0 1-0.416 1.92 5.248 5.248 0 0 1-0.32 0.864 4.704 4.704 0 0 1-1.6 2.144l-0.064 0.064-0.064 0.032-0.544 0.128-6.848-10.368-68.352-102.848-69.056-102.4-34.496-51.2-12.384-18.112c28.928 40.864 87.072 118.912 139.744 163.776l0.192 0.16 0.64 0.512c19.84 18.24 33.92 49.024 47.2 82.112a83.072 83.072 0 0 1 3.264 9.376 70.24 70.24 0 0 1 2.048 8.128 46.784 46.784 0 0 1 1.12 7.296c0.096 0.64 0.128 1.28 0.16 1.856a31.808 31.808 0 0 1 0.064 4.096" fill="#509970" ></path><path d="M736 555.008v271.456l-20.96-31.392-0.32-0.448-1.12-1.664-0.224-0.32-0.64-0.96-0.096-0.192-0.16-0.16-0.384-0.64-0.96-1.44-0.32-0.416-0.096-0.192-0.128-0.224-0.224-0.32-0.448-0.704-0.128-0.16-0.448-0.672-0.288-0.384-0.32-0.416-0.256-0.448-0.8-1.248-23.36-35.968-27.168-40.864c0.416-0.096 0.8-0.128 1.216-0.352 0.32-0.16 0.736-0.288 1.152-0.512l0.16-0.032a15.584 15.584 0 0 0 6.56-5.344c2.432-3.424 4.096-7.392 5.248-11.392 2.24-8.096 3.2-16.48 3.872-24.768 1.12-16.64 0.96-33.344 0.32-49.92-0.32-8.32-0.704-16.544-1.28-24.832v-0.128l0.16-2.24 0.256-4.96 0.32-5.344 0.544-8.768 0.32-4.64 0.32-4.768 0.48-5.92v-0.128l0.416-5.888 0.512-6.176 0.384-4.608 0.32-3.36 0.32-3.424 0.64-6.88c0.16-1.088 0.256-2.24 0.384-3.424a349.248 349.248 0 0 1 1.504-13.568 1186625.472 1186625.472 0 0 1 1.248-9.92l0.64-4.864 0.736-4.768 0.48-3.072c0.384-2.048 0.672-4.064 1.024-6.016a44.48 44.48 0 0 1 0.448-2.56l0.064-0.32c0.224-1.088 0.384-2.112 0.64-3.168 0.096-0.832 0.288-1.632 0.48-2.464 0.32-1.664 0.704-3.328 1.056-4.928 0.544-2.4 1.12-4.672 1.728-6.752l0.576-2.112c0.192-0.64 0.416-1.344 0.576-2.048l0.64-1.824c0.224-0.576 0.384-1.152 0.64-1.728l0.384-1.088 0.64-1.664a18.048 18.048 0 0 1 3.744-6.496l0.64-0.736c0.352-0.32 0.672-0.64 1.024-0.832a4.16 4.16 0 0 1 0.8-0.544 4.8 4.8 0 0 1 1.696-0.576C722.88 434.752 736 555.008 736 555.008" fill="#299555" ></path><path d="M736 319.744v235.264s-13.12-120.256-34.816-117.632a4.8 4.8 0 0 0-1.696 0.576 4.16 4.16 0 0 0-0.8 0.544c-0.352 0.224-0.64 0.512-1.024 0.832l-0.64 0.704a18.048 18.048 0 0 0-2.24 3.488 26.016 26.016 0 0 0-1.472 3.04l-0.672 1.664-0.416 1.088c-0.224 0.576-0.384 1.152-0.64 1.728l-0.608 1.824c-0.16 0.704-0.384 1.376-0.576 2.048l-0.576 2.112c-0.64 2.08-1.184 4.352-1.728 6.752l-1.056 4.928c-0.192 0.832-0.384 1.6-0.512 2.464-0.224 1.056-0.384 2.08-0.64 3.168v0.32a44.48 44.48 0 0 0-0.48 2.56 120.64 120.64 0 0 0-1.024 6.016l-0.48 3.072-0.704 4.768c-0.256 1.6-0.448 3.232-0.672 4.864a295.232 295.232 0 0 0-1.248 9.92 349.248 349.248 0 0 0-1.504 13.568l-0.352 3.424-0.672 6.88-0.32 3.424-0.32 3.36-0.384 4.608-0.512 6.176c-0.16 1.952-0.32 3.968-0.416 5.888v0.128c-0.192 2.016-0.32 3.968-0.448 5.92-0.16 1.6-0.224 3.2-0.352 4.768l-0.32 4.64-0.544 8.768-0.32 5.344a1162.88 1162.88 0 0 1-0.256 4.96l-0.128 2.24a669.28 669.28 0 0 0-2.048-24.896 1022.144 1022.144 0 0 0-6.56-49.44 1131.296 1131.296 0 0 0-3.36-20.896l-0.64-3.968a1489.184 1489.184 0 0 0-2.368-13.792v-0.064c-2.528-14.72-5.184-29.312-7.968-44.032l-0.384-1.92c-5.888-31.04-12.288-61.856-19.008-92.704l-1.28-6.016a71.04 71.04 0 0 0-0.64-2.976c-0.576-2.72-1.152-5.504-1.792-8.192l-0.128-0.736-0.032-0.224-0.064-0.064-0.032-0.288H736z" fill="#65B184" ></path><path d="M657.632 475.616c-3.456-6.144-13.984-22.912-21.6-17.984-7.2 4.672-14.816 38.56-18.688 58.272v0.096c-1.632 8.288-2.624 14.016-2.624 14.016l-0.096-5.76-0.032-2.336c0-0.8 0-1.632-0.064-2.56v-2.72c0-1.984 0-4.064-0.064-6.336l-0.288-23.872-0.064-6.016-0.096-7.872c-0.064-1.6-0.064-3.2-0.064-4.8l-0.128-9.76-0.032-4.928c0-0.864 0-1.696-0.064-2.56 0-2.432 0-4.896-0.064-7.36-0.064-2.592-0.064-5.216-0.096-7.84l-0.096-6.72-0.096-8-0.064-4.704-0.384-31.36a6.272 6.272 0 0 0 0-1.312v-6.4l0.064-0.288v-0.096a38.72 38.72 0 0 0-0.096-2.24v-0.288c0-14.784 6.56-33.44 11.072-44.512l1.792-4.288a0.64 0.64 0 0 1 0.096-0.32c0.48-1.152 0.96-2.08 1.28-2.88l0.64-1.28c0.064-0.288 0.224-0.512 0.32-0.768v-0.096l0.064 0.288 0.064 0.256 0.032 0.032 0.128 0.736a3956.416 3956.416 0 0 1 18.976 96.544l3.648 19.776c2.24 12.736 4.544 25.472 6.624 38.24" fill="#056737" ></path><path d="M288 663.232s10.592-68.672 19.328-36.736c8.704 32 48.704 132.768 83.712 200-35.712 0.064-103.04 0-103.04 0v-163.264z" fill="#0F8640" ></path><path d="M708.48 785.312l-0.8-1.248-23.36-35.968-27.168-40.864-0.032-0.096-0.064-0.032c0.128-0.064 0.288-0.064 0.416-0.096h0.032l0.032-0.096-0.512 0.128-6.848-10.368-68.352-102.848-69.056-102.4-34.496-51.2-12.384-18.112-0.704-0.96-1.216-1.792-20.576-30.112 33.856 51.648 34.144 51.456 68.32 102.848 69.024 102.4 34.56 51.168 25.472 36.992-0.288-0.448zM390.08 823.904l-3.072-8.544a288.832 288.832 0 0 1-3.072-8.704c-0.992-2.816-1.92-5.728-2.88-8.576l-0.16-0.448a1172.384 1172.384 0 0 1-28.16-97.28 2312.832 2312.832 0 0 1-27.584-128.928 3473.6 3473.6 0 0 1-21.024-130.176c-3.168-21.728-6.08-43.52-8.8-65.312a2299.008 2299.008 0 0 1-6.208-55.2l-0.064-0.896-0.16-1.216a88.64 88.64 0 0 1-0.48-4.448c-0.096-0.512-0.128-1.12-0.16-1.632-0.064-0.224-0.064-0.416-0.064-0.64 2.56 21.344 5.408 42.56 8.32 63.84 2.944 21.76 5.984 43.552 9.28 65.28 6.592 43.424 13.632 86.784 21.824 129.952 7.968 43.2 16.384 86.336 26.784 128.96 2.4 10.752 5.248 21.344 8.064 31.968 2.816 10.624 5.44 21.28 8.672 31.776 3.168 10.496 5.984 21.12 9.408 31.552l9.472 28.16 0.064 0.064v0.448M606.656 779.584c-29.632-58.432-56.64-118.144-86.592-176.32a1247.168 1247.168 0 0 0-47.712-85.696 425.92 425.92 0 0 0-27.968-40.224 161.12 161.12 0 0 0-16.512-17.856c-6.016-5.28-12.8-10.368-20.448-11.648a15.36 15.36 0 0 0-10.88 2.304 26.368 26.368 0 0 0-7.648 8.736 83.808 83.808 0 0 0-8 22.72 321.824 321.824 0 0 0-5.92 48.512 1060.704 1060.704 0 0 0-0.096 98.144c1.28 32.64 3.424 65.344 6.08 97.984 2.368 30.816 5.184 61.536 8.512 92.256l-0.16-1.408a41.568 41.568 0 0 1-0.416-3.264c-0.128-1.6-0.32-3.264-0.576-5.12a446.144 446.144 0 0 1-0.576-5.344l-0.032-0.416a48.416 48.416 0 0 1-0.416-3.552l-0.32-3.36-0.32-2.56c-0.096-1.344-0.288-2.72-0.416-4.064v-0.224l-0.128-1.152a2337.28 2337.28 0 0 1-12.864-159.744c-1.344-32.736-2.048-65.504-0.384-98.24a323.2 323.2 0 0 1 5.792-48.896c1.792-8.064 4.032-16.128 8.16-23.488a28.832 28.832 0 0 1 8.416-9.568 17.408 17.408 0 0 1 12.544-2.72c8.704 1.472 15.552 6.912 21.696 12.352 6.144 5.568 11.584 11.808 16.736 18.208 10.24 12.864 19.264 26.56 27.904 40.512 9.44 15.36 18.336 30.976 26.816 46.88a434.08 434.08 0 0 1 5.376 9.92c2.624 4.864 5.184 9.728 7.68 14.624 2.624 4.832 5.12 9.728 7.584 14.624 14.848 29.216 29.056 58.72 43.008 88.32 13.984 29.632 27.488 59.456 42.08 88.768" fill="#A3CCEF" ></path><path d="M675.36 664.832c-0.64 8.288-1.6 16.64-3.872 24.768a37.376 37.376 0 0 1-5.248 11.392 15.584 15.584 0 0 1-6.56 5.344l-0.16 0.032a14.944 14.944 0 0 1-2.016 0.64l0.032-0.096 0.096-0.032 0.064-0.064a14.336 14.336 0 0 0 8-6.176 38.4 38.4 0 0 0 4.544-11.392 129.28 129.28 0 0 0 3.104-24.544c0.864-16.512 0.416-33.12-0.128-49.728a659.68 659.68 0 0 0-2.976-49.696 1025.248 1025.248 0 0 0-6.24-49.408 2743.232 2743.232 0 0 0-12.992-78.496l-3.648-19.776a3956.416 3956.416 0 0 0-18.976-96.576c0.64 2.72 1.216 5.504 1.792 8.224l0.64 2.976 1.28 6.016c6.72 30.848 13.12 61.696 19.008 92.672l0.384 1.984c2.784 14.688 5.44 29.28 8 44.032 0.768 4.608 1.6 9.248 2.336 13.824l0.64 3.968c1.184 6.944 2.304 13.952 3.36 20.864 2.464 16.448 4.896 32.928 6.56 49.472 0.832 8.32 1.504 16.64 2.048 24.896v0.128c0.512 8.288 0.928 16.512 1.248 24.8 0.64 16.64 0.8 33.28-0.32 49.952" fill="#A3CCEF" ></path><path d="M690.752 0a32 32 0 0 1 22.624 9.376l173.248 173.248a32 32 0 0 1 9.376 22.624V992a32 32 0 0 1-32 32H160a32 32 0 0 1-32-32V32a32 32 0 0 1 32-32h530.752zM672 32H160v960h704V224h-160a32 32 0 0 1-32-32V32z" fill="#299555" ></path></symbol><symbol id="bf-max" viewBox="0 0 1024 1024"><path d="M690.752 0a32 32 0 0 1 22.624 9.376l173.248 173.248a32 32 0 0 1 9.376 22.624V992a32 32 0 0 1-32 32H160a32 32 0 0 1-32-32V32a32 32 0 0 1 32-32h530.752z" fill="#FFFFFF" ></path><path d="M690.752 0a32 32 0 0 1 22.624 9.376l173.248 173.248a32 32 0 0 1 9.376 22.624V992a32 32 0 0 1-32 32H160a32 32 0 0 1-32-32V32a32 32 0 0 1 32-32h530.752zM672 32H160v960h704V224h-160a32 32 0 0 1-32-32V32z" fill="#00ACAC" ></path><path d="M644.448 715.456c-0.64 1.728-1.28 3.456-2.016 5.184a106.528 106.528 0 0 1-13.408 24.96c-5.44 7.744-11.904 14.784-19.008 21.056l-0.128 0.192a7.744 7.744 0 0 0-0.64 0.512l-0.128 0.096c-14.208 12.256-30.752 21.76-48.48 28.16a161.824 161.824 0 0 1-55.296 9.728 126.048 126.048 0 0 1-40.416-6.72c-9.696-20.8-8.384-67.712 36.16-80.448h0.032c2.016 0.064 4 0 5.984-0.064 8.096-0.32 16.32-1.824 23.872-5.056a77.568 77.568 0 0 0 28.416-21.504c5.632-1.856 58.656-18.24 85.056 23.904" fill="#00848C" ></path><path d="M573.248 658.88v0.608c0.032 0.672 0 1.28-0.096 1.92v0.32c-0.032 0.576-0.224 1.152-0.256 1.664l-0.256 1.472c-1.152 5.6-3.552 10.88-6.496 15.808a79.936 79.936 0 0 1-35.84 30.816c-7.328 3.168-15.264 4.48-23.264 4.8a92.096 92.096 0 0 1-46.4-9.664 61.76 61.76 0 0 1-18.624-14.88 64.192 64.192 0 0 1-10.976-21.12l-0.032-0.16v-0.192a42.976 42.976 0 0 1 0-10.656l0.032-0.384c0.032-0.128 0.032-0.288 0.096-0.416l0.032-0.128v-0.128l0.096-0.256v-0.128a2.24 2.24 0 0 1 0.096-0.544l0.032-0.128a1.6 1.6 0 0 1 0.096-0.512l0.032-0.064v-0.096a7.744 7.744 0 0 1 0.32-1.472l0.224-0.896 0.32-1.056c0.064-0.352 0.16-0.768 0.32-1.12a4.832 4.832 0 0 1 4.48 4.32c0.416 3.008 1.152 5.984 2.112 8.896 1.664 3.936 3.552 7.808 5.824 11.456 5.856 9.728 14.624 19.072 25.216 23.52 23.648 9.856 52.384 8.64 74.944-4 22.944-12.8 26.944-28.096 27.744-34.624v-0.192l0.096-0.96v-0.096c0.096-0.576 0.096-1.024 0.096-1.344l0.032-0.384" fill="#008089" ></path><path d="M681.024 353.792c-9.312 50.016-33.856 89.056-67.2 116.416l0.992-1.344c2.88-3.936 5.6-8 8.128-12.16 5.12-8.32 9.408-17.152 12.672-26.4l0.032-0.064 0.192-0.576c3.936-11.584 6.112-23.904 5.984-36.192a105.888 105.888 0 0 0-7.04-36.16 117.312 117.312 0 0 0-9.024-18.752l-1.184-1.92a104.032 104.032 0 0 0-6.144-9.28c-0.448-0.8-0.992-1.504-1.536-2.208l-0.64-0.832a72.416 72.416 0 0 0-3.616-4.672l-0.128-0.192a160.448 160.448 0 0 0-20.512-21.28l0.032 0.096a97.12 97.12 0 0 0-8.576-6.528c-2.912-2.016-5.728-4.256-8.736-6.112-6.208-3.456-12.32-7.2-18.976-9.728-6.432-3.104-13.312-4.992-20.064-7.168l-10.4-2.368c-3.424-0.864-6.976-1.184-10.464-1.76a182.912 182.912 0 0 0-42.432-0.8c-14.016 1.344-28.064 3.872-41.472 8.288A182.784 182.784 0 0 1 507.136 256c10.56 0.064 21.088 0.832 31.552 2.56 62.976 9.888 114.432 46.656 142.368 95.232" fill="#03C7CB" ></path><path d="M649.984 693.248c-1.184 7.552-2.976 15.008-5.536 22.208-26.4-42.144-79.424-25.76-85.056-23.904 0.64-0.736 1.248-1.504 1.824-2.304l0.576-0.704a65.12 65.12 0 0 0 6.432-10.496 48.608 48.608 0 0 0 4-10.688c0.128-0.672 0.32-1.28 0.416-1.952 0.256-1.216 0.384-2.464 0.512-3.68v-0.32a9.024 9.024 0 0 0 0.096-1.92v-0.608l-0.032 0.384v-0.256a130.56 130.56 0 0 0-0.064-8.32 51.616 51.616 0 0 0-2.048-12.544l-0.512-1.664a52.928 52.928 0 0 0-0.704-1.92c-4.064-11.136-11.52-20.48-19.616-25.664a171.04 171.04 0 0 0 18.656-4.032c1.28-0.32 2.496-0.64 3.744-1.024 1.952-0.544 3.84-1.152 5.824-1.728a183.68 183.68 0 0 0 25.856-10.4c5.664-2.784 11.2-5.856 16.672-9.184l0.096 0.128 0.864 1.408 7.04 12.544c2.688 4.576 4.864 9.408 7.04 14.208 3.84 8.512 7.168 17.312 9.696 26.304 1.28 4.512 2.368 9.056 3.264 13.632 0.896 4.512 1.408 9.184 1.92 13.76v0.32c0.608 9.44 0.48 19.04-0.96 28.416" fill="#006570" ></path><path d="M638.848 393.504c0.128 7.648-0.64 15.328-2.24 22.784l-0.704 2.88a145.6 145.6 0 0 1-1.216 4.704c-0.096 0.32-0.192 0.576-0.224 0.896l-1.344 4.096-0.032 0.096-0.16 0.48a135.264 135.264 0 0 1-12.256 25.888 193.888 193.888 0 0 1-8 12c-1.088 1.504-2.208 2.976-3.392 4.448-62.144 46.752-92.832 31.488-103.104 22.848a214.496 214.496 0 0 0 51.584-33.44 52.256 52.256 0 0 0 13.12-15.392c1.92-3.168 3.2-6.592 4.064-10.176 3.424-14.944-1.6-28.48-12.896-40.096a66.56 66.56 0 0 0-21.696-15.04l-2.336-0.864c-0.736-0.288-1.536-0.544-2.336-0.8a109.76 109.76 0 0 0-1.92-0.544l-0.448-0.128a101.952 101.952 0 0 0-27.168-3.072h-0.128c-49.216-3.072-165.568 25.984-149.824-43.84a193.024 193.024 0 0 1 74.464-59.072l0.256-0.064c6.624-2.016 13.312-3.552 20.16-4.736l0.448-0.064c0.032 0 0.096 0 0.128-0.064l0.768-0.128a233.28 233.28 0 0 1 20.064-2.304 195.776 195.776 0 0 1 44.672 1.984c1.504 0.224 3.04 0.448 4.48 0.736 1.12 0.224 2.176 0.416 3.264 0.704l5.792 1.408 4.48 1.088c5.088 1.728 10.24 3.264 15.2 5.312 0.192 0.032 0.32 0.128 0.512 0.192 1.312 0.576 2.656 1.152 3.936 1.824a60.8 60.8 0 0 1 5.984 2.752c4.288 2.176 8.352 4.704 12.544 7.04 2.976 1.824 5.728 4 8.576 6.016 2.848 2.016 5.728 4.128 8.256 6.304l0.032 0.064c9.152 7.872 17.344 16.896 24.448 26.624 7.104 9.728 13.088 20.288 17.344 31.52 4.16 11.232 6.624 23.136 6.848 35.136" fill="#00ACAC" ></path><path d="M609.28 471.744c-1.728 2.272-3.616 4.544-5.44 6.816-5.856 6.72-11.904 13.12-18.304 19.2-1.152 1.056-2.304 2.08-3.52 3.104-1.344 1.152-2.688 2.336-4.096 3.456l-5.44 4.384c0 0.096-0.128 0.128-0.192 0.224l-0.672 0.48a297.472 297.472 0 0 1-9.632 7.296l-1.28 0.896-1.888 1.312c-2.304 1.664-4.672 3.296-7.04 4.896l-3.808 2.56-1.92 1.28c-0.64 0.48-1.312 0.864-1.92 1.28H544l-3.84 2.464c-1.312 0.864-2.592 1.696-3.904 2.496h-0.064a625.984 625.984 0 0 1-23.776 13.92c-0.096 0-0.224 0.064-0.352 0.16l-3.84 2.08c-1.344 0.768-2.72 1.536-4.128 2.24l-0.192 0.064c-5.472 2.88-11.04 5.664-16.576 8.352l-0.128 0.064-8.064 3.84-2.432 1.184-5.312 2.432a282.88 282.88 0 0 1-6.464 2.88l-1.376 0.64-0.64 0.256c-1.472 0.64-2.976 1.344-4.48 1.952-1.28 0.544-2.464 1.024-3.712 1.6l-0.8 0.32a531.2 531.2 0 0 1-7.456 3.104l-1.6 0.64c-0.448 0.16-0.96 0.32-1.376 0.544a10.848 10.848 0 0 0-1.056 0.448l-1.44 0.544-6.304 2.496-4.736 1.792-4.096 1.536-2.144 0.736-2.112 0.8-0.096 0.064v-61.664s11.904-3.36 29.216-9.408a538.24 538.24 0 0 0 52.96-21.728c0.832-0.32 1.664-0.8 2.464-1.152 10.24 8.64 40.96 23.904 103.104-22.88" fill="#00ACAC" ></path><path d="M574.944 435.616a33.696 33.696 0 0 1-4.64 11.104c-0.576 0.864-1.152 1.856-1.76 2.688a31.264 31.264 0 0 0-0.96-7.328c-2.688-11.04-10.976-19.84-20.352-25.824a79.488 79.488 0 0 0-8.736-4.768l-0.64-0.256a72.64 72.64 0 0 0-11.136-3.904c-0.544-0.192-1.088-0.32-1.6-0.448l-2.24-0.544c-0.128-0.032-0.224-0.032-0.32-0.096a86.496 86.496 0 0 0-19.584-2.08 84.288 84.288 0 0 0-40.576 9.824 56.064 56.064 0 0 0-15.648 12.288c-3.68 4.224-6.336 8.96-7.936 14.368a29.76 29.76 0 0 0-1.344 7.904l-2.784 2.336a89.664 89.664 0 0 1 1.92-30.08l0.416-2.368c0.352-1.6 0.8-3.168 1.344-4.672l0.768-2.016c0.032-0.064 0.032-0.16 0.128-0.256 0.32-0.736 0.64-1.504 1.024-2.24a50.24 50.24 0 0 1 10.56-14.176c2.56-2.464 5.44-4.736 8.512-6.752l0.224-0.192 2.144-1.344a79.808 79.808 0 0 1 13.12-6.304l0.48-0.16 2.432-0.8 2.72-0.864h0.224a4.352 4.352 0 0 1 0.704-0.256 4.352 4.352 0 0 1 0.704-0.224l1.472-0.384h0.096a16.64 16.64 0 0 1 1.408-0.352 74.272 74.272 0 0 1 4.672-0.96h0.096c2.016-0.32 4.064-0.64 6.208-0.864a39.04 39.04 0 0 1 3.424-0.32l1.184-0.032c0-0.064 0.064-0.064 0.064-0.064h0.832l1.056-0.096H506.144c9.056 0 18.4 0.576 27.2 3.04l0.416 0.128 1.92 0.544a35.552 35.552 0 0 1 4.672 1.696l2.304 0.96c7.264 3.36 13.792 8.32 19.392 14.016 11.296 11.648 16.32 25.184 12.896 40.128" fill="#006368" ></path><path d="M654.4 556.32a167.968 167.968 0 0 1-9.248 8.512h-0.032a246.176 246.176 0 0 1-23.68 17.44l-0.32 0.192h-0.096v0.064c-5.44 3.328-11.008 6.4-16.672 9.184a209.376 209.376 0 0 1-25.856 10.4c-1.952 0.576-3.84 1.184-5.824 1.728l-3.744 1.024a186.432 186.432 0 0 1-18.656 4.032 208.32 208.32 0 0 1-66.816 0.192l-0.672-0.128a208.16 208.16 0 0 1-61.12-19.776v-0.64c1.44-0.512 2.848-0.992 4.224-1.6l4.096-1.504 4.736-1.792 3.648-1.376 4.096-1.664a10.848 10.848 0 0 1 1.056-0.448c0.448-0.224 0.96-0.384 1.376-0.576a190.208 190.208 0 0 0 9.856-4.032l3.68-1.6c1.728-0.64 3.488-1.408 5.152-2.24a282.88 282.88 0 0 0 7.808-3.52c1.792-0.736 3.584-1.536 5.344-2.4l2.464-1.152 8.16-3.872h0.032a199.232 199.232 0 0 0 5.056 3.456l0.544 0.32c3.104 1.984 6.272 3.84 9.504 5.536l0.224 0.128 0.224 0.16 1.376 0.64c0.096 0.064 0.224 0.096 0.32 0.192l1.6 0.8 0.8 0.352a1.6 1.6 0 0 0 0.384 0.192l2.368 1.088c1.376 0.672 2.752 1.28 4.16 1.824 1.376 0.64 2.816 1.152 4.224 1.664a49.376 49.376 0 0 0 3.456 1.216l0.8 0.288c1.472 0.512 2.88 0.96 4.352 1.376a141.184 141.184 0 0 0 91.744-3.2l0.608-0.224c1.184-0.448 2.4-0.896 3.52-1.44 1.408-0.512 2.784-1.152 4.096-1.76l1.28-0.64 2.688-1.216c1.792-0.896 3.52-1.824 5.248-2.752l0.032-0.064c2.88-1.6 5.696-3.264 8.384-5.12 0.384-0.16 0.736-0.416 1.088-0.64l0.224-0.192c0.448-0.32 0.832-0.64 1.248-0.864 1.024-0.672 2.08-1.44 3.04-2.24 0.288-0.16 0.512-0.352 0.768-0.544l1.248-0.96c0.8-0.544 1.6-1.216 2.4-1.824" fill="#007278" ></path><path d="M464.928 798.624a89.92 89.92 0 0 1-13.856-6.208 81.888 81.888 0 0 1-21.824-17.6 93.376 93.376 0 0 1-15.264-23.744c-1.696-3.424-2.88-7.2-4.352-10.784-0.256-0.96-0.544-1.792-0.8-2.624a29.92 29.92 0 0 1-0.768-2.656l-0.736-2.72-0.064-0.256v-0.16l-0.608-2.208v-0.032l-0.032-0.16-0.448-2.016-0.192-0.768-0.16-0.96c-0.064-0.288-0.128-0.608-0.128-0.896-0.064-0.096-0.064-0.128-0.064-0.224v-0.192l-0.032-0.32-0.192-0.832-0.032-0.352-0.128-0.96-0.16-0.992c0-0.352 0-0.704-0.064-1.056v-0.064a49.92 49.92 0 0 1-0.032-7.328 47.104 47.104 0 0 1 0.768-5.984 46.912 46.912 0 0 1 3.072-10.24 62.336 62.336 0 0 1 5.6-10.24c0-0.096 0.096-0.16 0.16-0.224a14.592 14.592 0 0 1 1.696-2.304 54.848 54.848 0 0 1 11.52-11.072l1.504-0.96 0.032-0.064 0.064 0.064 1.6-1.024 0.032 0.16V670.944l0.128 0.352 0.064 0.064 0.064 0.32a3.68 3.68 0 0 0 0.416 1.088l0.032 0.224c0 0.032 0 0.096 0.064 0.128l0.032 0.192c1.024 3.2 2.176 6.304 3.584 9.312l0.064 0.064c1.6 3.36 3.52 6.592 5.76 9.6 5.024 6.464 11.552 11.616 18.624 15.68 12.64 6.72 26.944 10.016 41.216 10.208-44.544 12.736-45.856 59.68-36.16 80.448" fill="#006570" ></path><path d="M674.336 776.064c-15.68 28.16-36.864 48.96-63.808 66.56a167.552 167.552 0 0 1-54.944 23.424c-32.832 7.744-61.376 6.976-87.232 1.216a187.488 187.488 0 0 1-88.832-46.944c-12.16-19.648-17.536-62.176 28.608-83.584a14.432 14.432 0 0 1 0.32 1.024l0.736 2.592v0.096l0.128 0.32a92.288 92.288 0 0 0 10.496 23.328 84 84 0 0 0 45.76 37.024l0.928 0.352c12.416 4.32 25.696 6.272 38.816 6.496a162.816 162.816 0 0 0 54.912-9.12l1.44-0.48a152.128 152.128 0 0 0 49.44-28.704l1.024-0.96-0.16 0.16c7.264-6.464 13.76-13.696 19.36-21.664 5.536-7.936 10.304-16.48 13.6-25.6 3.744-8.96 6.08-18.4 7.424-28 1.344-9.6 1.344-19.328 0.544-28.96v-0.224a163.584 163.584 0 0 0-2.176-13.984 140.928 140.928 0 0 0-3.488-13.728l-0.064-0.352a184.032 184.032 0 0 0-10.368-25.92h0.064c-1.12-2.144-2.176-4.384-3.36-6.496l-2.112-4.096a236.8 236.8 0 0 0-6.72-11.584c-0.736-1.344-1.6-2.624-2.432-3.904l-0.768-1.216-0.224-0.32-0.128-0.16-0.096-0.128v-0.064h0.096l0.32-0.192c8.256-5.344 16.128-11.2 23.68-17.472l0.032 0.032a314.144 314.144 0 0 1 24.736 33.024c1.472 2.24 2.88 4.512 4.224 6.784 0.64 1.152 1.344 2.24 1.984 3.424l2.368 4.256 1.856 3.552a132.416 132.416 0 0 1 3.488 7.04c25.76 55.328 17.12 105.344-9.472 153.152" fill="#00ABB6" ></path><path d="M506.016 375.072H504.32l-0.128 0.032h-1.536l-1.504 0.096h-0.416c-0.416 0.064-0.864 0.064-1.28 0.096a39.04 39.04 0 0 0-2.24 0.224 70.528 70.528 0 0 0-7.36 0.96h-0.096a74.272 74.272 0 0 0-3.072 0.608h-0.128l-1.472 0.32a16.64 16.64 0 0 0-1.408 0.384h-0.096l-1.472 0.384a3.968 3.968 0 0 0-0.704 0.224 3.68 3.68 0 0 0-0.96 0.288c-0.096 0-0.192 0-0.32 0.064a3.264 3.264 0 0 0-0.64 0.192c-0.64 0.128-1.184 0.32-1.76 0.512l-2.4 0.864-0.448 0.16a33.12 33.12 0 0 0-3.168 1.216l-2.432 1.056a74.976 74.976 0 0 0-7.232 3.84c-0.128 0-0.224 0.128-0.352 0.16a33.824 33.824 0 0 0-2.112 1.376c-0.064 0.064-0.224 0.096-0.32 0.192a68.288 68.288 0 0 0-4.416 3.2c-0.96 0.8-1.92 1.536-2.816 2.4a3.328 3.328 0 0 0-0.704 0.64 13.856 13.856 0 0 0-6.528 7.2 29.952 29.952 0 0 0-1.984 2.848l-0.192 0.256c-0.576 0.896-1.056 1.824-1.536 2.72a11.52 11.52 0 0 0-0.8 1.696 9.312 9.312 0 0 0-0.672 1.312l-0.352 0.896c-0.096 0.096-0.096 0.192-0.128 0.256l-0.16 0.32a40.192 40.192 0 0 0-1.952 6.336l-0.448 2.432a89.664 89.664 0 0 0-1.92 30.08l-0.16 0.16s-86.656-7.904-93.888-9.728a132.8 132.8 0 0 1-2.176-0.672 10.848 10.848 0 0 1-1.056-0.448 21.824 21.824 0 0 1-8.32-5.44 21.888 21.888 0 0 1-3.328-4.16 25.248 25.248 0 0 1-2.944-23.264l0.32-1.248 0.288-1.184 0.032-0.096 0.224-0.96 0.256-1.088 0.576-2.4 0.16-0.48 0.32-1.408 0.864-3.168c0.96-3.104 2.08-7.104 3.616-11.776l1.792-5.44c0.384-0.896 0.736-1.888 1.088-2.88l1.92-4.896c0.64-1.664 1.344-3.296 2.016-4.896l2.176-4.896 1.728-3.584 1.152-2.304a144.704 144.704 0 0 1 14.88-23.424c-15.744 69.824 100.608 40.768 149.824 43.84" fill="#008B92" ></path><path d="M574.944 435.616a33.696 33.696 0 0 1-4.64 11.104c-0.576 0.864-1.152 1.856-1.76 2.688a31.264 31.264 0 0 0-0.96-7.328c-2.688-11.04-10.976-19.84-20.352-25.824a79.488 79.488 0 0 0-8.736-4.768l-0.64-0.256a72.64 72.64 0 0 0-11.136-3.904c-0.544-0.192-1.088-0.32-1.6-0.448l-2.24-0.544c-0.128-0.032-0.224-0.032-0.32-0.096 6.816-0.224 21.504-0.736 23.68-9.984 1.568-6.656-3.136-12.032-7.424-15.2h-0.032a22.368 22.368 0 0 0-5.024-2.784l1.92 0.544a35.552 35.552 0 0 1 4.672 1.696l2.304 0.96c7.264 3.36 13.792 8.32 19.392 14.016 11.296 11.648 16.32 25.184 12.896 40.128" fill="#048188" ></path><path d="M609.28 471.744c-1.728 2.272-3.616 4.544-5.44 6.816-5.856 6.72-11.904 13.12-18.304 19.2-1.152 1.056-2.304 2.08-3.52 3.104-1.344 1.152-2.688 2.336-4.096 3.456l-5.44 4.384c0 0.096-0.128 0.128-0.192 0.224l-0.672 0.48a297.472 297.472 0 0 1-9.632 7.296l-1.28 0.896-1.888 1.312c-0.832 0.64-1.696 1.216-2.56 1.824l-4.48 3.072-3.808 2.56-1.92 1.28c-0.64 0.48-1.312 0.864-1.92 1.28H544l-3.84 2.464c-1.312 0.864-2.592 1.696-3.904 2.496h-0.064a625.984 625.984 0 0 1-23.776 13.92c-0.096 0-0.224 0.064-0.352 0.16l-3.84 2.08c-1.344 0.768-2.72 1.536-4.128 2.24l-0.192 0.064c-5.472 2.88-11.04 5.664-16.576 8.352l-0.128 0.064-8.064 3.84-2.432 1.184-5.312 2.432a282.88 282.88 0 0 1-6.464 2.88l-1.376 0.64-0.64 0.256c-1.472 0.64-2.976 1.344-4.48 1.952-1.28 0.544-2.464 1.024-3.712 1.6l-0.8 0.32c-3.008 1.28-6.048 2.528-9.056 3.712-0.448 0.192-0.96 0.352-1.376 0.576a10.848 10.848 0 0 0-1.056 0.448l-1.44 0.544a67.776 67.776 0 0 1-2.656 1.12l-3.648 1.376-4.736 1.792-4.096 1.536-2.144 0.736-2.112 0.8-0.096-5.184v-56.416s11.904-3.36 29.216-9.408a538.24 538.24 0 0 0 52.96-21.728c0.832-0.32 1.664-0.8 2.464-1.152 10.24 8.64 40.96 23.904 103.104-22.88" fill="#008B92" ></path><path d="M572.64 664.864c-1.152 5.6-3.552 10.88-6.496 15.808a79.936 79.936 0 0 1-35.84 30.816c-7.328 3.168-15.264 4.48-23.264 4.8a92.16 92.16 0 0 1-46.4-9.664 61.824 61.824 0 0 1-18.624-14.88 64.192 64.192 0 0 1-10.976-21.12l0.64 2.048v0.096l0.064 0.224 0.064 0.032 0.64 1.92c-0.16-0.544-0.416-1.056-0.608-1.632 1.024 3.2 2.176 6.304 3.584 9.312l0.064 0.064c1.6 3.36 3.52 6.592 5.76 9.6 5.024 6.464 11.552 11.616 18.624 15.68 12.64 6.72 26.944 10.016 41.216 10.208h0.032c2.016 0.064 4 0.032 5.984-0.064 8.096-0.32 16.32-1.824 23.872-5.056a77.568 77.568 0 0 0 28.416-21.504c0.64-0.736 1.248-1.504 1.824-2.304l0.576-0.704a65.12 65.12 0 0 0 6.432-10.496 48.608 48.608 0 0 0 4-10.688c0.224-0.96 0.352-1.952 0.544-2.976a53.952 53.952 0 0 0-0.128 0.48" fill="#0AE0DA" ></path><path d="M652.896 664.64v-0.224a163.616 163.616 0 0 0-2.176-13.984 140.928 140.928 0 0 0-3.488-13.728l-0.064-0.32a184.032 184.032 0 0 0-10.368-25.92h0.064c-1.12-2.176-2.176-4.384-3.36-6.528l-2.112-4.096a236.8 236.8 0 0 0-5.12-8.896l-1.6-2.688c-0.736-1.344-1.6-2.624-2.432-3.904l-0.768-1.216 0.512 0.96 7.04 12.48c2.688 4.608 4.864 9.44 7.04 14.24 3.84 8.544 7.168 17.312 9.696 26.304 1.28 4.512 2.368 9.056 3.264 13.632 0.896 4.512 1.408 9.184 1.92 13.76v0.32c0.608 9.44 0.48 19.04-0.96 28.416a123.104 123.104 0 0 1-7.552 27.392 106.624 106.624 0 0 1-13.408 24.96c-5.44 7.744-11.904 14.784-19.008 21.056l-0.128 0.192-0.64 0.512-0.128 0.096c-14.208 12.256-30.752 21.76-48.48 28.16a161.728 161.728 0 0 1-55.296 9.728 126.048 126.048 0 0 1-40.416-6.72 89.92 89.92 0 0 1-13.856-6.208 81.888 81.888 0 0 1-21.824-17.6 93.376 93.376 0 0 1-15.264-23.744c-1.696-3.424-2.88-7.2-4.352-10.784-0.256-0.96-0.544-1.792-0.8-2.624a30.368 30.368 0 0 1-0.768-2.656l-0.736-2.72a56.928 56.928 0 0 0 1.184 5.056l-0.256-0.896-0.256-1.056a72.896 72.896 0 0 1-0.768-2.944l-0.448-1.984-0.032-0.32-0.224-0.64 0.032 0.416c0.064 0.16 0.064 0.32 0.096 0.512a81.536 81.536 0 0 0 1.792 7.328l0.736 2.624v0.064l0.128 0.32c1.28 3.552 2.368 7.136 3.968 10.656a95.904 95.904 0 0 0 6.528 12.672 84 84 0 0 0 45.76 37.024l0.928 0.352c12.416 4.32 25.696 6.272 38.816 6.496a162.816 162.816 0 0 0 54.912-9.12l1.44-0.48a152.128 152.128 0 0 0 49.44-28.672l0.992-0.992-0.128 0.192c7.264-6.496 13.76-13.76 19.36-21.696 5.536-7.936 10.304-16.48 13.6-25.6 3.744-8.96 6.08-18.4 7.424-28 1.344-9.6 1.344-19.328 0.544-28.96M641.824 393.472c0.128 12.288-2.048 24.608-5.984 36.16l-0.192 0.608-0.032 0.064a137.152 137.152 0 0 1-12.672 26.4 171.52 171.52 0 0 1-8.128 12.16c-0.352 0.448-0.64 0.896-1.024 1.344a227.84 227.84 0 0 1-8.064 10.048l-0.096 0.064a242.112 242.112 0 0 1-25.728 25.248c-9.184 7.808-18.88 14.944-28.864 21.632-20.064 13.28-41.536 24.32-63.648 33.568h-0.032v-0.064c2.784-1.312 5.6-2.688 8.32-4.064l8.32-4.256 0.128-0.096c1.408-0.704 2.784-1.472 4.16-2.24 1.312-0.64 2.592-1.376 3.904-2.08a290.464 290.464 0 0 0 24-14.08h0.096l3.872-2.496c1.28-0.8 2.56-1.6 3.84-2.464h0.128c0.608-0.416 1.28-0.8 1.92-1.28l5.888-3.84c1.472-0.96 2.912-1.984 4.352-3.072a109.056 109.056 0 0 0 5.696-4.032 297.472 297.472 0 0 0 9.6-7.296l0.704-0.48c0.064-0.096 0.16-0.128 0.192-0.224l5.44-4.384c1.408-1.12 2.752-2.304 4.128-3.456l3.52-3.072c6.4-6.144 12.416-12.48 18.272-19.232 1.824-2.24 3.712-4.544 5.44-6.784a193.888 193.888 0 0 0 11.424-16.448c4.96-8.224 9.12-16.864 12.224-25.92l0.16-0.448v-0.096l1.376-4.096c0.032-0.32 0.128-0.576 0.224-0.896a81.28 81.28 0 0 0 1.216-4.704l0.672-2.88a100.672 100.672 0 0 0-4.576-57.92 134.816 134.816 0 0 0-17.344-31.52 157.696 157.696 0 0 0-24.448-26.624l-0.032-0.064a122.272 122.272 0 0 0-8.256-6.304c-2.88-2.016-5.6-4.16-8.576-6.016-4.16-2.336-8.256-4.864-12.544-7.04a60.8 60.8 0 0 0-5.984-2.752c-1.28-0.672-2.624-1.28-3.936-1.824-0.192-0.064-0.32-0.16-0.512-0.192-4.96-2.048-10.112-3.584-15.232-5.312l-4.448-1.088-5.792-1.408a75.648 75.648 0 0 0-7.744-1.44 195.776 195.776 0 0 0-44.672-1.984c-6.72 0.48-13.44 1.248-20.064 2.304l-0.768 0.128c-0.032 0.064-0.096 0.064-0.128 0.064l-0.448 0.064a164 164 0 0 0-20.16 4.736c13.408-4.416 27.456-6.944 41.472-8.288 14.08-1.312 28.352-1.152 42.432 0.8 3.52 0.576 7.04 0.896 10.464 1.76l10.4 2.368c6.72 2.176 13.632 4.064 20.064 7.168 6.656 2.528 12.768 6.272 18.976 9.728 3.008 1.856 5.824 4.096 8.736 6.112 2.944 2.048 5.76 4.096 8.576 6.528L592 298.24c7.488 6.4 14.336 13.568 20.512 21.248l0.128 0.192c1.248 1.568 2.432 3.072 3.616 4.672l0.64 0.832a104.032 104.032 0 0 1 6.88 10.24c1.28 2.08 2.464 4.16 3.616 6.336l-0.8-1.6c3.2 5.504 5.952 11.2 8.224 17.184 4.384 11.52 6.816 23.808 7.008 36.16" fill="#0AE0DA" ></path><path d="M432.672 652.288l-0.32 1.12-0.288 1.056-0.224 0.896a7.744 7.744 0 0 0-0.32 1.472v0.096l-0.032 0.064a1.6 1.6 0 0 0-0.096 0.512l-0.032 0.128a2.24 2.24 0 0 0-0.096 0.544 1.28 1.28 0 0 0-0.096 0.384v0.128l-0.032 0.128-0.128 0.8a30.08 30.08 0 0 0-0.352 5.344c0 1.76 0.128 3.52 0.32 5.312v0.16l-0.288 0.16a6.528 6.528 0 0 0-0.64 0.416l-0.16 0.128-0.544 0.32h-0.032c-0.48 0.32-0.96 0.704-1.472 0.992a54.848 54.848 0 0 0-12.032 11.68c-0.384 0.544-0.832 1.088-1.184 1.696a27.104 27.104 0 0 0-2.56 3.968 62.336 62.336 0 0 0-4.544 10.048 46.912 46.912 0 0 0-2.144 9.184 47.104 47.104 0 0 0-0.352 10.816v0.064l0.096 1.056c0 0.352 0 0.704 0.064 1.056l0.32 2.048 0.032 0.352 0.384 2.272v0.48c0.064 0.096 0.064 0.192 0.064 0.288l0.448 2.016 0.064 0.096v0.128l0.16 0.736a81.536 81.536 0 0 0 1.472 6.304c-46.144 21.408-40.768 63.936-28.608 83.584a202.304 202.304 0 0 1-13.216-13.632c-26.944-30.656-42.112-66.688-46.112-107.328a29.92 29.92 0 0 1 2.496-16.224c3.328-6.176 7.136-12 11.52-17.504l98.464-13.344z" fill="#05D8D8" ></path><path d="M704 438.24a162.56 162.56 0 0 1-13.76 65.824 166.816 166.816 0 0 1-35.84 52.16l-2.4 1.92-1.28 0.96c-0.224 0.192-0.448 0.384-0.736 0.544l-3.04 2.24-1.28 0.864-0.192 0.128-1.088 0.736c-2.688 1.824-5.504 3.52-8.384 5.12h-0.032c-1.728 0.96-3.456 1.888-5.248 2.784-0.896 0.384-1.792 0.832-2.656 1.248l-1.28 0.608a56.544 56.544 0 0 1-5.152 2.176l-2.496 1.024-0.576 0.224a141.184 141.184 0 0 1-91.744 3.2l-4.384-1.376-0.8-0.288H521.6c-0.032 0-0.096 0-0.096-0.064a98.72 98.72 0 0 1-3.328-1.152l-4.224-1.664c-1.408-0.64-2.784-1.184-4.16-1.824a38.208 38.208 0 0 1-2.752-1.28l-0.8-0.352-1.6-0.8c-0.096-0.096-0.224-0.128-0.32-0.192a13.376 13.376 0 0 1-1.6-0.8c-0.096 0-0.128-0.064-0.224-0.128a135.904 135.904 0 0 1-9.504-5.536l-0.544-0.32v-0.032a199.232 199.232 0 0 1-5.056-3.424 382.016 382.016 0 0 0 63.68-33.6c9.952-6.656 19.648-13.792 28.832-21.6 9.216-7.744 17.856-16.32 25.728-25.248l0.096-0.064a227.84 227.84 0 0 0 8.064-10.048c33.376-27.36 57.92-66.4 67.232-116.416a178.304 178.304 0 0 1 8.576 16.96c0.416 0.96 0.864 1.888 1.216 2.816 0.32 0.64 0.576 1.312 0.8 1.984l1.472 3.648 1.376 3.84 1.28 3.84a132.96 132.96 0 0 1 3.232 11.2c0.32 1.248 0.64 2.432 0.896 3.68a119.712 119.712 0 0 1 1.92 9.696 96.032 96.032 0 0 1 1.056 7.552l0.32 2.752c0.32 3.36 0.64 6.816 0.704 10.24 0.096 2.048 0.128 4.16 0.128 6.24" fill="#00B9B9" ></path><path d="M683.808 622.944c-16.32-11.072-34.528-7.04-36.64 13.408a184.032 184.032 0 0 0-10.368-25.92h0.064c-1.12-2.144-2.176-4.384-3.36-6.496l-2.112-4.096a236.8 236.8 0 0 0-6.72-11.584c-0.736-1.344-1.6-2.624-2.432-3.904l-0.768-1.216-0.224-0.32-0.128-0.16-0.096-0.128v-0.064h0.096l0.32-0.192c8.256-5.344 16.128-11.2 23.68-17.472l0.032 0.032a314.144 314.144 0 0 1 22.464 29.536l2.272 3.488c1.472 2.24 2.848 4.512 4.224 6.784 0.64 1.152 1.344 2.24 1.984 3.424l2.368 4.256 1.856 3.552a132.416 132.416 0 0 1 3.488 7.04" fill="#03C7CB" ></path></symbol><symbol id="bf-skp" viewBox="0 0 1024 1024"><path d="M690.752 0a32 32 0 0 1 22.624 9.376l173.248 173.248a32 32 0 0 1 9.376 22.624V992a32 32 0 0 1-32 32H160a32 32 0 0 1-32-32V32a32 32 0 0 1 32-32h530.752z" fill="#FFFFFF" ></path><path d="M690.752 0a32 32 0 0 1 22.624 9.376l173.248 173.248a32 32 0 0 1 9.376 22.624V992a32 32 0 0 1-32 32H160a32 32 0 0 1-32-32V32a32 32 0 0 1 32-32h530.752zM672 32H160v960h704V224h-160a32 32 0 0 1-32-32V32z" fill="#E8445A" ></path><path d="M597.824 323.136l169.472 137.184-35.68 314.88-342.56 96.768-85.6-232.224-48.16-274.4 342.528-42.208z m-129.056 331.84l-98.688 22.304 21.44 69.12 98.656-28.096-21.44-63.36z m63.04-150.08l-200.416 27.008 12.512 57.408 124.864-17.6 15.488 43.392 129.6-23.456-82.048-86.784z m68.992-151.264l-300.288 36.352 12.48 57.408 233.088-28.128 73.856 76.224 128.384-23.456-147.52-118.4z" fill="#E8445A" ></path></symbol></svg>',v=(v=document.getElementsByTagName("script"))[v.length-1].getAttribute("data-injectcss");if(v&&!l.__iconfont__svg__cssinject__){l.__iconfont__svg__cssinject__=!0;try{document.write("<style>.svgfont {display: inline-block;width: 1em;height: 1em;fill: currentColor;vertical-align: -0.1em;font-size:16px;}</style>");}catch(l){console&&console.log(l);}}function f(){p||(p=!0,h());}a=function(){var l,a,c;(c=document.createElement("div")).innerHTML=d,d=null,(a=c.getElementsByTagName("svg")[0])&&(a.setAttribute("aria-hidden","true"),a.style.position="absolute",a.style.width=0,a.style.height=0,a.style.overflow="hidden",l=a,(c=document.body).firstChild?(a=c.firstChild).parentNode.insertBefore(l,a):c.appendChild(l));},document.addEventListener?~["complete","loaded","interactive"].indexOf(document.readyState)?setTimeout(a,0):(c=function(){document.removeEventListener("DOMContentLoaded",c,!1),a();},document.addEventListener("DOMContentLoaded",c,!1)):document.attachEvent&&(h=a,t=l.document,p=!1,(i=function(){try{t.documentElement.doScroll("left");}catch(l){return void setTimeout(i,50)}f();})(),t.onreadystatechange=function(){"complete"==t.readyState&&(t.onreadystatechange=null,f());});}(window);

var DomUtil2D = /** @class */ (function () {
    function DomUtil2D() {
    }
    DomUtil2D.create = function (tagName, idName, container) {
        var el = document.createElement(tagName);
        el.id = idName || '';
        el.style.position = 'absolute';
        el.style.width = '100%';
        el.style.top = 0;
        el.style.bottom = 0;
        if (container) {
            container.appendChild(el);
        }
        return el;
    };
    DomUtil2D.remove = function (el) {
        var parent = el.parentNode;
        if (parent) {
            parent.removeChild(el);
        }
    };
    DomUtil2D.setPosition = function (el, point) {
        el.position = point;
        el.style.left = point.x + 'px';
        el.style.top = point.y + 'px';
    };
    DomUtil2D.setOpacity = function (el, value) {
        if ('opacity' in el.style) {
            el.style.opacity = value;
        }
        else if ('filter' in el.style) {
            DomUtil2D._setOpacityIE(el, value);
        }
    };
    DomUtil2D._setOpacityIE = function (el, value) {
        var filter = false, filterName = 'DXImageTransform.Microsoft.Alpha';
        // filters collection throws an error if we try to retrieve a filter that doesn't exist
        try {
            filter = el.filters.item(filterName);
        }
        catch (e) {
            // don't set opacity to 1 if we haven't already set an opacity,
            // it isn't needed and breaks transparent pngs.
            if (value === 1) {
                return;
            }
        }
        value = Math.round(value * 100);
        if (filter) {
            filter.Enabled = (value !== 100);
            filter.Opacity = value;
        }
        else {
            el.style.filter += ' progid:' + filterName + '(opacity=' + value + ')';
        }
    };
    /**
     * split string on whitespace
     * @param {String} str
     * @returns {Array} words
     */
    DomUtil2D.splitStr = function (str) {
        return str.trim().split(/\s+/g);
    };
    /**
     * get the container offset relative to client
     * @param {object} domElement
     * @returns {object}
     */
    DomUtil2D.getContainerOffsetToClient = function (domElement) {
        var offsetObj;
        // 获取相对于视口(客户区域)的偏移量
        var getOffsetSum = function (ele) {
            var top = 0, left = 0;
            // 遍历父元素,获取相对与document的偏移量
            while (ele) {
                top += ele.offsetTop;
                left += ele.offsetLeft;
                ele = ele.offsetParent;
            }
            // 只处理document的滚动条(一般也用不着内部滚动条)
            var body = document.body, docElem = document.documentElement;
            //获取页面的scrollTop,scrollLeft(兼容性写法)
            var scrollTop = window.pageYOffset || docElem.scrollTop || body.scrollTop, scrollLeft = window.pageXOffset || docElem.scrollLeft || body.scrollLeft;
            // 减掉滚动距离，获得相对于客户区域的偏移量
            top -= scrollTop;
            left -= scrollLeft;
            return {
                top: top,
                left: left
            };
        }; // 获取相对于视口(客户区域)的偏移量(viewpoint), 不加页面的滚动量(scroll)
        var getOffsetRect = function (ele) {
            // getBoundingClientRect返回一个矩形对象，包含四个属性：left、top、right和bottom。分别表示元素各边与页面上边和左边的距离。
            //注意：IE、Firefox3+、Opera9.5、Chrome、Safari支持，在IE中，默认坐标从(2,2)开始计算，导致最终距离比其他浏览器多出两个像素，我们需要做个兼容。
            var box = ele.getBoundingClientRect();
            var body = document.body, docElem = document.documentElement;
            //获取页面的scrollTop,scrollLeft(兼容性写法)
            //var scrollTop = window.pageYOffset || docElem.scrollTop || body.scrollTop,
            //    scrollLeft = window.pageXOffset || docElem.scrollLeft || body.scrollLeft;
            var clientTop = docElem.clientTop || body.clientTop, clientLeft = docElem.clientLeft || body.clientLeft;
            var top = box.top - clientTop, left = box.left - clientLeft;
            return {
                //Math.round 兼容火狐浏览器bug
                top: Math.round(top),
                left: Math.round(left)
            };
        }; //获取元素相对于页面的偏移
        var getOffset = function (ele) {
            if (ele.getBoundingClientRect) {
                return getOffsetRect(ele);
            }
            else {
                return getOffsetSum(ele);
            }
        };
        if (domElement != document) {
            // 这种方式的目的是为了让外部直接传入clientX,clientY,然后计算出相对父容器的offsetX,offsetY值,
            // 即 offsetX = clientX - offsetV.left, offsetY = clientY - offsetV.top
            var offsetV = getOffset(domElement);
            // domElement.offsetLeft（offsetTop）是相对父容器的偏移量，如果用相对坐标表示，直接传回0
            //offset	: [ domElement.offsetLeft,  domElement.offsetTop ]
            offsetObj = {
                width: domElement.offsetWidth,
                height: domElement.offsetHeight,
                left: offsetV.left,
                top: offsetV.top
            };
        }
        else {
            offsetObj = {
                width: window.innerWidth,
                height: window.innerHeight,
                left: 0,
                top: 0
            };
        }
        return offsetObj;
    };
    /**
     * set css class name
     * @param {String} id
     * @param {String} cssName
     */
    DomUtil2D.setClassName = function (id, cssName) {
        var dom = document.getElementById(id);
        if (dom) {
            dom.className = cssName;
        }
    };
    /**
     * add css class name
     * @param {String} id
     * @param {String} cssName
     */
    DomUtil2D.addClassName = function (id, cssName) {
        var a, b, c;
        var i, j;
        var s = /\s+/;
        var dom = document.getElementById(id);
        if (dom) {
            b = dom;
            if (cssName && typeof cssName == "string") {
                a = cssName.split(s);
                // 如果节点是元素节点，则 nodeType 属性将返回 1。
                // 如果节点是属性节点，则 nodeType 属性将返回 2。
                if (b.nodeType === 1) {
                    if (!b.className && a.length === 1) {
                        b.className = cssName;
                    }
                    else {
                        c = " " + b.className + " ";
                        for (i = 0, j = a.length; i < j; ++i) {
                            c.indexOf(" " + a[i] + " ") < 0 && (c += a[0] + " ");
                        }
                        b.className = c.trim();
                    }
                }
            }
        }
    };
    /**
     * remove css class name
     * @param {String} id
     * @param {String} cssName
     */
    DomUtil2D.removeClassName = function (id, className) {
        var a, b, c;
        var i, j;
        var s = /\s+/;
        var dom = document.getElementById(id);
        if (dom) {
            c = dom;
            if (className && typeof className == "string") {
                a = (className || "").split(s);
                if (c.nodeType === 1 && c.className) {
                    b = (" " + c.className + " ").replace('O', " ");
                    for (i = 0, j = a.length; i < j; i++) {
                        while (b.indexOf(" " + a[i] + " ") >= 0) {
                            b = b.replace(" " + a[i] + " ", " ");
                        }
                    }
                    c.className = className ? b.trim() : "";
                }
            }
        }
    };
    /**
     * show or hide element
     * @param {String} id
     * @param {Boolean} isShow
     */
    DomUtil2D.showOrHideElement = function (id, isShow) {
        var dom = document.getElementById(id);
        if (dom) {
            if (isShow) {
                dom.style.display = "";
            }
            else {
                dom.style.display = "none";
            }
        }
    };
    DomUtil2D.prototype.getStyleString = function (style) {
        var elements = [];
        for (var key in style) {
            var val = style[key];
            elements.push(key);
            elements.push(':');
            elements.push(val);
            elements.push('; ');
        }
        return elements.join('');
    };
    DomUtil2D.cloneStyle = function (style) {
        var clone = {};
        for (var key in style) {
            clone[key] = style[key];
        }
        return clone;
    };
    DomUtil2D.removeStyleAttribute = function (style, attrs) {
        if (!Array.isArray(attrs)) {
            attrs = [attrs];
        }
        attrs.forEach(function (key) {
            if (key in style) {
                delete style[key];
            }
        });
    };
    DomUtil2D.trimRight = function (text) {
        if (text.length === 0) {
            return "";
        }
        var lastNonSpace = text.length - 1;
        for (var i = lastNonSpace; i >= 0; --i) {
            if (text.charAt(i) !== ' ') {
                lastNonSpace = i;
                break;
            }
        }
        return text.substr(0, lastNonSpace + 1);
    };
    DomUtil2D.trimLeft = function (text) {
        if (text.length === 0) {
            return "";
        }
        var firstNonSpace = 0;
        for (var i = 0; i < text.length; ++i) {
            if (text.charAt(i) !== ' ') {
                firstNonSpace = i;
                break;
            }
        }
        return text.substr(firstNonSpace);
    };
    DomUtil2D.matchesSelector = function (domElem, selector) {
        if (domElem.matches) {
            return domElem.matches(selector);
        }
        if (domElem.matchesSelector) {
            return domElem.matchesSelector(selector);
        }
        if (domElem.webkitMatchesSelector) {
            return domElem.webkitMatchesSelector(selector);
        }
        if (domElem.msMatchesSelector) {
            return domElem.msMatchesSelector(selector);
        }
        if (domElem.mozMatchesSelector) {
            return domElem.mozMatchesSelector(selector);
        }
        if (domElem.oMatchesSelector) {
            return domElem.oMatchesSelector(selector);
        }
        if (domElem.querySelectorAll) {
            var matches = (domElem.document || domElem.ownerDocument).querySelectorAll(selector), i = 0;
            // while (matches[i] && matches[i] !== element) i++;
            while (matches[i] && matches[i] !== domElem)
                i++;
            return matches[i] ? true : false;
        }
        return false;
    };
    DomUtil2D.toTranslate3d = function (x, y) {
        return 'translate3d(' + x + 'px,' + y + 'px,0)';
    };
    DomUtil2D.setCursorStyle = function (element, direction) {
        var cursor;
        switch (direction) {
            case 'n':
            case 's':
                cursor = 'ns-resize';
                break;
            case 'w':
            case 'e':
                cursor = 'ew-resize';
                break;
            case 'ne':
            case 'sw':
                cursor = 'nesw-resize';
                break;
            case 'nw':
            case 'se':
                cursor = 'nwse-resize';
                break;
        }
        element.style.cursor = cursor;
    };
    DomUtil2D.debounce = function (func, timeout) {
        var _this = this;
        if (timeout === void 0) { timeout = 500; }
        var timer;
        return function () {
            var args = [];
            for (var _i = 0; _i < arguments.length; _i++) {
                args[_i] = arguments[_i];
            }
            if (timer)
                clearTimeout(timer);
            timer = setTimeout(function () {
                func.apply(_this, args);
                timer = undefined;
            }, timeout);
        };
    };
    return DomUtil2D;
}());

var SNS$1 = 'Glodon.Bimface.Module.Linkage2D3D.Editor';
var SNSM$1 = 'Glodon.Bimface.Module.ModuleManager';
/**
 * @classdesc 类：图模联动编辑器
 * @class Glodon.Bimface.Module.Linkage2D3D.Editor
 * @description 构造图模联动编辑器
 * @constructs Glodon.Bimface.Module.Linkage2D3D.Editor
 * @param {Glodon.Bimface.Module.Linkage2D3D.EditorConfig} editorConfig 图模联动编辑器的配置项
 */
var Editor = /** @class */ (function (_super) {
    __extends(Editor, _super);
    function Editor(config) {
        var _this = this;
        SDM.send(SNS$1, "bf_c_linkage2D3DEditor_new");
        SDM.send(SNSM$1, "bf_c_loadModule_new");
        _this = _super.call(this, config) || this;
        _this._webApplication3D = config.webApplication3D;
        _this._data = config;
        _this.init();
        _this.getResourceListHandler = config.getResourceListHandler;
        _this.getViewTokenHandler = config.getViewTokenHandler;
        return _this;
    }
    Editor.prototype.init = function () {
        return __awaiter(this, void 0, void 0, function () {
            var renderItemMap, response, linkedDrawings, unlinkedDrawings, orderDrawings, viewer2dDom, model_1, drawings, drawingJson_1, unlinkedDrawingsData, modelMetaData, _a, _loop_1, i, setDrawingData_1, initDrawingDom, initDrawingDom, _b, initModule, linkModule, addResource, linkedFileListToolbar, viewerDom, self, showLeftSubToolbar, loadDrawingFn, exchangeDrawing, deboucneExchaning, hideLayout, debounceExchangLayout;
            var _this = this;
            return __generator(this, function (_c) {
                switch (_c.label) {
                    case 0:
                        renderItemMap = render("\n      <div class=\"bfmodule-linkage-editor\">\n        <div class=\"bfmodule-linkage-editor-leftside\">\n          <div ref=\"initModule\" class=\"bfmodule-linkage-editor-panel\"></div>\n          <div ref=\"linkModule\" class=\"bfmodule-linkage-editor-panel hide\"></div>\n        </div>\n        <div class=\"bfmodule-linkage-editor-viewer-container\">\n          <div ref=\"viewer2dDom\" class=\"bfmodule-linkage-editor-viewer\">            <div ref=\"selectList\" class=\"hide\"></div>\n          </div>\n          <div ref=\"viewerDom\" class=\"bfmodule-linkage-editor-viewer\"></div>\n        </div>\n      </div>\n    ", this.getDomElement(), this);
                        response = BaseModule._currentModuleData;
                        linkedDrawings = [];
                        unlinkedDrawings = [];
                        orderDrawings = [];
                        viewer2dDom = renderItemMap.viewer2dDom;
                        if (!response.moduleData) return [3 /*break*/, 2];
                        model_1 = response.moduleData.sources.find(function (file) { return file.id == response.moduleData.data.model.id; });
                        drawings = response.moduleData.data.drawings[0].list;
                        drawingJson_1 = function (response, model, drawing) {
                            return {
                                databagId: response.moduleData.sources.find(function (source) { return source.id == drawing.fileId; }).databagId,
                                isSupportComponentProperty: response.isSupportComponentProperty,
                                isSupportDrawing: response.isSupportDrawing,
                                isSupportFamilyTypeList: response.isSupportFamilyTypeList,
                                isSupportMaterialProperty: response.isSupportMaterialProperty,
                                isSupportMiniMap: response.isSupportMiniMap,
                                isSupportModelTree: response.isSupportModelTree,
                                isSupportRoomArea: response.isSupportRoomArea,
                                isSurpportWalk: response.isSurpportWalk,
                                jsSDKVersion: response.jsSDKVersion,
                                loadMode: response.loadMode,
                                modelId: drawing.fileId,
                                modelType: model.type,
                                name: drawing.fileName,
                                viewId: drawing.viewId,
                                renderType: response.renderType,
                                renderVersion: response.renderVersion,
                                sceneJsonInfo: response.sceneJsonInfo,
                                shell: response.shell,
                                split: response.split,
                                subRenders: response.subRenders,
                                type: response.type,
                                workerType: "dwg-transfer",
                                order: drawing.order,
                            };
                        };
                        drawings && drawings.length > 0 && drawings.map(function (drawing) {
                            linkedDrawings.push(drawingJson_1(response, model_1, drawing));
                        });
                        unlinkedDrawingsData = response.moduleData.data.drawings[0].unlinkedList;
                        unlinkedDrawingsData && unlinkedDrawingsData.length > 0 && unlinkedDrawingsData.map(function (drawing) {
                            unlinkedDrawings.push(drawingJson_1(response, model_1, drawing));
                        });
                        modelMetaData = {
                            databagId: model_1.databagId,
                            isSupportComponentProperty: response.isSupportComponentProperty,
                            isSupportDrawing: response.isSupportDrawing,
                            isSupportFamilyTypeList: response.isSupportFamilyTypeList,
                            isSupportMaterialProperty: response.isSupportMaterialProperty,
                            isSupportMiniMap: response.isSupportMiniMap,
                            isSupportModelTree: response.moduleData.data.drawings[0].displayMode.toolbar.modelToolbars.indexOf('ModelTree') > -1,
                            isSupportRoomArea: true,
                            isSurpportWalk: false,
                            jsSDKVersion: response.jsSDKVersion,
                            loadMode: response.loadMode,
                            modelId: model_1.id,
                            modelType: model_1.type,
                            name: response.name,
                            renderType: response.renderType,
                            renderVersion: response.renderVersion,
                            sceneJsonInfo: response.sceneJsonInfo,
                            shell: response.shell,
                            split: response.split,
                            subRenders: response.subRenders,
                            type: response.type,
                            viewType: "3DView",
                            workerType: response.workerType,
                        };
                        _a = this;
                        return [4 /*yield*/, this._initApp({
                                domElement: renderItemMap.viewerDom.getElement(),
                                viewMetaData: modelMetaData,
                                modelButtons: this._data.modelButtons,
                                drawingButtons: this._data.drawingButtons,
                            })];
                    case 1:
                        _a._applicationModel = _c.sent();
                        _loop_1 = function (i) {
                            var tmpLink = linkedDrawings.find(function (drawing) { return drawing.order == i; });
                            if (tmpLink) {
                                orderDrawings.push(tmpLink);
                            }
                            else {
                                var tmpUnlink = unlinkedDrawings.find(function (drawing) { return drawing.order == i; });
                                tmpUnlink && orderDrawings.push(tmpUnlink);
                            }
                        };
                        for (i = 0; i < linkedDrawings.length + unlinkedDrawings.length; i++) {
                            _loop_1(i);
                        }
                        // 有图纸则适配，否则视为初始化操作
                        if (linkedDrawings.length > 0 || unlinkedDrawings.length > 0) {
                            setDrawingData_1 = function () {
                                window.requestAnimationFrame(function () {
                                    _this.setCurrentData(response.moduleData.data.drawings);
                                    _this.hideModelPlugins();
                                    // 兼容初始时未包含序列号的情况
                                    eventBus.trigger(EditorEvent$1.AddResourceByViewToken, orderDrawings.length > 0 ? orderDrawings : linkedDrawings);
                                });
                            };
                            // 根据是否初始化模型而初始化图纸
                            if (this._webApplication3D) {
                                setDrawingData_1();
                            }
                            else {
                                this.getViewer3D().addEventListener('ModelAdded', function () {
                                    setDrawingData_1();
                                    _this.getViewer3D().getModel(_this.getViewer3D().getModel().modelId).getBoundingBox(_this.getViewer3D().getModel().modelId, function (data) {
                                        _this.getApp3D().modelMaxHeight = data.currentBoundingBox.max.z;
                                        _this.getApp3D().modelMinHeight = data.currentBoundingBox.min.z;
                                    });
                                });
                            }
                        }
                        else {
                            initDrawingDom = new InitDrawingDom(this);
                            viewer2dDom.append(initDrawingDom.getDomElement());
                            if (response.moduleData.data.drawings[0].displayMode.toolbar.drawingToolbars.length != 3
                                || response.moduleData.data.drawings[0].displayMode.toolbar.modelToolbars.length != 3) { // 仅包含设置
                                if (this._webApplication3D) {
                                    this.setCurrentData(response.moduleData.data.drawings);
                                    this.hideModelPlugins();
                                }
                                else {
                                    this.getViewer3D().addEventListener('ModelAdded', function () {
                                        requestAnimationFrame(function () {
                                            _this.setCurrentData(response.moduleData.data.drawings);
                                            _this.hideModelPlugins();
                                        });
                                    });
                                }
                            }
                        }
                        return [3 /*break*/, 4];
                    case 2:
                        initDrawingDom = new InitDrawingDom(this);
                        viewer2dDom.append(initDrawingDom.getDomElement());
                        _b = this;
                        return [4 /*yield*/, this._initApp({ domElement: renderItemMap.viewerDom.getElement(), viewToken: this.getModuleData().viewToken })];
                    case 3:
                        _b._applicationModel = _c.sent();
                        _c.label = 4;
                    case 4:
                        initModule = new InitModule(this);
                        renderItemMap.initModule.append(initModule.getDomElement());
                        linkModule = new LinkModule(this);
                        renderItemMap.linkModule.append(linkModule.getDomElement());
                        addResource = new AddResource(this);
                        renderItemMap.initModule.append(addResource.getDomElement());
                        linkedFileListToolbar = new LinkedFileListToolbar(this);
                        renderItemMap.selectList.append(linkedFileListToolbar.getDomElement());
                        viewerDom = renderItemMap.viewerDom;
                        self = this;
                        showLeftSubToolbar = function () {
                            var showLeftTool = function () {
                                if (self.outLinkModulePage) {
                                    requestAnimationFrame(function () {
                                        self.getApp2D().UI.getToolbar('LeftSubToolbar') && self.getApp2D().UI.getToolbar('LeftSubToolbar').hide();
                                    });
                                    return;
                                }
                                self.getApp2D().UI.getToolbar('LeftSubToolbar').show();
                                self.getApp2D().UI.getToolbar('LeftSubToolbar').element.style.display = 'block';
                                self.getApp2D().UI.getToolbar('LeftSubToolbar').element.style.top = '10px';
                                self.getApp2D().UI.getToolbar('LeftSubToolbar').element.style.bottom = 'inherit';
                                self.getApp2D().UI.getToolbar('LeftSubToolbar').element.addClass('bf-toolbar-select');
                                _this.getViewerDrawing().removeEventListener(window.Glodon.Bimface.Application.WebApplicationDrawingEvent.Loaded, showLeftSubToolbar);
                            };
                            // 根据状态循环判断是否已经结束hide
                            var showAfterHide = function () {
                                if (!self.getApp2D().hideLeftSubToolbar) {
                                    setTimeout(showAfterHide, 9);
                                }
                                else {
                                    showLeftTool();
                                }
                            };
                            // 图纸存在没有重新加载的情况，该情况直接展示
                            if (self.getApp2D().UI.getToolbar('LeftSubToolbar')) {
                                showLeftTool();
                                showAfterHide();
                            }
                        };
                        eventBus.on(EditorEvent$1.InitModule, function (data) {
                            renderItemMap.initModule.removeClass('hide');
                            renderItemMap.linkModule.addClass('hide');
                            viewer2dDom.css('width', '50%');
                            viewerDom.css('width', '50%');
                            var viewid = _this.getViewerDrawing().getCurrentViewId();
                            _this.getViewerDrawing().addEventListener('Loaded', function () {
                                _this.getViewerDrawing().showViewById(viewid);
                            });
                            _this.getViewer3D().getViewer().setHomeView(0);
                            _this.getApp2D().UI.getToolbar('LeftSubToolbar') && _this.getApp2D().UI.getToolbar('LeftSubToolbar').hide();
                            _this.getApp3D().UI.linkedList && _this.getApp3D().linkConfig.showDrawingList && _this.getApp3D().UI.linkedList.getDomElement().removeClass('hide');
                        });
                        // 图纸列表从零加载图纸时加载第一张
                        eventBus.on(EditorEvent$1.AddResourceByViewToken, function (data) {
                            if (!_this.getViewerDrawing() || (_this.getViewerDrawing() && _this.getViewerDrawing().loadedDrawings.length == 0)) {
                                viewer2dDom.css('width', '50%');
                                viewerDom.css('width', '50%');
                                renderItemMap.selectList.getElement().removeClass('hide');
                            }
                            if (viewer2dDom.getElement().getElementsByClassName('bfmodule-linkage-editor-init-drawing-dom').length > 0) {
                                viewer2dDom.getElement().getElementsByClassName('bfmodule-linkage-editor-init-drawing-dom')[0].parentElement.style.display = 'none';
                            }
                        });
                        loadDrawingFn = function (data) {
                            _this.reOpenMiniMap();
                            if (data[0].viewToken) {
                                _this.changeDrawing({ domElement: renderItemMap.viewer2dDom.getElement(), viewToken: data[0].viewToken, id: data[0].id, drawingButtons: _this._data.drawingButtons });
                            }
                            else {
                                _this.changeDrawing({ domElement: renderItemMap.viewer2dDom.getElement(), viewMetaData: data[0], id: data[0].id, drawingButtons: _this._data.drawingButtons });
                            }
                            // 切换layout
                            _this.getViewerDrawing().addEventListener("Loaded", function () {
                                _this.getViewerDrawing().showViewById(data[0].viewId);
                            });
                        };
                        exchangeDrawing = function (data) {
                            if (data.length == 0) {
                                _this.getViewerDrawing() && _this.getViewerDrawing().removeAllDrawings();
                                if (viewer2dDom.getElement().getElementsByClassName('bfmodule-linkage-editor-init-drawing-dom').length > 0
                                    && !(viewer2dDom.getElement().getElementsByClassName('bfmodule-linkage-editor-init-drawing-dom')[0].outerHTML.indexOf('还没有图纸，请先') > 0)) {
                                    viewer2dDom.getElement().getElementsByClassName('bfmodule-linkage-editor-init-drawing-dom')[0].style.display = 'flex';
                                    viewer2dDom.getElement().getElementsByClassName('bfmodule-linkage-editor-init-drawing-dom')[0].parentElement.style.display = 'block';
                                }
                                else {
                                    var initDrawingDom = new InitDrawingDom(_this);
                                    viewer2dDom.getElement().getElementsByClassName('bf-drawing-wrap viewerDrawingWrap')[0].style.display = 'none';
                                    viewer2dDom.append(initDrawingDom.getDomElement());
                                }
                            }
                            else {
                                _this.getViewerDrawing() && _this.getViewerDrawing().removeEventListener(window.Glodon.Bimface.Application.WebApplicationDrawingEvent.Loaded, showLeftSubToolbar);
                                viewer2dDom.getElement().getElementsByClassName('bf-drawing-wrap viewerDrawingWrap').length > 0
                                    && (viewer2dDom.getElement().getElementsByClassName('bf-drawing-wrap viewerDrawingWrap')[0].style.display = 'block');
                                if (!_this.getViewerDrawing()) {
                                    loadDrawingFn(data);
                                }
                                else if (((data[0].id || data[0].modelId) != _this.getViewerDrawing().getDrawing().modelId)
                                    || (_this.getViewerDrawing().loadedDrawings.length == 1 && _this.getViewerDrawing().loadedDrawings[0].initRemoved == true)) {
                                    loadDrawingFn(data);
                                }
                                requestAnimationFrame(function () {
                                    menuManagement(self.getApp3D(), self.getApp2D());
                                });
                            }
                        };
                        deboucneExchaning = DomUtil2D.debounce(exchangeDrawing);
                        eventBus.on(EditorEvent$1.LoadedDrawingChanged, deboucneExchaning);
                        hideLayout = function () {
                            requestAnimationFrame(function () {
                                _this.getApp2D().UI.getToolbar('LeftSubToolbar') && _this.getApp2D().UI.getToolbar('LeftSubToolbar').hide();
                                eventBus.off(EditorEvent$1.LoadedDrawingChanged, debounceExchangLayout);
                                _this.getViewerDrawing().removeEventListener("ViewChanged", hideLayout);
                            });
                        };
                        debounceExchangLayout = DomUtil2D.debounce(function () {
                            _this.getViewerDrawing().addEventListener("ViewChanged", hideLayout);
                        });
                        eventBus.on(EditorEvent$1.LoadedDrawingChanged, debounceExchangLayout);
                        eventBus.on(EditorEvent$1.LinkDrawing, function (data) { return __awaiter(_this, void 0, void 0, function () {
                            return __generator(this, function (_a) {
                                renderItemMap.initModule.addClass('hide');
                                renderItemMap.linkModule.removeClass('hide');
                                viewerDom.css('width', '50%');
                                viewer2dDom.css('width', '50%');
                                // 隐藏初始化页面
                                viewer2dDom.getElement().getElementsByClassName('bfmodule-linkage-editor-init-drawing-dom').length > 0
                                    && (viewer2dDom.getElement().getElementsByClassName('bfmodule-linkage-editor-init-drawing-dom')[0].style.display = 'none');
                                //隐藏图纸上列表且存在视口列表时在左上方展示
                                this.getApp3D().UI.linkedList && this.getApp3D().UI.linkedList.getDomElement().addClass('hide');
                                // 点击链接时在左上方展示layout切换列表（若存在）
                                this.getViewerDrawing().addEventListener(window.Glodon.Bimface.Application.WebApplicationDrawingEvent.Loaded, showLeftSubToolbar);
                                requestAnimationFrame(function () {
                                    showLeftSubToolbar();
                                    menuManagement(self.getApp3D(), self.getApp2D());
                                });
                                return [2 /*return*/];
                            });
                        }); });
                        return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 获取Viewer3D对象
     * @function Glodon.Bimface.Module.Linkage2D3D.Editor.prototype.getViewer3D
     * @return {Glodon.Bimface.Viewer.Viewer3D} Viewer3D对象
     */
    /**
     * 获取ViewerDrawing对象
     * @function Glodon.Bimface.Module.Linkage2D3D.Editor.prototype.getViewerDrawing
     * @return {Glodon.Bimface.Viewer.ViewerDrawing} ViewerDrawing对象
     */
    /**
     * 获取当前的图模联动数据
     * @function Glodon.Bimface.Module.Linkage2D3D.Editor.prototype.getCurrentData
     * @return {Object} 图模联动数据
     */
    /**
     * 销毁图模联动编辑器
     * @function Glodon.Bimface.Module.Linkage2D3D.Editor.prototype.destroy
     */
    Editor.prototype.destroy = function () {
        this._applicationModel && this._applicationModel.destroy();
        _super.prototype.destroy.call(this);
    };
    return Editor;
}(LinkageModule));

var InitPreviewDrawingDom = /** @class */ (function () {
    function InitPreviewDrawingDom(app) {
        this.app = app;
        this.domElement = new Dom().createElement({ elementType: 'div' }).css('height', '100%');
        this.data = {};
        this.renderItemMap = render("\n    <div class=\"bfmodule-linkage-editor-init-drawing-dom\">\n      <div class=\"bfmodule-linkage-editor-init-tip-img\"></div>\n      <div class=\"bfmodule-linkage-editor-init-drawing-text\">\n        \u8FD8\u6CA1\u6709\u56FE\u7EB8\uFF0C\u8BF7\u5148\u5173\u8054\u56FE\u7EB8\n      </div>\n    </div>\n  ", this.domElement, this);
    }
    InitPreviewDrawingDom.prototype.getDomElement = function () {
        return this.domElement;
    };
    return InitPreviewDrawingDom;
}());

var SNS = 'Glodon.Bimface.Module.Linkage2D3D.WebApplication';
var SNSM = 'Glodon.Bimface.Module.ModuleManager';
/**
 * @classdesc 类：图模联动应用
 * @class Glodon.Bimface.Module.Linkage2D3D.WebApplication
 * @description 构造图模联动应用
 * @constructs Glodon.Bimface.Module.Linkage2D3D.WebApplication
 * @param {Glodon.Bimface.Module.Linkage2D3D.WebApplicationConfig} webApplicationConfig 图模联动应用的配置项
 */
var WebApplication = /** @class */ (function (_super) {
    __extends(WebApplication, _super);
    function WebApplication(config) {
        var _this = this;
        SDM.send(SNS, "bf_c_linkage2D3DWebApplication_new");
        SDM.send(SNSM, "bf_c_loadModule_new");
        _this = _super.call(this, config) || this;
        _this._data = config;
        _this.init();
        return _this;
    }
    WebApplication.prototype.init = function () {
        return __awaiter(this, void 0, void 0, function () {
            var response, model, drawings, linkedDrawings, modelMetaData, _a, self, initPreviewDrawingDom;
            var _this = this;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        this.renderItemMap = render("\n      <div class=\"bfmodule-linkage-editor\">\n        <div class=\"bfmodule-linkage-editor-viewer-container\">\n          <div ref=\"viewer2dDom\" class=\"bfmodule-linkage-editor-viewer\">            <div ref=\"selectList\" class=\"hide\"></div>\n          </div>\n          <div ref=\"viewerDom\" class=\"bfmodule-linkage-editor-viewer\"></div>\n        </div>\n      </div>\n    ", this.getDomElement(), this);
                        response = BaseModule._currentModuleData;
                        model = response.moduleData.sources.find(function (file) { return file.id == response.moduleData.data.model.id; });
                        drawings = response.moduleData.data.drawings[0].list;
                        linkedDrawings = [];
                        drawings.map(function (drawing) {
                            linkedDrawings.push({
                                databagId: response.moduleData.sources.find(function (source) { return source.id == drawing.fileId; }).databagId,
                                isSupportComponentProperty: response.isSupportComponentProperty,
                                isSupportDrawing: response.isSupportDrawing,
                                isSupportFamilyTypeList: response.isSupportFamilyTypeList,
                                isSupportMaterialProperty: response.isSupportMaterialProperty,
                                isSupportMiniMap: response.isSupportMiniMap,
                                isSupportModelTree: response.isSupportModelTree,
                                isSupportRoomArea: response.isSupportRoomArea,
                                isSurpportWalk: response.isSurpportWalk,
                                jsSDKVersion: response.jsSDKVersion,
                                loadMode: response.loadMode,
                                modelId: drawing.fileId,
                                modelType: model.type,
                                name: drawing.fileName,
                                viewId: drawing.viewId,
                                renderType: response.renderType,
                                renderVersion: response.renderVersion,
                                sceneJsonInfo: response.sceneJsonInfo,
                                shell: response.shell,
                                split: response.split,
                                subRenders: response.subRenders,
                                type: response.type,
                                workerType: "dwg-transfer"
                            });
                        });
                        modelMetaData = {
                            databagId: model.databagId,
                            isSupportComponentProperty: response.isSupportComponentProperty,
                            isSupportDrawing: response.isSupportDrawing,
                            isSupportFamilyTypeList: response.isSupportFamilyTypeList,
                            isSupportMaterialProperty: response.isSupportMaterialProperty,
                            isSupportMiniMap: response.isSupportMiniMap,
                            isSupportModelTree: response.moduleData.data.drawings[0].displayMode.toolbar.modelToolbars.indexOf('ModelTree') > -1,
                            isSupportRoomArea: true,
                            isSurpportWalk: false,
                            jsSDKVersion: response.jsSDKVersion,
                            loadMode: response.loadMode,
                            modelId: model.id,
                            modelType: model.type,
                            name: response.name,
                            renderType: response.renderType,
                            renderVersion: response.renderVersion,
                            sceneJsonInfo: response.sceneJsonInfo,
                            shell: response.shell,
                            split: response.split,
                            subRenders: response.subRenders,
                            type: response.type,
                            viewType: "3DView",
                            workerType: response.workerType
                        };
                        _a = this;
                        return [4 /*yield*/, this._initApp({
                                domElement: this.renderItemMap.viewerDom.getElement(),
                                viewMetaData: modelMetaData,
                                modelButtons: this._data.modelButtons,
                                drawingButtons: this._data.drawingButtons,
                            })];
                    case 1:
                        _a._applicationMode = _b.sent();
                        this.linkedFileListToolbar = new LinkedFileListToolbar(this);
                        this.renderItemMap.selectList.append(this.linkedFileListToolbar.getDomElement());
                        self = this;
                        if (linkedDrawings.length > 0) {
                            this._data.firstLoaded = false;
                            this.getViewer3D().addEventListener('ModelAdded', function () {
                                window.requestAnimationFrame(function () {
                                    _this.setCurrentData(response.moduleData.data.drawings);
                                    _this.hideModelPlugins();
                                    self.loadDrawing(linkedDrawings[0]);
                                    _this.getViewer3D().getModel(_this.getViewer3D().getModel().modelId).getBoundingBox(_this.getViewer3D().getModel().modelId, function (data) {
                                        _this.getApp3D().modelMaxHeight = data.currentBoundingBox.max.z;
                                        _this.getApp3D().modelMinHeight = data.currentBoundingBox.min.z;
                                    });
                                    var setDrawingList = function () {
                                        self.setLinkOptions(linkedDrawings);
                                        response.moduleData.data.drawings[0].displayMode.toolbar.drawingToolbars.indexOf('DrawingList') > -1
                                            && self.showDrawingList();
                                        self.getViewerDrawing().removeEventListener('Loaded', setDrawingList);
                                        requestAnimationFrame(function () {
                                            _this._data.firstLoaded = true;
                                        });
                                    };
                                    self.getViewerDrawing().addEventListener('Loaded', setDrawingList);
                                });
                            });
                        }
                        else {
                            initPreviewDrawingDom = new InitPreviewDrawingDom(this);
                            this.renderItemMap.viewer2dDom.append(initPreviewDrawingDom.getDomElement());
                        }
                        eventBus.on(EditorEvent$1.LoadedDrawingChanged, function (data) {
                            // 初始化时不调用切换方法，防止出现两次加载
                            if (_this.getViewerDrawing() && _this.getViewerDrawing().loadedDrawings
                                && ((_this.getViewerDrawing().loadedDrawings.length == 1 && _this.getViewerDrawing().loadedDrawings[0].drawing.drawingIndex > -1)
                                    || (_this.getViewerDrawing().loadedDrawings.length > 1))) {
                                _this.reOpenMiniMap();
                                _this._data.firstLoaded && _this.changeDrawing({ domElement: _this.renderItemMap.viewer2dDom.getElement(), viewMetaData: data[0], drawingButtons: _this._data.drawingButtons });
                            }
                            var linkedFile = _this.getApp3D().linkList.find(function (file) { return file.name == data[0].name; });
                            // 仅当图纸切换，而非初始加载，即读取了模型高度后，再进行剖切，重新绑定matrix等操作
                            if (_this.getApp3D().modelMaxHeight) {
                                // 剖切
                                if (linkedFile.allowSection) {
                                    var progress = getSectionProgress(_this.getViewer3D().getUnit(), linkedFile.sectionOffset, linkedFile.isFloor ? linkedFile.selectedHeight : linkedFile.baseHeight, _this.getApp3D().modelMaxHeight, _this.getApp3D().modelMinHeight);
                                    setModelSectionProgress(_this.getViewer3D(), progress);
                                    // 右键开启关闭剖切按钮
                                    requestAnimationFrame(function () {
                                        menuManagement(self.getApp3D(), self.getApp2D());
                                    });
                                }
                                else {
                                    _this.getViewer3D()._sectionPlane && _this.getViewer3D()._sectionPlane.exit();
                                }
                                // matrix
                                var _a = calculateLinkageMatrix(linkedFile.addedDrawingPoints, linkedFile.addedModelPoints), matrix = _a.matrix, rotation = _a.rotation;
                                _this.getViewerDrawing().cameraNode.matrix = matrix;
                                _this.getViewerDrawing().cameraNode.rotation = rotation;
                                // camera height value
                                _this.getViewerDrawing().cameraNode.savedHeight = (linkedFile.isFloor ? linkedFile.selectedHeight : linkedFile.baseHeight)
                                    + Number(linkedFile.allowSection ? linkedFile.sectionOffset : 0);
                            }
                        });
                        return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 销毁图模联动应用
     * @function Glodon.Bimface.Module.Linkage2D3D.WebApplication.prototype.destroy
     */
    /**
     * 获取Viewer3D对象
     * @function Glodon.Bimface.Module.Linkage2D3D.WebApplication.prototype.getViewer3D
     * @return {Glodon.Bimface.Viewer.Viewer3D} Viewer3D对象
     */
    /**
     * 获取ViewerDrawing对象
     * @function Glodon.Bimface.Module.Linkage2D3D.WebApplication.prototype.getViewerDrawing
     * @return {Glodon.Bimface.Viewer.ViewerDrawing} ViewerDrawing对象
     */
    WebApplication.prototype.loadDrawing = function (viewMetaData) {
        var _this = this;
        this.reOpenMiniMap();
        this.changeDrawing({ domElement: this.renderItemMap.viewer2dDom.getElement(), viewMetaData: viewMetaData, drawingButtons: this._data.drawingButtons });
        var initSection = function () {
            // 相机点
            _this.getViewerDrawing().cameraNode = addCameraNode(_this.getViewerDrawing(), _this.getApp3D());
            var _a = calculateLinkageMatrix(_this.getApp3D().linkList[0].addedDrawingPoints, _this.getApp3D().linkList[0].addedModelPoints), matrix = _a.matrix, rotation = _a.rotation;
            _this.getViewerDrawing().cameraNode.matrix = matrix;
            _this.getViewerDrawing().cameraNode.rotation = rotation;
            _this.getViewerDrawing().cameraNode.savedHeight = (_this.getApp3D().linkList[0].isFloor ? _this.getApp3D().linkList[0].selectedHeight : _this.getApp3D().linkList[0].baseHeight)
                + Number(_this.getApp3D().linkList[0].allowSection ? _this.getApp3D().linkList[0].sectionOffset : 0);
            // 剖切
            _this.getViewer3D().getModel(_this.getModelId()).getBoundingBox(_this.getModelId(), function (data) {
                _this.getApp3D().modelMaxHeight = data.currentBoundingBox.max.z;
                _this.getApp3D().modelMinHeight = data.currentBoundingBox.min.z;
                if (_this.getApp3D().linkList[0].allowSection) {
                    var progress = getSectionProgress(_this.getViewer3D().getUnit(), _this.getApp3D().linkList[0].sectionOffset, _this.getApp3D().linkList[0].isFloor ? _this.getApp3D().linkList[0].selectedHeight : _this.getApp3D().linkList[0].baseHeight, _this.getApp3D().modelMaxHeight, _this.getApp3D().modelMinHeight);
                    setModelSectionProgress(_this.getViewer3D(), progress);
                    requestAnimationFrame(function () {
                        menuManagement(_this.getApp3D(), _this.getApp2D());
                    });
                }
            });
            _this.getViewerDrawing().removeEventListener('Loaded', initSection);
            _this.getViewerDrawing().showViewById(viewMetaData.viewId);
        };
        this.getViewerDrawing().addEventListener('Loaded', initSection);
    };
    WebApplication.prototype.setLinkOptions = function (options) {
        this.allViewMetaData = options;
        this.linkedFileListToolbar.setAllDrawings(options);
        this.linkedFileListToolbar.setOptionList(options);
    };
    WebApplication.prototype.showDrawingList = function () {
        this.renderItemMap.selectList.getElement().removeClass('hide');
    };
    WebApplication.prototype.hideDrawingList = function () {
        this.renderItemMap.selectList.getElement().addClass('hide');
    };
    /**
     * 获取当前加载的图纸
     * @function Glodon.Bimface.Module.Linkage2D3D.WebApplication.prototype.getCurrentDrawing
     * @return {Object} 获取当前图纸对象
     */
    WebApplication.prototype.getCurrentDrawing = function () {
        var _this = this;
        var currentDrawing = this.getApp3D().originDrawings[0].list.find(function (draw) {
            return draw.fileId == _this.getViewerDrawing().getDrawing().modelId;
        });
        return currentDrawing;
    };
    /**
     * 获取图纸列表
     * @function Glodon.Bimface.Module.Linkage2D3D.WebApplication.prototype.getDrawingList
     * @return {Object} 图纸列表对象
     */
    WebApplication.prototype.getDrawingList = function () {
        return this.getApp3D().originDrawings;
    };
    /**
     * 获取二维图纸对应的三维模型坐标点
     * @function Glodon.Bimface.Module.Linkage2D3D.WebApplication.prototype.getModelPosition
     * @param {Object} option 参数对象
     * @param {Object} option.point 当前二维图纸中的坐标点，单位为毫米，如{"x" : 0 , "y" : 0}
     * @return {Object} 三维模型中的坐标点，单位同模型场景单位
     */
    WebApplication.prototype.getModelPosition = function (option) {
        var _this = this;
        var translatedPoint = new window.THREE.Vector3(option.point.x, option.point.y, 0);
        var translatedPosition = translatedPoint.applyMatrix4(this.getViewerDrawing().cameraNode.matrix);
        var linkedValue = this.getApp3D().linkList && this.getApp3D().linkList.find(function (file) {
            return file.id == _this.getViewerDrawing().getDrawing().modelId;
        });
        var height = linkedValue.isFloor ? linkedValue.selectedHeight : linkedValue.baseHeight;
        height = this.getViewer3D().getUnit() == 'Meter' ? height / 1000 : height;
        return { x: translatedPosition.x, y: translatedPosition.y, z: height };
    };
    /**
     * 获取三维模型对应的二维图纸坐标点
     * @function Glodon.Bimface.Module.Linkage2D3D.WebApplication.prototype.getDrawingPosition
     * @param {Object} option 参数对象
     * @param {Object} option.point 三维模型中的坐标点，单位同场景单位，如{"x":0, "y":0, "z":0}
     * @return {Object} 当前二维图纸中的坐标点，单位为毫米
     */
    WebApplication.prototype.getDrawingPosition = function (option) {
        var cloneMatrix = this.getViewerDrawing().cameraNode.matrix.clone();
        cloneMatrix.elements[10] = 1;
        var inverseMatrix = cloneMatrix.invert();
        var drawingWorldPosition = new window.THREE.Vector3(option.point.x, option.point.y, 1).applyMatrix4(inverseMatrix);
        return { x: drawingWorldPosition.x, y: drawingWorldPosition.y };
    };
    /**
     * 设置当前加载的图纸
     * @function Glodon.Bimface.Module.Linkage2D3D.WebApplication.prototype.setCurrentDrawing
     * @param {Object} option 设置当前图纸的参数对象
     * @param {String} option.fileId 图纸文件ID
     */
    WebApplication.prototype.setCurrentDrawing = function (option) {
        var file = this.allViewMetaData.find(function (file) { return file.modelId == option.fileId; });
        if (file) {
            this.getApp3D().UI.linkedList.setSelectedByValue(file);
        }
    };
    return WebApplication;
}(LinkageModule));

/**
 * @classdesc 类：图模联动编辑器的配置项
 * @class Glodon.Bimface.Module.Linkage2D3D.EditorConfig
 * @constructs Glodon.Bimface.Module.Linkage2D3D.EditorConfig
 * @description 构造图模联动编辑器的配置项
 */
var EditorConfig = /** @class */ (function () {
    function EditorConfig(options) {
        var config;
        if (options) {
            config = {
                /**
                 * 存放组件界面的DOM容器
                 * @var {DOMElement} Glodon.Bimface.Module.Linkage2D3D.EditorConfig.prototype.domElement
                 */
                domElement: options.domElement,
                /**
                 * 设置图纸工具条Button对象(Home：主视角，RectZoom：框选，DrawingMeasure：测量，Map：地图，Layers：图层，Setting：设置，FullScreen：全屏 默认全部加载)
                 * @var {DOMElement} Glodon.Bimface.Module.Linkage2D3D.EditorConfig.prototype.drawingButtons
                 */
                drawingButtons: options.drawingButtons,
                /**
                 * 设置模型工具条button对象(Home：主视角，RectangleSelect：框选，Measure：测量，Section：剖切，Walk：漫游，Map：地图，Property：构件详情， Setting：设置，Information：基本信息， FullScreen：全屏，默认全部加载)
                 * @var {DOMElement} Glodon.Bimface.Module.Linkage2D3D.EditorConfig.prototype.modelButtons
                 */
                modelButtons: options.modelButtons,
                /**
                 * 三维模型应用，可以使用已经构造好的模型应用，避免模型重复加载
                 * @var {DOMElement} Glodon.Bimface.Module.Linkage2D3D.EditorConfig.prototype.webApplication3D
                 */
                webApplication3D: options.webApplication3D,
                /**
                 * 获取待添加资源列表的方法，方法需返回一个Promise对象
                 * @var {DOMElement} Glodon.Bimface.Module.Linkage2D3D.EditorConfig.prototype.getResourceListHandler
                 */
                getResourceListHandler: options.getResourceListHandler,
                /**
                 * 添加资源时获取对应viewToken的方法，方法需返回一个Promise对象
                 * @var {DOMElement} Glodon.Bimface.Module.Linkage2D3D.EditorConfig.prototype.getViewTokenHandler
                 */
                getViewTokenHandler: options.getViewTokenHandler,
            };
        }
        return config;
    }
    return EditorConfig;
}());

/**
 * @classdesc 类：图模联动应用的配置项
 * @class Glodon.Bimface.Module.Linkage2D3D.WebApplicationConfig
 * @constructs Glodon.Bimface.Module.Linkage2D3D.WebApplicationConfig
 * @description 构造图模联动应用的配置项
 */
var WebApplicationConfig = /** @class */ (function () {
    function WebApplicationConfig(options) {
        var config;
        if (options) {
            options = {
                /**
                 * 存放组件界面的DOM容器
                 * @var {DOMElement} Glodon.Bimface.Module.Linkage2D3D.WebApplicationConfig.prototype.domElement
                 */
                domElement: options.domElement,
                /**
                 * 设置图纸工具条Button对象(Home：主视角，RectZoom：框选，DrawingMeasure：测量，Map：地图，Layers：图层，Setting：设置，FullScreen：全屏 默认全部加载)
                 * @var {DOMElement} Glodon.Bimface.Module.Linkage2D3D.WebApplicationConfig.prototype.drawingButtons
                 */
                drawingButtons: options.drawingButtons,
                /**
                 * 	设置模型工具条button对象(Home：主视角，RectangleSelect：框选，Measure：测量，Section：剖切，Walk：漫游，Map：地图，Property：构件详情， Setting：设置，Information：基本信息， FullScreen：全屏，默认全部加载)
                 * @var {DOMElement} Glodon.Bimface.Module.Linkage2D3D.WebApplicationConfig.prototype.modelButtons
                 */
                modelButtons: options.modelButtons,
                /**
                 * 三维模型应用，可以使用已经构造好的模型应用，避免模型重复加载
                 * @var {DOMElement} Glodon.Bimface.Module.Linkage2D3D.WebApplicationConfig.prototype.webApplication3D
                 */
                webApplication3D: options.webApplication3D,
            };
        }
        return config;
    }
    return WebApplicationConfig;
}());

var Linkage2D3DNS = ModuleNS.Linkage2D3D = ModuleNS.Linkage2D3D || {};
Linkage2D3DNS.Editor = Editor;
Linkage2D3DNS.WebApplication = WebApplication;
Linkage2D3DNS.EditorConfig = EditorConfig;
Linkage2D3DNS.WebApplicationConfig = WebApplicationConfig;
Linkage2D3DNS.EditorEvent = EditorEvent$1;
Linkage2D3DNS.WebApplicationEvent = WebApplicationEvent$1;

export { Linkage2D3DNS as default };
