/******************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */

var __assign = function() {
    __assign = Object.assign || function __assign(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};

function __awaiter(thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
}

function __generator(thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
}

/***** BIMFACE host *****/
window.hostConfig = window.hostConfig || {
    APIHost: 'https://api.bimface.com',
    resourceHost: 'https://m.bimface.com',
    staticHost: 'https://static.bimface.com',
    dataEnvType: "BIMFACE",
    securityApi: true,
};

/**
 * @classdesc 类：组件管理器的配置项
 * @class Glodon.Bimface.Module.ModuleManagerConfig
 * @constructs Glodon.Bimface.Module.ModuleManagerConfig
 * @description 构造组件管理器的配置项
 */
var ModuleManagerConfig = /** @class */ (function () {
    function ModuleManagerConfig() {
        window.hostConfig = window.hostConfig || {
            APIHost: 'https://api.bimface.com',
            resourceHost: 'https://m.bimface.com',
            staticHost: 'https://static.bimface.com',
            dataEnvType: "BIMFACE",
            securityApi: true,
        };
        /**
         * JSSDK版本号（缺省值：最新版本）
         * @var {String} Glodon.Bimface.Module.ModuleManagerConfig.prototype.version
         */
        var config = __assign({}, window.hostConfig);
        config.staticHost = window.hostConfig.staticHost + "/api";
        return config;
    }
    return ModuleManagerConfig;
}());

var ajax = function (options) {
    return new Promise(function (resolve, reject) {
        var defaults = {
            type: 'get',
            data: null,
        };
        var _opt = __assign(__assign({}, defaults), options);
        var xmlhttp = new XMLHttpRequest();
        xmlhttp.onreadystatechange = function () {
            if (xmlhttp.readyState == 4) {
                var status = xmlhttp.status;
                if (status >= 200 && status < 300 || status == 304 || (status === 0 && window.location.protocol === 'file:')) {
                    var json = void 0;
                    try {
                        json = JSON.parse(xmlhttp.responseText);
                    }
                    catch (error) {
                        throw error;
                    }
                    resolve({
                        json: json,
                        responseText: xmlhttp.responseText,
                        responseXML: xmlhttp.responseXML,
                    });
                }
                else {
                    reject(status);
                }
            }
        };
        xmlhttp.open(_opt.type, _opt.url, _opt.async);
        xmlhttp.send(_opt.data);
    });
};

var loadHelper = /** @class */ (function () {
    function loadHelper(config) {
        this._config = config;
    }
    loadHelper.prototype.getModuleData = function (viewToken) {
        var _this = this;
        return new Promise(function (resolve, reject) {
            ajax({
                type: 'post',
                data: viewToken,
                url: _this._config.APIHost + "/inside/databag",
            }).then(function (res) {
                if (res.json.data) {
                    resolve(res.json.data);
                }
                else {
                    reject(res.json);
                }
            });
        });
    };
    loadHelper.prototype.loadScript = function (src) {
        return new Promise(function (resolve, reject) {
            var script = document.createElement('script');
            script.setAttribute('src', src);
            script.onload = function () {
                resolve(true);
            };
            script.onerror = function (e) {
                reject(e);
                console.error(e);
            };
            document.getElementsByTagName('head')[0].append(script);
        });
    };
    loadHelper.prototype.loadCss = function (src) {
        return new Promise(function (resolve, reject) {
            var link = document.createElement('link');
            link.setAttribute('href', src);
            link.setAttribute('rel', 'stylesheet');
            link.onload = function () {
                resolve(true);
            };
            link.onerror = function (e) {
                reject(e);
                console.error(e);
            };
            document.getElementsByTagName('head')[0].append(link);
        });
    };
    loadHelper.prototype.getStaticMainSrc = function (version) {
        return this._config.staticHost + "/Glodon/" + (this._config.build === 'Debug' ? 'Bimface' : version);
    };
    loadHelper.prototype.loadSDKLoader = function () {
        var _this = this;
        return new Promise(function (resolve, reject) {
            if (window.BimfaceSDKLoader) {
                resolve(true);
                return;
            }
            var src = _this._config.staticHost + "/BimfaceSDKLoader/<EMAIL>";
            _this.loadScript(src).then(function () { return resolve(true); }).catch(function () { return reject(); });
        });
    };
    loadHelper.prototype.loadUIComponents = function (version) {
        var _this = this;
        return new Promise(function (resolve, reject) {
            var _a, _b, _c;
            if ((_c = (_b = (_a = window.Glodon) === null || _a === void 0 ? void 0 : _a.Bimface) === null || _b === void 0 ? void 0 : _b.Tiles) === null || _c === void 0 ? void 0 : _c.UI) {
                return resolve(true);
            }
            var cssSrc = _this.getStaticMainSrc(version) + "/bimface.ui.css";
            _this.loadCss(cssSrc).then(function () { return resolve(true); }).catch(function () { return reject(); });
        });
    };
    return loadHelper;
}());

var Glodon = window.Glodon || {};
window.Glodon = Glodon;

var BimfaceNS = Glodon.Bimface = Glodon.Bimface || {};
var ModuleNS = BimfaceNS.Module = BimfaceNS.Module || {};

/**
 * @classdesc 类：组件管理器
 * @class Glodon.Bimface.Module.ModuleManager
 * @description 构造组件管理器
 * @constructs Glodon.Bimface.Module.ModuleManager
 * @param {Glodon.Bimface.Module.ModuleManagerConfig} managerConfig 组件管理器的配置项
 */
var ModuleManager = /** @class */ (function () {
    function ModuleManager(config) {
        this._loadedModule = {};
        this._config = config;
        this._helper = new loadHelper(config);
    }
    /**
     * 加载组件
     * @function Glodon.Bimface.Module.ModuleManager.prototype.loadModule
     * @param {Object} option 加载组件的配置项
     * @param {String} option.viewToken	组件数据的访问凭证
     * @param {String} option.moduleType 组件类型，Linkage2D3D为图模联动组件
     */
    ModuleManager.prototype.loadModule = function (option) {
        return __awaiter(this, void 0, void 0, function () {
            var loaderConfig, moduleData, version, CModule, key;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        loaderConfig = new window.BimfaceSDKLoaderConfig();
                        loaderConfig = __assign(__assign({}, loaderConfig), this._config);
                        loaderConfig.viewToken = option.viewToken;
                        return [4 /*yield*/, window.BimfaceSDKLoader.load(loaderConfig).catch(function (err) { return console.log(err); })];
                    case 1:
                        _a.sent();
                        return [4 /*yield*/, this._helper.loadSDKLoader()];
                    case 2:
                        _a.sent();
                        return [4 /*yield*/, this._helper.getModuleData(option.viewToken)];
                    case 3:
                        moduleData = (_a.sent());
                        if (moduleData.modelType != "moduleData") {
                            return [2 /*return*/, Promise.reject("[BIMFACE ERROR]: Invalid ViewToken, viewToken: [" + option.viewToken + "]")];
                        }
                        moduleData.viewToken = option.viewToken;
                        version = moduleData.jsSDKVersion;
                        return [4 /*yield*/, this._helper.loadUIComponents(version)];
                    case 4:
                        _a.sent();
                        if (!!this._loadedModule[option.moduleType]) return [3 /*break*/, 6];
                        this._helper.loadCss(this._helper.getStaticMainSrc(version) + "/modules/" + option.moduleType + ".css");
                        return [4 /*yield*/, import(this._helper.getStaticMainSrc(version) + "/modules/" + option.moduleType + ".js")];
                    case 5:
                        CModule = _a.sent();
                        this._loadedModule[option.moduleType] = CModule.default;
                        _a.label = 6;
                    case 6:
                        for (key in this._loadedModule[option.moduleType]) {
                            this._loadedModule[option.moduleType][key].setCurrentModuleData && this._loadedModule[option.moduleType][key].setCurrentModuleData(moduleData);
                        }
                        return [2 /*return*/, moduleData];
                }
            });
        });
    };
    return ModuleManager;
}());
ModuleNS.ModuleManager = ModuleManager;
ModuleNS.ModuleManagerConfig = ModuleManagerConfig;

export { ModuleManager, ModuleManagerConfig };
