window.BimfaceLanguage={name:"en_GB",bf_btn_fullScreen:"Full Screen",bf_btn_home:"Home",bf_btn_info:"Information",bf_btn_layers:"Layers",bf_btn_sheets:"Sheets",bf_btn_map:"Map",bf_btn_measure:"Measure",bf_btn_modelTree:"Model Tree",bf_btn_nav:"Navigation",bf_btn_props:"Properties",bf_btn_section:"Section",bf_btn_settings:"Settings",bf_btn_zoom:"Zoom",bf_btn_explode:"Model Explosion",bf_btn_previousPage:"Previous",bf_btn_nextPage:"Next",bf_btn_pan:"Pan",bf_btn_zoomIn:"Zoom In",bf_btn_zoomOut:"Zoom Out",bf_btn_search_search:"Search",bf_btn_search_previous:"Previous",bf_btn_search_next:"Next",bf_btn_search_textInput:"Please enter text",bf_contextmenu_hide:"Hide components",bf_contextmenu_isolate:"Isolate components",bf_contextmenu_isolate_hidden:"Make others hidden",bf_contextmenu_isolate_translucent:"Make others translucent",bf_contextmenu_showAll:"Show all",bf_contextmenu_transparent:"Make components translucent",bf_contextmenu_hideElement:"Hide Element",bf_contextmenu_hideLayer:"Hide Layer",bf_general_cancel:"Cancel",bf_general_save:"Save",bf_general_delete:"Delete",bf_general_exit:"Exit",bf_general_off:"Off",bf_general_on:"On",bf_general_edit:"Edit",bf_panel_viewSheet:"View Sheet",bf_panel_layers_all:"All layers",bf_panel_map_cut:"Cut or isolate the model with rectangular selection.",bf_panel_map_isolation:"Isolate",bf_panel_map_level:"Level:",bf_panel_map_height:"Height: ",bf_panel_map_section:"Section",bf_panel_measure_angle:"Angle",bf_panel_measure_area:"Area",bf_panel_measure_latlon:"Latitude&Longitude",bf_panel_measure_degree:"Degrees",bf_panel_measure_distance:"Distance",bf_panel_measure_elevation:"Elevation",bf_panel_measure_mindis:"Min Distance",bf_panel_measure_unitsTip:"Apply to Distance, Min Distance and Volume measurement.",bf_panel_measure_laser:"Laser Rangefinder",bf_panel_modelTree_allComponents:"All",bf_panel_modelTree_allFiles:"All files",bf_panel_modelTree_areas:"Areas",bf_panel_modelTree_components:"Components",bf_panel_modelTree_drawings:"Drawings",bf_panel_modelTree_fileLink:"Link Mode",bf_panel_modelTree_files:"Files",bf_panel_modelTree_rooms1:"Rooms",bf_panel_modelTree_rooms2:"Rooms",bf_panel_modelTree_modelGroup:"Sets",bf_panel_modelTree_group:"Group",bf_panel_modelTree_assembly:"Assembly",bf_panel_modelTree_mep:"System",bf_panel_modelTree_transparent:"Transparent",bf_panel_modelTree_nameUndefined:"Undefined",bf_panel_modelTree_specialtyUndefined:"Undefined",bf_panel_nav_addHere:"Add keyframe here",bf_panel_nav_addKeyframe:"Add keyframe",bf_panel_nav_clearKeyframe:"Clear keyframe",bf_panel_nav_collision:"Collision",bf_panel_nav_gravity:"Gravity",bf_panel_nav_play:"Play",bf_panel_nav_playnav:"Play",bf_panel_nav_position:"Position",bf_panel_nav_speed:"Speed:",bf_panel_nav_time:"Time:",bf_panel_nav_timeInput:"Duration of walkthrough",bf_panel_nav_walkthrough:"Walkthrough",bf_panel_nav_walkTips1:"Use ",bf_panel_nav_walkTips2:"and ",bf_panel_nav_walkTips3:"to move",bf_panel_nav_walkTips4:"Please select the starting point",bf_panel_nav_addWalkthrough:"Add walkthrough",bf_panel_nav_addWalkthroughHere:"Add walkthrough here",bf_panel_nav_walkthroughList:"Walkthrough list",bf_panel_nav_name:"Name: ",bf_panel_nav_second:"s",bf_panel_nav_walkthrough1:"Walkthrough",bf_panel_nav_walking:"Walking",bf_panel_nav_running:"Running",bf_panel_nav_tip_first_person:"First Person",bf_panel_nav_tip_third_person:"Third Person",bf_panel_props_mats:"Materials",bf_panel_props_matSel:"No matching material property",bf_panel_props_noProps:"No matching  property",bf_panel_props_props:"Properties",bf_panel_props_propSel:"Select a component to view its properties",bf_panel_explode:"Explode",bf_panel_search_textInput:"Please enter text",bf_panel_search_noresult:"No result found",bf_panel_section_box:"Section Box",bf_panel_section_plane:"Section Plane",bf_panel_section_selectPlane:"Custom Section",bf_panel_section_X:"X",bf_panel_section_Y:"Y",bf_panel_section_Z:"Z",bf_panel_settings_ambientLight:"Ambient light:",bf_panel_settings_bestAppear:"Best appearance",bf_panel_settings_bestPerform:"Best performance",bf_panel_settings_bgColor:"Background:",bf_panel_settings_modelBorder:"Model Outline:",bf_panel_settings_sectionBorder:"Section outline:",bf_panel_settings_contextMenu:"Context menu:",bf_panel_settings_displayModelBorder:"Show Model Outline",bf_panel_settings_displaySectionBorder:"Show Section Outline",bf_panel_settings_displayBorder:"Enable  wireframe",bf_panel_settings_displayLineWidth:"Display line width",bf_panel_settings_displayMode:"Display mode",bf_panel_settings_effect:"Effects",bf_panel_settings_effectPreset:"Effect preset:",bf_panel_settings_enableAbientLight:"Enable ambient light",bf_panel_settings_exposure:"Exposure:",bf_panel_settings_hover:"Mouse hover:",bf_panel_settings_hoverEffect:"Enable mouse hover effect",bf_panel_settings_iblBg:"Display:",bf_panel_settings_iblBgDisplay:"Display IBL",bf_panel_settings_iblSel:"Select IBL",bf_panel_settings_interaction:"Interaction",bf_panel_settings_leftOrbit:"Left",bf_panel_settings_lineWidth:"Line width:",bf_panel_settings_monoBgMode:"Monochrome",bf_panel_settings_moreOpt:"More options",bf_panel_settings_normalMode:"Normal",bf_panel_settings_orbitBtn:"Orbit button:",bf_panel_settings_restore:"Restore all to default settings",bf_panel_settings_reverseDir:"Reverse mouse zoom direction",bf_panel_settings_rightOrbit:"Right",bf_panel_settings_whiteBgMode:"White background",bf_panel_settings_zoomDir:"Zoom direction:",bf_tip_measure_angle:"Angle",bf_tip_measure_area:"Area",bf_tip_measure_distance:"Distance",bf_tip_measure_polyline_distance:"空间距离测量",bf_tip_measure_groundline_distance:"贴地距离测量",bf_tip_measure_elevation:"Elevation",bf_tip_measure_mindis:"Min Distance",bf_tip_measure_setting:"Setting",bf_tip_measure_triangle:"三角测量",bf_tip_measure_position:"Position",bf_tip_measure_spatial_area:"空间面积",bf_tip_measure_projected_area:"投影面积",bf_tip_measure_surface_area:"贴地面积",bf_tip_measure_clear:"清空",bf_tip_measure_volume:"Volume",bf_panel_measure_volume:"Volume",bf_panel_measure_volumeTip:"This object does not support Volume measurement.",bf_tip_measure_polyline:"Polyline",bf_panel_measure_total_distance:"Total Length",bf_tip_props_mats:"Materials",bf_tip_props_props:"Properties",bf_tip_section_fitBox:"Fit",bf_tip_section_hide:"Hide",bf_tip_section_resetBox:"Reset",bf_panel_section_reverse:"Reverse",bf_panel_modelTree_loading:"Loading...",bf_panel_modelTree_noDrawing:"No drawing information",bf_panel_modelTree_noRoom:"No room information",bf_panel_modelTree_noFile:"No file information",bf_panel_modelTree_noComponent:"No component information",bf_panel_modelTree_noData:"No data",bf_panel_modelTree_noSystem:"No system information",bf_panel_modelTree_componentNotFound:"Component not found",bf_panel_modelTree_entityNotFound:"Entity not found",bf_panel_nav_keyframe:"Keyframe",bf_panel_nav_stop:"Stop",bf_panel_setting_ssao:"Enable ambient occlusion",bf_panel_settings_ssao:"SSAO",bf_panel_settings_enableSsao:"Enable SSAO",bf_panel_info_files:"Number of subfiles",bf_panel_info_component:"Number of components",bf_panel_info_mesh:"Number of triangles",bf_panel_info_vertex:"Number of vertexes",bf_panel_settings_shadow:"Shadow:",bf_panel_settings_enableShadow:"Enable shadow",bf_btn_fullScreen_exit:"Exit full screen",bf_panel_roomEdit_insufficientNode:"Delete failed, the number of nodes must be greater than 3.",bf_panel_roomEdit_selfIntersection:"Boundary self-intersection, please re-edit.",bf_tip_roomEdit_dragNode:"Drag Node",bf_tip_roomEdit_addNode:"Add Node",bf_tip_roomEdit_deleteNode:"Delete Node",bf_tip_roomEdit_editHeight:"Edit Height",bf_general_move:"Move",bf_general_flap:"Flap",bf_general_point_edit:"Edit Point",bf_general_zoom:"Zoom",bf_general_rotate:"Rotate",bf_tip_props_rooms:"Rooms",bf_contextmenu_roomEdit:"Room Edit",bf_panel_nav_exit:"Exit",bf_viewHouse_up:"Up",bf_viewHouse_down:"Down",bf_viewHouse_east:"East",bf_viewHouse_South:"South",bf_viewHouse_West:"West",bf_viewHouse_North:"North",bf_load:"Loading...",bf_viewHouse_home:"Home",bf_viewHouse_orthographic:"Orthographic",bf_viewHouse_perspective:"Perspective",bf_viewHouse_setAsHome:"Set current view as Home",bf_viewHouse_resetHome:"Reset Home",bf_panel_measure_setting:"Setting",bf_panel_measure_clearAll:"Clear All",bf_panel_measure_scale:"Scale",bf_panel_measure_units:"Units",bf_panel_measure_precision:"Precision",bf_panel_measure_scaleInput:"Input scale, 1-2000",bf_panel_measure_save:"Save",bf_general_none:"None",bf_browser_notSupported:"Oops! Your browser is not supported",bf_browser_suggestion:"Recommend to use Chorme, Edge or Firefox browsers",bfui_button_home:"Home",bfui_button_property:"Properties",bf_panel_clashDetective_clashDetective:"Clash Detective",bf_panel_clashDetective_modelA:"ModelA",bf_panel_clashDetective_modelB:"ModelB",bf_panel_clashDetective_intergration:"Integration",bf_panel_clashDetective_tip_notLoaded:"Model Not Loaded",bf_panel_clashDetective_clash_num:"Clash",bf_panel_clashDetective_componentA:"ComponentA",bf_panel_clashDetective_componentB:"ComponentB",bf_panel_basic_attribute:"Basic Properties",bf_panel_views:"Views",bf_tip_flow_effect:"Flow animation"};