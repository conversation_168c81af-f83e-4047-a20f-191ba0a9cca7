
var ProtobufDecoderModule = (function() {
  var _scriptDir = typeof document !== 'undefined' && document.currentScript ? document.currentScript.src : undefined;
  if (typeof __filename !== 'undefined') _scriptDir = _scriptDir || __filename;
  return (
function(ProtobufDecoderModule) {
  ProtobufDecoderModule = ProtobufDecoderModule || {};

var Module=typeof ProtobufDecoderModule!=="undefined"?ProtobufDecoderModule:{};var readyPromiseResolve,readyPromiseReject;Module["ready"]=new Promise(function(resolve,reject){readyPromiseResolve=resolve;readyPromiseReject=reject});var isRuntimeInitialized=false;var isModuleParsed=false;Module["onRuntimeInitialized"]=function(){isRuntimeInitialized=true;if(isModuleParsed){if(typeof Module["onModuleLoaded"]==="function"){Module["onModuleLoaded"](Module)}}};Module["onModuleParsed"]=function(){isModuleParsed=true;if(isRuntimeInitialized){if(typeof Module["onModuleLoaded"]==="function"){Module["onModuleLoaded"](Module)}}};function isVersionSupported(versionString){if(typeof versionString!=="string")return false;const version=versionString.split(".");if(version.length<2||version.length>3)return false;if(version[0]==1&&version[1]>=0&&version[1]<=4)return true;if(version[0]!=0||version[1]>10)return false;return true}Module["isVersionSupported"]=isVersionSupported;var moduleOverrides={};var key;for(key in Module){if(Module.hasOwnProperty(key)){moduleOverrides[key]=Module[key]}}var arguments_=[];var thisProgram="./this.program";var quit_=function(status,toThrow){throw toThrow};var ENVIRONMENT_IS_WEB=typeof window==="object";var ENVIRONMENT_IS_WORKER=typeof importScripts==="function";var ENVIRONMENT_IS_NODE=typeof process==="object"&&typeof process.versions==="object"&&typeof process.versions.node==="string";var scriptDirectory="";function locateFile(path){if(Module["locateFile"]){return Module["locateFile"](path,scriptDirectory)}return scriptDirectory+path}var read_,readAsync,readBinary,setWindowTitle;function logExceptionOnExit(e){if(e instanceof ExitStatus)return;var toLog=e;err("exiting due to exception: "+toLog)}var nodeFS;var nodePath;if(ENVIRONMENT_IS_NODE){if(ENVIRONMENT_IS_WORKER){scriptDirectory=require("path").dirname(scriptDirectory)+"/"}else{scriptDirectory=__dirname+"/"}read_=function shell_read(filename,binary){if(!nodeFS)nodeFS=require("fs");if(!nodePath)nodePath=require("path");filename=nodePath["normalize"](filename);return nodeFS["readFileSync"](filename,binary?null:"utf8")};readBinary=function readBinary(filename){var ret=read_(filename,true);if(!ret.buffer){ret=new Uint8Array(ret)}assert(ret.buffer);return ret};readAsync=function readAsync(filename,onload,onerror){if(!nodeFS)nodeFS=require("fs");if(!nodePath)nodePath=require("path");filename=nodePath["normalize"](filename);nodeFS["readFile"](filename,function(err,data){if(err)onerror(err);else onload(data.buffer)})};if(process["argv"].length>1){thisProgram=process["argv"][1].replace(/\\/g,"/")}arguments_=process["argv"].slice(2);quit_=function(status,toThrow){if(keepRuntimeAlive()){process["exitCode"]=status;throw toThrow}logExceptionOnExit(toThrow);process["exit"](status)};Module["inspect"]=function(){return"[Emscripten Module object]"}}else if(ENVIRONMENT_IS_WEB||ENVIRONMENT_IS_WORKER){if(ENVIRONMENT_IS_WORKER){scriptDirectory=self.location.href}else if(typeof document!=="undefined"&&document.currentScript){scriptDirectory=document.currentScript.src}if(_scriptDir){scriptDirectory=_scriptDir}if(scriptDirectory.indexOf("blob:")!==0){scriptDirectory=scriptDirectory.substr(0,scriptDirectory.replace(/[?#].*/,"").lastIndexOf("/")+1)}else{scriptDirectory=""}{read_=function(url){var xhr=new XMLHttpRequest;xhr.open("GET",url,false);xhr.send(null);return xhr.responseText};if(ENVIRONMENT_IS_WORKER){readBinary=function(url){var xhr=new XMLHttpRequest;xhr.open("GET",url,false);xhr.responseType="arraybuffer";xhr.send(null);return new Uint8Array(xhr.response)}}readAsync=function(url,onload,onerror){var xhr=new XMLHttpRequest;xhr.open("GET",url,true);xhr.responseType="arraybuffer";xhr.onload=function(){if(xhr.status==200||xhr.status==0&&xhr.response){onload(xhr.response);return}onerror()};xhr.onerror=onerror;xhr.send(null)}}setWindowTitle=function(title){document.title=title}}else{}var out=Module["print"]||console.log.bind(console);var err=Module["printErr"]||console.warn.bind(console);for(key in moduleOverrides){if(moduleOverrides.hasOwnProperty(key)){Module[key]=moduleOverrides[key]}}moduleOverrides=null;if(Module["arguments"])arguments_=Module["arguments"];if(Module["thisProgram"])thisProgram=Module["thisProgram"];if(Module["quit"])quit_=Module["quit"];var wasmBinary;if(Module["wasmBinary"])wasmBinary=Module["wasmBinary"];var noExitRuntime=Module["noExitRuntime"]||true;if(typeof WebAssembly!=="object"){abort("no native wasm support detected")}var wasmMemory;var ABORT=false;var EXITSTATUS;function assert(condition,text){if(!condition){abort("Assertion failed: "+text)}}var UTF8Decoder=typeof TextDecoder!=="undefined"?new TextDecoder("utf8"):undefined;function UTF8ArrayToString(heap,idx,maxBytesToRead){var endIdx=idx+maxBytesToRead;var endPtr=idx;while(heap[endPtr]&&!(endPtr>=endIdx))++endPtr;if(endPtr-idx>16&&heap.subarray&&UTF8Decoder){return UTF8Decoder.decode(heap.subarray(idx,endPtr))}else{var str="";while(idx<endPtr){var u0=heap[idx++];if(!(u0&128)){str+=String.fromCharCode(u0);continue}var u1=heap[idx++]&63;if((u0&224)==192){str+=String.fromCharCode((u0&31)<<6|u1);continue}var u2=heap[idx++]&63;if((u0&240)==224){u0=(u0&15)<<12|u1<<6|u2}else{u0=(u0&7)<<18|u1<<12|u2<<6|heap[idx++]&63}if(u0<65536){str+=String.fromCharCode(u0)}else{var ch=u0-65536;str+=String.fromCharCode(55296|ch>>10,56320|ch&1023)}}}return str}function UTF8ToString(ptr,maxBytesToRead){return ptr?UTF8ArrayToString(HEAPU8,ptr,maxBytesToRead):""}function alignUp(x,multiple){if(x%multiple>0){x+=multiple-x%multiple}return x}var buffer,HEAP8,HEAPU8,HEAP16,HEAPU16,HEAP32,HEAPU32,HEAPF32,HEAPF64;function updateGlobalBufferAndViews(buf){buffer=buf;Module["HEAP8"]=HEAP8=new Int8Array(buf);Module["HEAP16"]=HEAP16=new Int16Array(buf);Module["HEAP32"]=HEAP32=new Int32Array(buf);Module["HEAPU8"]=HEAPU8=new Uint8Array(buf);Module["HEAPU16"]=HEAPU16=new Uint16Array(buf);Module["HEAPU32"]=HEAPU32=new Uint32Array(buf);Module["HEAPF32"]=HEAPF32=new Float32Array(buf);Module["HEAPF64"]=HEAPF64=new Float64Array(buf)}var INITIAL_MEMORY=Module["INITIAL_MEMORY"]||16777216;var wasmTable;var __ATPRERUN__=[];var __ATINIT__=[];var __ATPOSTRUN__=[];var runtimeInitialized=false;var runtimeKeepaliveCounter=0;function keepRuntimeAlive(){return noExitRuntime||runtimeKeepaliveCounter>0}function preRun(){if(Module["preRun"]){if(typeof Module["preRun"]=="function")Module["preRun"]=[Module["preRun"]];while(Module["preRun"].length){addOnPreRun(Module["preRun"].shift())}}callRuntimeCallbacks(__ATPRERUN__)}function initRuntime(){runtimeInitialized=true;callRuntimeCallbacks(__ATINIT__)}function postRun(){if(Module["postRun"]){if(typeof Module["postRun"]=="function")Module["postRun"]=[Module["postRun"]];while(Module["postRun"].length){addOnPostRun(Module["postRun"].shift())}}callRuntimeCallbacks(__ATPOSTRUN__)}function addOnPreRun(cb){__ATPRERUN__.unshift(cb)}function addOnInit(cb){__ATINIT__.unshift(cb)}function addOnPostRun(cb){__ATPOSTRUN__.unshift(cb)}var runDependencies=0;var runDependencyWatcher=null;var dependenciesFulfilled=null;function addRunDependency(id){runDependencies++;if(Module["monitorRunDependencies"]){Module["monitorRunDependencies"](runDependencies)}}function removeRunDependency(id){runDependencies--;if(Module["monitorRunDependencies"]){Module["monitorRunDependencies"](runDependencies)}if(runDependencies==0){if(runDependencyWatcher!==null){clearInterval(runDependencyWatcher);runDependencyWatcher=null}if(dependenciesFulfilled){var callback=dependenciesFulfilled;dependenciesFulfilled=null;callback()}}}Module["preloadedImages"]={};Module["preloadedAudios"]={};function abort(what){{if(Module["onAbort"]){Module["onAbort"](what)}}what="Aborted("+what+")";err(what);ABORT=true;EXITSTATUS=1;what+=". Build with -s ASSERTIONS=1 for more info.";var e=new WebAssembly.RuntimeError(what);readyPromiseReject(e);throw e}var dataURIPrefix="data:application/octet-stream;base64,";function isDataURI(filename){return filename.startsWith(dataURIPrefix)}function isFileURI(filename){return filename.startsWith("file://")}var wasmBinaryFile;wasmBinaryFile="bimtiles_decoder.wasm";if(!isDataURI(wasmBinaryFile)){wasmBinaryFile=locateFile(wasmBinaryFile)}function getBinary(file){try{if(file==wasmBinaryFile&&wasmBinary){return new Uint8Array(wasmBinary)}if(readBinary){return readBinary(file)}else{throw"both async and sync fetching of the wasm failed"}}catch(err){abort(err)}}function getBinaryPromise(){if(!wasmBinary&&(ENVIRONMENT_IS_WEB||ENVIRONMENT_IS_WORKER)){if(typeof fetch==="function"&&!isFileURI(wasmBinaryFile)){return fetch(wasmBinaryFile,{credentials:"same-origin"}).then(function(response){if(!response["ok"]){throw"failed to load wasm binary file at '"+wasmBinaryFile+"'"}return response["arrayBuffer"]()}).catch(function(){return getBinary(wasmBinaryFile)})}else{if(readAsync){return new Promise(function(resolve,reject){readAsync(wasmBinaryFile,function(response){resolve(new Uint8Array(response))},reject)})}}}return Promise.resolve().then(function(){return getBinary(wasmBinaryFile)})}function createWasm(){var info={"a":asmLibraryArg};function receiveInstance(instance,module){var exports=instance.exports;Module["asm"]=exports;wasmMemory=Module["asm"]["i"];updateGlobalBufferAndViews(wasmMemory.buffer);wasmTable=Module["asm"]["k"];addOnInit(Module["asm"]["j"]);removeRunDependency("wasm-instantiate")}addRunDependency("wasm-instantiate");function receiveInstantiationResult(result){receiveInstance(result["instance"])}function instantiateArrayBuffer(receiver){return getBinaryPromise().then(function(binary){return WebAssembly.instantiate(binary,info)}).then(function(instance){return instance}).then(receiver,function(reason){err("failed to asynchronously prepare wasm: "+reason);abort(reason)})}function instantiateAsync(){if(!wasmBinary&&typeof WebAssembly.instantiateStreaming==="function"&&!isDataURI(wasmBinaryFile)&&!isFileURI(wasmBinaryFile)&&typeof fetch==="function"){return fetch(wasmBinaryFile,{credentials:"same-origin"}).then(function(response){var result=WebAssembly.instantiateStreaming(response,info);return result.then(receiveInstantiationResult,function(reason){err("wasm streaming compile failed: "+reason);err("falling back to ArrayBuffer instantiation");return instantiateArrayBuffer(receiveInstantiationResult)})})}else{return instantiateArrayBuffer(receiveInstantiationResult)}}if(Module["instantiateWasm"]){try{var exports=Module["instantiateWasm"](info,receiveInstance);return exports}catch(e){err("Module.instantiateWasm callback failed with error: "+e);return false}}instantiateAsync().catch(readyPromiseReject);return{}}function callRuntimeCallbacks(callbacks){while(callbacks.length>0){var callback=callbacks.shift();if(typeof callback=="function"){callback(Module);continue}var func=callback.func;if(typeof func==="number"){if(callback.arg===undefined){getWasmTableEntry(func)()}else{getWasmTableEntry(func)(callback.arg)}}else{func(callback.arg===undefined?null:callback.arg)}}}var wasmTableMirror=[];function getWasmTableEntry(funcPtr){var func=wasmTableMirror[funcPtr];if(!func){if(funcPtr>=wasmTableMirror.length)wasmTableMirror.length=funcPtr+1;wasmTableMirror[funcPtr]=func=wasmTable.get(funcPtr)}return func}function ___cxa_allocate_exception(size){return _malloc(size+16)+16}function ExceptionInfo(excPtr){this.excPtr=excPtr;this.ptr=excPtr-16;this.set_type=function(type){HEAP32[this.ptr+4>>2]=type};this.get_type=function(){return HEAP32[this.ptr+4>>2]};this.set_destructor=function(destructor){HEAP32[this.ptr+8>>2]=destructor};this.get_destructor=function(){return HEAP32[this.ptr+8>>2]};this.set_refcount=function(refcount){HEAP32[this.ptr>>2]=refcount};this.set_caught=function(caught){caught=caught?1:0;HEAP8[this.ptr+12>>0]=caught};this.get_caught=function(){return HEAP8[this.ptr+12>>0]!=0};this.set_rethrown=function(rethrown){rethrown=rethrown?1:0;HEAP8[this.ptr+13>>0]=rethrown};this.get_rethrown=function(){return HEAP8[this.ptr+13>>0]!=0};this.init=function(type,destructor){this.set_type(type);this.set_destructor(destructor);this.set_refcount(0);this.set_caught(false);this.set_rethrown(false)};this.add_ref=function(){var value=HEAP32[this.ptr>>2];HEAP32[this.ptr>>2]=value+1};this.release_ref=function(){var prev=HEAP32[this.ptr>>2];HEAP32[this.ptr>>2]=prev-1;return prev===1}}var exceptionLast=0;var uncaughtExceptionCount=0;function ___cxa_throw(ptr,type,destructor){var info=new ExceptionInfo(ptr);info.init(type,destructor);exceptionLast=ptr;uncaughtExceptionCount++;throw ptr}function _abort(){abort("")}function _emscripten_memcpy_big(dest,src,num){HEAPU8.copyWithin(dest,src,src+num)}function emscripten_realloc_buffer(size){try{wasmMemory.grow(size-buffer.byteLength+65535>>>16);updateGlobalBufferAndViews(wasmMemory.buffer);return 1}catch(e){}}function _emscripten_resize_heap(requestedSize){var oldSize=HEAPU8.length;requestedSize=requestedSize>>>0;var maxHeapSize=2147483648;if(requestedSize>maxHeapSize){return false}for(var cutDown=1;cutDown<=4;cutDown*=2){var overGrownHeapSize=oldSize*(1+.2/cutDown);overGrownHeapSize=Math.min(overGrownHeapSize,requestedSize+100663296);var newSize=Math.min(maxHeapSize,alignUp(Math.max(requestedSize,overGrownHeapSize),65536));var replacement=emscripten_realloc_buffer(newSize);if(replacement){return true}}return false}var SYSCALLS={mappings:{},buffers:[null,[],[]],printChar:function(stream,curr){var buffer=SYSCALLS.buffers[stream];if(curr===0||curr===10){(stream===1?out:err)(UTF8ArrayToString(buffer,0));buffer.length=0}else{buffer.push(curr)}},varargs:undefined,get:function(){SYSCALLS.varargs+=4;var ret=HEAP32[SYSCALLS.varargs-4>>2];return ret},getStr:function(ptr){var ret=UTF8ToString(ptr);return ret},get64:function(low,high){return low}};function _fd_close(fd){return 0}function _fd_seek(fd,offset_low,offset_high,whence,newOffset){}function _fd_write(fd,iov,iovcnt,pnum){var num=0;for(var i=0;i<iovcnt;i++){var ptr=HEAP32[iov>>2];var len=HEAP32[iov+4>>2];iov+=8;for(var j=0;j<len;j++){SYSCALLS.printChar(fd,HEAPU8[ptr+j])}num+=len}HEAP32[pnum>>2]=num;return 0}var asmLibraryArg={"b":___cxa_allocate_exception,"a":___cxa_throw,"g":_abort,"e":_emscripten_memcpy_big,"f":_emscripten_resize_heap,"h":_fd_close,"d":_fd_seek,"c":_fd_write};var asm=createWasm();var ___wasm_call_ctors=Module["___wasm_call_ctors"]=function(){return(___wasm_call_ctors=Module["___wasm_call_ctors"]=Module["asm"]["j"]).apply(null,arguments)};var _emscripten_bind_VoidPtr___destroy___0=Module["_emscripten_bind_VoidPtr___destroy___0"]=function(){return(_emscripten_bind_VoidPtr___destroy___0=Module["_emscripten_bind_VoidPtr___destroy___0"]=Module["asm"]["l"]).apply(null,arguments)};var _emscripten_bind_Status_code_0=Module["_emscripten_bind_Status_code_0"]=function(){return(_emscripten_bind_Status_code_0=Module["_emscripten_bind_Status_code_0"]=Module["asm"]["m"]).apply(null,arguments)};var _emscripten_bind_Status_ok_0=Module["_emscripten_bind_Status_ok_0"]=function(){return(_emscripten_bind_Status_ok_0=Module["_emscripten_bind_Status_ok_0"]=Module["asm"]["n"]).apply(null,arguments)};var _emscripten_bind_Status_error_msg_0=Module["_emscripten_bind_Status_error_msg_0"]=function(){return(_emscripten_bind_Status_error_msg_0=Module["_emscripten_bind_Status_error_msg_0"]=Module["asm"]["o"]).apply(null,arguments)};var _emscripten_bind_Status___destroy___0=Module["_emscripten_bind_Status___destroy___0"]=function(){return(_emscripten_bind_Status___destroy___0=Module["_emscripten_bind_Status___destroy___0"]=Module["asm"]["p"]).apply(null,arguments)};var _emscripten_bind_GeometryAttribute_GeometryAttribute_0=Module["_emscripten_bind_GeometryAttribute_GeometryAttribute_0"]=function(){return(_emscripten_bind_GeometryAttribute_GeometryAttribute_0=Module["_emscripten_bind_GeometryAttribute_GeometryAttribute_0"]=Module["asm"]["q"]).apply(null,arguments)};var _emscripten_bind_GeometryAttribute_attribute_type_0=Module["_emscripten_bind_GeometryAttribute_attribute_type_0"]=function(){return(_emscripten_bind_GeometryAttribute_attribute_type_0=Module["_emscripten_bind_GeometryAttribute_attribute_type_0"]=Module["asm"]["r"]).apply(null,arguments)};var _emscripten_bind_GeometryAttribute_data_type_0=Module["_emscripten_bind_GeometryAttribute_data_type_0"]=function(){return(_emscripten_bind_GeometryAttribute_data_type_0=Module["_emscripten_bind_GeometryAttribute_data_type_0"]=Module["asm"]["s"]).apply(null,arguments)};var _emscripten_bind_GeometryAttribute_num_components_0=Module["_emscripten_bind_GeometryAttribute_num_components_0"]=function(){return(_emscripten_bind_GeometryAttribute_num_components_0=Module["_emscripten_bind_GeometryAttribute_num_components_0"]=Module["asm"]["t"]).apply(null,arguments)};var _emscripten_bind_GeometryAttribute_num_points_0=Module["_emscripten_bind_GeometryAttribute_num_points_0"]=function(){return(_emscripten_bind_GeometryAttribute_num_points_0=Module["_emscripten_bind_GeometryAttribute_num_points_0"]=Module["asm"]["u"]).apply(null,arguments)};var _emscripten_bind_GeometryAttribute_byte_stride_0=Module["_emscripten_bind_GeometryAttribute_byte_stride_0"]=function(){return(_emscripten_bind_GeometryAttribute_byte_stride_0=Module["_emscripten_bind_GeometryAttribute_byte_stride_0"]=Module["asm"]["v"]).apply(null,arguments)};var _emscripten_bind_GeometryAttribute_byte_offset_0=Module["_emscripten_bind_GeometryAttribute_byte_offset_0"]=function(){return(_emscripten_bind_GeometryAttribute_byte_offset_0=Module["_emscripten_bind_GeometryAttribute_byte_offset_0"]=Module["asm"]["w"]).apply(null,arguments)};var _emscripten_bind_GeometryAttribute_byte_length_0=Module["_emscripten_bind_GeometryAttribute_byte_length_0"]=function(){return(_emscripten_bind_GeometryAttribute_byte_length_0=Module["_emscripten_bind_GeometryAttribute_byte_length_0"]=Module["asm"]["x"]).apply(null,arguments)};var _emscripten_bind_GeometryAttribute_unique_id_0=Module["_emscripten_bind_GeometryAttribute_unique_id_0"]=function(){return(_emscripten_bind_GeometryAttribute_unique_id_0=Module["_emscripten_bind_GeometryAttribute_unique_id_0"]=Module["asm"]["y"]).apply(null,arguments)};var _emscripten_bind_GeometryAttribute_normalized_0=Module["_emscripten_bind_GeometryAttribute_normalized_0"]=function(){return(_emscripten_bind_GeometryAttribute_normalized_0=Module["_emscripten_bind_GeometryAttribute_normalized_0"]=Module["asm"]["z"]).apply(null,arguments)};var _emscripten_bind_GeometryAttribute_compressed_0=Module["_emscripten_bind_GeometryAttribute_compressed_0"]=function(){return(_emscripten_bind_GeometryAttribute_compressed_0=Module["_emscripten_bind_GeometryAttribute_compressed_0"]=Module["asm"]["A"]).apply(null,arguments)};var _emscripten_bind_GeometryAttribute___destroy___0=Module["_emscripten_bind_GeometryAttribute___destroy___0"]=function(){return(_emscripten_bind_GeometryAttribute___destroy___0=Module["_emscripten_bind_GeometryAttribute___destroy___0"]=Module["asm"]["B"]).apply(null,arguments)};var _emscripten_bind_MeshEntity_MeshEntity_0=Module["_emscripten_bind_MeshEntity_MeshEntity_0"]=function(){return(_emscripten_bind_MeshEntity_MeshEntity_0=Module["_emscripten_bind_MeshEntity_MeshEntity_0"]=Module["asm"]["C"]).apply(null,arguments)};var _emscripten_bind_MeshEntity_GetAttribute_1=Module["_emscripten_bind_MeshEntity_GetAttribute_1"]=function(){return(_emscripten_bind_MeshEntity_GetAttribute_1=Module["_emscripten_bind_MeshEntity_GetAttribute_1"]=Module["asm"]["D"]).apply(null,arguments)};var _emscripten_bind_MeshEntity_num_attributes_0=Module["_emscripten_bind_MeshEntity_num_attributes_0"]=function(){return(_emscripten_bind_MeshEntity_num_attributes_0=Module["_emscripten_bind_MeshEntity_num_attributes_0"]=Module["asm"]["E"]).apply(null,arguments)};var _emscripten_bind_MeshEntity_GetSubMeshEntity_1=Module["_emscripten_bind_MeshEntity_GetSubMeshEntity_1"]=function(){return(_emscripten_bind_MeshEntity_GetSubMeshEntity_1=Module["_emscripten_bind_MeshEntity_GetSubMeshEntity_1"]=Module["asm"]["F"]).apply(null,arguments)};var _emscripten_bind_MeshEntity_num_sub_entities_0=Module["_emscripten_bind_MeshEntity_num_sub_entities_0"]=function(){return(_emscripten_bind_MeshEntity_num_sub_entities_0=Module["_emscripten_bind_MeshEntity_num_sub_entities_0"]=Module["asm"]["G"]).apply(null,arguments)};var _emscripten_bind_MeshEntity_get_user_id_0=Module["_emscripten_bind_MeshEntity_get_user_id_0"]=function(){return(_emscripten_bind_MeshEntity_get_user_id_0=Module["_emscripten_bind_MeshEntity_get_user_id_0"]=Module["asm"]["H"]).apply(null,arguments)};var _emscripten_bind_MeshEntity_get_userdata_id_0=Module["_emscripten_bind_MeshEntity_get_userdata_id_0"]=function(){return(_emscripten_bind_MeshEntity_get_userdata_id_0=Module["_emscripten_bind_MeshEntity_get_userdata_id_0"]=Module["asm"]["I"]).apply(null,arguments)};var _emscripten_bind_MeshEntity_get_material_id_0=Module["_emscripten_bind_MeshEntity_get_material_id_0"]=function(){return(_emscripten_bind_MeshEntity_get_material_id_0=Module["_emscripten_bind_MeshEntity_get_material_id_0"]=Module["asm"]["J"]).apply(null,arguments)};var _emscripten_bind_MeshEntity_get_node_id_0=Module["_emscripten_bind_MeshEntity_get_node_id_0"]=function(){return(_emscripten_bind_MeshEntity_get_node_id_0=Module["_emscripten_bind_MeshEntity_get_node_id_0"]=Module["asm"]["K"]).apply(null,arguments)};var _emscripten_bind_MeshEntity_get_primitive_id_0=Module["_emscripten_bind_MeshEntity_get_primitive_id_0"]=function(){return(_emscripten_bind_MeshEntity_get_primitive_id_0=Module["_emscripten_bind_MeshEntity_get_primitive_id_0"]=Module["asm"]["L"]).apply(null,arguments)};var _emscripten_bind_MeshEntity_primitive_mode_0=Module["_emscripten_bind_MeshEntity_primitive_mode_0"]=function(){return(_emscripten_bind_MeshEntity_primitive_mode_0=Module["_emscripten_bind_MeshEntity_primitive_mode_0"]=Module["asm"]["M"]).apply(null,arguments)};var _emscripten_bind_MeshEntity_node_type_0=Module["_emscripten_bind_MeshEntity_node_type_0"]=function(){return(_emscripten_bind_MeshEntity_node_type_0=Module["_emscripten_bind_MeshEntity_node_type_0"]=Module["asm"]["N"]).apply(null,arguments)};var _emscripten_bind_MeshEntity_get_scene_id_0=Module["_emscripten_bind_MeshEntity_get_scene_id_0"]=function(){return(_emscripten_bind_MeshEntity_get_scene_id_0=Module["_emscripten_bind_MeshEntity_get_scene_id_0"]=Module["asm"]["O"]).apply(null,arguments)};var _emscripten_bind_MeshEntity_get_scene_name_0=Module["_emscripten_bind_MeshEntity_get_scene_name_0"]=function(){return(_emscripten_bind_MeshEntity_get_scene_name_0=Module["_emscripten_bind_MeshEntity_get_scene_name_0"]=Module["asm"]["P"]).apply(null,arguments)};var _emscripten_bind_MeshEntity_num_components_bbox_0=Module["_emscripten_bind_MeshEntity_num_components_bbox_0"]=function(){return(_emscripten_bind_MeshEntity_num_components_bbox_0=Module["_emscripten_bind_MeshEntity_num_components_bbox_0"]=Module["asm"]["Q"]).apply(null,arguments)};var _emscripten_bind_MeshEntity_byte_length_per_component_bbox_0=Module["_emscripten_bind_MeshEntity_byte_length_per_component_bbox_0"]=function(){return(_emscripten_bind_MeshEntity_byte_length_per_component_bbox_0=Module["_emscripten_bind_MeshEntity_byte_length_per_component_bbox_0"]=Module["asm"]["R"]).apply(null,arguments)};var _emscripten_bind_MeshEntity_num_components_matrix_0=Module["_emscripten_bind_MeshEntity_num_components_matrix_0"]=function(){return(_emscripten_bind_MeshEntity_num_components_matrix_0=Module["_emscripten_bind_MeshEntity_num_components_matrix_0"]=Module["asm"]["S"]).apply(null,arguments)};var _emscripten_bind_MeshEntity_byte_length_per_component_matrix_0=Module["_emscripten_bind_MeshEntity_byte_length_per_component_matrix_0"]=function(){return(_emscripten_bind_MeshEntity_byte_length_per_component_matrix_0=Module["_emscripten_bind_MeshEntity_byte_length_per_component_matrix_0"]=Module["asm"]["T"]).apply(null,arguments)};var _emscripten_bind_MeshEntity_num_components_translation_0=Module["_emscripten_bind_MeshEntity_num_components_translation_0"]=function(){return(_emscripten_bind_MeshEntity_num_components_translation_0=Module["_emscripten_bind_MeshEntity_num_components_translation_0"]=Module["asm"]["U"]).apply(null,arguments)};var _emscripten_bind_MeshEntity_byte_length_per_component_translation_0=Module["_emscripten_bind_MeshEntity_byte_length_per_component_translation_0"]=function(){return(_emscripten_bind_MeshEntity_byte_length_per_component_translation_0=Module["_emscripten_bind_MeshEntity_byte_length_per_component_translation_0"]=Module["asm"]["V"]).apply(null,arguments)};var _emscripten_bind_MeshEntity_GetMatrixFloat32Array_2=Module["_emscripten_bind_MeshEntity_GetMatrixFloat32Array_2"]=function(){return(_emscripten_bind_MeshEntity_GetMatrixFloat32Array_2=Module["_emscripten_bind_MeshEntity_GetMatrixFloat32Array_2"]=Module["asm"]["W"]).apply(null,arguments)};var _emscripten_bind_MeshEntity_GetBBoxFloat32Array_2=Module["_emscripten_bind_MeshEntity_GetBBoxFloat32Array_2"]=function(){return(_emscripten_bind_MeshEntity_GetBBoxFloat32Array_2=Module["_emscripten_bind_MeshEntity_GetBBoxFloat32Array_2"]=Module["asm"]["X"]).apply(null,arguments)};var _emscripten_bind_MeshEntity_GetTranslationFloat64Array_2=Module["_emscripten_bind_MeshEntity_GetTranslationFloat64Array_2"]=function(){return(_emscripten_bind_MeshEntity_GetTranslationFloat64Array_2=Module["_emscripten_bind_MeshEntity_GetTranslationFloat64Array_2"]=Module["asm"]["Y"]).apply(null,arguments)};var _emscripten_bind_MeshEntity___destroy___0=Module["_emscripten_bind_MeshEntity___destroy___0"]=function(){return(_emscripten_bind_MeshEntity___destroy___0=Module["_emscripten_bind_MeshEntity___destroy___0"]=Module["asm"]["Z"]).apply(null,arguments)};var _emscripten_bind_BimTilesMesh_BimTilesMesh_0=Module["_emscripten_bind_BimTilesMesh_BimTilesMesh_0"]=function(){return(_emscripten_bind_BimTilesMesh_BimTilesMesh_0=Module["_emscripten_bind_BimTilesMesh_BimTilesMesh_0"]=Module["asm"]["_"]).apply(null,arguments)};var _emscripten_bind_BimTilesMesh_GetMeshEntity_1=Module["_emscripten_bind_BimTilesMesh_GetMeshEntity_1"]=function(){return(_emscripten_bind_BimTilesMesh_GetMeshEntity_1=Module["_emscripten_bind_BimTilesMesh_GetMeshEntity_1"]=Module["asm"]["$"]).apply(null,arguments)};var _emscripten_bind_BimTilesMesh_num_mesh_entities_0=Module["_emscripten_bind_BimTilesMesh_num_mesh_entities_0"]=function(){return(_emscripten_bind_BimTilesMesh_num_mesh_entities_0=Module["_emscripten_bind_BimTilesMesh_num_mesh_entities_0"]=Module["asm"]["aa"]).apply(null,arguments)};var _emscripten_bind_BimTilesMesh_get_tile_id_0=Module["_emscripten_bind_BimTilesMesh_get_tile_id_0"]=function(){return(_emscripten_bind_BimTilesMesh_get_tile_id_0=Module["_emscripten_bind_BimTilesMesh_get_tile_id_0"]=Module["asm"]["ba"]).apply(null,arguments)};var _emscripten_bind_BimTilesMesh_get_block_id_0=Module["_emscripten_bind_BimTilesMesh_get_block_id_0"]=function(){return(_emscripten_bind_BimTilesMesh_get_block_id_0=Module["_emscripten_bind_BimTilesMesh_get_block_id_0"]=Module["asm"]["ca"]).apply(null,arguments)};var _emscripten_bind_BimTilesMesh_get_buffer_block_id_0=Module["_emscripten_bind_BimTilesMesh_get_buffer_block_id_0"]=function(){return(_emscripten_bind_BimTilesMesh_get_buffer_block_id_0=Module["_emscripten_bind_BimTilesMesh_get_buffer_block_id_0"]=Module["asm"]["da"]).apply(null,arguments)};var _emscripten_bind_BimTilesMesh_get_material_block_id_0=Module["_emscripten_bind_BimTilesMesh_get_material_block_id_0"]=function(){return(_emscripten_bind_BimTilesMesh_get_material_block_id_0=Module["_emscripten_bind_BimTilesMesh_get_material_block_id_0"]=Module["asm"]["ea"]).apply(null,arguments)};var _emscripten_bind_BimTilesMesh_get_userdata_block_id_0=Module["_emscripten_bind_BimTilesMesh_get_userdata_block_id_0"]=function(){return(_emscripten_bind_BimTilesMesh_get_userdata_block_id_0=Module["_emscripten_bind_BimTilesMesh_get_userdata_block_id_0"]=Module["asm"]["fa"]).apply(null,arguments)};var _emscripten_bind_BimTilesMesh_num_components_bbox_0=Module["_emscripten_bind_BimTilesMesh_num_components_bbox_0"]=function(){return(_emscripten_bind_BimTilesMesh_num_components_bbox_0=Module["_emscripten_bind_BimTilesMesh_num_components_bbox_0"]=Module["asm"]["ga"]).apply(null,arguments)};var _emscripten_bind_BimTilesMesh_byte_length_per_component_bbox_0=Module["_emscripten_bind_BimTilesMesh_byte_length_per_component_bbox_0"]=function(){return(_emscripten_bind_BimTilesMesh_byte_length_per_component_bbox_0=Module["_emscripten_bind_BimTilesMesh_byte_length_per_component_bbox_0"]=Module["asm"]["ha"]).apply(null,arguments)};var _emscripten_bind_BimTilesMesh_num_components_matrix_0=Module["_emscripten_bind_BimTilesMesh_num_components_matrix_0"]=function(){return(_emscripten_bind_BimTilesMesh_num_components_matrix_0=Module["_emscripten_bind_BimTilesMesh_num_components_matrix_0"]=Module["asm"]["ia"]).apply(null,arguments)};var _emscripten_bind_BimTilesMesh_byte_length_per_component_matrix_0=Module["_emscripten_bind_BimTilesMesh_byte_length_per_component_matrix_0"]=function(){return(_emscripten_bind_BimTilesMesh_byte_length_per_component_matrix_0=Module["_emscripten_bind_BimTilesMesh_byte_length_per_component_matrix_0"]=Module["asm"]["ja"]).apply(null,arguments)};var _emscripten_bind_BimTilesMesh_num_components_translation_0=Module["_emscripten_bind_BimTilesMesh_num_components_translation_0"]=function(){return(_emscripten_bind_BimTilesMesh_num_components_translation_0=Module["_emscripten_bind_BimTilesMesh_num_components_translation_0"]=Module["asm"]["ka"]).apply(null,arguments)};var _emscripten_bind_BimTilesMesh_byte_length_per_component_translation_0=Module["_emscripten_bind_BimTilesMesh_byte_length_per_component_translation_0"]=function(){return(_emscripten_bind_BimTilesMesh_byte_length_per_component_translation_0=Module["_emscripten_bind_BimTilesMesh_byte_length_per_component_translation_0"]=Module["asm"]["la"]).apply(null,arguments)};var _emscripten_bind_BimTilesMesh_GetMatrixFloat32Array_2=Module["_emscripten_bind_BimTilesMesh_GetMatrixFloat32Array_2"]=function(){return(_emscripten_bind_BimTilesMesh_GetMatrixFloat32Array_2=Module["_emscripten_bind_BimTilesMesh_GetMatrixFloat32Array_2"]=Module["asm"]["ma"]).apply(null,arguments)};var _emscripten_bind_BimTilesMesh_GetBBoxFloat32Array_2=Module["_emscripten_bind_BimTilesMesh_GetBBoxFloat32Array_2"]=function(){return(_emscripten_bind_BimTilesMesh_GetBBoxFloat32Array_2=Module["_emscripten_bind_BimTilesMesh_GetBBoxFloat32Array_2"]=Module["asm"]["na"]).apply(null,arguments)};var _emscripten_bind_BimTilesMesh_GetTranslationFloat64Array_2=Module["_emscripten_bind_BimTilesMesh_GetTranslationFloat64Array_2"]=function(){return(_emscripten_bind_BimTilesMesh_GetTranslationFloat64Array_2=Module["_emscripten_bind_BimTilesMesh_GetTranslationFloat64Array_2"]=Module["asm"]["oa"]).apply(null,arguments)};var _emscripten_bind_BimTilesMesh___destroy___0=Module["_emscripten_bind_BimTilesMesh___destroy___0"]=function(){return(_emscripten_bind_BimTilesMesh___destroy___0=Module["_emscripten_bind_BimTilesMesh___destroy___0"]=Module["asm"]["pa"]).apply(null,arguments)};var _emscripten_bind_InstanceEntity_InstanceEntity_0=Module["_emscripten_bind_InstanceEntity_InstanceEntity_0"]=function(){return(_emscripten_bind_InstanceEntity_InstanceEntity_0=Module["_emscripten_bind_InstanceEntity_InstanceEntity_0"]=Module["asm"]["qa"]).apply(null,arguments)};var _emscripten_bind_InstanceEntity_get_user_id_0=Module["_emscripten_bind_InstanceEntity_get_user_id_0"]=function(){return(_emscripten_bind_InstanceEntity_get_user_id_0=Module["_emscripten_bind_InstanceEntity_get_user_id_0"]=Module["asm"]["ra"]).apply(null,arguments)};var _emscripten_bind_InstanceEntity_get_userdata_id_0=Module["_emscripten_bind_InstanceEntity_get_userdata_id_0"]=function(){return(_emscripten_bind_InstanceEntity_get_userdata_id_0=Module["_emscripten_bind_InstanceEntity_get_userdata_id_0"]=Module["asm"]["sa"]).apply(null,arguments)};var _emscripten_bind_InstanceEntity_get_color_0=Module["_emscripten_bind_InstanceEntity_get_color_0"]=function(){return(_emscripten_bind_InstanceEntity_get_color_0=Module["_emscripten_bind_InstanceEntity_get_color_0"]=Module["asm"]["ta"]).apply(null,arguments)};var _emscripten_bind_InstanceEntity_num_components_matrix_0=Module["_emscripten_bind_InstanceEntity_num_components_matrix_0"]=function(){return(_emscripten_bind_InstanceEntity_num_components_matrix_0=Module["_emscripten_bind_InstanceEntity_num_components_matrix_0"]=Module["asm"]["ua"]).apply(null,arguments)};var _emscripten_bind_InstanceEntity_data_type_matrix_0=Module["_emscripten_bind_InstanceEntity_data_type_matrix_0"]=function(){return(_emscripten_bind_InstanceEntity_data_type_matrix_0=Module["_emscripten_bind_InstanceEntity_data_type_matrix_0"]=Module["asm"]["va"]).apply(null,arguments)};var _emscripten_bind_InstanceEntity_num_components_bbox_0=Module["_emscripten_bind_InstanceEntity_num_components_bbox_0"]=function(){return(_emscripten_bind_InstanceEntity_num_components_bbox_0=Module["_emscripten_bind_InstanceEntity_num_components_bbox_0"]=Module["asm"]["wa"]).apply(null,arguments)};var _emscripten_bind_InstanceEntity_data_type_bbox_0=Module["_emscripten_bind_InstanceEntity_data_type_bbox_0"]=function(){return(_emscripten_bind_InstanceEntity_data_type_bbox_0=Module["_emscripten_bind_InstanceEntity_data_type_bbox_0"]=Module["asm"]["xa"]).apply(null,arguments)};var _emscripten_bind_InstanceEntity_num_components_translation_0=Module["_emscripten_bind_InstanceEntity_num_components_translation_0"]=function(){return(_emscripten_bind_InstanceEntity_num_components_translation_0=Module["_emscripten_bind_InstanceEntity_num_components_translation_0"]=Module["asm"]["ya"]).apply(null,arguments)};var _emscripten_bind_InstanceEntity_data_type_translation_0=Module["_emscripten_bind_InstanceEntity_data_type_translation_0"]=function(){return(_emscripten_bind_InstanceEntity_data_type_translation_0=Module["_emscripten_bind_InstanceEntity_data_type_translation_0"]=Module["asm"]["za"]).apply(null,arguments)};var _emscripten_bind_InstanceEntity_num_components_packing_uvs_0=Module["_emscripten_bind_InstanceEntity_num_components_packing_uvs_0"]=function(){return(_emscripten_bind_InstanceEntity_num_components_packing_uvs_0=Module["_emscripten_bind_InstanceEntity_num_components_packing_uvs_0"]=Module["asm"]["Aa"]).apply(null,arguments)};var _emscripten_bind_InstanceEntity___destroy___0=Module["_emscripten_bind_InstanceEntity___destroy___0"]=function(){return(_emscripten_bind_InstanceEntity___destroy___0=Module["_emscripten_bind_InstanceEntity___destroy___0"]=Module["asm"]["Ba"]).apply(null,arguments)};var _emscripten_bind_BimTilesInstance_BimTilesInstance_0=Module["_emscripten_bind_BimTilesInstance_BimTilesInstance_0"]=function(){return(_emscripten_bind_BimTilesInstance_BimTilesInstance_0=Module["_emscripten_bind_BimTilesInstance_BimTilesInstance_0"]=Module["asm"]["Ca"]).apply(null,arguments)};var _emscripten_bind_BimTilesInstance_get_blob_id_0=Module["_emscripten_bind_BimTilesInstance_get_blob_id_0"]=function(){return(_emscripten_bind_BimTilesInstance_get_blob_id_0=Module["_emscripten_bind_BimTilesInstance_get_blob_id_0"]=Module["asm"]["Da"]).apply(null,arguments)};var _emscripten_bind_BimTilesInstance_get_tile_id_0=Module["_emscripten_bind_BimTilesInstance_get_tile_id_0"]=function(){return(_emscripten_bind_BimTilesInstance_get_tile_id_0=Module["_emscripten_bind_BimTilesInstance_get_tile_id_0"]=Module["asm"]["Ea"]).apply(null,arguments)};var _emscripten_bind_BimTilesInstance_get_block_id_0=Module["_emscripten_bind_BimTilesInstance_get_block_id_0"]=function(){return(_emscripten_bind_BimTilesInstance_get_block_id_0=Module["_emscripten_bind_BimTilesInstance_get_block_id_0"]=Module["asm"]["Fa"]).apply(null,arguments)};var _emscripten_bind_BimTilesInstance_get_entity_url_0=Module["_emscripten_bind_BimTilesInstance_get_entity_url_0"]=function(){return(_emscripten_bind_BimTilesInstance_get_entity_url_0=Module["_emscripten_bind_BimTilesInstance_get_entity_url_0"]=Module["asm"]["Ga"]).apply(null,arguments)};var _emscripten_bind_BimTilesInstance_num_instance_entities_0=Module["_emscripten_bind_BimTilesInstance_num_instance_entities_0"]=function(){return(_emscripten_bind_BimTilesInstance_num_instance_entities_0=Module["_emscripten_bind_BimTilesInstance_num_instance_entities_0"]=Module["asm"]["Ha"]).apply(null,arguments)};var _emscripten_bind_BimTilesInstance_GetInstanceEntity_1=Module["_emscripten_bind_BimTilesInstance_GetInstanceEntity_1"]=function(){return(_emscripten_bind_BimTilesInstance_GetInstanceEntity_1=Module["_emscripten_bind_BimTilesInstance_GetInstanceEntity_1"]=Module["asm"]["Ia"]).apply(null,arguments)};var _emscripten_bind_BimTilesInstance_num_components_matrix_0=Module["_emscripten_bind_BimTilesInstance_num_components_matrix_0"]=function(){return(_emscripten_bind_BimTilesInstance_num_components_matrix_0=Module["_emscripten_bind_BimTilesInstance_num_components_matrix_0"]=Module["asm"]["Ja"]).apply(null,arguments)};var _emscripten_bind_BimTilesInstance_data_type_matrix_0=Module["_emscripten_bind_BimTilesInstance_data_type_matrix_0"]=function(){return(_emscripten_bind_BimTilesInstance_data_type_matrix_0=Module["_emscripten_bind_BimTilesInstance_data_type_matrix_0"]=Module["asm"]["Ka"]).apply(null,arguments)};var _emscripten_bind_BimTilesInstance_GetHeaderMatrixFloat32Array_2=Module["_emscripten_bind_BimTilesInstance_GetHeaderMatrixFloat32Array_2"]=function(){return(_emscripten_bind_BimTilesInstance_GetHeaderMatrixFloat32Array_2=Module["_emscripten_bind_BimTilesInstance_GetHeaderMatrixFloat32Array_2"]=Module["asm"]["La"]).apply(null,arguments)};var _emscripten_bind_BimTilesInstance_num_components_bbox_0=Module["_emscripten_bind_BimTilesInstance_num_components_bbox_0"]=function(){return(_emscripten_bind_BimTilesInstance_num_components_bbox_0=Module["_emscripten_bind_BimTilesInstance_num_components_bbox_0"]=Module["asm"]["Ma"]).apply(null,arguments)};var _emscripten_bind_BimTilesInstance_data_type_bbox_0=Module["_emscripten_bind_BimTilesInstance_data_type_bbox_0"]=function(){return(_emscripten_bind_BimTilesInstance_data_type_bbox_0=Module["_emscripten_bind_BimTilesInstance_data_type_bbox_0"]=Module["asm"]["Na"]).apply(null,arguments)};var _emscripten_bind_BimTilesInstance_GetHeaderBBoxFloat32Array_2=Module["_emscripten_bind_BimTilesInstance_GetHeaderBBoxFloat32Array_2"]=function(){return(_emscripten_bind_BimTilesInstance_GetHeaderBBoxFloat32Array_2=Module["_emscripten_bind_BimTilesInstance_GetHeaderBBoxFloat32Array_2"]=Module["asm"]["Oa"]).apply(null,arguments)};var _emscripten_bind_BimTilesInstance_num_components_translation_0=Module["_emscripten_bind_BimTilesInstance_num_components_translation_0"]=function(){return(_emscripten_bind_BimTilesInstance_num_components_translation_0=Module["_emscripten_bind_BimTilesInstance_num_components_translation_0"]=Module["asm"]["Pa"]).apply(null,arguments)};var _emscripten_bind_BimTilesInstance_data_type_translation_0=Module["_emscripten_bind_BimTilesInstance_data_type_translation_0"]=function(){return(_emscripten_bind_BimTilesInstance_data_type_translation_0=Module["_emscripten_bind_BimTilesInstance_data_type_translation_0"]=Module["asm"]["Qa"]).apply(null,arguments)};var _emscripten_bind_BimTilesInstance_GetHeaderTranslationFloat64Array_2=Module["_emscripten_bind_BimTilesInstance_GetHeaderTranslationFloat64Array_2"]=function(){return(_emscripten_bind_BimTilesInstance_GetHeaderTranslationFloat64Array_2=Module["_emscripten_bind_BimTilesInstance_GetHeaderTranslationFloat64Array_2"]=Module["asm"]["Ra"]).apply(null,arguments)};var _emscripten_bind_BimTilesInstance_num_components_packing_uvs_0=Module["_emscripten_bind_BimTilesInstance_num_components_packing_uvs_0"]=function(){return(_emscripten_bind_BimTilesInstance_num_components_packing_uvs_0=Module["_emscripten_bind_BimTilesInstance_num_components_packing_uvs_0"]=Module["asm"]["Sa"]).apply(null,arguments)};var _emscripten_bind_BimTilesInstance_GetFloat32ArrayOfHeaderPackingUvs_2=Module["_emscripten_bind_BimTilesInstance_GetFloat32ArrayOfHeaderPackingUvs_2"]=function(){return(_emscripten_bind_BimTilesInstance_GetFloat32ArrayOfHeaderPackingUvs_2=Module["_emscripten_bind_BimTilesInstance_GetFloat32ArrayOfHeaderPackingUvs_2"]=Module["asm"]["Ta"]).apply(null,arguments)};var _emscripten_bind_BimTilesInstance_GetMatrixFloat32Array_3=Module["_emscripten_bind_BimTilesInstance_GetMatrixFloat32Array_3"]=function(){return(_emscripten_bind_BimTilesInstance_GetMatrixFloat32Array_3=Module["_emscripten_bind_BimTilesInstance_GetMatrixFloat32Array_3"]=Module["asm"]["Ua"]).apply(null,arguments)};var _emscripten_bind_BimTilesInstance_GetBBoxFloat32Array_3=Module["_emscripten_bind_BimTilesInstance_GetBBoxFloat32Array_3"]=function(){return(_emscripten_bind_BimTilesInstance_GetBBoxFloat32Array_3=Module["_emscripten_bind_BimTilesInstance_GetBBoxFloat32Array_3"]=Module["asm"]["Va"]).apply(null,arguments)};var _emscripten_bind_BimTilesInstance_GetTranslationFloat64Array_3=Module["_emscripten_bind_BimTilesInstance_GetTranslationFloat64Array_3"]=function(){return(_emscripten_bind_BimTilesInstance_GetTranslationFloat64Array_3=Module["_emscripten_bind_BimTilesInstance_GetTranslationFloat64Array_3"]=Module["asm"]["Wa"]).apply(null,arguments)};var _emscripten_bind_BimTilesInstance_GetFloat32ArrayOfPackingUvs_3=Module["_emscripten_bind_BimTilesInstance_GetFloat32ArrayOfPackingUvs_3"]=function(){return(_emscripten_bind_BimTilesInstance_GetFloat32ArrayOfPackingUvs_3=Module["_emscripten_bind_BimTilesInstance_GetFloat32ArrayOfPackingUvs_3"]=Module["asm"]["Xa"]).apply(null,arguments)};var _emscripten_bind_BimTilesInstance___destroy___0=Module["_emscripten_bind_BimTilesInstance___destroy___0"]=function(){return(_emscripten_bind_BimTilesInstance___destroy___0=Module["_emscripten_bind_BimTilesInstance___destroy___0"]=Module["asm"]["Ya"]).apply(null,arguments)};var _emscripten_bind_CustomDataItem_CustomDataItem_0=Module["_emscripten_bind_CustomDataItem_CustomDataItem_0"]=function(){return(_emscripten_bind_CustomDataItem_CustomDataItem_0=Module["_emscripten_bind_CustomDataItem_CustomDataItem_0"]=Module["asm"]["Za"]).apply(null,arguments)};var _emscripten_bind_CustomDataItem_get_name_0=Module["_emscripten_bind_CustomDataItem_get_name_0"]=function(){return(_emscripten_bind_CustomDataItem_get_name_0=Module["_emscripten_bind_CustomDataItem_get_name_0"]=Module["asm"]["_a"]).apply(null,arguments)};var _emscripten_bind_CustomDataItem___destroy___0=Module["_emscripten_bind_CustomDataItem___destroy___0"]=function(){return(_emscripten_bind_CustomDataItem___destroy___0=Module["_emscripten_bind_CustomDataItem___destroy___0"]=Module["asm"]["$a"]).apply(null,arguments)};var _emscripten_bind_ImageItem_ImageItem_0=Module["_emscripten_bind_ImageItem_ImageItem_0"]=function(){return(_emscripten_bind_ImageItem_ImageItem_0=Module["_emscripten_bind_ImageItem_ImageItem_0"]=Module["asm"]["ab"]).apply(null,arguments)};var _emscripten_bind_ImageItem_get_url_0=Module["_emscripten_bind_ImageItem_get_url_0"]=function(){return(_emscripten_bind_ImageItem_get_url_0=Module["_emscripten_bind_ImageItem_get_url_0"]=Module["asm"]["bb"]).apply(null,arguments)};var _emscripten_bind_ImageItem_get_type_0=Module["_emscripten_bind_ImageItem_get_type_0"]=function(){return(_emscripten_bind_ImageItem_get_type_0=Module["_emscripten_bind_ImageItem_get_type_0"]=Module["asm"]["cb"]).apply(null,arguments)};var _emscripten_bind_ImageItem_get_width_0=Module["_emscripten_bind_ImageItem_get_width_0"]=function(){return(_emscripten_bind_ImageItem_get_width_0=Module["_emscripten_bind_ImageItem_get_width_0"]=Module["asm"]["db"]).apply(null,arguments)};var _emscripten_bind_ImageItem_get_height_0=Module["_emscripten_bind_ImageItem_get_height_0"]=function(){return(_emscripten_bind_ImageItem_get_height_0=Module["_emscripten_bind_ImageItem_get_height_0"]=Module["asm"]["eb"]).apply(null,arguments)};var _emscripten_bind_ImageItem_get_dimension_0=Module["_emscripten_bind_ImageItem_get_dimension_0"]=function(){return(_emscripten_bind_ImageItem_get_dimension_0=Module["_emscripten_bind_ImageItem_get_dimension_0"]=Module["asm"]["fb"]).apply(null,arguments)};var _emscripten_bind_ImageItem_get_buffer_id_0=Module["_emscripten_bind_ImageItem_get_buffer_id_0"]=function(){return(_emscripten_bind_ImageItem_get_buffer_id_0=Module["_emscripten_bind_ImageItem_get_buffer_id_0"]=Module["asm"]["gb"]).apply(null,arguments)};var _emscripten_bind_ImageItem_get_byte_offset_0=Module["_emscripten_bind_ImageItem_get_byte_offset_0"]=function(){return(_emscripten_bind_ImageItem_get_byte_offset_0=Module["_emscripten_bind_ImageItem_get_byte_offset_0"]=Module["asm"]["hb"]).apply(null,arguments)};var _emscripten_bind_ImageItem_get_byte_length_0=Module["_emscripten_bind_ImageItem_get_byte_length_0"]=function(){return(_emscripten_bind_ImageItem_get_byte_length_0=Module["_emscripten_bind_ImageItem_get_byte_length_0"]=Module["asm"]["ib"]).apply(null,arguments)};var _emscripten_bind_ImageItem_HasImageBuffer_0=Module["_emscripten_bind_ImageItem_HasImageBuffer_0"]=function(){return(_emscripten_bind_ImageItem_HasImageBuffer_0=Module["_emscripten_bind_ImageItem_HasImageBuffer_0"]=Module["asm"]["jb"]).apply(null,arguments)};var _emscripten_bind_ImageItem_GetImageBuffer_2=Module["_emscripten_bind_ImageItem_GetImageBuffer_2"]=function(){return(_emscripten_bind_ImageItem_GetImageBuffer_2=Module["_emscripten_bind_ImageItem_GetImageBuffer_2"]=Module["asm"]["kb"]).apply(null,arguments)};var _emscripten_bind_ImageItem___destroy___0=Module["_emscripten_bind_ImageItem___destroy___0"]=function(){return(_emscripten_bind_ImageItem___destroy___0=Module["_emscripten_bind_ImageItem___destroy___0"]=Module["asm"]["lb"]).apply(null,arguments)};var _emscripten_bind_TextureItem_TextureItem_0=Module["_emscripten_bind_TextureItem_TextureItem_0"]=function(){return(_emscripten_bind_TextureItem_TextureItem_0=Module["_emscripten_bind_TextureItem_TextureItem_0"]=Module["asm"]["mb"]).apply(null,arguments)};var _emscripten_bind_TextureItem_get_name_0=Module["_emscripten_bind_TextureItem_get_name_0"]=function(){return(_emscripten_bind_TextureItem_get_name_0=Module["_emscripten_bind_TextureItem_get_name_0"]=Module["asm"]["nb"]).apply(null,arguments)};var _emscripten_bind_TextureItem_get_type_0=Module["_emscripten_bind_TextureItem_get_type_0"]=function(){return(_emscripten_bind_TextureItem_get_type_0=Module["_emscripten_bind_TextureItem_get_type_0"]=Module["asm"]["ob"]).apply(null,arguments)};var _emscripten_bind_TextureItem_get_image_0=Module["_emscripten_bind_TextureItem_get_image_0"]=function(){return(_emscripten_bind_TextureItem_get_image_0=Module["_emscripten_bind_TextureItem_get_image_0"]=Module["asm"]["pb"]).apply(null,arguments)};var _emscripten_bind_TextureItem_get_repeat_u_0=Module["_emscripten_bind_TextureItem_get_repeat_u_0"]=function(){return(_emscripten_bind_TextureItem_get_repeat_u_0=Module["_emscripten_bind_TextureItem_get_repeat_u_0"]=Module["asm"]["qb"]).apply(null,arguments)};var _emscripten_bind_TextureItem_get_repeat_v_0=Module["_emscripten_bind_TextureItem_get_repeat_v_0"]=function(){return(_emscripten_bind_TextureItem_get_repeat_v_0=Module["_emscripten_bind_TextureItem_get_repeat_v_0"]=Module["asm"]["rb"]).apply(null,arguments)};var _emscripten_bind_TextureItem_get_angle_0=Module["_emscripten_bind_TextureItem_get_angle_0"]=function(){return(_emscripten_bind_TextureItem_get_angle_0=Module["_emscripten_bind_TextureItem_get_angle_0"]=Module["asm"]["sb"]).apply(null,arguments)};var _emscripten_bind_TextureItem_get_alpha_0=Module["_emscripten_bind_TextureItem_get_alpha_0"]=function(){return(_emscripten_bind_TextureItem_get_alpha_0=Module["_emscripten_bind_TextureItem_get_alpha_0"]=Module["asm"]["tb"]).apply(null,arguments)};var _emscripten_bind_TextureItem_get_depth_0=Module["_emscripten_bind_TextureItem_get_depth_0"]=function(){return(_emscripten_bind_TextureItem_get_depth_0=Module["_emscripten_bind_TextureItem_get_depth_0"]=Module["asm"]["ub"]).apply(null,arguments)};var _emscripten_bind_TextureItem_get_offset_u_0=Module["_emscripten_bind_TextureItem_get_offset_u_0"]=function(){return(_emscripten_bind_TextureItem_get_offset_u_0=Module["_emscripten_bind_TextureItem_get_offset_u_0"]=Module["asm"]["vb"]).apply(null,arguments)};var _emscripten_bind_TextureItem_get_offset_v_0=Module["_emscripten_bind_TextureItem_get_offset_v_0"]=function(){return(_emscripten_bind_TextureItem_get_offset_v_0=Module["_emscripten_bind_TextureItem_get_offset_v_0"]=Module["asm"]["wb"]).apply(null,arguments)};var _emscripten_bind_TextureItem_get_scale_u_0=Module["_emscripten_bind_TextureItem_get_scale_u_0"]=function(){return(_emscripten_bind_TextureItem_get_scale_u_0=Module["_emscripten_bind_TextureItem_get_scale_u_0"]=Module["asm"]["xb"]).apply(null,arguments)};var _emscripten_bind_TextureItem_get_scale_v_0=Module["_emscripten_bind_TextureItem_get_scale_v_0"]=function(){return(_emscripten_bind_TextureItem_get_scale_v_0=Module["_emscripten_bind_TextureItem_get_scale_v_0"]=Module["asm"]["yb"]).apply(null,arguments)};var _emscripten_bind_TextureItem_num_entries_lod_images_0=Module["_emscripten_bind_TextureItem_num_entries_lod_images_0"]=function(){return(_emscripten_bind_TextureItem_num_entries_lod_images_0=Module["_emscripten_bind_TextureItem_num_entries_lod_images_0"]=Module["asm"]["zb"]).apply(null,arguments)};var _emscripten_bind_TextureItem_byte_length_per_entry_lod_images_0=Module["_emscripten_bind_TextureItem_byte_length_per_entry_lod_images_0"]=function(){return(_emscripten_bind_TextureItem_byte_length_per_entry_lod_images_0=Module["_emscripten_bind_TextureItem_byte_length_per_entry_lod_images_0"]=Module["asm"]["Ab"]).apply(null,arguments)};var _emscripten_bind_TextureItem___destroy___0=Module["_emscripten_bind_TextureItem___destroy___0"]=function(){return(_emscripten_bind_TextureItem___destroy___0=Module["_emscripten_bind_TextureItem___destroy___0"]=Module["asm"]["Bb"]).apply(null,arguments)};var _emscripten_bind_MaterialItem_MaterialItem_0=Module["_emscripten_bind_MaterialItem_MaterialItem_0"]=function(){return(_emscripten_bind_MaterialItem_MaterialItem_0=Module["_emscripten_bind_MaterialItem_MaterialItem_0"]=Module["asm"]["Cb"]).apply(null,arguments)};var _emscripten_bind_MaterialItem_get_name_0=Module["_emscripten_bind_MaterialItem_get_name_0"]=function(){return(_emscripten_bind_MaterialItem_get_name_0=Module["_emscripten_bind_MaterialItem_get_name_0"]=Module["asm"]["Db"]).apply(null,arguments)};var _emscripten_bind_MaterialItem_get_type_0=Module["_emscripten_bind_MaterialItem_get_type_0"]=function(){return(_emscripten_bind_MaterialItem_get_type_0=Module["_emscripten_bind_MaterialItem_get_type_0"]=Module["asm"]["Eb"]).apply(null,arguments)};var _emscripten_bind_MaterialItem_get_color_0=Module["_emscripten_bind_MaterialItem_get_color_0"]=function(){return(_emscripten_bind_MaterialItem_get_color_0=Module["_emscripten_bind_MaterialItem_get_color_0"]=Module["asm"]["Fb"]).apply(null,arguments)};var _emscripten_bind_MaterialItem_get_double_side_0=Module["_emscripten_bind_MaterialItem_get_double_side_0"]=function(){return(_emscripten_bind_MaterialItem_get_double_side_0=Module["_emscripten_bind_MaterialItem_get_double_side_0"]=Module["asm"]["Gb"]).apply(null,arguments)};var _emscripten_bind_MaterialItem_get_receive_ibl_0=Module["_emscripten_bind_MaterialItem_get_receive_ibl_0"]=function(){return(_emscripten_bind_MaterialItem_get_receive_ibl_0=Module["_emscripten_bind_MaterialItem_get_receive_ibl_0"]=Module["asm"]["Hb"]).apply(null,arguments)};var _emscripten_bind_MaterialItem_get_opacity_0=Module["_emscripten_bind_MaterialItem_get_opacity_0"]=function(){return(_emscripten_bind_MaterialItem_get_opacity_0=Module["_emscripten_bind_MaterialItem_get_opacity_0"]=Module["asm"]["Ib"]).apply(null,arguments)};var _emscripten_bind_MaterialItem_get_metalness_0=Module["_emscripten_bind_MaterialItem_get_metalness_0"]=function(){return(_emscripten_bind_MaterialItem_get_metalness_0=Module["_emscripten_bind_MaterialItem_get_metalness_0"]=Module["asm"]["Jb"]).apply(null,arguments)};var _emscripten_bind_MaterialItem_get_roughness_0=Module["_emscripten_bind_MaterialItem_get_roughness_0"]=function(){return(_emscripten_bind_MaterialItem_get_roughness_0=Module["_emscripten_bind_MaterialItem_get_roughness_0"]=Module["asm"]["Kb"]).apply(null,arguments)};var _emscripten_bind_MaterialItem_num_entries_textures_0=Module["_emscripten_bind_MaterialItem_num_entries_textures_0"]=function(){return(_emscripten_bind_MaterialItem_num_entries_textures_0=Module["_emscripten_bind_MaterialItem_num_entries_textures_0"]=Module["asm"]["Lb"]).apply(null,arguments)};var _emscripten_bind_MaterialItem_byte_length_per_entry_textures_0=Module["_emscripten_bind_MaterialItem_byte_length_per_entry_textures_0"]=function(){return(_emscripten_bind_MaterialItem_byte_length_per_entry_textures_0=Module["_emscripten_bind_MaterialItem_byte_length_per_entry_textures_0"]=Module["asm"]["Mb"]).apply(null,arguments)};var _emscripten_bind_MaterialItem_num_entries_custom_data_items_0=Module["_emscripten_bind_MaterialItem_num_entries_custom_data_items_0"]=function(){return(_emscripten_bind_MaterialItem_num_entries_custom_data_items_0=Module["_emscripten_bind_MaterialItem_num_entries_custom_data_items_0"]=Module["asm"]["Nb"]).apply(null,arguments)};var _emscripten_bind_MaterialItem_GetCustomDataItem_1=Module["_emscripten_bind_MaterialItem_GetCustomDataItem_1"]=function(){return(_emscripten_bind_MaterialItem_GetCustomDataItem_1=Module["_emscripten_bind_MaterialItem_GetCustomDataItem_1"]=Module["asm"]["Ob"]).apply(null,arguments)};var _emscripten_bind_MaterialItem___destroy___0=Module["_emscripten_bind_MaterialItem___destroy___0"]=function(){return(_emscripten_bind_MaterialItem___destroy___0=Module["_emscripten_bind_MaterialItem___destroy___0"]=Module["asm"]["Pb"]).apply(null,arguments)};var _emscripten_bind_BimTilesMaterial_BimTilesMaterial_0=Module["_emscripten_bind_BimTilesMaterial_BimTilesMaterial_0"]=function(){return(_emscripten_bind_BimTilesMaterial_BimTilesMaterial_0=Module["_emscripten_bind_BimTilesMaterial_BimTilesMaterial_0"]=Module["asm"]["Qb"]).apply(null,arguments)};var _emscripten_bind_BimTilesMaterial_get_tile_id_0=Module["_emscripten_bind_BimTilesMaterial_get_tile_id_0"]=function(){return(_emscripten_bind_BimTilesMaterial_get_tile_id_0=Module["_emscripten_bind_BimTilesMaterial_get_tile_id_0"]=Module["asm"]["Rb"]).apply(null,arguments)};var _emscripten_bind_BimTilesMaterial_get_block_id_0=Module["_emscripten_bind_BimTilesMaterial_get_block_id_0"]=function(){return(_emscripten_bind_BimTilesMaterial_get_block_id_0=Module["_emscripten_bind_BimTilesMaterial_get_block_id_0"]=Module["asm"]["Sb"]).apply(null,arguments)};var _emscripten_bind_BimTilesMaterial_get_material_count_0=Module["_emscripten_bind_BimTilesMaterial_get_material_count_0"]=function(){return(_emscripten_bind_BimTilesMaterial_get_material_count_0=Module["_emscripten_bind_BimTilesMaterial_get_material_count_0"]=Module["asm"]["Tb"]).apply(null,arguments)};var _emscripten_bind_BimTilesMaterial_get_texture_count_0=Module["_emscripten_bind_BimTilesMaterial_get_texture_count_0"]=function(){return(_emscripten_bind_BimTilesMaterial_get_texture_count_0=Module["_emscripten_bind_BimTilesMaterial_get_texture_count_0"]=Module["asm"]["Ub"]).apply(null,arguments)};var _emscripten_bind_BimTilesMaterial_get_image_count_0=Module["_emscripten_bind_BimTilesMaterial_get_image_count_0"]=function(){return(_emscripten_bind_BimTilesMaterial_get_image_count_0=Module["_emscripten_bind_BimTilesMaterial_get_image_count_0"]=Module["asm"]["Vb"]).apply(null,arguments)};var _emscripten_bind_BimTilesMaterial_GetMaterialItemCount_0=Module["_emscripten_bind_BimTilesMaterial_GetMaterialItemCount_0"]=function(){return(_emscripten_bind_BimTilesMaterial_GetMaterialItemCount_0=Module["_emscripten_bind_BimTilesMaterial_GetMaterialItemCount_0"]=Module["asm"]["Wb"]).apply(null,arguments)};var _emscripten_bind_BimTilesMaterial_GetTextureItemCount_0=Module["_emscripten_bind_BimTilesMaterial_GetTextureItemCount_0"]=function(){return(_emscripten_bind_BimTilesMaterial_GetTextureItemCount_0=Module["_emscripten_bind_BimTilesMaterial_GetTextureItemCount_0"]=Module["asm"]["Xb"]).apply(null,arguments)};var _emscripten_bind_BimTilesMaterial_GetImageItemCount_0=Module["_emscripten_bind_BimTilesMaterial_GetImageItemCount_0"]=function(){return(_emscripten_bind_BimTilesMaterial_GetImageItemCount_0=Module["_emscripten_bind_BimTilesMaterial_GetImageItemCount_0"]=Module["asm"]["Yb"]).apply(null,arguments)};var _emscripten_bind_BimTilesMaterial_GetMaterialItem_1=Module["_emscripten_bind_BimTilesMaterial_GetMaterialItem_1"]=function(){return(_emscripten_bind_BimTilesMaterial_GetMaterialItem_1=Module["_emscripten_bind_BimTilesMaterial_GetMaterialItem_1"]=Module["asm"]["Zb"]).apply(null,arguments)};var _emscripten_bind_BimTilesMaterial_GetTextureItem_1=Module["_emscripten_bind_BimTilesMaterial_GetTextureItem_1"]=function(){return(_emscripten_bind_BimTilesMaterial_GetTextureItem_1=Module["_emscripten_bind_BimTilesMaterial_GetTextureItem_1"]=Module["asm"]["_b"]).apply(null,arguments)};var _emscripten_bind_BimTilesMaterial_GetImageItem_1=Module["_emscripten_bind_BimTilesMaterial_GetImageItem_1"]=function(){return(_emscripten_bind_BimTilesMaterial_GetImageItem_1=Module["_emscripten_bind_BimTilesMaterial_GetImageItem_1"]=Module["asm"]["$b"]).apply(null,arguments)};var _emscripten_bind_BimTilesMaterial_GetTextureIdsInt32Array_3=Module["_emscripten_bind_BimTilesMaterial_GetTextureIdsInt32Array_3"]=function(){return(_emscripten_bind_BimTilesMaterial_GetTextureIdsInt32Array_3=Module["_emscripten_bind_BimTilesMaterial_GetTextureIdsInt32Array_3"]=Module["asm"]["ac"]).apply(null,arguments)};var _emscripten_bind_BimTilesMaterial_GetLodImageIdsInt32Array_3=Module["_emscripten_bind_BimTilesMaterial_GetLodImageIdsInt32Array_3"]=function(){return(_emscripten_bind_BimTilesMaterial_GetLodImageIdsInt32Array_3=Module["_emscripten_bind_BimTilesMaterial_GetLodImageIdsInt32Array_3"]=Module["asm"]["bc"]).apply(null,arguments)};var _emscripten_bind_BimTilesMaterial_GetKeyForCustomDataWithInt32Values_2=Module["_emscripten_bind_BimTilesMaterial_GetKeyForCustomDataWithInt32Values_2"]=function(){return(_emscripten_bind_BimTilesMaterial_GetKeyForCustomDataWithInt32Values_2=Module["_emscripten_bind_BimTilesMaterial_GetKeyForCustomDataWithInt32Values_2"]=Module["asm"]["cc"]).apply(null,arguments)};var _emscripten_bind_BimTilesMaterial_GetValueInt32ArrayForCustomData_3=Module["_emscripten_bind_BimTilesMaterial_GetValueInt32ArrayForCustomData_3"]=function(){return(_emscripten_bind_BimTilesMaterial_GetValueInt32ArrayForCustomData_3=Module["_emscripten_bind_BimTilesMaterial_GetValueInt32ArrayForCustomData_3"]=Module["asm"]["dc"]).apply(null,arguments)};var _emscripten_bind_BimTilesMaterial_GetKeyForCustomDataWithFloatValues_2=Module["_emscripten_bind_BimTilesMaterial_GetKeyForCustomDataWithFloatValues_2"]=function(){return(_emscripten_bind_BimTilesMaterial_GetKeyForCustomDataWithFloatValues_2=Module["_emscripten_bind_BimTilesMaterial_GetKeyForCustomDataWithFloatValues_2"]=Module["asm"]["ec"]).apply(null,arguments)};var _emscripten_bind_BimTilesMaterial_GetValueFloatArrayForCustomData_3=Module["_emscripten_bind_BimTilesMaterial_GetValueFloatArrayForCustomData_3"]=function(){return(_emscripten_bind_BimTilesMaterial_GetValueFloatArrayForCustomData_3=Module["_emscripten_bind_BimTilesMaterial_GetValueFloatArrayForCustomData_3"]=Module["asm"]["fc"]).apply(null,arguments)};var _emscripten_bind_BimTilesMaterial_GetKeyForCustomDataWithStringValues_2=Module["_emscripten_bind_BimTilesMaterial_GetKeyForCustomDataWithStringValues_2"]=function(){return(_emscripten_bind_BimTilesMaterial_GetKeyForCustomDataWithStringValues_2=Module["_emscripten_bind_BimTilesMaterial_GetKeyForCustomDataWithStringValues_2"]=Module["asm"]["gc"]).apply(null,arguments)};var _emscripten_bind_BimTilesMaterial_GetValueForCustomDataWithStringValues_2=Module["_emscripten_bind_BimTilesMaterial_GetValueForCustomDataWithStringValues_2"]=function(){return(_emscripten_bind_BimTilesMaterial_GetValueForCustomDataWithStringValues_2=Module["_emscripten_bind_BimTilesMaterial_GetValueForCustomDataWithStringValues_2"]=Module["asm"]["hc"]).apply(null,arguments)};var _emscripten_bind_BimTilesMaterial___destroy___0=Module["_emscripten_bind_BimTilesMaterial___destroy___0"]=function(){return(_emscripten_bind_BimTilesMaterial___destroy___0=Module["_emscripten_bind_BimTilesMaterial___destroy___0"]=Module["asm"]["ic"]).apply(null,arguments)};var _emscripten_bind_UserIdInfo_UserIdInfo_0=Module["_emscripten_bind_UserIdInfo_UserIdInfo_0"]=function(){return(_emscripten_bind_UserIdInfo_UserIdInfo_0=Module["_emscripten_bind_UserIdInfo_UserIdInfo_0"]=Module["asm"]["jc"]).apply(null,arguments)};var _emscripten_bind_UserIdInfo_get_tile_id_0=Module["_emscripten_bind_UserIdInfo_get_tile_id_0"]=function(){return(_emscripten_bind_UserIdInfo_get_tile_id_0=Module["_emscripten_bind_UserIdInfo_get_tile_id_0"]=Module["asm"]["kc"]).apply(null,arguments)};var _emscripten_bind_UserIdInfo_get_block_id_0=Module["_emscripten_bind_UserIdInfo_get_block_id_0"]=function(){return(_emscripten_bind_UserIdInfo_get_block_id_0=Module["_emscripten_bind_UserIdInfo_get_block_id_0"]=Module["asm"]["lc"]).apply(null,arguments)};var _emscripten_bind_UserIdInfo_get_node_id_0=Module["_emscripten_bind_UserIdInfo_get_node_id_0"]=function(){return(_emscripten_bind_UserIdInfo_get_node_id_0=Module["_emscripten_bind_UserIdInfo_get_node_id_0"]=Module["asm"]["mc"]).apply(null,arguments)};var _emscripten_bind_UserIdInfo_get_user_id_0=Module["_emscripten_bind_UserIdInfo_get_user_id_0"]=function(){return(_emscripten_bind_UserIdInfo_get_user_id_0=Module["_emscripten_bind_UserIdInfo_get_user_id_0"]=Module["asm"]["nc"]).apply(null,arguments)};var _emscripten_bind_UserIdInfo_get_userdata_id_0=Module["_emscripten_bind_UserIdInfo_get_userdata_id_0"]=function(){return(_emscripten_bind_UserIdInfo_get_userdata_id_0=Module["_emscripten_bind_UserIdInfo_get_userdata_id_0"]=Module["asm"]["oc"]).apply(null,arguments)};var _emscripten_bind_UserIdInfo_num_bbox_components_0=Module["_emscripten_bind_UserIdInfo_num_bbox_components_0"]=function(){return(_emscripten_bind_UserIdInfo_num_bbox_components_0=Module["_emscripten_bind_UserIdInfo_num_bbox_components_0"]=Module["asm"]["pc"]).apply(null,arguments)};var _emscripten_bind_UserIdInfo_bytesize_bbox_per_component_0=Module["_emscripten_bind_UserIdInfo_bytesize_bbox_per_component_0"]=function(){return(_emscripten_bind_UserIdInfo_bytesize_bbox_per_component_0=Module["_emscripten_bind_UserIdInfo_bytesize_bbox_per_component_0"]=Module["asm"]["qc"]).apply(null,arguments)};var _emscripten_bind_UserIdInfo___destroy___0=Module["_emscripten_bind_UserIdInfo___destroy___0"]=function(){return(_emscripten_bind_UserIdInfo___destroy___0=Module["_emscripten_bind_UserIdInfo___destroy___0"]=Module["asm"]["rc"]).apply(null,arguments)};var _emscripten_bind_BimTilesUserId_BimTilesUserId_0=Module["_emscripten_bind_BimTilesUserId_BimTilesUserId_0"]=function(){return(_emscripten_bind_BimTilesUserId_BimTilesUserId_0=Module["_emscripten_bind_BimTilesUserId_BimTilesUserId_0"]=Module["asm"]["sc"]).apply(null,arguments)};var _emscripten_bind_BimTilesUserId_get_tile_id_0=Module["_emscripten_bind_BimTilesUserId_get_tile_id_0"]=function(){return(_emscripten_bind_BimTilesUserId_get_tile_id_0=Module["_emscripten_bind_BimTilesUserId_get_tile_id_0"]=Module["asm"]["tc"]).apply(null,arguments)};var _emscripten_bind_BimTilesUserId_get_block_id_0=Module["_emscripten_bind_BimTilesUserId_get_block_id_0"]=function(){return(_emscripten_bind_BimTilesUserId_get_block_id_0=Module["_emscripten_bind_BimTilesUserId_get_block_id_0"]=Module["asm"]["uc"]).apply(null,arguments)};var _emscripten_bind_BimTilesUserId_get_user_id_min_0=Module["_emscripten_bind_BimTilesUserId_get_user_id_min_0"]=function(){return(_emscripten_bind_BimTilesUserId_get_user_id_min_0=Module["_emscripten_bind_BimTilesUserId_get_user_id_min_0"]=Module["asm"]["vc"]).apply(null,arguments)};var _emscripten_bind_BimTilesUserId_get_user_id_max_0=Module["_emscripten_bind_BimTilesUserId_get_user_id_max_0"]=function(){return(_emscripten_bind_BimTilesUserId_get_user_id_max_0=Module["_emscripten_bind_BimTilesUserId_get_user_id_max_0"]=Module["asm"]["wc"]).apply(null,arguments)};var _emscripten_bind_BimTilesUserId_num_user_id_infos_0=Module["_emscripten_bind_BimTilesUserId_num_user_id_infos_0"]=function(){return(_emscripten_bind_BimTilesUserId_num_user_id_infos_0=Module["_emscripten_bind_BimTilesUserId_num_user_id_infos_0"]=Module["asm"]["xc"]).apply(null,arguments)};var _emscripten_bind_BimTilesUserId_num_user_ids_0=Module["_emscripten_bind_BimTilesUserId_num_user_ids_0"]=function(){return(_emscripten_bind_BimTilesUserId_num_user_ids_0=Module["_emscripten_bind_BimTilesUserId_num_user_ids_0"]=Module["asm"]["yc"]).apply(null,arguments)};var _emscripten_bind_BimTilesUserId_get_first_user_id_0=Module["_emscripten_bind_BimTilesUserId_get_first_user_id_0"]=function(){return(_emscripten_bind_BimTilesUserId_get_first_user_id_0=Module["_emscripten_bind_BimTilesUserId_get_first_user_id_0"]=Module["asm"]["zc"]).apply(null,arguments)};var _emscripten_bind_BimTilesUserId_get_last_user_id_0=Module["_emscripten_bind_BimTilesUserId_get_last_user_id_0"]=function(){return(_emscripten_bind_BimTilesUserId_get_last_user_id_0=Module["_emscripten_bind_BimTilesUserId_get_last_user_id_0"]=Module["asm"]["Ac"]).apply(null,arguments)};var _emscripten_bind_BimTilesUserId_GetUserIdInfo_1=Module["_emscripten_bind_BimTilesUserId_GetUserIdInfo_1"]=function(){return(_emscripten_bind_BimTilesUserId_GetUserIdInfo_1=Module["_emscripten_bind_BimTilesUserId_GetUserIdInfo_1"]=Module["asm"]["Bc"]).apply(null,arguments)};var _emscripten_bind_BimTilesUserId_GetUserIdStr_1=Module["_emscripten_bind_BimTilesUserId_GetUserIdStr_1"]=function(){return(_emscripten_bind_BimTilesUserId_GetUserIdStr_1=Module["_emscripten_bind_BimTilesUserId_GetUserIdStr_1"]=Module["asm"]["Cc"]).apply(null,arguments)};var _emscripten_bind_BimTilesUserId_GetBBoxFloat32Array_3=Module["_emscripten_bind_BimTilesUserId_GetBBoxFloat32Array_3"]=function(){return(_emscripten_bind_BimTilesUserId_GetBBoxFloat32Array_3=Module["_emscripten_bind_BimTilesUserId_GetBBoxFloat32Array_3"]=Module["asm"]["Dc"]).apply(null,arguments)};var _emscripten_bind_BimTilesUserId___destroy___0=Module["_emscripten_bind_BimTilesUserId___destroy___0"]=function(){return(_emscripten_bind_BimTilesUserId___destroy___0=Module["_emscripten_bind_BimTilesUserId___destroy___0"]=Module["asm"]["Ec"]).apply(null,arguments)};var _emscripten_bind_UserDataItem_UserDataItem_0=Module["_emscripten_bind_UserDataItem_UserDataItem_0"]=function(){return(_emscripten_bind_UserDataItem_UserDataItem_0=Module["_emscripten_bind_UserDataItem_UserDataItem_0"]=Module["asm"]["Fc"]).apply(null,arguments)};var _emscripten_bind_UserDataItem_get_item_name_0=Module["_emscripten_bind_UserDataItem_get_item_name_0"]=function(){return(_emscripten_bind_UserDataItem_get_item_name_0=Module["_emscripten_bind_UserDataItem_get_item_name_0"]=Module["asm"]["Gc"]).apply(null,arguments)};var _emscripten_bind_UserDataItem_num_components_0=Module["_emscripten_bind_UserDataItem_num_components_0"]=function(){return(_emscripten_bind_UserDataItem_num_components_0=Module["_emscripten_bind_UserDataItem_num_components_0"]=Module["asm"]["Hc"]).apply(null,arguments)};var _emscripten_bind_UserDataItem_num_entries_int32_0=Module["_emscripten_bind_UserDataItem_num_entries_int32_0"]=function(){return(_emscripten_bind_UserDataItem_num_entries_int32_0=Module["_emscripten_bind_UserDataItem_num_entries_int32_0"]=Module["asm"]["Ic"]).apply(null,arguments)};var _emscripten_bind_UserDataItem_num_entries_float_0=Module["_emscripten_bind_UserDataItem_num_entries_float_0"]=function(){return(_emscripten_bind_UserDataItem_num_entries_float_0=Module["_emscripten_bind_UserDataItem_num_entries_float_0"]=Module["asm"]["Jc"]).apply(null,arguments)};var _emscripten_bind_UserDataItem_num_entries_string_0=Module["_emscripten_bind_UserDataItem_num_entries_string_0"]=function(){return(_emscripten_bind_UserDataItem_num_entries_string_0=Module["_emscripten_bind_UserDataItem_num_entries_string_0"]=Module["asm"]["Kc"]).apply(null,arguments)};var _emscripten_bind_UserDataItem_byte_length_per_component_0=Module["_emscripten_bind_UserDataItem_byte_length_per_component_0"]=function(){return(_emscripten_bind_UserDataItem_byte_length_per_component_0=Module["_emscripten_bind_UserDataItem_byte_length_per_component_0"]=Module["asm"]["Lc"]).apply(null,arguments)};var _emscripten_bind_UserDataItem_byte_length_per_component_float_0=Module["_emscripten_bind_UserDataItem_byte_length_per_component_float_0"]=function(){return(_emscripten_bind_UserDataItem_byte_length_per_component_float_0=Module["_emscripten_bind_UserDataItem_byte_length_per_component_float_0"]=Module["asm"]["Mc"]).apply(null,arguments)};var _emscripten_bind_UserDataItem___destroy___0=Module["_emscripten_bind_UserDataItem___destroy___0"]=function(){return(_emscripten_bind_UserDataItem___destroy___0=Module["_emscripten_bind_UserDataItem___destroy___0"]=Module["asm"]["Nc"]).apply(null,arguments)};var _emscripten_bind_BimTilesUserData_BimTilesUserData_0=Module["_emscripten_bind_BimTilesUserData_BimTilesUserData_0"]=function(){return(_emscripten_bind_BimTilesUserData_BimTilesUserData_0=Module["_emscripten_bind_BimTilesUserData_BimTilesUserData_0"]=Module["asm"]["Oc"]).apply(null,arguments)};var _emscripten_bind_BimTilesUserData_GetTileId_0=Module["_emscripten_bind_BimTilesUserData_GetTileId_0"]=function(){return(_emscripten_bind_BimTilesUserData_GetTileId_0=Module["_emscripten_bind_BimTilesUserData_GetTileId_0"]=Module["asm"]["Pc"]).apply(null,arguments)};var _emscripten_bind_BimTilesUserData_GetBlockId_0=Module["_emscripten_bind_BimTilesUserData_GetBlockId_0"]=function(){return(_emscripten_bind_BimTilesUserData_GetBlockId_0=Module["_emscripten_bind_BimTilesUserData_GetBlockId_0"]=Module["asm"]["Qc"]).apply(null,arguments)};var _emscripten_bind_BimTilesUserData_GetUserdataCount_0=Module["_emscripten_bind_BimTilesUserData_GetUserdataCount_0"]=function(){return(_emscripten_bind_BimTilesUserData_GetUserdataCount_0=Module["_emscripten_bind_BimTilesUserData_GetUserdataCount_0"]=Module["asm"]["Rc"]).apply(null,arguments)};var _emscripten_bind_BimTilesUserData_GetKeysCount_0=Module["_emscripten_bind_BimTilesUserData_GetKeysCount_0"]=function(){return(_emscripten_bind_BimTilesUserData_GetKeysCount_0=Module["_emscripten_bind_BimTilesUserData_GetKeysCount_0"]=Module["asm"]["Sc"]).apply(null,arguments)};var _emscripten_bind_BimTilesUserData_GetValuesCount_0=Module["_emscripten_bind_BimTilesUserData_GetValuesCount_0"]=function(){return(_emscripten_bind_BimTilesUserData_GetValuesCount_0=Module["_emscripten_bind_BimTilesUserData_GetValuesCount_0"]=Module["asm"]["Tc"]).apply(null,arguments)};var _emscripten_bind_BimTilesUserData_GetStringKey_1=Module["_emscripten_bind_BimTilesUserData_GetStringKey_1"]=function(){return(_emscripten_bind_BimTilesUserData_GetStringKey_1=Module["_emscripten_bind_BimTilesUserData_GetStringKey_1"]=Module["asm"]["Uc"]).apply(null,arguments)};var _emscripten_bind_BimTilesUserData_GetStringValue_1=Module["_emscripten_bind_BimTilesUserData_GetStringValue_1"]=function(){return(_emscripten_bind_BimTilesUserData_GetStringValue_1=Module["_emscripten_bind_BimTilesUserData_GetStringValue_1"]=Module["asm"]["Vc"]).apply(null,arguments)};var _emscripten_bind_BimTilesUserData_GetUserDataItem_1=Module["_emscripten_bind_BimTilesUserData_GetUserDataItem_1"]=function(){return(_emscripten_bind_BimTilesUserData_GetUserDataItem_1=Module["_emscripten_bind_BimTilesUserData_GetUserDataItem_1"]=Module["asm"]["Wc"]).apply(null,arguments)};var _emscripten_bind_BimTilesUserData_GetUserDataItemCount_0=Module["_emscripten_bind_BimTilesUserData_GetUserDataItemCount_0"]=function(){return(_emscripten_bind_BimTilesUserData_GetUserDataItemCount_0=Module["_emscripten_bind_BimTilesUserData_GetUserDataItemCount_0"]=Module["asm"]["Xc"]).apply(null,arguments)};var _emscripten_bind_BimTilesUserData_GetKeyIndexInt32Array_3=Module["_emscripten_bind_BimTilesUserData_GetKeyIndexInt32Array_3"]=function(){return(_emscripten_bind_BimTilesUserData_GetKeyIndexInt32Array_3=Module["_emscripten_bind_BimTilesUserData_GetKeyIndexInt32Array_3"]=Module["asm"]["Yc"]).apply(null,arguments)};var _emscripten_bind_BimTilesUserData_GetValueIndexInt32Array_3=Module["_emscripten_bind_BimTilesUserData_GetValueIndexInt32Array_3"]=function(){return(_emscripten_bind_BimTilesUserData_GetValueIndexInt32Array_3=Module["_emscripten_bind_BimTilesUserData_GetValueIndexInt32Array_3"]=Module["asm"]["Zc"]).apply(null,arguments)};var _emscripten_bind_BimTilesUserData_GetKeyIndexFloatArray_3=Module["_emscripten_bind_BimTilesUserData_GetKeyIndexFloatArray_3"]=function(){return(_emscripten_bind_BimTilesUserData_GetKeyIndexFloatArray_3=Module["_emscripten_bind_BimTilesUserData_GetKeyIndexFloatArray_3"]=Module["asm"]["_c"]).apply(null,arguments)};var _emscripten_bind_BimTilesUserData_GetValueIndexFloatArray_3=Module["_emscripten_bind_BimTilesUserData_GetValueIndexFloatArray_3"]=function(){return(_emscripten_bind_BimTilesUserData_GetValueIndexFloatArray_3=Module["_emscripten_bind_BimTilesUserData_GetValueIndexFloatArray_3"]=Module["asm"]["$c"]).apply(null,arguments)};var _emscripten_bind_BimTilesUserData_GetKeyIndexStringArray_3=Module["_emscripten_bind_BimTilesUserData_GetKeyIndexStringArray_3"]=function(){return(_emscripten_bind_BimTilesUserData_GetKeyIndexStringArray_3=Module["_emscripten_bind_BimTilesUserData_GetKeyIndexStringArray_3"]=Module["asm"]["ad"]).apply(null,arguments)};var _emscripten_bind_BimTilesUserData_GetValueIndexStringArray_3=Module["_emscripten_bind_BimTilesUserData_GetValueIndexStringArray_3"]=function(){return(_emscripten_bind_BimTilesUserData_GetValueIndexStringArray_3=Module["_emscripten_bind_BimTilesUserData_GetValueIndexStringArray_3"]=Module["asm"]["bd"]).apply(null,arguments)};var _emscripten_bind_BimTilesUserData___destroy___0=Module["_emscripten_bind_BimTilesUserData___destroy___0"]=function(){return(_emscripten_bind_BimTilesUserData___destroy___0=Module["_emscripten_bind_BimTilesUserData___destroy___0"]=Module["asm"]["cd"]).apply(null,arguments)};var _emscripten_bind_BimTilesTileId_BimTilesTileId_0=Module["_emscripten_bind_BimTilesTileId_BimTilesTileId_0"]=function(){return(_emscripten_bind_BimTilesTileId_BimTilesTileId_0=Module["_emscripten_bind_BimTilesTileId_BimTilesTileId_0"]=Module["asm"]["dd"]).apply(null,arguments)};var _emscripten_bind_BimTilesTileId_get_tile_path_1=Module["_emscripten_bind_BimTilesTileId_get_tile_path_1"]=function(){return(_emscripten_bind_BimTilesTileId_get_tile_path_1=Module["_emscripten_bind_BimTilesTileId_get_tile_path_1"]=Module["asm"]["ed"]).apply(null,arguments)};var _emscripten_bind_BimTilesTileId_num_tile_ids_0=Module["_emscripten_bind_BimTilesTileId_num_tile_ids_0"]=function(){return(_emscripten_bind_BimTilesTileId_num_tile_ids_0=Module["_emscripten_bind_BimTilesTileId_num_tile_ids_0"]=Module["asm"]["fd"]).apply(null,arguments)};var _emscripten_bind_BimTilesTileId_num_tile_paths_0=Module["_emscripten_bind_BimTilesTileId_num_tile_paths_0"]=function(){return(_emscripten_bind_BimTilesTileId_num_tile_paths_0=Module["_emscripten_bind_BimTilesTileId_num_tile_paths_0"]=Module["asm"]["gd"]).apply(null,arguments)};var _emscripten_bind_BimTilesTileId___destroy___0=Module["_emscripten_bind_BimTilesTileId___destroy___0"]=function(){return(_emscripten_bind_BimTilesTileId___destroy___0=Module["_emscripten_bind_BimTilesTileId___destroy___0"]=Module["asm"]["hd"]).apply(null,arguments)};var _emscripten_bind_NodeIdItem_NodeIdItem_0=Module["_emscripten_bind_NodeIdItem_NodeIdItem_0"]=function(){return(_emscripten_bind_NodeIdItem_NodeIdItem_0=Module["_emscripten_bind_NodeIdItem_NodeIdItem_0"]=Module["asm"]["id"]).apply(null,arguments)};var _emscripten_bind_NodeIdItem_get_tile_id_0=Module["_emscripten_bind_NodeIdItem_get_tile_id_0"]=function(){return(_emscripten_bind_NodeIdItem_get_tile_id_0=Module["_emscripten_bind_NodeIdItem_get_tile_id_0"]=Module["asm"]["jd"]).apply(null,arguments)};var _emscripten_bind_NodeIdItem_get_block_id_0=Module["_emscripten_bind_NodeIdItem_get_block_id_0"]=function(){return(_emscripten_bind_NodeIdItem_get_block_id_0=Module["_emscripten_bind_NodeIdItem_get_block_id_0"]=Module["asm"]["kd"]).apply(null,arguments)};var _emscripten_bind_NodeIdItem_get_node_id_0=Module["_emscripten_bind_NodeIdItem_get_node_id_0"]=function(){return(_emscripten_bind_NodeIdItem_get_node_id_0=Module["_emscripten_bind_NodeIdItem_get_node_id_0"]=Module["asm"]["ld"]).apply(null,arguments)};var _emscripten_bind_NodeIdItem_get_user_id_0=Module["_emscripten_bind_NodeIdItem_get_user_id_0"]=function(){return(_emscripten_bind_NodeIdItem_get_user_id_0=Module["_emscripten_bind_NodeIdItem_get_user_id_0"]=Module["asm"]["md"]).apply(null,arguments)};var _emscripten_bind_NodeIdItem_get_userdata_id_0=Module["_emscripten_bind_NodeIdItem_get_userdata_id_0"]=function(){return(_emscripten_bind_NodeIdItem_get_userdata_id_0=Module["_emscripten_bind_NodeIdItem_get_userdata_id_0"]=Module["asm"]["nd"]).apply(null,arguments)};var _emscripten_bind_NodeIdItem_num_components_bbox_0=Module["_emscripten_bind_NodeIdItem_num_components_bbox_0"]=function(){return(_emscripten_bind_NodeIdItem_num_components_bbox_0=Module["_emscripten_bind_NodeIdItem_num_components_bbox_0"]=Module["asm"]["od"]).apply(null,arguments)};var _emscripten_bind_NodeIdItem_byte_lenth_per_component_bbox_0=Module["_emscripten_bind_NodeIdItem_byte_lenth_per_component_bbox_0"]=function(){return(_emscripten_bind_NodeIdItem_byte_lenth_per_component_bbox_0=Module["_emscripten_bind_NodeIdItem_byte_lenth_per_component_bbox_0"]=Module["asm"]["pd"]).apply(null,arguments)};var _emscripten_bind_NodeIdItem___destroy___0=Module["_emscripten_bind_NodeIdItem___destroy___0"]=function(){return(_emscripten_bind_NodeIdItem___destroy___0=Module["_emscripten_bind_NodeIdItem___destroy___0"]=Module["asm"]["qd"]).apply(null,arguments)};var _emscripten_bind_TreeNodeItem_TreeNodeItem_0=Module["_emscripten_bind_TreeNodeItem_TreeNodeItem_0"]=function(){return(_emscripten_bind_TreeNodeItem_TreeNodeItem_0=Module["_emscripten_bind_TreeNodeItem_TreeNodeItem_0"]=Module["asm"]["rd"]).apply(null,arguments)};var _emscripten_bind_TreeNodeItem_get_node_id_0=Module["_emscripten_bind_TreeNodeItem_get_node_id_0"]=function(){return(_emscripten_bind_TreeNodeItem_get_node_id_0=Module["_emscripten_bind_TreeNodeItem_get_node_id_0"]=Module["asm"]["sd"]).apply(null,arguments)};var _emscripten_bind_TreeNodeItem_num_nodes_0=Module["_emscripten_bind_TreeNodeItem_num_nodes_0"]=function(){return(_emscripten_bind_TreeNodeItem_num_nodes_0=Module["_emscripten_bind_TreeNodeItem_num_nodes_0"]=Module["asm"]["td"]).apply(null,arguments)};var _emscripten_bind_TreeNodeItem_byte_lenth_per_component_node_0=Module["_emscripten_bind_TreeNodeItem_byte_lenth_per_component_node_0"]=function(){return(_emscripten_bind_TreeNodeItem_byte_lenth_per_component_node_0=Module["_emscripten_bind_TreeNodeItem_byte_lenth_per_component_node_0"]=Module["asm"]["ud"]).apply(null,arguments)};var _emscripten_bind_TreeNodeItem_num_children_0=Module["_emscripten_bind_TreeNodeItem_num_children_0"]=function(){return(_emscripten_bind_TreeNodeItem_num_children_0=Module["_emscripten_bind_TreeNodeItem_num_children_0"]=Module["asm"]["vd"]).apply(null,arguments)};var _emscripten_bind_TreeNodeItem_byte_lenth_per_component_childnode_0=Module["_emscripten_bind_TreeNodeItem_byte_lenth_per_component_childnode_0"]=function(){return(_emscripten_bind_TreeNodeItem_byte_lenth_per_component_childnode_0=Module["_emscripten_bind_TreeNodeItem_byte_lenth_per_component_childnode_0"]=Module["asm"]["wd"]).apply(null,arguments)};var _emscripten_bind_TreeNodeItem_num_components_bbox_0=Module["_emscripten_bind_TreeNodeItem_num_components_bbox_0"]=function(){return(_emscripten_bind_TreeNodeItem_num_components_bbox_0=Module["_emscripten_bind_TreeNodeItem_num_components_bbox_0"]=Module["asm"]["xd"]).apply(null,arguments)};var _emscripten_bind_TreeNodeItem_byte_lenth_per_component_bbox_0=Module["_emscripten_bind_TreeNodeItem_byte_lenth_per_component_bbox_0"]=function(){return(_emscripten_bind_TreeNodeItem_byte_lenth_per_component_bbox_0=Module["_emscripten_bind_TreeNodeItem_byte_lenth_per_component_bbox_0"]=Module["asm"]["yd"]).apply(null,arguments)};var _emscripten_bind_TreeNodeItem___destroy___0=Module["_emscripten_bind_TreeNodeItem___destroy___0"]=function(){return(_emscripten_bind_TreeNodeItem___destroy___0=Module["_emscripten_bind_TreeNodeItem___destroy___0"]=Module["asm"]["zd"]).apply(null,arguments)};var _emscripten_bind_BimTilesSearchTree_BimTilesSearchTree_0=Module["_emscripten_bind_BimTilesSearchTree_BimTilesSearchTree_0"]=function(){return(_emscripten_bind_BimTilesSearchTree_BimTilesSearchTree_0=Module["_emscripten_bind_BimTilesSearchTree_BimTilesSearchTree_0"]=Module["asm"]["Ad"]).apply(null,arguments)};var _emscripten_bind_BimTilesSearchTree_GetNodeIdCount_0=Module["_emscripten_bind_BimTilesSearchTree_GetNodeIdCount_0"]=function(){return(_emscripten_bind_BimTilesSearchTree_GetNodeIdCount_0=Module["_emscripten_bind_BimTilesSearchTree_GetNodeIdCount_0"]=Module["asm"]["Bd"]).apply(null,arguments)};var _emscripten_bind_BimTilesSearchTree_GetTreeNodeCount_0=Module["_emscripten_bind_BimTilesSearchTree_GetTreeNodeCount_0"]=function(){return(_emscripten_bind_BimTilesSearchTree_GetTreeNodeCount_0=Module["_emscripten_bind_BimTilesSearchTree_GetTreeNodeCount_0"]=Module["asm"]["Cd"]).apply(null,arguments)};var _emscripten_bind_BimTilesSearchTree_GetNodeIdItem_1=Module["_emscripten_bind_BimTilesSearchTree_GetNodeIdItem_1"]=function(){return(_emscripten_bind_BimTilesSearchTree_GetNodeIdItem_1=Module["_emscripten_bind_BimTilesSearchTree_GetNodeIdItem_1"]=Module["asm"]["Dd"]).apply(null,arguments)};var _emscripten_bind_BimTilesSearchTree_GetTreeNodeItem_1=Module["_emscripten_bind_BimTilesSearchTree_GetTreeNodeItem_1"]=function(){return(_emscripten_bind_BimTilesSearchTree_GetTreeNodeItem_1=Module["_emscripten_bind_BimTilesSearchTree_GetTreeNodeItem_1"]=Module["asm"]["Ed"]).apply(null,arguments)};var _emscripten_bind_BimTilesSearchTree_GetNodeIdBBoxFloat32Array_3=Module["_emscripten_bind_BimTilesSearchTree_GetNodeIdBBoxFloat32Array_3"]=function(){return(_emscripten_bind_BimTilesSearchTree_GetNodeIdBBoxFloat32Array_3=Module["_emscripten_bind_BimTilesSearchTree_GetNodeIdBBoxFloat32Array_3"]=Module["asm"]["Fd"]).apply(null,arguments)};var _emscripten_bind_BimTilesSearchTree_GetTreeNodeBBoxFloat32Array_3=Module["_emscripten_bind_BimTilesSearchTree_GetTreeNodeBBoxFloat32Array_3"]=function(){return(_emscripten_bind_BimTilesSearchTree_GetTreeNodeBBoxFloat32Array_3=Module["_emscripten_bind_BimTilesSearchTree_GetTreeNodeBBoxFloat32Array_3"]=Module["asm"]["Gd"]).apply(null,arguments)};var _emscripten_bind_BimTilesSearchTree_GetTreeNodeInt32Array_3=Module["_emscripten_bind_BimTilesSearchTree_GetTreeNodeInt32Array_3"]=function(){return(_emscripten_bind_BimTilesSearchTree_GetTreeNodeInt32Array_3=Module["_emscripten_bind_BimTilesSearchTree_GetTreeNodeInt32Array_3"]=Module["asm"]["Hd"]).apply(null,arguments)};var _emscripten_bind_BimTilesSearchTree_GetTreeChildNodeInt32Array_3=Module["_emscripten_bind_BimTilesSearchTree_GetTreeChildNodeInt32Array_3"]=function(){return(_emscripten_bind_BimTilesSearchTree_GetTreeChildNodeInt32Array_3=Module["_emscripten_bind_BimTilesSearchTree_GetTreeChildNodeInt32Array_3"]=Module["asm"]["Id"]).apply(null,arguments)};var _emscripten_bind_BimTilesSearchTree___destroy___0=Module["_emscripten_bind_BimTilesSearchTree___destroy___0"]=function(){return(_emscripten_bind_BimTilesSearchTree___destroy___0=Module["_emscripten_bind_BimTilesSearchTree___destroy___0"]=Module["asm"]["Jd"]).apply(null,arguments)};var _emscripten_bind_NodeIdItemV2_NodeIdItemV2_0=Module["_emscripten_bind_NodeIdItemV2_NodeIdItemV2_0"]=function(){return(_emscripten_bind_NodeIdItemV2_NodeIdItemV2_0=Module["_emscripten_bind_NodeIdItemV2_NodeIdItemV2_0"]=Module["asm"]["Kd"]).apply(null,arguments)};var _emscripten_bind_NodeIdItemV2_get_tile_id_0=Module["_emscripten_bind_NodeIdItemV2_get_tile_id_0"]=function(){return(_emscripten_bind_NodeIdItemV2_get_tile_id_0=Module["_emscripten_bind_NodeIdItemV2_get_tile_id_0"]=Module["asm"]["Ld"]).apply(null,arguments)};var _emscripten_bind_NodeIdItemV2_get_node_id_0=Module["_emscripten_bind_NodeIdItemV2_get_node_id_0"]=function(){return(_emscripten_bind_NodeIdItemV2_get_node_id_0=Module["_emscripten_bind_NodeIdItemV2_get_node_id_0"]=Module["asm"]["Md"]).apply(null,arguments)};var _emscripten_bind_NodeIdItemV2_get_user_id_0=Module["_emscripten_bind_NodeIdItemV2_get_user_id_0"]=function(){return(_emscripten_bind_NodeIdItemV2_get_user_id_0=Module["_emscripten_bind_NodeIdItemV2_get_user_id_0"]=Module["asm"]["Nd"]).apply(null,arguments)};var _emscripten_bind_NodeIdItemV2_get_bbox_id_0=Module["_emscripten_bind_NodeIdItemV2_get_bbox_id_0"]=function(){return(_emscripten_bind_NodeIdItemV2_get_bbox_id_0=Module["_emscripten_bind_NodeIdItemV2_get_bbox_id_0"]=Module["asm"]["Od"]).apply(null,arguments)};var _emscripten_bind_NodeIdItemV2___destroy___0=Module["_emscripten_bind_NodeIdItemV2___destroy___0"]=function(){return(_emscripten_bind_NodeIdItemV2___destroy___0=Module["_emscripten_bind_NodeIdItemV2___destroy___0"]=Module["asm"]["Pd"]).apply(null,arguments)};var _emscripten_bind_BimTilesSearchTreeV2_BimTilesSearchTreeV2_0=Module["_emscripten_bind_BimTilesSearchTreeV2_BimTilesSearchTreeV2_0"]=function(){return(_emscripten_bind_BimTilesSearchTreeV2_BimTilesSearchTreeV2_0=Module["_emscripten_bind_BimTilesSearchTreeV2_BimTilesSearchTreeV2_0"]=Module["asm"]["Qd"]).apply(null,arguments)};var _emscripten_bind_BimTilesSearchTreeV2_GetTreeNodeCount_0=Module["_emscripten_bind_BimTilesSearchTreeV2_GetTreeNodeCount_0"]=function(){return(_emscripten_bind_BimTilesSearchTreeV2_GetTreeNodeCount_0=Module["_emscripten_bind_BimTilesSearchTreeV2_GetTreeNodeCount_0"]=Module["asm"]["Rd"]).apply(null,arguments)};var _emscripten_bind_BimTilesSearchTreeV2_GetTreeNodeItem_1=Module["_emscripten_bind_BimTilesSearchTreeV2_GetTreeNodeItem_1"]=function(){return(_emscripten_bind_BimTilesSearchTreeV2_GetTreeNodeItem_1=Module["_emscripten_bind_BimTilesSearchTreeV2_GetTreeNodeItem_1"]=Module["asm"]["Sd"]).apply(null,arguments)};var _emscripten_bind_BimTilesSearchTreeV2_GetTreeNodeBBoxFloat32Array_3=Module["_emscripten_bind_BimTilesSearchTreeV2_GetTreeNodeBBoxFloat32Array_3"]=function(){return(_emscripten_bind_BimTilesSearchTreeV2_GetTreeNodeBBoxFloat32Array_3=Module["_emscripten_bind_BimTilesSearchTreeV2_GetTreeNodeBBoxFloat32Array_3"]=Module["asm"]["Td"]).apply(null,arguments)};var _emscripten_bind_BimTilesSearchTreeV2_GetTreeNodeInt32Array_3=Module["_emscripten_bind_BimTilesSearchTreeV2_GetTreeNodeInt32Array_3"]=function(){return(_emscripten_bind_BimTilesSearchTreeV2_GetTreeNodeInt32Array_3=Module["_emscripten_bind_BimTilesSearchTreeV2_GetTreeNodeInt32Array_3"]=Module["asm"]["Ud"]).apply(null,arguments)};var _emscripten_bind_BimTilesSearchTreeV2_GetTreeChildNodeInt32Array_3=Module["_emscripten_bind_BimTilesSearchTreeV2_GetTreeChildNodeInt32Array_3"]=function(){return(_emscripten_bind_BimTilesSearchTreeV2_GetTreeChildNodeInt32Array_3=Module["_emscripten_bind_BimTilesSearchTreeV2_GetTreeChildNodeInt32Array_3"]=Module["asm"]["Vd"]).apply(null,arguments)};var _emscripten_bind_BimTilesSearchTreeV2___destroy___0=Module["_emscripten_bind_BimTilesSearchTreeV2___destroy___0"]=function(){return(_emscripten_bind_BimTilesSearchTreeV2___destroy___0=Module["_emscripten_bind_BimTilesSearchTreeV2___destroy___0"]=Module["asm"]["Wd"]).apply(null,arguments)};var _emscripten_bind_BimTilesUserIdV2_BimTilesUserIdV2_0=Module["_emscripten_bind_BimTilesUserIdV2_BimTilesUserIdV2_0"]=function(){return(_emscripten_bind_BimTilesUserIdV2_BimTilesUserIdV2_0=Module["_emscripten_bind_BimTilesUserIdV2_BimTilesUserIdV2_0"]=Module["asm"]["Xd"]).apply(null,arguments)};var _emscripten_bind_BimTilesUserIdV2_get_tile_id_0=Module["_emscripten_bind_BimTilesUserIdV2_get_tile_id_0"]=function(){return(_emscripten_bind_BimTilesUserIdV2_get_tile_id_0=Module["_emscripten_bind_BimTilesUserIdV2_get_tile_id_0"]=Module["asm"]["Yd"]).apply(null,arguments)};var _emscripten_bind_BimTilesUserIdV2_get_block_id_0=Module["_emscripten_bind_BimTilesUserIdV2_get_block_id_0"]=function(){return(_emscripten_bind_BimTilesUserIdV2_get_block_id_0=Module["_emscripten_bind_BimTilesUserIdV2_get_block_id_0"]=Module["asm"]["Zd"]).apply(null,arguments)};var _emscripten_bind_BimTilesUserIdV2_get_user_id_min_0=Module["_emscripten_bind_BimTilesUserIdV2_get_user_id_min_0"]=function(){return(_emscripten_bind_BimTilesUserIdV2_get_user_id_min_0=Module["_emscripten_bind_BimTilesUserIdV2_get_user_id_min_0"]=Module["asm"]["_d"]).apply(null,arguments)};var _emscripten_bind_BimTilesUserIdV2_get_user_id_max_0=Module["_emscripten_bind_BimTilesUserIdV2_get_user_id_max_0"]=function(){return(_emscripten_bind_BimTilesUserIdV2_get_user_id_max_0=Module["_emscripten_bind_BimTilesUserIdV2_get_user_id_max_0"]=Module["asm"]["$d"]).apply(null,arguments)};var _emscripten_bind_BimTilesUserIdV2_num_user_ids_0=Module["_emscripten_bind_BimTilesUserIdV2_num_user_ids_0"]=function(){return(_emscripten_bind_BimTilesUserIdV2_num_user_ids_0=Module["_emscripten_bind_BimTilesUserIdV2_num_user_ids_0"]=Module["asm"]["ae"]).apply(null,arguments)};var _emscripten_bind_BimTilesUserIdV2_num_userdata_ids_0=Module["_emscripten_bind_BimTilesUserIdV2_num_userdata_ids_0"]=function(){return(_emscripten_bind_BimTilesUserIdV2_num_userdata_ids_0=Module["_emscripten_bind_BimTilesUserIdV2_num_userdata_ids_0"]=Module["asm"]["be"]).apply(null,arguments)};var _emscripten_bind_BimTilesUserIdV2_num_nodelist_ids_0=Module["_emscripten_bind_BimTilesUserIdV2_num_nodelist_ids_0"]=function(){return(_emscripten_bind_BimTilesUserIdV2_num_nodelist_ids_0=Module["_emscripten_bind_BimTilesUserIdV2_num_nodelist_ids_0"]=Module["asm"]["ce"]).apply(null,arguments)};var _emscripten_bind_BimTilesUserIdV2_get_first_user_id_0=Module["_emscripten_bind_BimTilesUserIdV2_get_first_user_id_0"]=function(){return(_emscripten_bind_BimTilesUserIdV2_get_first_user_id_0=Module["_emscripten_bind_BimTilesUserIdV2_get_first_user_id_0"]=Module["asm"]["de"]).apply(null,arguments)};var _emscripten_bind_BimTilesUserIdV2_get_last_user_id_0=Module["_emscripten_bind_BimTilesUserIdV2_get_last_user_id_0"]=function(){return(_emscripten_bind_BimTilesUserIdV2_get_last_user_id_0=Module["_emscripten_bind_BimTilesUserIdV2_get_last_user_id_0"]=Module["asm"]["ee"]).apply(null,arguments)};var _emscripten_bind_BimTilesUserIdV2_GetUserIdStr_1=Module["_emscripten_bind_BimTilesUserIdV2_GetUserIdStr_1"]=function(){return(_emscripten_bind_BimTilesUserIdV2_GetUserIdStr_1=Module["_emscripten_bind_BimTilesUserIdV2_GetUserIdStr_1"]=Module["asm"]["fe"]).apply(null,arguments)};var _emscripten_bind_BimTilesUserIdV2_GetUserDataIdsInt32Array_2=Module["_emscripten_bind_BimTilesUserIdV2_GetUserDataIdsInt32Array_2"]=function(){return(_emscripten_bind_BimTilesUserIdV2_GetUserDataIdsInt32Array_2=Module["_emscripten_bind_BimTilesUserIdV2_GetUserDataIdsInt32Array_2"]=Module["asm"]["ge"]).apply(null,arguments)};var _emscripten_bind_BimTilesUserIdV2_GetNodeListIdsInt32Array_2=Module["_emscripten_bind_BimTilesUserIdV2_GetNodeListIdsInt32Array_2"]=function(){return(_emscripten_bind_BimTilesUserIdV2_GetNodeListIdsInt32Array_2=Module["_emscripten_bind_BimTilesUserIdV2_GetNodeListIdsInt32Array_2"]=Module["asm"]["he"]).apply(null,arguments)};var _emscripten_bind_BimTilesUserIdV2___destroy___0=Module["_emscripten_bind_BimTilesUserIdV2___destroy___0"]=function(){return(_emscripten_bind_BimTilesUserIdV2___destroy___0=Module["_emscripten_bind_BimTilesUserIdV2___destroy___0"]=Module["asm"]["ie"]).apply(null,arguments)};var _emscripten_bind_ViewNodeItem_ViewNodeItem_0=Module["_emscripten_bind_ViewNodeItem_ViewNodeItem_0"]=function(){return(_emscripten_bind_ViewNodeItem_ViewNodeItem_0=Module["_emscripten_bind_ViewNodeItem_ViewNodeItem_0"]=Module["asm"]["je"]).apply(null,arguments)};var _emscripten_bind_ViewNodeItem_get_node_id_0=Module["_emscripten_bind_ViewNodeItem_get_node_id_0"]=function(){return(_emscripten_bind_ViewNodeItem_get_node_id_0=Module["_emscripten_bind_ViewNodeItem_get_node_id_0"]=Module["asm"]["ke"]).apply(null,arguments)};var _emscripten_bind_ViewNodeItem_get_node_parent_id_0=Module["_emscripten_bind_ViewNodeItem_get_node_parent_id_0"]=function(){return(_emscripten_bind_ViewNodeItem_get_node_parent_id_0=Module["_emscripten_bind_ViewNodeItem_get_node_parent_id_0"]=Module["asm"]["le"]).apply(null,arguments)};var _emscripten_bind_ViewNodeItem_num_tile_ids_0=Module["_emscripten_bind_ViewNodeItem_num_tile_ids_0"]=function(){return(_emscripten_bind_ViewNodeItem_num_tile_ids_0=Module["_emscripten_bind_ViewNodeItem_num_tile_ids_0"]=Module["asm"]["me"]).apply(null,arguments)};var _emscripten_bind_ViewNodeItem_byte_lenth_per_tile_ids_0=Module["_emscripten_bind_ViewNodeItem_byte_lenth_per_tile_ids_0"]=function(){return(_emscripten_bind_ViewNodeItem_byte_lenth_per_tile_ids_0=Module["_emscripten_bind_ViewNodeItem_byte_lenth_per_tile_ids_0"]=Module["asm"]["ne"]).apply(null,arguments)};var _emscripten_bind_ViewNodeItem_num_components_bbox_0=Module["_emscripten_bind_ViewNodeItem_num_components_bbox_0"]=function(){return(_emscripten_bind_ViewNodeItem_num_components_bbox_0=Module["_emscripten_bind_ViewNodeItem_num_components_bbox_0"]=Module["asm"]["oe"]).apply(null,arguments)};var _emscripten_bind_ViewNodeItem_byte_lenth_per_component_bbox_0=Module["_emscripten_bind_ViewNodeItem_byte_lenth_per_component_bbox_0"]=function(){return(_emscripten_bind_ViewNodeItem_byte_lenth_per_component_bbox_0=Module["_emscripten_bind_ViewNodeItem_byte_lenth_per_component_bbox_0"]=Module["asm"]["pe"]).apply(null,arguments)};var _emscripten_bind_ViewNodeItem___destroy___0=Module["_emscripten_bind_ViewNodeItem___destroy___0"]=function(){return(_emscripten_bind_ViewNodeItem___destroy___0=Module["_emscripten_bind_ViewNodeItem___destroy___0"]=Module["asm"]["qe"]).apply(null,arguments)};var _emscripten_bind_BimTilesViewTree_BimTilesViewTree_0=Module["_emscripten_bind_BimTilesViewTree_BimTilesViewTree_0"]=function(){return(_emscripten_bind_BimTilesViewTree_BimTilesViewTree_0=Module["_emscripten_bind_BimTilesViewTree_BimTilesViewTree_0"]=Module["asm"]["re"]).apply(null,arguments)};var _emscripten_bind_BimTilesViewTree_get_tree_type_0=Module["_emscripten_bind_BimTilesViewTree_get_tree_type_0"]=function(){return(_emscripten_bind_BimTilesViewTree_get_tree_type_0=Module["_emscripten_bind_BimTilesViewTree_get_tree_type_0"]=Module["asm"]["se"]).apply(null,arguments)};var _emscripten_bind_BimTilesViewTree_num_geometry_errors_0=Module["_emscripten_bind_BimTilesViewTree_num_geometry_errors_0"]=function(){return(_emscripten_bind_BimTilesViewTree_num_geometry_errors_0=Module["_emscripten_bind_BimTilesViewTree_num_geometry_errors_0"]=Module["asm"]["te"]).apply(null,arguments)};var _emscripten_bind_BimTilesViewTree_GetViewNodeCount_0=Module["_emscripten_bind_BimTilesViewTree_GetViewNodeCount_0"]=function(){return(_emscripten_bind_BimTilesViewTree_GetViewNodeCount_0=Module["_emscripten_bind_BimTilesViewTree_GetViewNodeCount_0"]=Module["asm"]["ue"]).apply(null,arguments)};var _emscripten_bind_BimTilesViewTree_GetViewNodeItem_1=Module["_emscripten_bind_BimTilesViewTree_GetViewNodeItem_1"]=function(){return(_emscripten_bind_BimTilesViewTree_GetViewNodeItem_1=Module["_emscripten_bind_BimTilesViewTree_GetViewNodeItem_1"]=Module["asm"]["ve"]).apply(null,arguments)};var _emscripten_bind_BimTilesViewTree_GetGeometryErrorsFloat32Array_2=Module["_emscripten_bind_BimTilesViewTree_GetGeometryErrorsFloat32Array_2"]=function(){return(_emscripten_bind_BimTilesViewTree_GetGeometryErrorsFloat32Array_2=Module["_emscripten_bind_BimTilesViewTree_GetGeometryErrorsFloat32Array_2"]=Module["asm"]["we"]).apply(null,arguments)};var _emscripten_bind_BimTilesViewTree_GetViewNodeBBoxFloat32Array_3=Module["_emscripten_bind_BimTilesViewTree_GetViewNodeBBoxFloat32Array_3"]=function(){return(_emscripten_bind_BimTilesViewTree_GetViewNodeBBoxFloat32Array_3=Module["_emscripten_bind_BimTilesViewTree_GetViewNodeBBoxFloat32Array_3"]=Module["asm"]["xe"]).apply(null,arguments)};var _emscripten_bind_BimTilesViewTree_GetViewNodeTileIdsInt32Array_3=Module["_emscripten_bind_BimTilesViewTree_GetViewNodeTileIdsInt32Array_3"]=function(){return(_emscripten_bind_BimTilesViewTree_GetViewNodeTileIdsInt32Array_3=Module["_emscripten_bind_BimTilesViewTree_GetViewNodeTileIdsInt32Array_3"]=Module["asm"]["ye"]).apply(null,arguments)};var _emscripten_bind_BimTilesViewTree___destroy___0=Module["_emscripten_bind_BimTilesViewTree___destroy___0"]=function(){return(_emscripten_bind_BimTilesViewTree___destroy___0=Module["_emscripten_bind_BimTilesViewTree___destroy___0"]=Module["asm"]["ze"]).apply(null,arguments)};var _emscripten_bind_BimTilesNodeId_BimTilesNodeId_0=Module["_emscripten_bind_BimTilesNodeId_BimTilesNodeId_0"]=function(){return(_emscripten_bind_BimTilesNodeId_BimTilesNodeId_0=Module["_emscripten_bind_BimTilesNodeId_BimTilesNodeId_0"]=Module["asm"]["Ae"]).apply(null,arguments)};var _emscripten_bind_BimTilesNodeId_GetNodeIdItemCount_0=Module["_emscripten_bind_BimTilesNodeId_GetNodeIdItemCount_0"]=function(){return(_emscripten_bind_BimTilesNodeId_GetNodeIdItemCount_0=Module["_emscripten_bind_BimTilesNodeId_GetNodeIdItemCount_0"]=Module["asm"]["Be"]).apply(null,arguments)};var _emscripten_bind_BimTilesNodeId_GetNodeIdItem_1=Module["_emscripten_bind_BimTilesNodeId_GetNodeIdItem_1"]=function(){return(_emscripten_bind_BimTilesNodeId_GetNodeIdItem_1=Module["_emscripten_bind_BimTilesNodeId_GetNodeIdItem_1"]=Module["asm"]["Ce"]).apply(null,arguments)};var _emscripten_bind_BimTilesNodeId___destroy___0=Module["_emscripten_bind_BimTilesNodeId___destroy___0"]=function(){return(_emscripten_bind_BimTilesNodeId___destroy___0=Module["_emscripten_bind_BimTilesNodeId___destroy___0"]=Module["asm"]["De"]).apply(null,arguments)};var _emscripten_bind_NodeBBoxItem_NodeBBoxItem_0=Module["_emscripten_bind_NodeBBoxItem_NodeBBoxItem_0"]=function(){return(_emscripten_bind_NodeBBoxItem_NodeBBoxItem_0=Module["_emscripten_bind_NodeBBoxItem_NodeBBoxItem_0"]=Module["asm"]["Ee"]).apply(null,arguments)};var _emscripten_bind_NodeBBoxItem_num_components_bbox_0=Module["_emscripten_bind_NodeBBoxItem_num_components_bbox_0"]=function(){return(_emscripten_bind_NodeBBoxItem_num_components_bbox_0=Module["_emscripten_bind_NodeBBoxItem_num_components_bbox_0"]=Module["asm"]["Fe"]).apply(null,arguments)};var _emscripten_bind_NodeBBoxItem_byte_lenth_per_component_bbox_0=Module["_emscripten_bind_NodeBBoxItem_byte_lenth_per_component_bbox_0"]=function(){return(_emscripten_bind_NodeBBoxItem_byte_lenth_per_component_bbox_0=Module["_emscripten_bind_NodeBBoxItem_byte_lenth_per_component_bbox_0"]=Module["asm"]["Ge"]).apply(null,arguments)};var _emscripten_bind_NodeBBoxItem___destroy___0=Module["_emscripten_bind_NodeBBoxItem___destroy___0"]=function(){return(_emscripten_bind_NodeBBoxItem___destroy___0=Module["_emscripten_bind_NodeBBoxItem___destroy___0"]=Module["asm"]["He"]).apply(null,arguments)};var _emscripten_bind_BimTilesNodeBox_BimTilesNodeBox_0=Module["_emscripten_bind_BimTilesNodeBox_BimTilesNodeBox_0"]=function(){return(_emscripten_bind_BimTilesNodeBox_BimTilesNodeBox_0=Module["_emscripten_bind_BimTilesNodeBox_BimTilesNodeBox_0"]=Module["asm"]["Ie"]).apply(null,arguments)};var _emscripten_bind_BimTilesNodeBox_GetNodeBBoxItemCount_0=Module["_emscripten_bind_BimTilesNodeBox_GetNodeBBoxItemCount_0"]=function(){return(_emscripten_bind_BimTilesNodeBox_GetNodeBBoxItemCount_0=Module["_emscripten_bind_BimTilesNodeBox_GetNodeBBoxItemCount_0"]=Module["asm"]["Je"]).apply(null,arguments)};var _emscripten_bind_BimTilesNodeBox_GetNodeBBoxItem_1=Module["_emscripten_bind_BimTilesNodeBox_GetNodeBBoxItem_1"]=function(){return(_emscripten_bind_BimTilesNodeBox_GetNodeBBoxItem_1=Module["_emscripten_bind_BimTilesNodeBox_GetNodeBBoxItem_1"]=Module["asm"]["Ke"]).apply(null,arguments)};var _emscripten_bind_BimTilesNodeBox_GetBBoxFloat32Array_3=Module["_emscripten_bind_BimTilesNodeBox_GetBBoxFloat32Array_3"]=function(){return(_emscripten_bind_BimTilesNodeBox_GetBBoxFloat32Array_3=Module["_emscripten_bind_BimTilesNodeBox_GetBBoxFloat32Array_3"]=Module["asm"]["Le"]).apply(null,arguments)};var _emscripten_bind_BimTilesNodeBox___destroy___0=Module["_emscripten_bind_BimTilesNodeBox___destroy___0"]=function(){return(_emscripten_bind_BimTilesNodeBox___destroy___0=Module["_emscripten_bind_BimTilesNodeBox___destroy___0"]=Module["asm"]["Me"]).apply(null,arguments)};var _emscripten_bind_BimTilesUserIdV3_BimTilesUserIdV3_0=Module["_emscripten_bind_BimTilesUserIdV3_BimTilesUserIdV3_0"]=function(){return(_emscripten_bind_BimTilesUserIdV3_BimTilesUserIdV3_0=Module["_emscripten_bind_BimTilesUserIdV3_BimTilesUserIdV3_0"]=Module["asm"]["Ne"]).apply(null,arguments)};var _emscripten_bind_BimTilesUserIdV3_get_user_id_min_0=Module["_emscripten_bind_BimTilesUserIdV3_get_user_id_min_0"]=function(){return(_emscripten_bind_BimTilesUserIdV3_get_user_id_min_0=Module["_emscripten_bind_BimTilesUserIdV3_get_user_id_min_0"]=Module["asm"]["Oe"]).apply(null,arguments)};var _emscripten_bind_BimTilesUserIdV3_get_user_id_max_0=Module["_emscripten_bind_BimTilesUserIdV3_get_user_id_max_0"]=function(){return(_emscripten_bind_BimTilesUserIdV3_get_user_id_max_0=Module["_emscripten_bind_BimTilesUserIdV3_get_user_id_max_0"]=Module["asm"]["Pe"]).apply(null,arguments)};var _emscripten_bind_BimTilesUserIdV3_num_user_ids_0=Module["_emscripten_bind_BimTilesUserIdV3_num_user_ids_0"]=function(){return(_emscripten_bind_BimTilesUserIdV3_num_user_ids_0=Module["_emscripten_bind_BimTilesUserIdV3_num_user_ids_0"]=Module["asm"]["Qe"]).apply(null,arguments)};var _emscripten_bind_BimTilesUserIdV3_num_userdata_indices_0=Module["_emscripten_bind_BimTilesUserIdV3_num_userdata_indices_0"]=function(){return(_emscripten_bind_BimTilesUserIdV3_num_userdata_indices_0=Module["_emscripten_bind_BimTilesUserIdV3_num_userdata_indices_0"]=Module["asm"]["Re"]).apply(null,arguments)};var _emscripten_bind_BimTilesUserIdV3_GetUserIdStr_1=Module["_emscripten_bind_BimTilesUserIdV3_GetUserIdStr_1"]=function(){return(_emscripten_bind_BimTilesUserIdV3_GetUserIdStr_1=Module["_emscripten_bind_BimTilesUserIdV3_GetUserIdStr_1"]=Module["asm"]["Se"]).apply(null,arguments)};var _emscripten_bind_BimTilesUserIdV3_GetInt32ArrayForUserDataIndices_2=Module["_emscripten_bind_BimTilesUserIdV3_GetInt32ArrayForUserDataIndices_2"]=function(){return(_emscripten_bind_BimTilesUserIdV3_GetInt32ArrayForUserDataIndices_2=Module["_emscripten_bind_BimTilesUserIdV3_GetInt32ArrayForUserDataIndices_2"]=Module["asm"]["Te"]).apply(null,arguments)};var _emscripten_bind_BimTilesUserIdV3___destroy___0=Module["_emscripten_bind_BimTilesUserIdV3___destroy___0"]=function(){return(_emscripten_bind_BimTilesUserIdV3___destroy___0=Module["_emscripten_bind_BimTilesUserIdV3___destroy___0"]=Module["asm"]["Ue"]).apply(null,arguments)};var _emscripten_bind_BimTilesUserIdBoxV3_BimTilesUserIdBoxV3_0=Module["_emscripten_bind_BimTilesUserIdBoxV3_BimTilesUserIdBoxV3_0"]=function(){return(_emscripten_bind_BimTilesUserIdBoxV3_BimTilesUserIdBoxV3_0=Module["_emscripten_bind_BimTilesUserIdBoxV3_BimTilesUserIdBoxV3_0"]=Module["asm"]["Ve"]).apply(null,arguments)};var _emscripten_bind_BimTilesUserIdBoxV3_num_node_boxes_0=Module["_emscripten_bind_BimTilesUserIdBoxV3_num_node_boxes_0"]=function(){return(_emscripten_bind_BimTilesUserIdBoxV3_num_node_boxes_0=Module["_emscripten_bind_BimTilesUserIdBoxV3_num_node_boxes_0"]=Module["asm"]["We"]).apply(null,arguments)};var _emscripten_bind_BimTilesUserIdBoxV3_GetNodeBBoxItem_1=Module["_emscripten_bind_BimTilesUserIdBoxV3_GetNodeBBoxItem_1"]=function(){return(_emscripten_bind_BimTilesUserIdBoxV3_GetNodeBBoxItem_1=Module["_emscripten_bind_BimTilesUserIdBoxV3_GetNodeBBoxItem_1"]=Module["asm"]["Xe"]).apply(null,arguments)};var _emscripten_bind_BimTilesUserIdBoxV3_GetBBoxFloat32Array_3=Module["_emscripten_bind_BimTilesUserIdBoxV3_GetBBoxFloat32Array_3"]=function(){return(_emscripten_bind_BimTilesUserIdBoxV3_GetBBoxFloat32Array_3=Module["_emscripten_bind_BimTilesUserIdBoxV3_GetBBoxFloat32Array_3"]=Module["asm"]["Ye"]).apply(null,arguments)};var _emscripten_bind_BimTilesUserIdBoxV3___destroy___0=Module["_emscripten_bind_BimTilesUserIdBoxV3___destroy___0"]=function(){return(_emscripten_bind_BimTilesUserIdBoxV3___destroy___0=Module["_emscripten_bind_BimTilesUserIdBoxV3___destroy___0"]=Module["asm"]["Ze"]).apply(null,arguments)};var _emscripten_bind_MeshNodeV3_MeshNodeV3_0=Module["_emscripten_bind_MeshNodeV3_MeshNodeV3_0"]=function(){return(_emscripten_bind_MeshNodeV3_MeshNodeV3_0=Module["_emscripten_bind_MeshNodeV3_MeshNodeV3_0"]=Module["asm"]["_e"]).apply(null,arguments)};var _emscripten_bind_MeshNodeV3_GetAttribute_1=Module["_emscripten_bind_MeshNodeV3_GetAttribute_1"]=function(){return(_emscripten_bind_MeshNodeV3_GetAttribute_1=Module["_emscripten_bind_MeshNodeV3_GetAttribute_1"]=Module["asm"]["$e"]).apply(null,arguments)};var _emscripten_bind_MeshNodeV3_num_attributes_0=Module["_emscripten_bind_MeshNodeV3_num_attributes_0"]=function(){return(_emscripten_bind_MeshNodeV3_num_attributes_0=Module["_emscripten_bind_MeshNodeV3_num_attributes_0"]=Module["asm"]["af"]).apply(null,arguments)};var _emscripten_bind_MeshNodeV3_get_node_id_0=Module["_emscripten_bind_MeshNodeV3_get_node_id_0"]=function(){return(_emscripten_bind_MeshNodeV3_get_node_id_0=Module["_emscripten_bind_MeshNodeV3_get_node_id_0"]=Module["asm"]["bf"]).apply(null,arguments)};var _emscripten_bind_MeshNodeV3_get_primitive_id_0=Module["_emscripten_bind_MeshNodeV3_get_primitive_id_0"]=function(){return(_emscripten_bind_MeshNodeV3_get_primitive_id_0=Module["_emscripten_bind_MeshNodeV3_get_primitive_id_0"]=Module["asm"]["cf"]).apply(null,arguments)};var _emscripten_bind_MeshNodeV3_get_material_id_0=Module["_emscripten_bind_MeshNodeV3_get_material_id_0"]=function(){return(_emscripten_bind_MeshNodeV3_get_material_id_0=Module["_emscripten_bind_MeshNodeV3_get_material_id_0"]=Module["asm"]["df"]).apply(null,arguments)};var _emscripten_bind_MeshNodeV3_primitive_mode_0=Module["_emscripten_bind_MeshNodeV3_primitive_mode_0"]=function(){return(_emscripten_bind_MeshNodeV3_primitive_mode_0=Module["_emscripten_bind_MeshNodeV3_primitive_mode_0"]=Module["asm"]["ef"]).apply(null,arguments)};var _emscripten_bind_MeshNodeV3_node_type_0=Module["_emscripten_bind_MeshNodeV3_node_type_0"]=function(){return(_emscripten_bind_MeshNodeV3_node_type_0=Module["_emscripten_bind_MeshNodeV3_node_type_0"]=Module["asm"]["ff"]).apply(null,arguments)};var _emscripten_bind_MeshNodeV3_num_components_bbox_0=Module["_emscripten_bind_MeshNodeV3_num_components_bbox_0"]=function(){return(_emscripten_bind_MeshNodeV3_num_components_bbox_0=Module["_emscripten_bind_MeshNodeV3_num_components_bbox_0"]=Module["asm"]["gf"]).apply(null,arguments)};var _emscripten_bind_MeshNodeV3_byte_length_per_component_bbox_0=Module["_emscripten_bind_MeshNodeV3_byte_length_per_component_bbox_0"]=function(){return(_emscripten_bind_MeshNodeV3_byte_length_per_component_bbox_0=Module["_emscripten_bind_MeshNodeV3_byte_length_per_component_bbox_0"]=Module["asm"]["hf"]).apply(null,arguments)};var _emscripten_bind_MeshNodeV3_GetBBoxFloat32Array_2=Module["_emscripten_bind_MeshNodeV3_GetBBoxFloat32Array_2"]=function(){return(_emscripten_bind_MeshNodeV3_GetBBoxFloat32Array_2=Module["_emscripten_bind_MeshNodeV3_GetBBoxFloat32Array_2"]=Module["asm"]["jf"]).apply(null,arguments)};var _emscripten_bind_MeshNodeV3_num_components_matrix_0=Module["_emscripten_bind_MeshNodeV3_num_components_matrix_0"]=function(){return(_emscripten_bind_MeshNodeV3_num_components_matrix_0=Module["_emscripten_bind_MeshNodeV3_num_components_matrix_0"]=Module["asm"]["kf"]).apply(null,arguments)};var _emscripten_bind_MeshNodeV3_byte_length_per_component_matrix_0=Module["_emscripten_bind_MeshNodeV3_byte_length_per_component_matrix_0"]=function(){return(_emscripten_bind_MeshNodeV3_byte_length_per_component_matrix_0=Module["_emscripten_bind_MeshNodeV3_byte_length_per_component_matrix_0"]=Module["asm"]["lf"]).apply(null,arguments)};var _emscripten_bind_MeshNodeV3_GetMatrixFloat32Array_2=Module["_emscripten_bind_MeshNodeV3_GetMatrixFloat32Array_2"]=function(){return(_emscripten_bind_MeshNodeV3_GetMatrixFloat32Array_2=Module["_emscripten_bind_MeshNodeV3_GetMatrixFloat32Array_2"]=Module["asm"]["mf"]).apply(null,arguments)};var _emscripten_bind_MeshNodeV3_num_components_translation_0=Module["_emscripten_bind_MeshNodeV3_num_components_translation_0"]=function(){return(_emscripten_bind_MeshNodeV3_num_components_translation_0=Module["_emscripten_bind_MeshNodeV3_num_components_translation_0"]=Module["asm"]["nf"]).apply(null,arguments)};var _emscripten_bind_MeshNodeV3_byte_length_per_component_translation_0=Module["_emscripten_bind_MeshNodeV3_byte_length_per_component_translation_0"]=function(){return(_emscripten_bind_MeshNodeV3_byte_length_per_component_translation_0=Module["_emscripten_bind_MeshNodeV3_byte_length_per_component_translation_0"]=Module["asm"]["of"]).apply(null,arguments)};var _emscripten_bind_MeshNodeV3_GetTranslationFloat64Array_2=Module["_emscripten_bind_MeshNodeV3_GetTranslationFloat64Array_2"]=function(){return(_emscripten_bind_MeshNodeV3_GetTranslationFloat64Array_2=Module["_emscripten_bind_MeshNodeV3_GetTranslationFloat64Array_2"]=Module["asm"]["pf"]).apply(null,arguments)};var _emscripten_bind_MeshNodeV3___destroy___0=Module["_emscripten_bind_MeshNodeV3___destroy___0"]=function(){return(_emscripten_bind_MeshNodeV3___destroy___0=Module["_emscripten_bind_MeshNodeV3___destroy___0"]=Module["asm"]["qf"]).apply(null,arguments)};var _emscripten_bind_UseridIndexInfo_UseridIndexInfo_0=Module["_emscripten_bind_UseridIndexInfo_UseridIndexInfo_0"]=function(){return(_emscripten_bind_UseridIndexInfo_UseridIndexInfo_0=Module["_emscripten_bind_UseridIndexInfo_UseridIndexInfo_0"]=Module["asm"]["rf"]).apply(null,arguments)};var _emscripten_bind_UseridIndexInfo_get_node_id_0=Module["_emscripten_bind_UseridIndexInfo_get_node_id_0"]=function(){return(_emscripten_bind_UseridIndexInfo_get_node_id_0=Module["_emscripten_bind_UseridIndexInfo_get_node_id_0"]=Module["asm"]["sf"]).apply(null,arguments)};var _emscripten_bind_UseridIndexInfo_get_userid_index_0=Module["_emscripten_bind_UseridIndexInfo_get_userid_index_0"]=function(){return(_emscripten_bind_UseridIndexInfo_get_userid_index_0=Module["_emscripten_bind_UseridIndexInfo_get_userid_index_0"]=Module["asm"]["tf"]).apply(null,arguments)};var _emscripten_bind_UseridIndexInfo_get_index_start_0=Module["_emscripten_bind_UseridIndexInfo_get_index_start_0"]=function(){return(_emscripten_bind_UseridIndexInfo_get_index_start_0=Module["_emscripten_bind_UseridIndexInfo_get_index_start_0"]=Module["asm"]["uf"]).apply(null,arguments)};var _emscripten_bind_UseridIndexInfo_get_index_end_0=Module["_emscripten_bind_UseridIndexInfo_get_index_end_0"]=function(){return(_emscripten_bind_UseridIndexInfo_get_index_end_0=Module["_emscripten_bind_UseridIndexInfo_get_index_end_0"]=Module["asm"]["vf"]).apply(null,arguments)};var _emscripten_bind_UseridIndexInfo_get_position_start_0=Module["_emscripten_bind_UseridIndexInfo_get_position_start_0"]=function(){return(_emscripten_bind_UseridIndexInfo_get_position_start_0=Module["_emscripten_bind_UseridIndexInfo_get_position_start_0"]=Module["asm"]["wf"]).apply(null,arguments)};var _emscripten_bind_UseridIndexInfo_get_position_end_0=Module["_emscripten_bind_UseridIndexInfo_get_position_end_0"]=function(){return(_emscripten_bind_UseridIndexInfo_get_position_end_0=Module["_emscripten_bind_UseridIndexInfo_get_position_end_0"]=Module["asm"]["xf"]).apply(null,arguments)};var _emscripten_bind_UseridIndexInfo___destroy___0=Module["_emscripten_bind_UseridIndexInfo___destroy___0"]=function(){return(_emscripten_bind_UseridIndexInfo___destroy___0=Module["_emscripten_bind_UseridIndexInfo___destroy___0"]=Module["asm"]["yf"]).apply(null,arguments)};var _emscripten_bind_BimTilesMeshV3_BimTilesMeshV3_0=Module["_emscripten_bind_BimTilesMeshV3_BimTilesMeshV3_0"]=function(){return(_emscripten_bind_BimTilesMeshV3_BimTilesMeshV3_0=Module["_emscripten_bind_BimTilesMeshV3_BimTilesMeshV3_0"]=Module["asm"]["zf"]).apply(null,arguments)};var _emscripten_bind_BimTilesMeshV3_get_tile_id_0=Module["_emscripten_bind_BimTilesMeshV3_get_tile_id_0"]=function(){return(_emscripten_bind_BimTilesMeshV3_get_tile_id_0=Module["_emscripten_bind_BimTilesMeshV3_get_tile_id_0"]=Module["asm"]["Af"]).apply(null,arguments)};var _emscripten_bind_BimTilesMeshV3_get_block_id_0=Module["_emscripten_bind_BimTilesMeshV3_get_block_id_0"]=function(){return(_emscripten_bind_BimTilesMeshV3_get_block_id_0=Module["_emscripten_bind_BimTilesMeshV3_get_block_id_0"]=Module["asm"]["Bf"]).apply(null,arguments)};var _emscripten_bind_BimTilesMeshV3_get_buffer_block_id_0=Module["_emscripten_bind_BimTilesMeshV3_get_buffer_block_id_0"]=function(){return(_emscripten_bind_BimTilesMeshV3_get_buffer_block_id_0=Module["_emscripten_bind_BimTilesMeshV3_get_buffer_block_id_0"]=Module["asm"]["Cf"]).apply(null,arguments)};var _emscripten_bind_BimTilesMeshV3_get_material_block_id_0=Module["_emscripten_bind_BimTilesMeshV3_get_material_block_id_0"]=function(){return(_emscripten_bind_BimTilesMeshV3_get_material_block_id_0=Module["_emscripten_bind_BimTilesMeshV3_get_material_block_id_0"]=Module["asm"]["Df"]).apply(null,arguments)};var _emscripten_bind_BimTilesMeshV3_get_userdata_block_id_0=Module["_emscripten_bind_BimTilesMeshV3_get_userdata_block_id_0"]=function(){return(_emscripten_bind_BimTilesMeshV3_get_userdata_block_id_0=Module["_emscripten_bind_BimTilesMeshV3_get_userdata_block_id_0"]=Module["asm"]["Ef"]).apply(null,arguments)};var _emscripten_bind_BimTilesMeshV3_GetNode_1=Module["_emscripten_bind_BimTilesMeshV3_GetNode_1"]=function(){return(_emscripten_bind_BimTilesMeshV3_GetNode_1=Module["_emscripten_bind_BimTilesMeshV3_GetNode_1"]=Module["asm"]["Ff"]).apply(null,arguments)};var _emscripten_bind_BimTilesMeshV3_num_mesh_nodes_0=Module["_emscripten_bind_BimTilesMeshV3_num_mesh_nodes_0"]=function(){return(_emscripten_bind_BimTilesMeshV3_num_mesh_nodes_0=Module["_emscripten_bind_BimTilesMeshV3_num_mesh_nodes_0"]=Module["asm"]["Gf"]).apply(null,arguments)};var _emscripten_bind_BimTilesMeshV3_GetUseridIndexInfo_1=Module["_emscripten_bind_BimTilesMeshV3_GetUseridIndexInfo_1"]=function(){return(_emscripten_bind_BimTilesMeshV3_GetUseridIndexInfo_1=Module["_emscripten_bind_BimTilesMeshV3_GetUseridIndexInfo_1"]=Module["asm"]["Hf"]).apply(null,arguments)};var _emscripten_bind_BimTilesMeshV3_num_userid_index_infos_0=Module["_emscripten_bind_BimTilesMeshV3_num_userid_index_infos_0"]=function(){return(_emscripten_bind_BimTilesMeshV3_num_userid_index_infos_0=Module["_emscripten_bind_BimTilesMeshV3_num_userid_index_infos_0"]=Module["asm"]["If"]).apply(null,arguments)};var _emscripten_bind_BimTilesMeshV3___destroy___0=Module["_emscripten_bind_BimTilesMeshV3___destroy___0"]=function(){return(_emscripten_bind_BimTilesMeshV3___destroy___0=Module["_emscripten_bind_BimTilesMeshV3___destroy___0"]=Module["asm"]["Jf"]).apply(null,arguments)};var _emscripten_bind_Decoder_Decoder_0=Module["_emscripten_bind_Decoder_Decoder_0"]=function(){return(_emscripten_bind_Decoder_Decoder_0=Module["_emscripten_bind_Decoder_Decoder_0"]=Module["asm"]["Kf"]).apply(null,arguments)};var _emscripten_bind_Decoder_DecodeFromArray_3=Module["_emscripten_bind_Decoder_DecodeFromArray_3"]=function(){return(_emscripten_bind_Decoder_DecodeFromArray_3=Module["_emscripten_bind_Decoder_DecodeFromArray_3"]=Module["asm"]["Lf"]).apply(null,arguments)};var _emscripten_bind_Decoder_ReleaseAllBuffers_0=Module["_emscripten_bind_Decoder_ReleaseAllBuffers_0"]=function(){return(_emscripten_bind_Decoder_ReleaseAllBuffers_0=Module["_emscripten_bind_Decoder_ReleaseAllBuffers_0"]=Module["asm"]["Mf"]).apply(null,arguments)};var _emscripten_bind_Decoder_GetBlockTypeCount_0=Module["_emscripten_bind_Decoder_GetBlockTypeCount_0"]=function(){return(_emscripten_bind_Decoder_GetBlockTypeCount_0=Module["_emscripten_bind_Decoder_GetBlockTypeCount_0"]=Module["asm"]["Nf"]).apply(null,arguments)};var _emscripten_bind_Decoder_GetBlockType_1=Module["_emscripten_bind_Decoder_GetBlockType_1"]=function(){return(_emscripten_bind_Decoder_GetBlockType_1=Module["_emscripten_bind_Decoder_GetBlockType_1"]=Module["asm"]["Of"]).apply(null,arguments)};var _emscripten_bind_Decoder_GetBlockCount_2=Module["_emscripten_bind_Decoder_GetBlockCount_2"]=function(){return(_emscripten_bind_Decoder_GetBlockCount_2=Module["_emscripten_bind_Decoder_GetBlockCount_2"]=Module["asm"]["Pf"]).apply(null,arguments)};var _emscripten_bind_Decoder_GetMesh_1=Module["_emscripten_bind_Decoder_GetMesh_1"]=function(){return(_emscripten_bind_Decoder_GetMesh_1=Module["_emscripten_bind_Decoder_GetMesh_1"]=Module["asm"]["Qf"]).apply(null,arguments)};var _emscripten_bind_Decoder_GetInstance_1=Module["_emscripten_bind_Decoder_GetInstance_1"]=function(){return(_emscripten_bind_Decoder_GetInstance_1=Module["_emscripten_bind_Decoder_GetInstance_1"]=Module["asm"]["Rf"]).apply(null,arguments)};var _emscripten_bind_Decoder_GetMaterial_1=Module["_emscripten_bind_Decoder_GetMaterial_1"]=function(){return(_emscripten_bind_Decoder_GetMaterial_1=Module["_emscripten_bind_Decoder_GetMaterial_1"]=Module["asm"]["Sf"]).apply(null,arguments)};var _emscripten_bind_Decoder_GetUserId_1=Module["_emscripten_bind_Decoder_GetUserId_1"]=function(){return(_emscripten_bind_Decoder_GetUserId_1=Module["_emscripten_bind_Decoder_GetUserId_1"]=Module["asm"]["Tf"]).apply(null,arguments)};var _emscripten_bind_Decoder_GetUserData_1=Module["_emscripten_bind_Decoder_GetUserData_1"]=function(){return(_emscripten_bind_Decoder_GetUserData_1=Module["_emscripten_bind_Decoder_GetUserData_1"]=Module["asm"]["Uf"]).apply(null,arguments)};var _emscripten_bind_Decoder_GetTileId_1=Module["_emscripten_bind_Decoder_GetTileId_1"]=function(){return(_emscripten_bind_Decoder_GetTileId_1=Module["_emscripten_bind_Decoder_GetTileId_1"]=Module["asm"]["Vf"]).apply(null,arguments)};var _emscripten_bind_Decoder_GetSearchTree_1=Module["_emscripten_bind_Decoder_GetSearchTree_1"]=function(){return(_emscripten_bind_Decoder_GetSearchTree_1=Module["_emscripten_bind_Decoder_GetSearchTree_1"]=Module["asm"]["Wf"]).apply(null,arguments)};var _emscripten_bind_Decoder_GetViewTree_1=Module["_emscripten_bind_Decoder_GetViewTree_1"]=function(){return(_emscripten_bind_Decoder_GetViewTree_1=Module["_emscripten_bind_Decoder_GetViewTree_1"]=Module["asm"]["Xf"]).apply(null,arguments)};var _emscripten_bind_Decoder_GetSearchTreeV2_1=Module["_emscripten_bind_Decoder_GetSearchTreeV2_1"]=function(){return(_emscripten_bind_Decoder_GetSearchTreeV2_1=Module["_emscripten_bind_Decoder_GetSearchTreeV2_1"]=Module["asm"]["Yf"]).apply(null,arguments)};var _emscripten_bind_Decoder_GetUserIdV2_1=Module["_emscripten_bind_Decoder_GetUserIdV2_1"]=function(){return(_emscripten_bind_Decoder_GetUserIdV2_1=Module["_emscripten_bind_Decoder_GetUserIdV2_1"]=Module["asm"]["Zf"]).apply(null,arguments)};var _emscripten_bind_Decoder_GetNodeId_1=Module["_emscripten_bind_Decoder_GetNodeId_1"]=function(){return(_emscripten_bind_Decoder_GetNodeId_1=Module["_emscripten_bind_Decoder_GetNodeId_1"]=Module["asm"]["_f"]).apply(null,arguments)};var _emscripten_bind_Decoder_GetNodeBox_1=Module["_emscripten_bind_Decoder_GetNodeBox_1"]=function(){return(_emscripten_bind_Decoder_GetNodeBox_1=Module["_emscripten_bind_Decoder_GetNodeBox_1"]=Module["asm"]["$f"]).apply(null,arguments)};var _emscripten_bind_Decoder_GetUserIdV3_1=Module["_emscripten_bind_Decoder_GetUserIdV3_1"]=function(){return(_emscripten_bind_Decoder_GetUserIdV3_1=Module["_emscripten_bind_Decoder_GetUserIdV3_1"]=Module["asm"]["ag"]).apply(null,arguments)};var _emscripten_bind_Decoder_GetMeshV3_1=Module["_emscripten_bind_Decoder_GetMeshV3_1"]=function(){return(_emscripten_bind_Decoder_GetMeshV3_1=Module["_emscripten_bind_Decoder_GetMeshV3_1"]=Module["asm"]["bg"]).apply(null,arguments)};var _emscripten_bind_Decoder_GetUserIdBoxV3_1=Module["_emscripten_bind_Decoder_GetUserIdBoxV3_1"]=function(){return(_emscripten_bind_Decoder_GetUserIdBoxV3_1=Module["_emscripten_bind_Decoder_GetUserIdBoxV3_1"]=Module["asm"]["cg"]).apply(null,arguments)};var _emscripten_bind_Decoder_GetAttributeDataArrayWithDataType_5=Module["_emscripten_bind_Decoder_GetAttributeDataArrayWithDataType_5"]=function(){return(_emscripten_bind_Decoder_GetAttributeDataArrayWithDataType_5=Module["_emscripten_bind_Decoder_GetAttributeDataArrayWithDataType_5"]=Module["asm"]["dg"]).apply(null,arguments)};var _emscripten_bind_Decoder_GetAttributeDataArray_4=Module["_emscripten_bind_Decoder_GetAttributeDataArray_4"]=function(){return(_emscripten_bind_Decoder_GetAttributeDataArray_4=Module["_emscripten_bind_Decoder_GetAttributeDataArray_4"]=Module["asm"]["eg"]).apply(null,arguments)};var _emscripten_bind_Decoder_GetAttributeDataArrayV3WithDataType_5=Module["_emscripten_bind_Decoder_GetAttributeDataArrayV3WithDataType_5"]=function(){return(_emscripten_bind_Decoder_GetAttributeDataArrayV3WithDataType_5=Module["_emscripten_bind_Decoder_GetAttributeDataArrayV3WithDataType_5"]=Module["asm"]["fg"]).apply(null,arguments)};var _emscripten_bind_Decoder_GetAttributeDataArrayV3_4=Module["_emscripten_bind_Decoder_GetAttributeDataArrayV3_4"]=function(){return(_emscripten_bind_Decoder_GetAttributeDataArrayV3_4=Module["_emscripten_bind_Decoder_GetAttributeDataArrayV3_4"]=Module["asm"]["gg"]).apply(null,arguments)};var _emscripten_bind_Decoder___destroy___0=Module["_emscripten_bind_Decoder___destroy___0"]=function(){return(_emscripten_bind_Decoder___destroy___0=Module["_emscripten_bind_Decoder___destroy___0"]=Module["asm"]["hg"]).apply(null,arguments)};var _emscripten_enum_bimtiles_DataType_DT_INVALID=Module["_emscripten_enum_bimtiles_DataType_DT_INVALID"]=function(){return(_emscripten_enum_bimtiles_DataType_DT_INVALID=Module["_emscripten_enum_bimtiles_DataType_DT_INVALID"]=Module["asm"]["ig"]).apply(null,arguments)};var _emscripten_enum_bimtiles_DataType_DT_INT8=Module["_emscripten_enum_bimtiles_DataType_DT_INT8"]=function(){return(_emscripten_enum_bimtiles_DataType_DT_INT8=Module["_emscripten_enum_bimtiles_DataType_DT_INT8"]=Module["asm"]["jg"]).apply(null,arguments)};var _emscripten_enum_bimtiles_DataType_DT_UINT8=Module["_emscripten_enum_bimtiles_DataType_DT_UINT8"]=function(){return(_emscripten_enum_bimtiles_DataType_DT_UINT8=Module["_emscripten_enum_bimtiles_DataType_DT_UINT8"]=Module["asm"]["kg"]).apply(null,arguments)};var _emscripten_enum_bimtiles_DataType_DT_INT16=Module["_emscripten_enum_bimtiles_DataType_DT_INT16"]=function(){return(_emscripten_enum_bimtiles_DataType_DT_INT16=Module["_emscripten_enum_bimtiles_DataType_DT_INT16"]=Module["asm"]["lg"]).apply(null,arguments)};var _emscripten_enum_bimtiles_DataType_DT_UINT16=Module["_emscripten_enum_bimtiles_DataType_DT_UINT16"]=function(){return(_emscripten_enum_bimtiles_DataType_DT_UINT16=Module["_emscripten_enum_bimtiles_DataType_DT_UINT16"]=Module["asm"]["mg"]).apply(null,arguments)};var _emscripten_enum_bimtiles_DataType_DT_INT32=Module["_emscripten_enum_bimtiles_DataType_DT_INT32"]=function(){return(_emscripten_enum_bimtiles_DataType_DT_INT32=Module["_emscripten_enum_bimtiles_DataType_DT_INT32"]=Module["asm"]["ng"]).apply(null,arguments)};var _emscripten_enum_bimtiles_DataType_DT_UINT32=Module["_emscripten_enum_bimtiles_DataType_DT_UINT32"]=function(){return(_emscripten_enum_bimtiles_DataType_DT_UINT32=Module["_emscripten_enum_bimtiles_DataType_DT_UINT32"]=Module["asm"]["og"]).apply(null,arguments)};var _emscripten_enum_bimtiles_DataType_DT_INT64=Module["_emscripten_enum_bimtiles_DataType_DT_INT64"]=function(){return(_emscripten_enum_bimtiles_DataType_DT_INT64=Module["_emscripten_enum_bimtiles_DataType_DT_INT64"]=Module["asm"]["pg"]).apply(null,arguments)};var _emscripten_enum_bimtiles_DataType_DT_UINT64=Module["_emscripten_enum_bimtiles_DataType_DT_UINT64"]=function(){return(_emscripten_enum_bimtiles_DataType_DT_UINT64=Module["_emscripten_enum_bimtiles_DataType_DT_UINT64"]=Module["asm"]["qg"]).apply(null,arguments)};var _emscripten_enum_bimtiles_DataType_DT_FLOAT32=Module["_emscripten_enum_bimtiles_DataType_DT_FLOAT32"]=function(){return(_emscripten_enum_bimtiles_DataType_DT_FLOAT32=Module["_emscripten_enum_bimtiles_DataType_DT_FLOAT32"]=Module["asm"]["rg"]).apply(null,arguments)};var _emscripten_enum_bimtiles_DataType_DT_FLOAT64=Module["_emscripten_enum_bimtiles_DataType_DT_FLOAT64"]=function(){return(_emscripten_enum_bimtiles_DataType_DT_FLOAT64=Module["_emscripten_enum_bimtiles_DataType_DT_FLOAT64"]=Module["asm"]["sg"]).apply(null,arguments)};var _emscripten_enum_bimtiles_DataType_DT_BOOL=Module["_emscripten_enum_bimtiles_DataType_DT_BOOL"]=function(){return(_emscripten_enum_bimtiles_DataType_DT_BOOL=Module["_emscripten_enum_bimtiles_DataType_DT_BOOL"]=Module["asm"]["tg"]).apply(null,arguments)};var _emscripten_enum_bimtiles_DataType_DT_TYPES_COUNT=Module["_emscripten_enum_bimtiles_DataType_DT_TYPES_COUNT"]=function(){return(_emscripten_enum_bimtiles_DataType_DT_TYPES_COUNT=Module["_emscripten_enum_bimtiles_DataType_DT_TYPES_COUNT"]=Module["asm"]["ug"]).apply(null,arguments)};var _emscripten_enum_bimtiles_BlockType_BT_UNKNOWN=Module["_emscripten_enum_bimtiles_BlockType_BT_UNKNOWN"]=function(){return(_emscripten_enum_bimtiles_BlockType_BT_UNKNOWN=Module["_emscripten_enum_bimtiles_BlockType_BT_UNKNOWN"]=Module["asm"]["vg"]).apply(null,arguments)};var _emscripten_enum_bimtiles_BlockType_BT_MESH=Module["_emscripten_enum_bimtiles_BlockType_BT_MESH"]=function(){return(_emscripten_enum_bimtiles_BlockType_BT_MESH=Module["_emscripten_enum_bimtiles_BlockType_BT_MESH"]=Module["asm"]["wg"]).apply(null,arguments)};var _emscripten_enum_bimtiles_BlockType_BT_LINE=Module["_emscripten_enum_bimtiles_BlockType_BT_LINE"]=function(){return(_emscripten_enum_bimtiles_BlockType_BT_LINE=Module["_emscripten_enum_bimtiles_BlockType_BT_LINE"]=Module["asm"]["xg"]).apply(null,arguments)};var _emscripten_enum_bimtiles_BlockType_BT_POINT=Module["_emscripten_enum_bimtiles_BlockType_BT_POINT"]=function(){return(_emscripten_enum_bimtiles_BlockType_BT_POINT=Module["_emscripten_enum_bimtiles_BlockType_BT_POINT"]=Module["asm"]["yg"]).apply(null,arguments)};var _emscripten_enum_bimtiles_BlockType_BT_SYMBOL=Module["_emscripten_enum_bimtiles_BlockType_BT_SYMBOL"]=function(){return(_emscripten_enum_bimtiles_BlockType_BT_SYMBOL=Module["_emscripten_enum_bimtiles_BlockType_BT_SYMBOL"]=Module["asm"]["zg"]).apply(null,arguments)};var _emscripten_enum_bimtiles_BlockType_BT_INSTANCE=Module["_emscripten_enum_bimtiles_BlockType_BT_INSTANCE"]=function(){return(_emscripten_enum_bimtiles_BlockType_BT_INSTANCE=Module["_emscripten_enum_bimtiles_BlockType_BT_INSTANCE"]=Module["asm"]["Ag"]).apply(null,arguments)};var _emscripten_enum_bimtiles_BlockType_BT_INDEX=Module["_emscripten_enum_bimtiles_BlockType_BT_INDEX"]=function(){return(_emscripten_enum_bimtiles_BlockType_BT_INDEX=Module["_emscripten_enum_bimtiles_BlockType_BT_INDEX"]=Module["asm"]["Bg"]).apply(null,arguments)};var _emscripten_enum_bimtiles_BlockType_BT_BUFFER=Module["_emscripten_enum_bimtiles_BlockType_BT_BUFFER"]=function(){return(_emscripten_enum_bimtiles_BlockType_BT_BUFFER=Module["_emscripten_enum_bimtiles_BlockType_BT_BUFFER"]=Module["asm"]["Cg"]).apply(null,arguments)};var _emscripten_enum_bimtiles_BlockType_BT_MATERIAL=Module["_emscripten_enum_bimtiles_BlockType_BT_MATERIAL"]=function(){return(_emscripten_enum_bimtiles_BlockType_BT_MATERIAL=Module["_emscripten_enum_bimtiles_BlockType_BT_MATERIAL"]=Module["asm"]["Dg"]).apply(null,arguments)};var _emscripten_enum_bimtiles_BlockType_BT_USERDATA=Module["_emscripten_enum_bimtiles_BlockType_BT_USERDATA"]=function(){return(_emscripten_enum_bimtiles_BlockType_BT_USERDATA=Module["_emscripten_enum_bimtiles_BlockType_BT_USERDATA"]=Module["asm"]["Eg"]).apply(null,arguments)};var _emscripten_enum_bimtiles_BlockType_BT_USERID=Module["_emscripten_enum_bimtiles_BlockType_BT_USERID"]=function(){return(_emscripten_enum_bimtiles_BlockType_BT_USERID=Module["_emscripten_enum_bimtiles_BlockType_BT_USERID"]=Module["asm"]["Fg"]).apply(null,arguments)};var _emscripten_enum_bimtiles_BlockType_BT_TILEID=Module["_emscripten_enum_bimtiles_BlockType_BT_TILEID"]=function(){return(_emscripten_enum_bimtiles_BlockType_BT_TILEID=Module["_emscripten_enum_bimtiles_BlockType_BT_TILEID"]=Module["asm"]["Gg"]).apply(null,arguments)};var _emscripten_enum_bimtiles_BlockType_BT_VIEWTREE=Module["_emscripten_enum_bimtiles_BlockType_BT_VIEWTREE"]=function(){return(_emscripten_enum_bimtiles_BlockType_BT_VIEWTREE=Module["_emscripten_enum_bimtiles_BlockType_BT_VIEWTREE"]=Module["asm"]["Hg"]).apply(null,arguments)};var _emscripten_enum_bimtiles_BlockType_BT_NODEID=Module["_emscripten_enum_bimtiles_BlockType_BT_NODEID"]=function(){return(_emscripten_enum_bimtiles_BlockType_BT_NODEID=Module["_emscripten_enum_bimtiles_BlockType_BT_NODEID"]=Module["asm"]["Ig"]).apply(null,arguments)};var _emscripten_enum_bimtiles_BlockType_BT_NODEBOX=Module["_emscripten_enum_bimtiles_BlockType_BT_NODEBOX"]=function(){return(_emscripten_enum_bimtiles_BlockType_BT_NODEBOX=Module["_emscripten_enum_bimtiles_BlockType_BT_NODEBOX"]=Module["asm"]["Jg"]).apply(null,arguments)};var _emscripten_enum_bimtiles_BlockType_BT_USERIDBOX=Module["_emscripten_enum_bimtiles_BlockType_BT_USERIDBOX"]=function(){return(_emscripten_enum_bimtiles_BlockType_BT_USERIDBOX=Module["_emscripten_enum_bimtiles_BlockType_BT_USERIDBOX"]=Module["asm"]["Kg"]).apply(null,arguments)};var _emscripten_enum_bimtiles_BlockType_BT_NUM_BLOCK_TYPES=Module["_emscripten_enum_bimtiles_BlockType_BT_NUM_BLOCK_TYPES"]=function(){return(_emscripten_enum_bimtiles_BlockType_BT_NUM_BLOCK_TYPES=Module["_emscripten_enum_bimtiles_BlockType_BT_NUM_BLOCK_TYPES"]=Module["asm"]["Lg"]).apply(null,arguments)};var _emscripten_enum_bimtiles_DataVersion_DV_V1=Module["_emscripten_enum_bimtiles_DataVersion_DV_V1"]=function(){return(_emscripten_enum_bimtiles_DataVersion_DV_V1=Module["_emscripten_enum_bimtiles_DataVersion_DV_V1"]=Module["asm"]["Mg"]).apply(null,arguments)};var _emscripten_enum_bimtiles_DataVersion_DV_V2=Module["_emscripten_enum_bimtiles_DataVersion_DV_V2"]=function(){return(_emscripten_enum_bimtiles_DataVersion_DV_V2=Module["_emscripten_enum_bimtiles_DataVersion_DV_V2"]=Module["asm"]["Ng"]).apply(null,arguments)};var _emscripten_enum_bimtiles_DataVersion_DV_V3=Module["_emscripten_enum_bimtiles_DataVersion_DV_V3"]=function(){return(_emscripten_enum_bimtiles_DataVersion_DV_V3=Module["_emscripten_enum_bimtiles_DataVersion_DV_V3"]=Module["asm"]["Og"]).apply(null,arguments)};var _emscripten_enum_bimtiles_GeometryAttributeType_GAT_INVALID=Module["_emscripten_enum_bimtiles_GeometryAttributeType_GAT_INVALID"]=function(){return(_emscripten_enum_bimtiles_GeometryAttributeType_GAT_INVALID=Module["_emscripten_enum_bimtiles_GeometryAttributeType_GAT_INVALID"]=Module["asm"]["Pg"]).apply(null,arguments)};var _emscripten_enum_bimtiles_GeometryAttributeType_GAT_POSITION=Module["_emscripten_enum_bimtiles_GeometryAttributeType_GAT_POSITION"]=function(){return(_emscripten_enum_bimtiles_GeometryAttributeType_GAT_POSITION=Module["_emscripten_enum_bimtiles_GeometryAttributeType_GAT_POSITION"]=Module["asm"]["Qg"]).apply(null,arguments)};var _emscripten_enum_bimtiles_GeometryAttributeType_GAT_INDEX=Module["_emscripten_enum_bimtiles_GeometryAttributeType_GAT_INDEX"]=function(){return(_emscripten_enum_bimtiles_GeometryAttributeType_GAT_INDEX=Module["_emscripten_enum_bimtiles_GeometryAttributeType_GAT_INDEX"]=Module["asm"]["Rg"]).apply(null,arguments)};var _emscripten_enum_bimtiles_GeometryAttributeType_GAT_NORMAL=Module["_emscripten_enum_bimtiles_GeometryAttributeType_GAT_NORMAL"]=function(){return(_emscripten_enum_bimtiles_GeometryAttributeType_GAT_NORMAL=Module["_emscripten_enum_bimtiles_GeometryAttributeType_GAT_NORMAL"]=Module["asm"]["Sg"]).apply(null,arguments)};var _emscripten_enum_bimtiles_GeometryAttributeType_GAT_COLOR=Module["_emscripten_enum_bimtiles_GeometryAttributeType_GAT_COLOR"]=function(){return(_emscripten_enum_bimtiles_GeometryAttributeType_GAT_COLOR=Module["_emscripten_enum_bimtiles_GeometryAttributeType_GAT_COLOR"]=Module["asm"]["Tg"]).apply(null,arguments)};var _emscripten_enum_bimtiles_GeometryAttributeType_GAT_TEX_COORD=Module["_emscripten_enum_bimtiles_GeometryAttributeType_GAT_TEX_COORD"]=function(){return(_emscripten_enum_bimtiles_GeometryAttributeType_GAT_TEX_COORD=Module["_emscripten_enum_bimtiles_GeometryAttributeType_GAT_TEX_COORD"]=Module["asm"]["Ug"]).apply(null,arguments)};var _emscripten_enum_bimtiles_GeometryAttributeType_GAT_TEX_COORD2=Module["_emscripten_enum_bimtiles_GeometryAttributeType_GAT_TEX_COORD2"]=function(){return(_emscripten_enum_bimtiles_GeometryAttributeType_GAT_TEX_COORD2=Module["_emscripten_enum_bimtiles_GeometryAttributeType_GAT_TEX_COORD2"]=Module["asm"]["Vg"]).apply(null,arguments)};var _emscripten_enum_bimtiles_GeometryAttributeType_GAT_TEX_COORD3=Module["_emscripten_enum_bimtiles_GeometryAttributeType_GAT_TEX_COORD3"]=function(){return(_emscripten_enum_bimtiles_GeometryAttributeType_GAT_TEX_COORD3=Module["_emscripten_enum_bimtiles_GeometryAttributeType_GAT_TEX_COORD3"]=Module["asm"]["Wg"]).apply(null,arguments)};var _emscripten_enum_bimtiles_GeometryAttributeType_GAT_USERID=Module["_emscripten_enum_bimtiles_GeometryAttributeType_GAT_USERID"]=function(){return(_emscripten_enum_bimtiles_GeometryAttributeType_GAT_USERID=Module["_emscripten_enum_bimtiles_GeometryAttributeType_GAT_USERID"]=Module["asm"]["Xg"]).apply(null,arguments)};var _emscripten_enum_bimtiles_GeometryAttributeType_GAT_BARYCENTRIC=Module["_emscripten_enum_bimtiles_GeometryAttributeType_GAT_BARYCENTRIC"]=function(){return(_emscripten_enum_bimtiles_GeometryAttributeType_GAT_BARYCENTRIC=Module["_emscripten_enum_bimtiles_GeometryAttributeType_GAT_BARYCENTRIC"]=Module["asm"]["Yg"]).apply(null,arguments)};var _emscripten_enum_bimtiles_GeometryAttributeType_GAT_ATTRIBUTES_COUNT=Module["_emscripten_enum_bimtiles_GeometryAttributeType_GAT_ATTRIBUTES_COUNT"]=function(){return(_emscripten_enum_bimtiles_GeometryAttributeType_GAT_ATTRIBUTES_COUNT=Module["_emscripten_enum_bimtiles_GeometryAttributeType_GAT_ATTRIBUTES_COUNT"]=Module["asm"]["Zg"]).apply(null,arguments)};var _emscripten_enum_bimtiles_PrimitiveMode_PM_POINTS=Module["_emscripten_enum_bimtiles_PrimitiveMode_PM_POINTS"]=function(){return(_emscripten_enum_bimtiles_PrimitiveMode_PM_POINTS=Module["_emscripten_enum_bimtiles_PrimitiveMode_PM_POINTS"]=Module["asm"]["_g"]).apply(null,arguments)};var _emscripten_enum_bimtiles_PrimitiveMode_PM_LINES=Module["_emscripten_enum_bimtiles_PrimitiveMode_PM_LINES"]=function(){return(_emscripten_enum_bimtiles_PrimitiveMode_PM_LINES=Module["_emscripten_enum_bimtiles_PrimitiveMode_PM_LINES"]=Module["asm"]["$g"]).apply(null,arguments)};var _emscripten_enum_bimtiles_PrimitiveMode_PM_LINE_STRIP=Module["_emscripten_enum_bimtiles_PrimitiveMode_PM_LINE_STRIP"]=function(){return(_emscripten_enum_bimtiles_PrimitiveMode_PM_LINE_STRIP=Module["_emscripten_enum_bimtiles_PrimitiveMode_PM_LINE_STRIP"]=Module["asm"]["ah"]).apply(null,arguments)};var _emscripten_enum_bimtiles_PrimitiveMode_PM_TRIANGLES=Module["_emscripten_enum_bimtiles_PrimitiveMode_PM_TRIANGLES"]=function(){return(_emscripten_enum_bimtiles_PrimitiveMode_PM_TRIANGLES=Module["_emscripten_enum_bimtiles_PrimitiveMode_PM_TRIANGLES"]=Module["asm"]["bh"]).apply(null,arguments)};var _emscripten_enum_bimtiles_PrimitiveMode_PM_TRIANGLE_STRIP=Module["_emscripten_enum_bimtiles_PrimitiveMode_PM_TRIANGLE_STRIP"]=function(){return(_emscripten_enum_bimtiles_PrimitiveMode_PM_TRIANGLE_STRIP=Module["_emscripten_enum_bimtiles_PrimitiveMode_PM_TRIANGLE_STRIP"]=Module["asm"]["ch"]).apply(null,arguments)};var _emscripten_enum_bimtiles_PrimitiveMode_PM_TRIANGLE_FAN=Module["_emscripten_enum_bimtiles_PrimitiveMode_PM_TRIANGLE_FAN"]=function(){return(_emscripten_enum_bimtiles_PrimitiveMode_PM_TRIANGLE_FAN=Module["_emscripten_enum_bimtiles_PrimitiveMode_PM_TRIANGLE_FAN"]=Module["asm"]["dh"]).apply(null,arguments)};var _emscripten_enum_bimtiles_NodeType_NT_UNKNOW_NODE=Module["_emscripten_enum_bimtiles_NodeType_NT_UNKNOW_NODE"]=function(){return(_emscripten_enum_bimtiles_NodeType_NT_UNKNOW_NODE=Module["_emscripten_enum_bimtiles_NodeType_NT_UNKNOW_NODE"]=Module["asm"]["eh"]).apply(null,arguments)};var _emscripten_enum_bimtiles_NodeType_NT_COMMON_NODE=Module["_emscripten_enum_bimtiles_NodeType_NT_COMMON_NODE"]=function(){return(_emscripten_enum_bimtiles_NodeType_NT_COMMON_NODE=Module["_emscripten_enum_bimtiles_NodeType_NT_COMMON_NODE"]=Module["asm"]["fh"]).apply(null,arguments)};var _emscripten_enum_bimtiles_NodeType_NT_BATCHED_MAIN_NODE=Module["_emscripten_enum_bimtiles_NodeType_NT_BATCHED_MAIN_NODE"]=function(){return(_emscripten_enum_bimtiles_NodeType_NT_BATCHED_MAIN_NODE=Module["_emscripten_enum_bimtiles_NodeType_NT_BATCHED_MAIN_NODE"]=Module["asm"]["gh"]).apply(null,arguments)};var _emscripten_enum_bimtiles_NodeType_NT_BATCHED_SUB_NODE=Module["_emscripten_enum_bimtiles_NodeType_NT_BATCHED_SUB_NODE"]=function(){return(_emscripten_enum_bimtiles_NodeType_NT_BATCHED_SUB_NODE=Module["_emscripten_enum_bimtiles_NodeType_NT_BATCHED_SUB_NODE"]=Module["asm"]["hh"]).apply(null,arguments)};var _emscripten_enum_bimtiles_ComponentDataType_CD_VEC2=Module["_emscripten_enum_bimtiles_ComponentDataType_CD_VEC2"]=function(){return(_emscripten_enum_bimtiles_ComponentDataType_CD_VEC2=Module["_emscripten_enum_bimtiles_ComponentDataType_CD_VEC2"]=Module["asm"]["ih"]).apply(null,arguments)};var _emscripten_enum_bimtiles_ComponentDataType_CD_VEC3=Module["_emscripten_enum_bimtiles_ComponentDataType_CD_VEC3"]=function(){return(_emscripten_enum_bimtiles_ComponentDataType_CD_VEC3=Module["_emscripten_enum_bimtiles_ComponentDataType_CD_VEC3"]=Module["asm"]["jh"]).apply(null,arguments)};var _emscripten_enum_bimtiles_ComponentDataType_CD_VEC4=Module["_emscripten_enum_bimtiles_ComponentDataType_CD_VEC4"]=function(){return(_emscripten_enum_bimtiles_ComponentDataType_CD_VEC4=Module["_emscripten_enum_bimtiles_ComponentDataType_CD_VEC4"]=Module["asm"]["kh"]).apply(null,arguments)};var _emscripten_enum_bimtiles_ComponentDataType_CD_MAT2=Module["_emscripten_enum_bimtiles_ComponentDataType_CD_MAT2"]=function(){return(_emscripten_enum_bimtiles_ComponentDataType_CD_MAT2=Module["_emscripten_enum_bimtiles_ComponentDataType_CD_MAT2"]=Module["asm"]["lh"]).apply(null,arguments)};var _emscripten_enum_bimtiles_ComponentDataType_CD_MAT3=Module["_emscripten_enum_bimtiles_ComponentDataType_CD_MAT3"]=function(){return(_emscripten_enum_bimtiles_ComponentDataType_CD_MAT3=Module["_emscripten_enum_bimtiles_ComponentDataType_CD_MAT3"]=Module["asm"]["mh"]).apply(null,arguments)};var _emscripten_enum_bimtiles_ComponentDataType_CD_MAT4=Module["_emscripten_enum_bimtiles_ComponentDataType_CD_MAT4"]=function(){return(_emscripten_enum_bimtiles_ComponentDataType_CD_MAT4=Module["_emscripten_enum_bimtiles_ComponentDataType_CD_MAT4"]=Module["asm"]["nh"]).apply(null,arguments)};var _emscripten_enum_bimtiles_ComponentDataType_CD_SCALAR=Module["_emscripten_enum_bimtiles_ComponentDataType_CD_SCALAR"]=function(){return(_emscripten_enum_bimtiles_ComponentDataType_CD_SCALAR=Module["_emscripten_enum_bimtiles_ComponentDataType_CD_SCALAR"]=Module["asm"]["oh"]).apply(null,arguments)};var _emscripten_enum_bimtiles_ComponentDataType_CD_BUFFER=Module["_emscripten_enum_bimtiles_ComponentDataType_CD_BUFFER"]=function(){return(_emscripten_enum_bimtiles_ComponentDataType_CD_BUFFER=Module["_emscripten_enum_bimtiles_ComponentDataType_CD_BUFFER"]=Module["asm"]["ph"]).apply(null,arguments)};var _emscripten_enum_bimtiles_StatusCode_OK=Module["_emscripten_enum_bimtiles_StatusCode_OK"]=function(){return(_emscripten_enum_bimtiles_StatusCode_OK=Module["_emscripten_enum_bimtiles_StatusCode_OK"]=Module["asm"]["qh"]).apply(null,arguments)};var _emscripten_enum_bimtiles_StatusCode_ERROR=Module["_emscripten_enum_bimtiles_StatusCode_ERROR"]=function(){return(_emscripten_enum_bimtiles_StatusCode_ERROR=Module["_emscripten_enum_bimtiles_StatusCode_ERROR"]=Module["asm"]["rh"]).apply(null,arguments)};var _emscripten_enum_bimtiles_StatusCode_IO_ERROR=Module["_emscripten_enum_bimtiles_StatusCode_IO_ERROR"]=function(){return(_emscripten_enum_bimtiles_StatusCode_IO_ERROR=Module["_emscripten_enum_bimtiles_StatusCode_IO_ERROR"]=Module["asm"]["sh"]).apply(null,arguments)};var _emscripten_enum_bimtiles_StatusCode_INVALID_PARAMETER=Module["_emscripten_enum_bimtiles_StatusCode_INVALID_PARAMETER"]=function(){return(_emscripten_enum_bimtiles_StatusCode_INVALID_PARAMETER=Module["_emscripten_enum_bimtiles_StatusCode_INVALID_PARAMETER"]=Module["asm"]["th"]).apply(null,arguments)};var _emscripten_enum_bimtiles_StatusCode_UNSUPPORTED_VERSION=Module["_emscripten_enum_bimtiles_StatusCode_UNSUPPORTED_VERSION"]=function(){return(_emscripten_enum_bimtiles_StatusCode_UNSUPPORTED_VERSION=Module["_emscripten_enum_bimtiles_StatusCode_UNSUPPORTED_VERSION"]=Module["asm"]["uh"]).apply(null,arguments)};var _emscripten_enum_bimtiles_StatusCode_UNKNOWN_VERSION=Module["_emscripten_enum_bimtiles_StatusCode_UNKNOWN_VERSION"]=function(){return(_emscripten_enum_bimtiles_StatusCode_UNKNOWN_VERSION=Module["_emscripten_enum_bimtiles_StatusCode_UNKNOWN_VERSION"]=Module["asm"]["vh"]).apply(null,arguments)};var _free=Module["_free"]=function(){return(_free=Module["_free"]=Module["asm"]["wh"]).apply(null,arguments)};var _malloc=Module["_malloc"]=function(){return(_malloc=Module["_malloc"]=Module["asm"]["xh"]).apply(null,arguments)};Module["callRuntimeCallbacks"]=callRuntimeCallbacks;var calledRun;function ExitStatus(status){this.name="ExitStatus";this.message="Program terminated with exit("+status+")";this.status=status}dependenciesFulfilled=function runCaller(){if(!calledRun)run();if(!calledRun)dependenciesFulfilled=runCaller};function run(args){args=args||arguments_;if(runDependencies>0){return}preRun();if(runDependencies>0){return}function doRun(){if(calledRun)return;calledRun=true;Module["calledRun"]=true;if(ABORT)return;initRuntime();readyPromiseResolve(Module);if(Module["onRuntimeInitialized"])Module["onRuntimeInitialized"]();postRun()}if(Module["setStatus"]){Module["setStatus"]("Running...");setTimeout(function(){setTimeout(function(){Module["setStatus"]("")},1);doRun()},1)}else{doRun()}}Module["run"]=run;if(Module["preInit"]){if(typeof Module["preInit"]=="function")Module["preInit"]=[Module["preInit"]];while(Module["preInit"].length>0){Module["preInit"].pop()()}}run();function WrapperObject(){}WrapperObject.prototype=Object.create(WrapperObject.prototype);WrapperObject.prototype.constructor=WrapperObject;WrapperObject.prototype.__class__=WrapperObject;WrapperObject.__cache__={};Module["WrapperObject"]=WrapperObject;function getCache(__class__){return(__class__||WrapperObject).__cache__}Module["getCache"]=getCache;function wrapPointer(ptr,__class__){var cache=getCache(__class__);var ret=cache[ptr];if(ret)return ret;ret=Object.create((__class__||WrapperObject).prototype);ret.ptr=ptr;return cache[ptr]=ret}Module["wrapPointer"]=wrapPointer;function castObject(obj,__class__){return wrapPointer(obj.ptr,__class__)}Module["castObject"]=castObject;Module["NULL"]=wrapPointer(0);function destroy(obj){if(!obj["__destroy__"])throw"Error: Cannot destroy object. (Did you create it yourself?)";obj["__destroy__"]();delete getCache(obj.__class__)[obj.ptr]}Module["destroy"]=destroy;function compare(obj1,obj2){return obj1.ptr===obj2.ptr}Module["compare"]=compare;function getPointer(obj){return obj.ptr}Module["getPointer"]=getPointer;function getClass(obj){return obj.__class__}Module["getClass"]=getClass;var ensureCache={buffer:0,size:0,pos:0,temps:[],needed:0,prepare:function(){if(ensureCache.needed){for(var i=0;i<ensureCache.temps.length;i++){Module["_free"](ensureCache.temps[i])}ensureCache.temps.length=0;Module["_free"](ensureCache.buffer);ensureCache.buffer=0;ensureCache.size+=ensureCache.needed;ensureCache.needed=0}if(!ensureCache.buffer){ensureCache.size+=128;ensureCache.buffer=Module["_malloc"](ensureCache.size);assert(ensureCache.buffer)}ensureCache.pos=0},alloc:function(array,view){assert(ensureCache.buffer);var bytes=view.BYTES_PER_ELEMENT;var len=array.length*bytes;len=len+7&-8;var ret;if(ensureCache.pos+len>=ensureCache.size){assert(len>0);ensureCache.needed+=len;ret=Module["_malloc"](len);ensureCache.temps.push(ret)}else{ret=ensureCache.buffer+ensureCache.pos;ensureCache.pos+=len}return ret},copy:function(array,view,offset){offset>>>=0;var bytes=view.BYTES_PER_ELEMENT;switch(bytes){case 2:offset>>>=1;break;case 4:offset>>>=2;break;case 8:offset>>>=3;break}for(var i=0;i<array.length;i++){view[offset+i]=array[i]}}};function ensureInt8(value){if(typeof value==="object"){var offset=ensureCache.alloc(value,HEAP8);ensureCache.copy(value,HEAP8,offset);return offset}return value}function VoidPtr(){throw"cannot construct a VoidPtr, no constructor in IDL"}VoidPtr.prototype=Object.create(WrapperObject.prototype);VoidPtr.prototype.constructor=VoidPtr;VoidPtr.prototype.__class__=VoidPtr;VoidPtr.__cache__={};Module["VoidPtr"]=VoidPtr;VoidPtr.prototype["__destroy__"]=VoidPtr.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_VoidPtr___destroy___0(self)};function Status(){throw"cannot construct a Status, no constructor in IDL"}Status.prototype=Object.create(WrapperObject.prototype);Status.prototype.constructor=Status;Status.prototype.__class__=Status;Status.__cache__={};Module["Status"]=Status;Status.prototype["code"]=Status.prototype.code=function(){var self=this.ptr;return _emscripten_bind_Status_code_0(self)};Status.prototype["ok"]=Status.prototype.ok=function(){var self=this.ptr;return!!_emscripten_bind_Status_ok_0(self)};Status.prototype["error_msg"]=Status.prototype.error_msg=function(){var self=this.ptr;return UTF8ToString(_emscripten_bind_Status_error_msg_0(self))};Status.prototype["__destroy__"]=Status.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_Status___destroy___0(self)};function GeometryAttribute(){this.ptr=_emscripten_bind_GeometryAttribute_GeometryAttribute_0();getCache(GeometryAttribute)[this.ptr]=this}GeometryAttribute.prototype=Object.create(WrapperObject.prototype);GeometryAttribute.prototype.constructor=GeometryAttribute;GeometryAttribute.prototype.__class__=GeometryAttribute;GeometryAttribute.__cache__={};Module["GeometryAttribute"]=GeometryAttribute;GeometryAttribute.prototype["attribute_type"]=GeometryAttribute.prototype.attribute_type=function(){var self=this.ptr;return _emscripten_bind_GeometryAttribute_attribute_type_0(self)};GeometryAttribute.prototype["data_type"]=GeometryAttribute.prototype.data_type=function(){var self=this.ptr;return _emscripten_bind_GeometryAttribute_data_type_0(self)};GeometryAttribute.prototype["num_components"]=GeometryAttribute.prototype.num_components=function(){var self=this.ptr;return _emscripten_bind_GeometryAttribute_num_components_0(self)};GeometryAttribute.prototype["num_points"]=GeometryAttribute.prototype.num_points=function(){var self=this.ptr;return _emscripten_bind_GeometryAttribute_num_points_0(self)};GeometryAttribute.prototype["byte_stride"]=GeometryAttribute.prototype.byte_stride=function(){var self=this.ptr;return _emscripten_bind_GeometryAttribute_byte_stride_0(self)};GeometryAttribute.prototype["byte_offset"]=GeometryAttribute.prototype.byte_offset=function(){var self=this.ptr;return _emscripten_bind_GeometryAttribute_byte_offset_0(self)};GeometryAttribute.prototype["byte_length"]=GeometryAttribute.prototype.byte_length=function(){var self=this.ptr;return _emscripten_bind_GeometryAttribute_byte_length_0(self)};GeometryAttribute.prototype["unique_id"]=GeometryAttribute.prototype.unique_id=function(){var self=this.ptr;return _emscripten_bind_GeometryAttribute_unique_id_0(self)};GeometryAttribute.prototype["normalized"]=GeometryAttribute.prototype.normalized=function(){var self=this.ptr;return!!_emscripten_bind_GeometryAttribute_normalized_0(self)};GeometryAttribute.prototype["compressed"]=GeometryAttribute.prototype.compressed=function(){var self=this.ptr;return!!_emscripten_bind_GeometryAttribute_compressed_0(self)};GeometryAttribute.prototype["__destroy__"]=GeometryAttribute.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_GeometryAttribute___destroy___0(self)};function MeshEntity(){this.ptr=_emscripten_bind_MeshEntity_MeshEntity_0();getCache(MeshEntity)[this.ptr]=this}MeshEntity.prototype=Object.create(WrapperObject.prototype);MeshEntity.prototype.constructor=MeshEntity;MeshEntity.prototype.__class__=MeshEntity;MeshEntity.__cache__={};Module["MeshEntity"]=MeshEntity;MeshEntity.prototype["GetAttribute"]=MeshEntity.prototype.GetAttribute=function(att_id){var self=this.ptr;if(att_id&&typeof att_id==="object")att_id=att_id.ptr;return wrapPointer(_emscripten_bind_MeshEntity_GetAttribute_1(self,att_id),GeometryAttribute)};MeshEntity.prototype["num_attributes"]=MeshEntity.prototype.num_attributes=function(){var self=this.ptr;return _emscripten_bind_MeshEntity_num_attributes_0(self)};MeshEntity.prototype["GetSubMeshEntity"]=MeshEntity.prototype.GetSubMeshEntity=function(entity_id){var self=this.ptr;if(entity_id&&typeof entity_id==="object")entity_id=entity_id.ptr;return wrapPointer(_emscripten_bind_MeshEntity_GetSubMeshEntity_1(self,entity_id),MeshEntity)};MeshEntity.prototype["num_sub_entities"]=MeshEntity.prototype.num_sub_entities=function(){var self=this.ptr;return _emscripten_bind_MeshEntity_num_sub_entities_0(self)};MeshEntity.prototype["get_user_id"]=MeshEntity.prototype.get_user_id=function(){var self=this.ptr;return _emscripten_bind_MeshEntity_get_user_id_0(self)};MeshEntity.prototype["get_userdata_id"]=MeshEntity.prototype.get_userdata_id=function(){var self=this.ptr;return _emscripten_bind_MeshEntity_get_userdata_id_0(self)};MeshEntity.prototype["get_material_id"]=MeshEntity.prototype.get_material_id=function(){var self=this.ptr;return _emscripten_bind_MeshEntity_get_material_id_0(self)};MeshEntity.prototype["get_node_id"]=MeshEntity.prototype.get_node_id=function(){var self=this.ptr;return _emscripten_bind_MeshEntity_get_node_id_0(self)};MeshEntity.prototype["get_primitive_id"]=MeshEntity.prototype.get_primitive_id=function(){var self=this.ptr;return _emscripten_bind_MeshEntity_get_primitive_id_0(self)};MeshEntity.prototype["primitive_mode"]=MeshEntity.prototype.primitive_mode=function(){var self=this.ptr;return _emscripten_bind_MeshEntity_primitive_mode_0(self)};MeshEntity.prototype["node_type"]=MeshEntity.prototype.node_type=function(){var self=this.ptr;return _emscripten_bind_MeshEntity_node_type_0(self)};MeshEntity.prototype["get_scene_id"]=MeshEntity.prototype.get_scene_id=function(){var self=this.ptr;return _emscripten_bind_MeshEntity_get_scene_id_0(self)};MeshEntity.prototype["get_scene_name"]=MeshEntity.prototype.get_scene_name=function(){var self=this.ptr;return UTF8ToString(_emscripten_bind_MeshEntity_get_scene_name_0(self))};MeshEntity.prototype["num_components_bbox"]=MeshEntity.prototype.num_components_bbox=function(){var self=this.ptr;return _emscripten_bind_MeshEntity_num_components_bbox_0(self)};MeshEntity.prototype["byte_length_per_component_bbox"]=MeshEntity.prototype.byte_length_per_component_bbox=function(){var self=this.ptr;return _emscripten_bind_MeshEntity_byte_length_per_component_bbox_0(self)};MeshEntity.prototype["num_components_matrix"]=MeshEntity.prototype.num_components_matrix=function(){var self=this.ptr;return _emscripten_bind_MeshEntity_num_components_matrix_0(self)};MeshEntity.prototype["byte_length_per_component_matrix"]=MeshEntity.prototype.byte_length_per_component_matrix=function(){var self=this.ptr;return _emscripten_bind_MeshEntity_byte_length_per_component_matrix_0(self)};MeshEntity.prototype["num_components_translation"]=MeshEntity.prototype.num_components_translation=function(){var self=this.ptr;return _emscripten_bind_MeshEntity_num_components_translation_0(self)};MeshEntity.prototype["byte_length_per_component_translation"]=MeshEntity.prototype.byte_length_per_component_translation=function(){var self=this.ptr;return _emscripten_bind_MeshEntity_byte_length_per_component_translation_0(self)};MeshEntity.prototype["GetMatrixFloat32Array"]=MeshEntity.prototype.GetMatrixFloat32Array=function(out_size,out_values){var self=this.ptr;if(out_size&&typeof out_size==="object")out_size=out_size.ptr;if(out_values&&typeof out_values==="object")out_values=out_values.ptr;return!!_emscripten_bind_MeshEntity_GetMatrixFloat32Array_2(self,out_size,out_values)};MeshEntity.prototype["GetBBoxFloat32Array"]=MeshEntity.prototype.GetBBoxFloat32Array=function(out_size,out_values){var self=this.ptr;if(out_size&&typeof out_size==="object")out_size=out_size.ptr;if(out_values&&typeof out_values==="object")out_values=out_values.ptr;return!!_emscripten_bind_MeshEntity_GetBBoxFloat32Array_2(self,out_size,out_values)};MeshEntity.prototype["GetTranslationFloat64Array"]=MeshEntity.prototype.GetTranslationFloat64Array=function(out_size,out_values){var self=this.ptr;if(out_size&&typeof out_size==="object")out_size=out_size.ptr;if(out_values&&typeof out_values==="object")out_values=out_values.ptr;return!!_emscripten_bind_MeshEntity_GetTranslationFloat64Array_2(self,out_size,out_values)};MeshEntity.prototype["__destroy__"]=MeshEntity.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_MeshEntity___destroy___0(self)};function BimTilesMesh(){this.ptr=_emscripten_bind_BimTilesMesh_BimTilesMesh_0();getCache(BimTilesMesh)[this.ptr]=this}BimTilesMesh.prototype=Object.create(WrapperObject.prototype);BimTilesMesh.prototype.constructor=BimTilesMesh;BimTilesMesh.prototype.__class__=BimTilesMesh;BimTilesMesh.__cache__={};Module["BimTilesMesh"]=BimTilesMesh;BimTilesMesh.prototype["GetMeshEntity"]=BimTilesMesh.prototype.GetMeshEntity=function(entity_id){var self=this.ptr;if(entity_id&&typeof entity_id==="object")entity_id=entity_id.ptr;return wrapPointer(_emscripten_bind_BimTilesMesh_GetMeshEntity_1(self,entity_id),MeshEntity)};BimTilesMesh.prototype["num_mesh_entities"]=BimTilesMesh.prototype.num_mesh_entities=function(){var self=this.ptr;return _emscripten_bind_BimTilesMesh_num_mesh_entities_0(self)};BimTilesMesh.prototype["get_tile_id"]=BimTilesMesh.prototype.get_tile_id=function(){var self=this.ptr;return _emscripten_bind_BimTilesMesh_get_tile_id_0(self)};BimTilesMesh.prototype["get_block_id"]=BimTilesMesh.prototype.get_block_id=function(){var self=this.ptr;return _emscripten_bind_BimTilesMesh_get_block_id_0(self)};BimTilesMesh.prototype["get_buffer_block_id"]=BimTilesMesh.prototype.get_buffer_block_id=function(){var self=this.ptr;return _emscripten_bind_BimTilesMesh_get_buffer_block_id_0(self)};BimTilesMesh.prototype["get_material_block_id"]=BimTilesMesh.prototype.get_material_block_id=function(){var self=this.ptr;return _emscripten_bind_BimTilesMesh_get_material_block_id_0(self)};BimTilesMesh.prototype["get_userdata_block_id"]=BimTilesMesh.prototype.get_userdata_block_id=function(){var self=this.ptr;return _emscripten_bind_BimTilesMesh_get_userdata_block_id_0(self)};BimTilesMesh.prototype["num_components_bbox"]=BimTilesMesh.prototype.num_components_bbox=function(){var self=this.ptr;return _emscripten_bind_BimTilesMesh_num_components_bbox_0(self)};BimTilesMesh.prototype["byte_length_per_component_bbox"]=BimTilesMesh.prototype.byte_length_per_component_bbox=function(){var self=this.ptr;return _emscripten_bind_BimTilesMesh_byte_length_per_component_bbox_0(self)};BimTilesMesh.prototype["num_components_matrix"]=BimTilesMesh.prototype.num_components_matrix=function(){var self=this.ptr;return _emscripten_bind_BimTilesMesh_num_components_matrix_0(self)};BimTilesMesh.prototype["byte_length_per_component_matrix"]=BimTilesMesh.prototype.byte_length_per_component_matrix=function(){var self=this.ptr;return _emscripten_bind_BimTilesMesh_byte_length_per_component_matrix_0(self)};BimTilesMesh.prototype["num_components_translation"]=BimTilesMesh.prototype.num_components_translation=function(){var self=this.ptr;return _emscripten_bind_BimTilesMesh_num_components_translation_0(self)};BimTilesMesh.prototype["byte_length_per_component_translation"]=BimTilesMesh.prototype.byte_length_per_component_translation=function(){var self=this.ptr;return _emscripten_bind_BimTilesMesh_byte_length_per_component_translation_0(self)};BimTilesMesh.prototype["GetMatrixFloat32Array"]=BimTilesMesh.prototype.GetMatrixFloat32Array=function(out_size,out_values){var self=this.ptr;if(out_size&&typeof out_size==="object")out_size=out_size.ptr;if(out_values&&typeof out_values==="object")out_values=out_values.ptr;return!!_emscripten_bind_BimTilesMesh_GetMatrixFloat32Array_2(self,out_size,out_values)};BimTilesMesh.prototype["GetBBoxFloat32Array"]=BimTilesMesh.prototype.GetBBoxFloat32Array=function(out_size,out_values){var self=this.ptr;if(out_size&&typeof out_size==="object")out_size=out_size.ptr;if(out_values&&typeof out_values==="object")out_values=out_values.ptr;return!!_emscripten_bind_BimTilesMesh_GetBBoxFloat32Array_2(self,out_size,out_values)};BimTilesMesh.prototype["GetTranslationFloat64Array"]=BimTilesMesh.prototype.GetTranslationFloat64Array=function(out_size,out_values){var self=this.ptr;if(out_size&&typeof out_size==="object")out_size=out_size.ptr;if(out_values&&typeof out_values==="object")out_values=out_values.ptr;return!!_emscripten_bind_BimTilesMesh_GetTranslationFloat64Array_2(self,out_size,out_values)};BimTilesMesh.prototype["__destroy__"]=BimTilesMesh.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_BimTilesMesh___destroy___0(self)};function InstanceEntity(){this.ptr=_emscripten_bind_InstanceEntity_InstanceEntity_0();getCache(InstanceEntity)[this.ptr]=this}InstanceEntity.prototype=Object.create(WrapperObject.prototype);InstanceEntity.prototype.constructor=InstanceEntity;InstanceEntity.prototype.__class__=InstanceEntity;InstanceEntity.__cache__={};Module["InstanceEntity"]=InstanceEntity;InstanceEntity.prototype["get_user_id"]=InstanceEntity.prototype.get_user_id=function(){var self=this.ptr;return _emscripten_bind_InstanceEntity_get_user_id_0(self)};InstanceEntity.prototype["get_userdata_id"]=InstanceEntity.prototype.get_userdata_id=function(){var self=this.ptr;return _emscripten_bind_InstanceEntity_get_userdata_id_0(self)};InstanceEntity.prototype["get_color"]=InstanceEntity.prototype.get_color=function(){var self=this.ptr;return _emscripten_bind_InstanceEntity_get_color_0(self)};InstanceEntity.prototype["num_components_matrix"]=InstanceEntity.prototype.num_components_matrix=function(){var self=this.ptr;return _emscripten_bind_InstanceEntity_num_components_matrix_0(self)};InstanceEntity.prototype["data_type_matrix"]=InstanceEntity.prototype.data_type_matrix=function(){var self=this.ptr;return _emscripten_bind_InstanceEntity_data_type_matrix_0(self)};InstanceEntity.prototype["num_components_bbox"]=InstanceEntity.prototype.num_components_bbox=function(){var self=this.ptr;return _emscripten_bind_InstanceEntity_num_components_bbox_0(self)};InstanceEntity.prototype["data_type_bbox"]=InstanceEntity.prototype.data_type_bbox=function(){var self=this.ptr;return _emscripten_bind_InstanceEntity_data_type_bbox_0(self)};InstanceEntity.prototype["num_components_translation"]=InstanceEntity.prototype.num_components_translation=function(){var self=this.ptr;return _emscripten_bind_InstanceEntity_num_components_translation_0(self)};InstanceEntity.prototype["data_type_translation"]=InstanceEntity.prototype.data_type_translation=function(){var self=this.ptr;return _emscripten_bind_InstanceEntity_data_type_translation_0(self)};InstanceEntity.prototype["num_components_packing_uvs"]=InstanceEntity.prototype.num_components_packing_uvs=function(){var self=this.ptr;return _emscripten_bind_InstanceEntity_num_components_packing_uvs_0(self)};InstanceEntity.prototype["__destroy__"]=InstanceEntity.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_InstanceEntity___destroy___0(self)};function BimTilesInstance(){this.ptr=_emscripten_bind_BimTilesInstance_BimTilesInstance_0();getCache(BimTilesInstance)[this.ptr]=this}BimTilesInstance.prototype=Object.create(WrapperObject.prototype);BimTilesInstance.prototype.constructor=BimTilesInstance;BimTilesInstance.prototype.__class__=BimTilesInstance;BimTilesInstance.__cache__={};Module["BimTilesInstance"]=BimTilesInstance;BimTilesInstance.prototype["get_blob_id"]=BimTilesInstance.prototype.get_blob_id=function(){var self=this.ptr;return _emscripten_bind_BimTilesInstance_get_blob_id_0(self)};BimTilesInstance.prototype["get_tile_id"]=BimTilesInstance.prototype.get_tile_id=function(){var self=this.ptr;return _emscripten_bind_BimTilesInstance_get_tile_id_0(self)};BimTilesInstance.prototype["get_block_id"]=BimTilesInstance.prototype.get_block_id=function(){var self=this.ptr;return _emscripten_bind_BimTilesInstance_get_block_id_0(self)};BimTilesInstance.prototype["get_entity_url"]=BimTilesInstance.prototype.get_entity_url=function(){var self=this.ptr;return UTF8ToString(_emscripten_bind_BimTilesInstance_get_entity_url_0(self))};BimTilesInstance.prototype["num_instance_entities"]=BimTilesInstance.prototype.num_instance_entities=function(){var self=this.ptr;return _emscripten_bind_BimTilesInstance_num_instance_entities_0(self)};BimTilesInstance.prototype["GetInstanceEntity"]=BimTilesInstance.prototype.GetInstanceEntity=function(entity_id){var self=this.ptr;if(entity_id&&typeof entity_id==="object")entity_id=entity_id.ptr;return wrapPointer(_emscripten_bind_BimTilesInstance_GetInstanceEntity_1(self,entity_id),InstanceEntity)};BimTilesInstance.prototype["num_components_matrix"]=BimTilesInstance.prototype.num_components_matrix=function(){var self=this.ptr;return _emscripten_bind_BimTilesInstance_num_components_matrix_0(self)};BimTilesInstance.prototype["data_type_matrix"]=BimTilesInstance.prototype.data_type_matrix=function(){var self=this.ptr;return _emscripten_bind_BimTilesInstance_data_type_matrix_0(self)};BimTilesInstance.prototype["GetHeaderMatrixFloat32Array"]=BimTilesInstance.prototype.GetHeaderMatrixFloat32Array=function(out_size,out_value){var self=this.ptr;if(out_size&&typeof out_size==="object")out_size=out_size.ptr;if(out_value&&typeof out_value==="object")out_value=out_value.ptr;return!!_emscripten_bind_BimTilesInstance_GetHeaderMatrixFloat32Array_2(self,out_size,out_value)};BimTilesInstance.prototype["num_components_bbox"]=BimTilesInstance.prototype.num_components_bbox=function(){var self=this.ptr;return _emscripten_bind_BimTilesInstance_num_components_bbox_0(self)};BimTilesInstance.prototype["data_type_bbox"]=BimTilesInstance.prototype.data_type_bbox=function(){var self=this.ptr;return _emscripten_bind_BimTilesInstance_data_type_bbox_0(self)};BimTilesInstance.prototype["GetHeaderBBoxFloat32Array"]=BimTilesInstance.prototype.GetHeaderBBoxFloat32Array=function(out_size,out_value){var self=this.ptr;if(out_size&&typeof out_size==="object")out_size=out_size.ptr;if(out_value&&typeof out_value==="object")out_value=out_value.ptr;return!!_emscripten_bind_BimTilesInstance_GetHeaderBBoxFloat32Array_2(self,out_size,out_value)};BimTilesInstance.prototype["num_components_translation"]=BimTilesInstance.prototype.num_components_translation=function(){var self=this.ptr;return _emscripten_bind_BimTilesInstance_num_components_translation_0(self)};BimTilesInstance.prototype["data_type_translation"]=BimTilesInstance.prototype.data_type_translation=function(){var self=this.ptr;return _emscripten_bind_BimTilesInstance_data_type_translation_0(self)};BimTilesInstance.prototype["GetHeaderTranslationFloat64Array"]=BimTilesInstance.prototype.GetHeaderTranslationFloat64Array=function(out_size,out_value){var self=this.ptr;if(out_size&&typeof out_size==="object")out_size=out_size.ptr;if(out_value&&typeof out_value==="object")out_value=out_value.ptr;return!!_emscripten_bind_BimTilesInstance_GetHeaderTranslationFloat64Array_2(self,out_size,out_value)};BimTilesInstance.prototype["num_components_packing_uvs"]=BimTilesInstance.prototype.num_components_packing_uvs=function(){var self=this.ptr;return _emscripten_bind_BimTilesInstance_num_components_packing_uvs_0(self)};BimTilesInstance.prototype["GetFloat32ArrayOfHeaderPackingUvs"]=BimTilesInstance.prototype.GetFloat32ArrayOfHeaderPackingUvs=function(out_size,out_value){var self=this.ptr;if(out_size&&typeof out_size==="object")out_size=out_size.ptr;if(out_value&&typeof out_value==="object")out_value=out_value.ptr;return!!_emscripten_bind_BimTilesInstance_GetFloat32ArrayOfHeaderPackingUvs_2(self,out_size,out_value)};BimTilesInstance.prototype["GetMatrixFloat32Array"]=BimTilesInstance.prototype.GetMatrixFloat32Array=function(entity,out_size,out_value){var self=this.ptr;if(entity&&typeof entity==="object")entity=entity.ptr;if(out_size&&typeof out_size==="object")out_size=out_size.ptr;if(out_value&&typeof out_value==="object")out_value=out_value.ptr;return!!_emscripten_bind_BimTilesInstance_GetMatrixFloat32Array_3(self,entity,out_size,out_value)};BimTilesInstance.prototype["GetBBoxFloat32Array"]=BimTilesInstance.prototype.GetBBoxFloat32Array=function(entity,out_size,out_value){var self=this.ptr;if(entity&&typeof entity==="object")entity=entity.ptr;if(out_size&&typeof out_size==="object")out_size=out_size.ptr;if(out_value&&typeof out_value==="object")out_value=out_value.ptr;return!!_emscripten_bind_BimTilesInstance_GetBBoxFloat32Array_3(self,entity,out_size,out_value)};BimTilesInstance.prototype["GetTranslationFloat64Array"]=BimTilesInstance.prototype.GetTranslationFloat64Array=function(entity,out_size,out_value){var self=this.ptr;if(entity&&typeof entity==="object")entity=entity.ptr;if(out_size&&typeof out_size==="object")out_size=out_size.ptr;if(out_value&&typeof out_value==="object")out_value=out_value.ptr;return!!_emscripten_bind_BimTilesInstance_GetTranslationFloat64Array_3(self,entity,out_size,out_value)};BimTilesInstance.prototype["GetFloat32ArrayOfPackingUvs"]=BimTilesInstance.prototype.GetFloat32ArrayOfPackingUvs=function(entity,out_size,out_value){var self=this.ptr;if(entity&&typeof entity==="object")entity=entity.ptr;if(out_size&&typeof out_size==="object")out_size=out_size.ptr;if(out_value&&typeof out_value==="object")out_value=out_value.ptr;return!!_emscripten_bind_BimTilesInstance_GetFloat32ArrayOfPackingUvs_3(self,entity,out_size,out_value)};BimTilesInstance.prototype["__destroy__"]=BimTilesInstance.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_BimTilesInstance___destroy___0(self)};function CustomDataItem(){this.ptr=_emscripten_bind_CustomDataItem_CustomDataItem_0();getCache(CustomDataItem)[this.ptr]=this}CustomDataItem.prototype=Object.create(WrapperObject.prototype);CustomDataItem.prototype.constructor=CustomDataItem;CustomDataItem.prototype.__class__=CustomDataItem;CustomDataItem.__cache__={};Module["CustomDataItem"]=CustomDataItem;CustomDataItem.prototype["get_name"]=CustomDataItem.prototype.get_name=function(){var self=this.ptr;return UTF8ToString(_emscripten_bind_CustomDataItem_get_name_0(self))};CustomDataItem.prototype["__destroy__"]=CustomDataItem.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_CustomDataItem___destroy___0(self)};function ImageItem(){this.ptr=_emscripten_bind_ImageItem_ImageItem_0();getCache(ImageItem)[this.ptr]=this}ImageItem.prototype=Object.create(WrapperObject.prototype);ImageItem.prototype.constructor=ImageItem;ImageItem.prototype.__class__=ImageItem;ImageItem.__cache__={};Module["ImageItem"]=ImageItem;ImageItem.prototype["get_url"]=ImageItem.prototype.get_url=function(){var self=this.ptr;return UTF8ToString(_emscripten_bind_ImageItem_get_url_0(self))};ImageItem.prototype["get_type"]=ImageItem.prototype.get_type=function(){var self=this.ptr;return UTF8ToString(_emscripten_bind_ImageItem_get_type_0(self))};ImageItem.prototype["get_width"]=ImageItem.prototype.get_width=function(){var self=this.ptr;return _emscripten_bind_ImageItem_get_width_0(self)};ImageItem.prototype["get_height"]=ImageItem.prototype.get_height=function(){var self=this.ptr;return _emscripten_bind_ImageItem_get_height_0(self)};ImageItem.prototype["get_dimension"]=ImageItem.prototype.get_dimension=function(){var self=this.ptr;return _emscripten_bind_ImageItem_get_dimension_0(self)};ImageItem.prototype["get_buffer_id"]=ImageItem.prototype.get_buffer_id=function(){var self=this.ptr;return _emscripten_bind_ImageItem_get_buffer_id_0(self)};ImageItem.prototype["get_byte_offset"]=ImageItem.prototype.get_byte_offset=function(){var self=this.ptr;return _emscripten_bind_ImageItem_get_byte_offset_0(self)};ImageItem.prototype["get_byte_length"]=ImageItem.prototype.get_byte_length=function(){var self=this.ptr;return _emscripten_bind_ImageItem_get_byte_length_0(self)};ImageItem.prototype["HasImageBuffer"]=ImageItem.prototype.HasImageBuffer=function(){var self=this.ptr;return!!_emscripten_bind_ImageItem_HasImageBuffer_0(self)};ImageItem.prototype["GetImageBuffer"]=ImageItem.prototype.GetImageBuffer=function(out_size,out_values){var self=this.ptr;if(out_size&&typeof out_size==="object")out_size=out_size.ptr;if(out_values&&typeof out_values==="object")out_values=out_values.ptr;return!!_emscripten_bind_ImageItem_GetImageBuffer_2(self,out_size,out_values)};ImageItem.prototype["__destroy__"]=ImageItem.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_ImageItem___destroy___0(self)};function TextureItem(){this.ptr=_emscripten_bind_TextureItem_TextureItem_0();getCache(TextureItem)[this.ptr]=this}TextureItem.prototype=Object.create(WrapperObject.prototype);TextureItem.prototype.constructor=TextureItem;TextureItem.prototype.__class__=TextureItem;TextureItem.__cache__={};Module["TextureItem"]=TextureItem;TextureItem.prototype["get_name"]=TextureItem.prototype.get_name=function(){var self=this.ptr;return UTF8ToString(_emscripten_bind_TextureItem_get_name_0(self))};TextureItem.prototype["get_type"]=TextureItem.prototype.get_type=function(){var self=this.ptr;return _emscripten_bind_TextureItem_get_type_0(self)};TextureItem.prototype["get_image"]=TextureItem.prototype.get_image=function(){var self=this.ptr;return _emscripten_bind_TextureItem_get_image_0(self)};TextureItem.prototype["get_repeat_u"]=TextureItem.prototype.get_repeat_u=function(){var self=this.ptr;return _emscripten_bind_TextureItem_get_repeat_u_0(self)};TextureItem.prototype["get_repeat_v"]=TextureItem.prototype.get_repeat_v=function(){var self=this.ptr;return _emscripten_bind_TextureItem_get_repeat_v_0(self)};TextureItem.prototype["get_angle"]=TextureItem.prototype.get_angle=function(){var self=this.ptr;return _emscripten_bind_TextureItem_get_angle_0(self)};TextureItem.prototype["get_alpha"]=TextureItem.prototype.get_alpha=function(){var self=this.ptr;return _emscripten_bind_TextureItem_get_alpha_0(self)};TextureItem.prototype["get_depth"]=TextureItem.prototype.get_depth=function(){var self=this.ptr;return _emscripten_bind_TextureItem_get_depth_0(self)};TextureItem.prototype["get_offset_u"]=TextureItem.prototype.get_offset_u=function(){var self=this.ptr;return _emscripten_bind_TextureItem_get_offset_u_0(self)};TextureItem.prototype["get_offset_v"]=TextureItem.prototype.get_offset_v=function(){var self=this.ptr;return _emscripten_bind_TextureItem_get_offset_v_0(self)};TextureItem.prototype["get_scale_u"]=TextureItem.prototype.get_scale_u=function(){var self=this.ptr;return _emscripten_bind_TextureItem_get_scale_u_0(self)};TextureItem.prototype["get_scale_v"]=TextureItem.prototype.get_scale_v=function(){var self=this.ptr;return _emscripten_bind_TextureItem_get_scale_v_0(self)};TextureItem.prototype["num_entries_lod_images"]=TextureItem.prototype.num_entries_lod_images=function(){var self=this.ptr;return _emscripten_bind_TextureItem_num_entries_lod_images_0(self)};TextureItem.prototype["byte_length_per_entry_lod_images"]=TextureItem.prototype.byte_length_per_entry_lod_images=function(){var self=this.ptr;return _emscripten_bind_TextureItem_byte_length_per_entry_lod_images_0(self)};TextureItem.prototype["__destroy__"]=TextureItem.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_TextureItem___destroy___0(self)};function MaterialItem(){this.ptr=_emscripten_bind_MaterialItem_MaterialItem_0();getCache(MaterialItem)[this.ptr]=this}MaterialItem.prototype=Object.create(WrapperObject.prototype);MaterialItem.prototype.constructor=MaterialItem;MaterialItem.prototype.__class__=MaterialItem;MaterialItem.__cache__={};Module["MaterialItem"]=MaterialItem;MaterialItem.prototype["get_name"]=MaterialItem.prototype.get_name=function(){var self=this.ptr;return UTF8ToString(_emscripten_bind_MaterialItem_get_name_0(self))};MaterialItem.prototype["get_type"]=MaterialItem.prototype.get_type=function(){var self=this.ptr;return _emscripten_bind_MaterialItem_get_type_0(self)};MaterialItem.prototype["get_color"]=MaterialItem.prototype.get_color=function(){var self=this.ptr;return _emscripten_bind_MaterialItem_get_color_0(self)};MaterialItem.prototype["get_double_side"]=MaterialItem.prototype.get_double_side=function(){var self=this.ptr;return _emscripten_bind_MaterialItem_get_double_side_0(self)};MaterialItem.prototype["get_receive_ibl"]=MaterialItem.prototype.get_receive_ibl=function(){var self=this.ptr;return _emscripten_bind_MaterialItem_get_receive_ibl_0(self)};MaterialItem.prototype["get_opacity"]=MaterialItem.prototype.get_opacity=function(){var self=this.ptr;return _emscripten_bind_MaterialItem_get_opacity_0(self)};MaterialItem.prototype["get_metalness"]=MaterialItem.prototype.get_metalness=function(){var self=this.ptr;return _emscripten_bind_MaterialItem_get_metalness_0(self)};MaterialItem.prototype["get_roughness"]=MaterialItem.prototype.get_roughness=function(){var self=this.ptr;return _emscripten_bind_MaterialItem_get_roughness_0(self)};MaterialItem.prototype["num_entries_textures"]=MaterialItem.prototype.num_entries_textures=function(){var self=this.ptr;return _emscripten_bind_MaterialItem_num_entries_textures_0(self)};MaterialItem.prototype["byte_length_per_entry_textures"]=MaterialItem.prototype.byte_length_per_entry_textures=function(){var self=this.ptr;return _emscripten_bind_MaterialItem_byte_length_per_entry_textures_0(self)};MaterialItem.prototype["num_entries_custom_data_items"]=MaterialItem.prototype.num_entries_custom_data_items=function(){var self=this.ptr;return _emscripten_bind_MaterialItem_num_entries_custom_data_items_0(self)};MaterialItem.prototype["GetCustomDataItem"]=MaterialItem.prototype.GetCustomDataItem=function(item_id){var self=this.ptr;if(item_id&&typeof item_id==="object")item_id=item_id.ptr;return wrapPointer(_emscripten_bind_MaterialItem_GetCustomDataItem_1(self,item_id),CustomDataItem)};MaterialItem.prototype["__destroy__"]=MaterialItem.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_MaterialItem___destroy___0(self)};function BimTilesMaterial(){this.ptr=_emscripten_bind_BimTilesMaterial_BimTilesMaterial_0();getCache(BimTilesMaterial)[this.ptr]=this}BimTilesMaterial.prototype=Object.create(WrapperObject.prototype);BimTilesMaterial.prototype.constructor=BimTilesMaterial;BimTilesMaterial.prototype.__class__=BimTilesMaterial;BimTilesMaterial.__cache__={};Module["BimTilesMaterial"]=BimTilesMaterial;BimTilesMaterial.prototype["get_tile_id"]=BimTilesMaterial.prototype.get_tile_id=function(){var self=this.ptr;return _emscripten_bind_BimTilesMaterial_get_tile_id_0(self)};BimTilesMaterial.prototype["get_block_id"]=BimTilesMaterial.prototype.get_block_id=function(){var self=this.ptr;return _emscripten_bind_BimTilesMaterial_get_block_id_0(self)};BimTilesMaterial.prototype["get_material_count"]=BimTilesMaterial.prototype.get_material_count=function(){var self=this.ptr;return _emscripten_bind_BimTilesMaterial_get_material_count_0(self)};BimTilesMaterial.prototype["get_texture_count"]=BimTilesMaterial.prototype.get_texture_count=function(){var self=this.ptr;return _emscripten_bind_BimTilesMaterial_get_texture_count_0(self)};BimTilesMaterial.prototype["get_image_count"]=BimTilesMaterial.prototype.get_image_count=function(){var self=this.ptr;return _emscripten_bind_BimTilesMaterial_get_image_count_0(self)};BimTilesMaterial.prototype["GetMaterialItemCount"]=BimTilesMaterial.prototype.GetMaterialItemCount=function(){var self=this.ptr;return _emscripten_bind_BimTilesMaterial_GetMaterialItemCount_0(self)};BimTilesMaterial.prototype["GetTextureItemCount"]=BimTilesMaterial.prototype.GetTextureItemCount=function(){var self=this.ptr;return _emscripten_bind_BimTilesMaterial_GetTextureItemCount_0(self)};BimTilesMaterial.prototype["GetImageItemCount"]=BimTilesMaterial.prototype.GetImageItemCount=function(){var self=this.ptr;return _emscripten_bind_BimTilesMaterial_GetImageItemCount_0(self)};BimTilesMaterial.prototype["GetMaterialItem"]=BimTilesMaterial.prototype.GetMaterialItem=function(item_id){var self=this.ptr;if(item_id&&typeof item_id==="object")item_id=item_id.ptr;return wrapPointer(_emscripten_bind_BimTilesMaterial_GetMaterialItem_1(self,item_id),MaterialItem)};BimTilesMaterial.prototype["GetTextureItem"]=BimTilesMaterial.prototype.GetTextureItem=function(item_id){var self=this.ptr;if(item_id&&typeof item_id==="object")item_id=item_id.ptr;return wrapPointer(_emscripten_bind_BimTilesMaterial_GetTextureItem_1(self,item_id),TextureItem)};BimTilesMaterial.prototype["GetImageItem"]=BimTilesMaterial.prototype.GetImageItem=function(item_id){var self=this.ptr;if(item_id&&typeof item_id==="object")item_id=item_id.ptr;return wrapPointer(_emscripten_bind_BimTilesMaterial_GetImageItem_1(self,item_id),ImageItem)};BimTilesMaterial.prototype["GetTextureIdsInt32Array"]=BimTilesMaterial.prototype.GetTextureIdsInt32Array=function(item,out_size,out_value){var self=this.ptr;if(item&&typeof item==="object")item=item.ptr;if(out_size&&typeof out_size==="object")out_size=out_size.ptr;if(out_value&&typeof out_value==="object")out_value=out_value.ptr;return!!_emscripten_bind_BimTilesMaterial_GetTextureIdsInt32Array_3(self,item,out_size,out_value)};BimTilesMaterial.prototype["GetLodImageIdsInt32Array"]=BimTilesMaterial.prototype.GetLodImageIdsInt32Array=function(item,out_size,out_value){var self=this.ptr;if(item&&typeof item==="object")item=item.ptr;if(out_size&&typeof out_size==="object")out_size=out_size.ptr;if(out_value&&typeof out_value==="object")out_value=out_value.ptr;return!!_emscripten_bind_BimTilesMaterial_GetLodImageIdsInt32Array_3(self,item,out_size,out_value)};BimTilesMaterial.prototype["GetKeyForCustomDataWithInt32Values"]=BimTilesMaterial.prototype.GetKeyForCustomDataWithInt32Values=function(m,key_id){var self=this.ptr;if(m&&typeof m==="object")m=m.ptr;if(key_id&&typeof key_id==="object")key_id=key_id.ptr;return UTF8ToString(_emscripten_bind_BimTilesMaterial_GetKeyForCustomDataWithInt32Values_2(self,m,key_id))};BimTilesMaterial.prototype["GetValueInt32ArrayForCustomData"]=BimTilesMaterial.prototype.GetValueInt32ArrayForCustomData=function(m,out_size,out_value){var self=this.ptr;if(m&&typeof m==="object")m=m.ptr;if(out_size&&typeof out_size==="object")out_size=out_size.ptr;if(out_value&&typeof out_value==="object")out_value=out_value.ptr;return!!_emscripten_bind_BimTilesMaterial_GetValueInt32ArrayForCustomData_3(self,m,out_size,out_value)};BimTilesMaterial.prototype["GetKeyForCustomDataWithFloatValues"]=BimTilesMaterial.prototype.GetKeyForCustomDataWithFloatValues=function(m,key_id){var self=this.ptr;if(m&&typeof m==="object")m=m.ptr;if(key_id&&typeof key_id==="object")key_id=key_id.ptr;return UTF8ToString(_emscripten_bind_BimTilesMaterial_GetKeyForCustomDataWithFloatValues_2(self,m,key_id))};BimTilesMaterial.prototype["GetValueFloatArrayForCustomData"]=BimTilesMaterial.prototype.GetValueFloatArrayForCustomData=function(m,out_size,out_value){var self=this.ptr;if(m&&typeof m==="object")m=m.ptr;if(out_size&&typeof out_size==="object")out_size=out_size.ptr;if(out_value&&typeof out_value==="object")out_value=out_value.ptr;return!!_emscripten_bind_BimTilesMaterial_GetValueFloatArrayForCustomData_3(self,m,out_size,out_value)};BimTilesMaterial.prototype["GetKeyForCustomDataWithStringValues"]=BimTilesMaterial.prototype.GetKeyForCustomDataWithStringValues=function(m,key_id){var self=this.ptr;if(m&&typeof m==="object")m=m.ptr;if(key_id&&typeof key_id==="object")key_id=key_id.ptr;return UTF8ToString(_emscripten_bind_BimTilesMaterial_GetKeyForCustomDataWithStringValues_2(self,m,key_id))};BimTilesMaterial.prototype["GetValueForCustomDataWithStringValues"]=BimTilesMaterial.prototype.GetValueForCustomDataWithStringValues=function(m,value_id){var self=this.ptr;if(m&&typeof m==="object")m=m.ptr;if(value_id&&typeof value_id==="object")value_id=value_id.ptr;return UTF8ToString(_emscripten_bind_BimTilesMaterial_GetValueForCustomDataWithStringValues_2(self,m,value_id))};BimTilesMaterial.prototype["__destroy__"]=BimTilesMaterial.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_BimTilesMaterial___destroy___0(self)};function UserIdInfo(){this.ptr=_emscripten_bind_UserIdInfo_UserIdInfo_0();getCache(UserIdInfo)[this.ptr]=this}UserIdInfo.prototype=Object.create(WrapperObject.prototype);UserIdInfo.prototype.constructor=UserIdInfo;UserIdInfo.prototype.__class__=UserIdInfo;UserIdInfo.__cache__={};Module["UserIdInfo"]=UserIdInfo;UserIdInfo.prototype["get_tile_id"]=UserIdInfo.prototype.get_tile_id=function(){var self=this.ptr;return _emscripten_bind_UserIdInfo_get_tile_id_0(self)};UserIdInfo.prototype["get_block_id"]=UserIdInfo.prototype.get_block_id=function(){var self=this.ptr;return _emscripten_bind_UserIdInfo_get_block_id_0(self)};UserIdInfo.prototype["get_node_id"]=UserIdInfo.prototype.get_node_id=function(){var self=this.ptr;return _emscripten_bind_UserIdInfo_get_node_id_0(self)};UserIdInfo.prototype["get_user_id"]=UserIdInfo.prototype.get_user_id=function(){var self=this.ptr;return _emscripten_bind_UserIdInfo_get_user_id_0(self)};UserIdInfo.prototype["get_userdata_id"]=UserIdInfo.prototype.get_userdata_id=function(){var self=this.ptr;return _emscripten_bind_UserIdInfo_get_userdata_id_0(self)};UserIdInfo.prototype["num_bbox_components"]=UserIdInfo.prototype.num_bbox_components=function(){var self=this.ptr;return _emscripten_bind_UserIdInfo_num_bbox_components_0(self)};UserIdInfo.prototype["bytesize_bbox_per_component"]=UserIdInfo.prototype.bytesize_bbox_per_component=function(){var self=this.ptr;return _emscripten_bind_UserIdInfo_bytesize_bbox_per_component_0(self)};UserIdInfo.prototype["__destroy__"]=UserIdInfo.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_UserIdInfo___destroy___0(self)};function BimTilesUserId(){this.ptr=_emscripten_bind_BimTilesUserId_BimTilesUserId_0();getCache(BimTilesUserId)[this.ptr]=this}BimTilesUserId.prototype=Object.create(WrapperObject.prototype);BimTilesUserId.prototype.constructor=BimTilesUserId;BimTilesUserId.prototype.__class__=BimTilesUserId;BimTilesUserId.__cache__={};Module["BimTilesUserId"]=BimTilesUserId;BimTilesUserId.prototype["get_tile_id"]=BimTilesUserId.prototype.get_tile_id=function(){var self=this.ptr;return _emscripten_bind_BimTilesUserId_get_tile_id_0(self)};BimTilesUserId.prototype["get_block_id"]=BimTilesUserId.prototype.get_block_id=function(){var self=this.ptr;return _emscripten_bind_BimTilesUserId_get_block_id_0(self)};BimTilesUserId.prototype["get_user_id_min"]=BimTilesUserId.prototype.get_user_id_min=function(){var self=this.ptr;return _emscripten_bind_BimTilesUserId_get_user_id_min_0(self)};BimTilesUserId.prototype["get_user_id_max"]=BimTilesUserId.prototype.get_user_id_max=function(){var self=this.ptr;return _emscripten_bind_BimTilesUserId_get_user_id_max_0(self)};BimTilesUserId.prototype["num_user_id_infos"]=BimTilesUserId.prototype.num_user_id_infos=function(){var self=this.ptr;return _emscripten_bind_BimTilesUserId_num_user_id_infos_0(self)};BimTilesUserId.prototype["num_user_ids"]=BimTilesUserId.prototype.num_user_ids=function(){var self=this.ptr;return _emscripten_bind_BimTilesUserId_num_user_ids_0(self)};BimTilesUserId.prototype["get_first_user_id"]=BimTilesUserId.prototype.get_first_user_id=function(){var self=this.ptr;return UTF8ToString(_emscripten_bind_BimTilesUserId_get_first_user_id_0(self))};BimTilesUserId.prototype["get_last_user_id"]=BimTilesUserId.prototype.get_last_user_id=function(){var self=this.ptr;return UTF8ToString(_emscripten_bind_BimTilesUserId_get_last_user_id_0(self))};BimTilesUserId.prototype["GetUserIdInfo"]=BimTilesUserId.prototype.GetUserIdInfo=function(user_id){var self=this.ptr;if(user_id&&typeof user_id==="object")user_id=user_id.ptr;return wrapPointer(_emscripten_bind_BimTilesUserId_GetUserIdInfo_1(self,user_id),UserIdInfo)};BimTilesUserId.prototype["GetUserIdStr"]=BimTilesUserId.prototype.GetUserIdStr=function(index){var self=this.ptr;if(index&&typeof index==="object")index=index.ptr;return UTF8ToString(_emscripten_bind_BimTilesUserId_GetUserIdStr_1(self,index))};BimTilesUserId.prototype["GetBBoxFloat32Array"]=BimTilesUserId.prototype.GetBBoxFloat32Array=function(m,out_size,out_value){var self=this.ptr;if(m&&typeof m==="object")m=m.ptr;if(out_size&&typeof out_size==="object")out_size=out_size.ptr;if(out_value&&typeof out_value==="object")out_value=out_value.ptr;return!!_emscripten_bind_BimTilesUserId_GetBBoxFloat32Array_3(self,m,out_size,out_value)};BimTilesUserId.prototype["__destroy__"]=BimTilesUserId.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_BimTilesUserId___destroy___0(self)};function UserDataItem(){this.ptr=_emscripten_bind_UserDataItem_UserDataItem_0();getCache(UserDataItem)[this.ptr]=this}UserDataItem.prototype=Object.create(WrapperObject.prototype);UserDataItem.prototype.constructor=UserDataItem;UserDataItem.prototype.__class__=UserDataItem;UserDataItem.__cache__={};Module["UserDataItem"]=UserDataItem;UserDataItem.prototype["get_item_name"]=UserDataItem.prototype.get_item_name=function(){var self=this.ptr;return UTF8ToString(_emscripten_bind_UserDataItem_get_item_name_0(self))};UserDataItem.prototype["num_components"]=UserDataItem.prototype.num_components=function(){var self=this.ptr;return _emscripten_bind_UserDataItem_num_components_0(self)};UserDataItem.prototype["num_entries_int32"]=UserDataItem.prototype.num_entries_int32=function(){var self=this.ptr;return _emscripten_bind_UserDataItem_num_entries_int32_0(self)};UserDataItem.prototype["num_entries_float"]=UserDataItem.prototype.num_entries_float=function(){var self=this.ptr;return _emscripten_bind_UserDataItem_num_entries_float_0(self)};UserDataItem.prototype["num_entries_string"]=UserDataItem.prototype.num_entries_string=function(){var self=this.ptr;return _emscripten_bind_UserDataItem_num_entries_string_0(self)};UserDataItem.prototype["byte_length_per_component"]=UserDataItem.prototype.byte_length_per_component=function(){var self=this.ptr;return _emscripten_bind_UserDataItem_byte_length_per_component_0(self)};UserDataItem.prototype["byte_length_per_component_float"]=UserDataItem.prototype.byte_length_per_component_float=function(){var self=this.ptr;return _emscripten_bind_UserDataItem_byte_length_per_component_float_0(self)};UserDataItem.prototype["__destroy__"]=UserDataItem.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_UserDataItem___destroy___0(self)};function BimTilesUserData(){this.ptr=_emscripten_bind_BimTilesUserData_BimTilesUserData_0();getCache(BimTilesUserData)[this.ptr]=this}BimTilesUserData.prototype=Object.create(WrapperObject.prototype);BimTilesUserData.prototype.constructor=BimTilesUserData;BimTilesUserData.prototype.__class__=BimTilesUserData;BimTilesUserData.__cache__={};Module["BimTilesUserData"]=BimTilesUserData;BimTilesUserData.prototype["GetTileId"]=BimTilesUserData.prototype.GetTileId=function(){var self=this.ptr;return _emscripten_bind_BimTilesUserData_GetTileId_0(self)};BimTilesUserData.prototype["GetBlockId"]=BimTilesUserData.prototype.GetBlockId=function(){var self=this.ptr;return _emscripten_bind_BimTilesUserData_GetBlockId_0(self)};BimTilesUserData.prototype["GetUserdataCount"]=BimTilesUserData.prototype.GetUserdataCount=function(){var self=this.ptr;return _emscripten_bind_BimTilesUserData_GetUserdataCount_0(self)};BimTilesUserData.prototype["GetKeysCount"]=BimTilesUserData.prototype.GetKeysCount=function(){var self=this.ptr;return _emscripten_bind_BimTilesUserData_GetKeysCount_0(self)};BimTilesUserData.prototype["GetValuesCount"]=BimTilesUserData.prototype.GetValuesCount=function(){var self=this.ptr;return _emscripten_bind_BimTilesUserData_GetValuesCount_0(self)};BimTilesUserData.prototype["GetStringKey"]=BimTilesUserData.prototype.GetStringKey=function(key_id){var self=this.ptr;if(key_id&&typeof key_id==="object")key_id=key_id.ptr;return UTF8ToString(_emscripten_bind_BimTilesUserData_GetStringKey_1(self,key_id))};BimTilesUserData.prototype["GetStringValue"]=BimTilesUserData.prototype.GetStringValue=function(value_id){var self=this.ptr;if(value_id&&typeof value_id==="object")value_id=value_id.ptr;return UTF8ToString(_emscripten_bind_BimTilesUserData_GetStringValue_1(self,value_id))};BimTilesUserData.prototype["GetUserDataItem"]=BimTilesUserData.prototype.GetUserDataItem=function(item_id){var self=this.ptr;if(item_id&&typeof item_id==="object")item_id=item_id.ptr;return wrapPointer(_emscripten_bind_BimTilesUserData_GetUserDataItem_1(self,item_id),UserDataItem)};BimTilesUserData.prototype["GetUserDataItemCount"]=BimTilesUserData.prototype.GetUserDataItemCount=function(){var self=this.ptr;return _emscripten_bind_BimTilesUserData_GetUserDataItemCount_0(self)};BimTilesUserData.prototype["GetKeyIndexInt32Array"]=BimTilesUserData.prototype.GetKeyIndexInt32Array=function(item,out_size,out_value){var self=this.ptr;if(item&&typeof item==="object")item=item.ptr;if(out_size&&typeof out_size==="object")out_size=out_size.ptr;if(out_value&&typeof out_value==="object")out_value=out_value.ptr;return!!_emscripten_bind_BimTilesUserData_GetKeyIndexInt32Array_3(self,item,out_size,out_value)};BimTilesUserData.prototype["GetValueIndexInt32Array"]=BimTilesUserData.prototype.GetValueIndexInt32Array=function(item,out_size,out_value){var self=this.ptr;if(item&&typeof item==="object")item=item.ptr;if(out_size&&typeof out_size==="object")out_size=out_size.ptr;if(out_value&&typeof out_value==="object")out_value=out_value.ptr;return!!_emscripten_bind_BimTilesUserData_GetValueIndexInt32Array_3(self,item,out_size,out_value)};BimTilesUserData.prototype["GetKeyIndexFloatArray"]=BimTilesUserData.prototype.GetKeyIndexFloatArray=function(item,out_size,out_value){var self=this.ptr;if(item&&typeof item==="object")item=item.ptr;if(out_size&&typeof out_size==="object")out_size=out_size.ptr;if(out_value&&typeof out_value==="object")out_value=out_value.ptr;return!!_emscripten_bind_BimTilesUserData_GetKeyIndexFloatArray_3(self,item,out_size,out_value)};BimTilesUserData.prototype["GetValueIndexFloatArray"]=BimTilesUserData.prototype.GetValueIndexFloatArray=function(item,out_size,out_value){var self=this.ptr;if(item&&typeof item==="object")item=item.ptr;if(out_size&&typeof out_size==="object")out_size=out_size.ptr;if(out_value&&typeof out_value==="object")out_value=out_value.ptr;return!!_emscripten_bind_BimTilesUserData_GetValueIndexFloatArray_3(self,item,out_size,out_value)};BimTilesUserData.prototype["GetKeyIndexStringArray"]=BimTilesUserData.prototype.GetKeyIndexStringArray=function(item,out_size,out_value){var self=this.ptr;if(item&&typeof item==="object")item=item.ptr;if(out_size&&typeof out_size==="object")out_size=out_size.ptr;if(out_value&&typeof out_value==="object")out_value=out_value.ptr;return!!_emscripten_bind_BimTilesUserData_GetKeyIndexStringArray_3(self,item,out_size,out_value)};BimTilesUserData.prototype["GetValueIndexStringArray"]=BimTilesUserData.prototype.GetValueIndexStringArray=function(item,out_size,out_value){var self=this.ptr;if(item&&typeof item==="object")item=item.ptr;if(out_size&&typeof out_size==="object")out_size=out_size.ptr;if(out_value&&typeof out_value==="object")out_value=out_value.ptr;return!!_emscripten_bind_BimTilesUserData_GetValueIndexStringArray_3(self,item,out_size,out_value)};BimTilesUserData.prototype["__destroy__"]=BimTilesUserData.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_BimTilesUserData___destroy___0(self)};function BimTilesTileId(){this.ptr=_emscripten_bind_BimTilesTileId_BimTilesTileId_0();getCache(BimTilesTileId)[this.ptr]=this}BimTilesTileId.prototype=Object.create(WrapperObject.prototype);BimTilesTileId.prototype.constructor=BimTilesTileId;BimTilesTileId.prototype.__class__=BimTilesTileId;BimTilesTileId.__cache__={};Module["BimTilesTileId"]=BimTilesTileId;BimTilesTileId.prototype["get_tile_path"]=BimTilesTileId.prototype.get_tile_path=function(tile_id){var self=this.ptr;if(tile_id&&typeof tile_id==="object")tile_id=tile_id.ptr;return UTF8ToString(_emscripten_bind_BimTilesTileId_get_tile_path_1(self,tile_id))};BimTilesTileId.prototype["num_tile_ids"]=BimTilesTileId.prototype.num_tile_ids=function(){var self=this.ptr;return _emscripten_bind_BimTilesTileId_num_tile_ids_0(self)};BimTilesTileId.prototype["num_tile_paths"]=BimTilesTileId.prototype.num_tile_paths=function(){var self=this.ptr;return _emscripten_bind_BimTilesTileId_num_tile_paths_0(self)};BimTilesTileId.prototype["__destroy__"]=BimTilesTileId.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_BimTilesTileId___destroy___0(self)};function NodeIdItem(){this.ptr=_emscripten_bind_NodeIdItem_NodeIdItem_0();getCache(NodeIdItem)[this.ptr]=this}NodeIdItem.prototype=Object.create(WrapperObject.prototype);NodeIdItem.prototype.constructor=NodeIdItem;NodeIdItem.prototype.__class__=NodeIdItem;NodeIdItem.__cache__={};Module["NodeIdItem"]=NodeIdItem;NodeIdItem.prototype["get_tile_id"]=NodeIdItem.prototype.get_tile_id=function(){var self=this.ptr;return _emscripten_bind_NodeIdItem_get_tile_id_0(self)};NodeIdItem.prototype["get_block_id"]=NodeIdItem.prototype.get_block_id=function(){var self=this.ptr;return _emscripten_bind_NodeIdItem_get_block_id_0(self)};NodeIdItem.prototype["get_node_id"]=NodeIdItem.prototype.get_node_id=function(){var self=this.ptr;return _emscripten_bind_NodeIdItem_get_node_id_0(self)};NodeIdItem.prototype["get_user_id"]=NodeIdItem.prototype.get_user_id=function(){var self=this.ptr;return _emscripten_bind_NodeIdItem_get_user_id_0(self)};NodeIdItem.prototype["get_userdata_id"]=NodeIdItem.prototype.get_userdata_id=function(){var self=this.ptr;return _emscripten_bind_NodeIdItem_get_userdata_id_0(self)};NodeIdItem.prototype["num_components_bbox"]=NodeIdItem.prototype.num_components_bbox=function(){var self=this.ptr;return _emscripten_bind_NodeIdItem_num_components_bbox_0(self)};NodeIdItem.prototype["byte_lenth_per_component_bbox"]=NodeIdItem.prototype.byte_lenth_per_component_bbox=function(){var self=this.ptr;return _emscripten_bind_NodeIdItem_byte_lenth_per_component_bbox_0(self)};NodeIdItem.prototype["__destroy__"]=NodeIdItem.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_NodeIdItem___destroy___0(self)};function TreeNodeItem(){this.ptr=_emscripten_bind_TreeNodeItem_TreeNodeItem_0();getCache(TreeNodeItem)[this.ptr]=this}TreeNodeItem.prototype=Object.create(WrapperObject.prototype);TreeNodeItem.prototype.constructor=TreeNodeItem;TreeNodeItem.prototype.__class__=TreeNodeItem;TreeNodeItem.__cache__={};Module["TreeNodeItem"]=TreeNodeItem;TreeNodeItem.prototype["get_node_id"]=TreeNodeItem.prototype.get_node_id=function(){var self=this.ptr;return _emscripten_bind_TreeNodeItem_get_node_id_0(self)};TreeNodeItem.prototype["num_nodes"]=TreeNodeItem.prototype.num_nodes=function(){var self=this.ptr;return _emscripten_bind_TreeNodeItem_num_nodes_0(self)};TreeNodeItem.prototype["byte_lenth_per_component_node"]=TreeNodeItem.prototype.byte_lenth_per_component_node=function(){var self=this.ptr;return _emscripten_bind_TreeNodeItem_byte_lenth_per_component_node_0(self)};TreeNodeItem.prototype["num_children"]=TreeNodeItem.prototype.num_children=function(){var self=this.ptr;return _emscripten_bind_TreeNodeItem_num_children_0(self)};TreeNodeItem.prototype["byte_lenth_per_component_childnode"]=TreeNodeItem.prototype.byte_lenth_per_component_childnode=function(){var self=this.ptr;return _emscripten_bind_TreeNodeItem_byte_lenth_per_component_childnode_0(self)};TreeNodeItem.prototype["num_components_bbox"]=TreeNodeItem.prototype.num_components_bbox=function(){var self=this.ptr;return _emscripten_bind_TreeNodeItem_num_components_bbox_0(self)};TreeNodeItem.prototype["byte_lenth_per_component_bbox"]=TreeNodeItem.prototype.byte_lenth_per_component_bbox=function(){var self=this.ptr;return _emscripten_bind_TreeNodeItem_byte_lenth_per_component_bbox_0(self)};TreeNodeItem.prototype["__destroy__"]=TreeNodeItem.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_TreeNodeItem___destroy___0(self)};function BimTilesSearchTree(){this.ptr=_emscripten_bind_BimTilesSearchTree_BimTilesSearchTree_0();getCache(BimTilesSearchTree)[this.ptr]=this}BimTilesSearchTree.prototype=Object.create(WrapperObject.prototype);BimTilesSearchTree.prototype.constructor=BimTilesSearchTree;BimTilesSearchTree.prototype.__class__=BimTilesSearchTree;BimTilesSearchTree.__cache__={};Module["BimTilesSearchTree"]=BimTilesSearchTree;BimTilesSearchTree.prototype["GetNodeIdCount"]=BimTilesSearchTree.prototype.GetNodeIdCount=function(){var self=this.ptr;return _emscripten_bind_BimTilesSearchTree_GetNodeIdCount_0(self)};BimTilesSearchTree.prototype["GetTreeNodeCount"]=BimTilesSearchTree.prototype.GetTreeNodeCount=function(){var self=this.ptr;return _emscripten_bind_BimTilesSearchTree_GetTreeNodeCount_0(self)};BimTilesSearchTree.prototype["GetNodeIdItem"]=BimTilesSearchTree.prototype.GetNodeIdItem=function(item_id){var self=this.ptr;if(item_id&&typeof item_id==="object")item_id=item_id.ptr;return wrapPointer(_emscripten_bind_BimTilesSearchTree_GetNodeIdItem_1(self,item_id),NodeIdItem)};BimTilesSearchTree.prototype["GetTreeNodeItem"]=BimTilesSearchTree.prototype.GetTreeNodeItem=function(item_id){var self=this.ptr;if(item_id&&typeof item_id==="object")item_id=item_id.ptr;return wrapPointer(_emscripten_bind_BimTilesSearchTree_GetTreeNodeItem_1(self,item_id),TreeNodeItem)};BimTilesSearchTree.prototype["GetNodeIdBBoxFloat32Array"]=BimTilesSearchTree.prototype.GetNodeIdBBoxFloat32Array=function(item,out_size,out_value){var self=this.ptr;if(item&&typeof item==="object")item=item.ptr;if(out_size&&typeof out_size==="object")out_size=out_size.ptr;if(out_value&&typeof out_value==="object")out_value=out_value.ptr;return!!_emscripten_bind_BimTilesSearchTree_GetNodeIdBBoxFloat32Array_3(self,item,out_size,out_value)};BimTilesSearchTree.prototype["GetTreeNodeBBoxFloat32Array"]=BimTilesSearchTree.prototype.GetTreeNodeBBoxFloat32Array=function(item,out_size,out_value){var self=this.ptr;if(item&&typeof item==="object")item=item.ptr;if(out_size&&typeof out_size==="object")out_size=out_size.ptr;if(out_value&&typeof out_value==="object")out_value=out_value.ptr;return!!_emscripten_bind_BimTilesSearchTree_GetTreeNodeBBoxFloat32Array_3(self,item,out_size,out_value)};BimTilesSearchTree.prototype["GetTreeNodeInt32Array"]=BimTilesSearchTree.prototype.GetTreeNodeInt32Array=function(item,out_size,out_value){var self=this.ptr;if(item&&typeof item==="object")item=item.ptr;if(out_size&&typeof out_size==="object")out_size=out_size.ptr;if(out_value&&typeof out_value==="object")out_value=out_value.ptr;return!!_emscripten_bind_BimTilesSearchTree_GetTreeNodeInt32Array_3(self,item,out_size,out_value)};BimTilesSearchTree.prototype["GetTreeChildNodeInt32Array"]=BimTilesSearchTree.prototype.GetTreeChildNodeInt32Array=function(item,out_size,out_value){var self=this.ptr;if(item&&typeof item==="object")item=item.ptr;if(out_size&&typeof out_size==="object")out_size=out_size.ptr;if(out_value&&typeof out_value==="object")out_value=out_value.ptr;return!!_emscripten_bind_BimTilesSearchTree_GetTreeChildNodeInt32Array_3(self,item,out_size,out_value)};BimTilesSearchTree.prototype["__destroy__"]=BimTilesSearchTree.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_BimTilesSearchTree___destroy___0(self)};function NodeIdItemV2(){this.ptr=_emscripten_bind_NodeIdItemV2_NodeIdItemV2_0();getCache(NodeIdItemV2)[this.ptr]=this}NodeIdItemV2.prototype=Object.create(WrapperObject.prototype);NodeIdItemV2.prototype.constructor=NodeIdItemV2;NodeIdItemV2.prototype.__class__=NodeIdItemV2;NodeIdItemV2.__cache__={};Module["NodeIdItemV2"]=NodeIdItemV2;NodeIdItemV2.prototype["get_tile_id"]=NodeIdItemV2.prototype.get_tile_id=function(){var self=this.ptr;return _emscripten_bind_NodeIdItemV2_get_tile_id_0(self)};NodeIdItemV2.prototype["get_node_id"]=NodeIdItemV2.prototype.get_node_id=function(){var self=this.ptr;return _emscripten_bind_NodeIdItemV2_get_node_id_0(self)};NodeIdItemV2.prototype["get_user_id"]=NodeIdItemV2.prototype.get_user_id=function(){var self=this.ptr;return _emscripten_bind_NodeIdItemV2_get_user_id_0(self)};NodeIdItemV2.prototype["get_bbox_id"]=NodeIdItemV2.prototype.get_bbox_id=function(){var self=this.ptr;return _emscripten_bind_NodeIdItemV2_get_bbox_id_0(self)};NodeIdItemV2.prototype["__destroy__"]=NodeIdItemV2.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_NodeIdItemV2___destroy___0(self)};function BimTilesSearchTreeV2(){this.ptr=_emscripten_bind_BimTilesSearchTreeV2_BimTilesSearchTreeV2_0();getCache(BimTilesSearchTreeV2)[this.ptr]=this}BimTilesSearchTreeV2.prototype=Object.create(WrapperObject.prototype);BimTilesSearchTreeV2.prototype.constructor=BimTilesSearchTreeV2;BimTilesSearchTreeV2.prototype.__class__=BimTilesSearchTreeV2;BimTilesSearchTreeV2.__cache__={};Module["BimTilesSearchTreeV2"]=BimTilesSearchTreeV2;BimTilesSearchTreeV2.prototype["GetTreeNodeCount"]=BimTilesSearchTreeV2.prototype.GetTreeNodeCount=function(){var self=this.ptr;return _emscripten_bind_BimTilesSearchTreeV2_GetTreeNodeCount_0(self)};BimTilesSearchTreeV2.prototype["GetTreeNodeItem"]=BimTilesSearchTreeV2.prototype.GetTreeNodeItem=function(item_id){var self=this.ptr;if(item_id&&typeof item_id==="object")item_id=item_id.ptr;return wrapPointer(_emscripten_bind_BimTilesSearchTreeV2_GetTreeNodeItem_1(self,item_id),TreeNodeItem)};BimTilesSearchTreeV2.prototype["GetTreeNodeBBoxFloat32Array"]=BimTilesSearchTreeV2.prototype.GetTreeNodeBBoxFloat32Array=function(item,out_size,out_value){var self=this.ptr;if(item&&typeof item==="object")item=item.ptr;if(out_size&&typeof out_size==="object")out_size=out_size.ptr;if(out_value&&typeof out_value==="object")out_value=out_value.ptr;return!!_emscripten_bind_BimTilesSearchTreeV2_GetTreeNodeBBoxFloat32Array_3(self,item,out_size,out_value)};BimTilesSearchTreeV2.prototype["GetTreeNodeInt32Array"]=BimTilesSearchTreeV2.prototype.GetTreeNodeInt32Array=function(item,out_size,out_value){var self=this.ptr;if(item&&typeof item==="object")item=item.ptr;if(out_size&&typeof out_size==="object")out_size=out_size.ptr;if(out_value&&typeof out_value==="object")out_value=out_value.ptr;return!!_emscripten_bind_BimTilesSearchTreeV2_GetTreeNodeInt32Array_3(self,item,out_size,out_value)};BimTilesSearchTreeV2.prototype["GetTreeChildNodeInt32Array"]=BimTilesSearchTreeV2.prototype.GetTreeChildNodeInt32Array=function(item,out_size,out_value){var self=this.ptr;if(item&&typeof item==="object")item=item.ptr;if(out_size&&typeof out_size==="object")out_size=out_size.ptr;if(out_value&&typeof out_value==="object")out_value=out_value.ptr;return!!_emscripten_bind_BimTilesSearchTreeV2_GetTreeChildNodeInt32Array_3(self,item,out_size,out_value)};BimTilesSearchTreeV2.prototype["__destroy__"]=BimTilesSearchTreeV2.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_BimTilesSearchTreeV2___destroy___0(self)};function BimTilesUserIdV2(){this.ptr=_emscripten_bind_BimTilesUserIdV2_BimTilesUserIdV2_0();getCache(BimTilesUserIdV2)[this.ptr]=this}BimTilesUserIdV2.prototype=Object.create(WrapperObject.prototype);BimTilesUserIdV2.prototype.constructor=BimTilesUserIdV2;BimTilesUserIdV2.prototype.__class__=BimTilesUserIdV2;BimTilesUserIdV2.__cache__={};Module["BimTilesUserIdV2"]=BimTilesUserIdV2;BimTilesUserIdV2.prototype["get_tile_id"]=BimTilesUserIdV2.prototype.get_tile_id=function(){var self=this.ptr;return _emscripten_bind_BimTilesUserIdV2_get_tile_id_0(self)};BimTilesUserIdV2.prototype["get_block_id"]=BimTilesUserIdV2.prototype.get_block_id=function(){var self=this.ptr;return _emscripten_bind_BimTilesUserIdV2_get_block_id_0(self)};BimTilesUserIdV2.prototype["get_user_id_min"]=BimTilesUserIdV2.prototype.get_user_id_min=function(){var self=this.ptr;return _emscripten_bind_BimTilesUserIdV2_get_user_id_min_0(self)};BimTilesUserIdV2.prototype["get_user_id_max"]=BimTilesUserIdV2.prototype.get_user_id_max=function(){var self=this.ptr;return _emscripten_bind_BimTilesUserIdV2_get_user_id_max_0(self)};BimTilesUserIdV2.prototype["num_user_ids"]=BimTilesUserIdV2.prototype.num_user_ids=function(){var self=this.ptr;return _emscripten_bind_BimTilesUserIdV2_num_user_ids_0(self)};BimTilesUserIdV2.prototype["num_userdata_ids"]=BimTilesUserIdV2.prototype.num_userdata_ids=function(){var self=this.ptr;return _emscripten_bind_BimTilesUserIdV2_num_userdata_ids_0(self)};BimTilesUserIdV2.prototype["num_nodelist_ids"]=BimTilesUserIdV2.prototype.num_nodelist_ids=function(){var self=this.ptr;return _emscripten_bind_BimTilesUserIdV2_num_nodelist_ids_0(self)};BimTilesUserIdV2.prototype["get_first_user_id"]=BimTilesUserIdV2.prototype.get_first_user_id=function(){var self=this.ptr;return UTF8ToString(_emscripten_bind_BimTilesUserIdV2_get_first_user_id_0(self))};BimTilesUserIdV2.prototype["get_last_user_id"]=BimTilesUserIdV2.prototype.get_last_user_id=function(){var self=this.ptr;return UTF8ToString(_emscripten_bind_BimTilesUserIdV2_get_last_user_id_0(self))};BimTilesUserIdV2.prototype["GetUserIdStr"]=BimTilesUserIdV2.prototype.GetUserIdStr=function(index){var self=this.ptr;if(index&&typeof index==="object")index=index.ptr;return UTF8ToString(_emscripten_bind_BimTilesUserIdV2_GetUserIdStr_1(self,index))};BimTilesUserIdV2.prototype["GetUserDataIdsInt32Array"]=BimTilesUserIdV2.prototype.GetUserDataIdsInt32Array=function(out_size,out_value){var self=this.ptr;if(out_size&&typeof out_size==="object")out_size=out_size.ptr;if(out_value&&typeof out_value==="object")out_value=out_value.ptr;return!!_emscripten_bind_BimTilesUserIdV2_GetUserDataIdsInt32Array_2(self,out_size,out_value)};BimTilesUserIdV2.prototype["GetNodeListIdsInt32Array"]=BimTilesUserIdV2.prototype.GetNodeListIdsInt32Array=function(out_size,out_value){var self=this.ptr;if(out_size&&typeof out_size==="object")out_size=out_size.ptr;if(out_value&&typeof out_value==="object")out_value=out_value.ptr;return!!_emscripten_bind_BimTilesUserIdV2_GetNodeListIdsInt32Array_2(self,out_size,out_value)};BimTilesUserIdV2.prototype["__destroy__"]=BimTilesUserIdV2.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_BimTilesUserIdV2___destroy___0(self)};function ViewNodeItem(){this.ptr=_emscripten_bind_ViewNodeItem_ViewNodeItem_0();getCache(ViewNodeItem)[this.ptr]=this}ViewNodeItem.prototype=Object.create(WrapperObject.prototype);ViewNodeItem.prototype.constructor=ViewNodeItem;ViewNodeItem.prototype.__class__=ViewNodeItem;ViewNodeItem.__cache__={};Module["ViewNodeItem"]=ViewNodeItem;ViewNodeItem.prototype["get_node_id"]=ViewNodeItem.prototype.get_node_id=function(){var self=this.ptr;return _emscripten_bind_ViewNodeItem_get_node_id_0(self)};ViewNodeItem.prototype["get_node_parent_id"]=ViewNodeItem.prototype.get_node_parent_id=function(){var self=this.ptr;return _emscripten_bind_ViewNodeItem_get_node_parent_id_0(self)};ViewNodeItem.prototype["num_tile_ids"]=ViewNodeItem.prototype.num_tile_ids=function(){var self=this.ptr;return _emscripten_bind_ViewNodeItem_num_tile_ids_0(self)};ViewNodeItem.prototype["byte_lenth_per_tile_ids"]=ViewNodeItem.prototype.byte_lenth_per_tile_ids=function(){var self=this.ptr;return _emscripten_bind_ViewNodeItem_byte_lenth_per_tile_ids_0(self)};ViewNodeItem.prototype["num_components_bbox"]=ViewNodeItem.prototype.num_components_bbox=function(){var self=this.ptr;return _emscripten_bind_ViewNodeItem_num_components_bbox_0(self)};ViewNodeItem.prototype["byte_lenth_per_component_bbox"]=ViewNodeItem.prototype.byte_lenth_per_component_bbox=function(){var self=this.ptr;return _emscripten_bind_ViewNodeItem_byte_lenth_per_component_bbox_0(self)};ViewNodeItem.prototype["__destroy__"]=ViewNodeItem.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_ViewNodeItem___destroy___0(self)};function BimTilesViewTree(){this.ptr=_emscripten_bind_BimTilesViewTree_BimTilesViewTree_0();getCache(BimTilesViewTree)[this.ptr]=this}BimTilesViewTree.prototype=Object.create(WrapperObject.prototype);BimTilesViewTree.prototype.constructor=BimTilesViewTree;BimTilesViewTree.prototype.__class__=BimTilesViewTree;BimTilesViewTree.__cache__={};Module["BimTilesViewTree"]=BimTilesViewTree;BimTilesViewTree.prototype["get_tree_type"]=BimTilesViewTree.prototype.get_tree_type=function(){var self=this.ptr;return _emscripten_bind_BimTilesViewTree_get_tree_type_0(self)};BimTilesViewTree.prototype["num_geometry_errors"]=BimTilesViewTree.prototype.num_geometry_errors=function(){var self=this.ptr;return _emscripten_bind_BimTilesViewTree_num_geometry_errors_0(self)};BimTilesViewTree.prototype["GetViewNodeCount"]=BimTilesViewTree.prototype.GetViewNodeCount=function(){var self=this.ptr;return _emscripten_bind_BimTilesViewTree_GetViewNodeCount_0(self)};BimTilesViewTree.prototype["GetViewNodeItem"]=BimTilesViewTree.prototype.GetViewNodeItem=function(item_id){var self=this.ptr;if(item_id&&typeof item_id==="object")item_id=item_id.ptr;return wrapPointer(_emscripten_bind_BimTilesViewTree_GetViewNodeItem_1(self,item_id),ViewNodeItem)};BimTilesViewTree.prototype["GetGeometryErrorsFloat32Array"]=BimTilesViewTree.prototype.GetGeometryErrorsFloat32Array=function(out_size,out_value){var self=this.ptr;if(out_size&&typeof out_size==="object")out_size=out_size.ptr;if(out_value&&typeof out_value==="object")out_value=out_value.ptr;return!!_emscripten_bind_BimTilesViewTree_GetGeometryErrorsFloat32Array_2(self,out_size,out_value)};BimTilesViewTree.prototype["GetViewNodeBBoxFloat32Array"]=BimTilesViewTree.prototype.GetViewNodeBBoxFloat32Array=function(item,out_size,out_value){var self=this.ptr;if(item&&typeof item==="object")item=item.ptr;if(out_size&&typeof out_size==="object")out_size=out_size.ptr;if(out_value&&typeof out_value==="object")out_value=out_value.ptr;return!!_emscripten_bind_BimTilesViewTree_GetViewNodeBBoxFloat32Array_3(self,item,out_size,out_value)};BimTilesViewTree.prototype["GetViewNodeTileIdsInt32Array"]=BimTilesViewTree.prototype.GetViewNodeTileIdsInt32Array=function(item,out_size,out_value){var self=this.ptr;if(item&&typeof item==="object")item=item.ptr;if(out_size&&typeof out_size==="object")out_size=out_size.ptr;if(out_value&&typeof out_value==="object")out_value=out_value.ptr;return!!_emscripten_bind_BimTilesViewTree_GetViewNodeTileIdsInt32Array_3(self,item,out_size,out_value)};BimTilesViewTree.prototype["__destroy__"]=BimTilesViewTree.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_BimTilesViewTree___destroy___0(self)};function BimTilesNodeId(){this.ptr=_emscripten_bind_BimTilesNodeId_BimTilesNodeId_0();getCache(BimTilesNodeId)[this.ptr]=this}BimTilesNodeId.prototype=Object.create(WrapperObject.prototype);BimTilesNodeId.prototype.constructor=BimTilesNodeId;BimTilesNodeId.prototype.__class__=BimTilesNodeId;BimTilesNodeId.__cache__={};Module["BimTilesNodeId"]=BimTilesNodeId;BimTilesNodeId.prototype["GetNodeIdItemCount"]=BimTilesNodeId.prototype.GetNodeIdItemCount=function(){var self=this.ptr;return _emscripten_bind_BimTilesNodeId_GetNodeIdItemCount_0(self)};BimTilesNodeId.prototype["GetNodeIdItem"]=BimTilesNodeId.prototype.GetNodeIdItem=function(item_id){var self=this.ptr;if(item_id&&typeof item_id==="object")item_id=item_id.ptr;return wrapPointer(_emscripten_bind_BimTilesNodeId_GetNodeIdItem_1(self,item_id),NodeIdItemV2)};BimTilesNodeId.prototype["__destroy__"]=BimTilesNodeId.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_BimTilesNodeId___destroy___0(self)};function NodeBBoxItem(){this.ptr=_emscripten_bind_NodeBBoxItem_NodeBBoxItem_0();getCache(NodeBBoxItem)[this.ptr]=this}NodeBBoxItem.prototype=Object.create(WrapperObject.prototype);NodeBBoxItem.prototype.constructor=NodeBBoxItem;NodeBBoxItem.prototype.__class__=NodeBBoxItem;NodeBBoxItem.__cache__={};Module["NodeBBoxItem"]=NodeBBoxItem;NodeBBoxItem.prototype["num_components_bbox"]=NodeBBoxItem.prototype.num_components_bbox=function(){var self=this.ptr;return _emscripten_bind_NodeBBoxItem_num_components_bbox_0(self)};NodeBBoxItem.prototype["byte_lenth_per_component_bbox"]=NodeBBoxItem.prototype.byte_lenth_per_component_bbox=function(){var self=this.ptr;return _emscripten_bind_NodeBBoxItem_byte_lenth_per_component_bbox_0(self)};NodeBBoxItem.prototype["__destroy__"]=NodeBBoxItem.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_NodeBBoxItem___destroy___0(self)};function BimTilesNodeBox(){this.ptr=_emscripten_bind_BimTilesNodeBox_BimTilesNodeBox_0();getCache(BimTilesNodeBox)[this.ptr]=this}BimTilesNodeBox.prototype=Object.create(WrapperObject.prototype);BimTilesNodeBox.prototype.constructor=BimTilesNodeBox;BimTilesNodeBox.prototype.__class__=BimTilesNodeBox;BimTilesNodeBox.__cache__={};Module["BimTilesNodeBox"]=BimTilesNodeBox;BimTilesNodeBox.prototype["GetNodeBBoxItemCount"]=BimTilesNodeBox.prototype.GetNodeBBoxItemCount=function(){var self=this.ptr;return _emscripten_bind_BimTilesNodeBox_GetNodeBBoxItemCount_0(self)};BimTilesNodeBox.prototype["GetNodeBBoxItem"]=BimTilesNodeBox.prototype.GetNodeBBoxItem=function(item_id){var self=this.ptr;if(item_id&&typeof item_id==="object")item_id=item_id.ptr;return wrapPointer(_emscripten_bind_BimTilesNodeBox_GetNodeBBoxItem_1(self,item_id),NodeBBoxItem)};BimTilesNodeBox.prototype["GetBBoxFloat32Array"]=BimTilesNodeBox.prototype.GetBBoxFloat32Array=function(item,out_size,out_value){var self=this.ptr;if(item&&typeof item==="object")item=item.ptr;if(out_size&&typeof out_size==="object")out_size=out_size.ptr;if(out_value&&typeof out_value==="object")out_value=out_value.ptr;return!!_emscripten_bind_BimTilesNodeBox_GetBBoxFloat32Array_3(self,item,out_size,out_value)};BimTilesNodeBox.prototype["__destroy__"]=BimTilesNodeBox.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_BimTilesNodeBox___destroy___0(self)};function BimTilesUserIdV3(){this.ptr=_emscripten_bind_BimTilesUserIdV3_BimTilesUserIdV3_0();getCache(BimTilesUserIdV3)[this.ptr]=this}BimTilesUserIdV3.prototype=Object.create(WrapperObject.prototype);BimTilesUserIdV3.prototype.constructor=BimTilesUserIdV3;BimTilesUserIdV3.prototype.__class__=BimTilesUserIdV3;BimTilesUserIdV3.__cache__={};Module["BimTilesUserIdV3"]=BimTilesUserIdV3;BimTilesUserIdV3.prototype["get_user_id_min"]=BimTilesUserIdV3.prototype.get_user_id_min=function(){var self=this.ptr;return _emscripten_bind_BimTilesUserIdV3_get_user_id_min_0(self)};BimTilesUserIdV3.prototype["get_user_id_max"]=BimTilesUserIdV3.prototype.get_user_id_max=function(){var self=this.ptr;return _emscripten_bind_BimTilesUserIdV3_get_user_id_max_0(self)};BimTilesUserIdV3.prototype["num_user_ids"]=BimTilesUserIdV3.prototype.num_user_ids=function(){var self=this.ptr;return _emscripten_bind_BimTilesUserIdV3_num_user_ids_0(self)};BimTilesUserIdV3.prototype["num_userdata_indices"]=BimTilesUserIdV3.prototype.num_userdata_indices=function(){var self=this.ptr;return _emscripten_bind_BimTilesUserIdV3_num_userdata_indices_0(self)};BimTilesUserIdV3.prototype["GetUserIdStr"]=BimTilesUserIdV3.prototype.GetUserIdStr=function(index){var self=this.ptr;if(index&&typeof index==="object")index=index.ptr;return UTF8ToString(_emscripten_bind_BimTilesUserIdV3_GetUserIdStr_1(self,index))};BimTilesUserIdV3.prototype["GetInt32ArrayForUserDataIndices"]=BimTilesUserIdV3.prototype.GetInt32ArrayForUserDataIndices=function(out_size,out_value){var self=this.ptr;if(out_size&&typeof out_size==="object")out_size=out_size.ptr;if(out_value&&typeof out_value==="object")out_value=out_value.ptr;return!!_emscripten_bind_BimTilesUserIdV3_GetInt32ArrayForUserDataIndices_2(self,out_size,out_value)};BimTilesUserIdV3.prototype["__destroy__"]=BimTilesUserIdV3.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_BimTilesUserIdV3___destroy___0(self)};function BimTilesUserIdBoxV3(){this.ptr=_emscripten_bind_BimTilesUserIdBoxV3_BimTilesUserIdBoxV3_0();getCache(BimTilesUserIdBoxV3)[this.ptr]=this}BimTilesUserIdBoxV3.prototype=Object.create(WrapperObject.prototype);BimTilesUserIdBoxV3.prototype.constructor=BimTilesUserIdBoxV3;BimTilesUserIdBoxV3.prototype.__class__=BimTilesUserIdBoxV3;BimTilesUserIdBoxV3.__cache__={};Module["BimTilesUserIdBoxV3"]=BimTilesUserIdBoxV3;BimTilesUserIdBoxV3.prototype["num_node_boxes"]=BimTilesUserIdBoxV3.prototype.num_node_boxes=function(){var self=this.ptr;return _emscripten_bind_BimTilesUserIdBoxV3_num_node_boxes_0(self)};BimTilesUserIdBoxV3.prototype["GetNodeBBoxItem"]=BimTilesUserIdBoxV3.prototype.GetNodeBBoxItem=function(item_id){var self=this.ptr;if(item_id&&typeof item_id==="object")item_id=item_id.ptr;return wrapPointer(_emscripten_bind_BimTilesUserIdBoxV3_GetNodeBBoxItem_1(self,item_id),NodeBBoxItem)};BimTilesUserIdBoxV3.prototype["GetBBoxFloat32Array"]=BimTilesUserIdBoxV3.prototype.GetBBoxFloat32Array=function(item,out_size,out_value){var self=this.ptr;if(item&&typeof item==="object")item=item.ptr;if(out_size&&typeof out_size==="object")out_size=out_size.ptr;if(out_value&&typeof out_value==="object")out_value=out_value.ptr;return!!_emscripten_bind_BimTilesUserIdBoxV3_GetBBoxFloat32Array_3(self,item,out_size,out_value)};BimTilesUserIdBoxV3.prototype["__destroy__"]=BimTilesUserIdBoxV3.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_BimTilesUserIdBoxV3___destroy___0(self)};function MeshNodeV3(){this.ptr=_emscripten_bind_MeshNodeV3_MeshNodeV3_0();getCache(MeshNodeV3)[this.ptr]=this}MeshNodeV3.prototype=Object.create(WrapperObject.prototype);MeshNodeV3.prototype.constructor=MeshNodeV3;MeshNodeV3.prototype.__class__=MeshNodeV3;MeshNodeV3.__cache__={};Module["MeshNodeV3"]=MeshNodeV3;MeshNodeV3.prototype["GetAttribute"]=MeshNodeV3.prototype.GetAttribute=function(att_id){var self=this.ptr;if(att_id&&typeof att_id==="object")att_id=att_id.ptr;return wrapPointer(_emscripten_bind_MeshNodeV3_GetAttribute_1(self,att_id),GeometryAttribute)};MeshNodeV3.prototype["num_attributes"]=MeshNodeV3.prototype.num_attributes=function(){var self=this.ptr;return _emscripten_bind_MeshNodeV3_num_attributes_0(self)};MeshNodeV3.prototype["get_node_id"]=MeshNodeV3.prototype.get_node_id=function(){var self=this.ptr;return _emscripten_bind_MeshNodeV3_get_node_id_0(self)};MeshNodeV3.prototype["get_primitive_id"]=MeshNodeV3.prototype.get_primitive_id=function(){var self=this.ptr;return _emscripten_bind_MeshNodeV3_get_primitive_id_0(self)};MeshNodeV3.prototype["get_material_id"]=MeshNodeV3.prototype.get_material_id=function(){var self=this.ptr;return _emscripten_bind_MeshNodeV3_get_material_id_0(self)};MeshNodeV3.prototype["primitive_mode"]=MeshNodeV3.prototype.primitive_mode=function(){var self=this.ptr;return _emscripten_bind_MeshNodeV3_primitive_mode_0(self)};MeshNodeV3.prototype["node_type"]=MeshNodeV3.prototype.node_type=function(){var self=this.ptr;return _emscripten_bind_MeshNodeV3_node_type_0(self)};MeshNodeV3.prototype["num_components_bbox"]=MeshNodeV3.prototype.num_components_bbox=function(){var self=this.ptr;return _emscripten_bind_MeshNodeV3_num_components_bbox_0(self)};MeshNodeV3.prototype["byte_length_per_component_bbox"]=MeshNodeV3.prototype.byte_length_per_component_bbox=function(){var self=this.ptr;return _emscripten_bind_MeshNodeV3_byte_length_per_component_bbox_0(self)};MeshNodeV3.prototype["GetBBoxFloat32Array"]=MeshNodeV3.prototype.GetBBoxFloat32Array=function(out_size,out_values){var self=this.ptr;if(out_size&&typeof out_size==="object")out_size=out_size.ptr;if(out_values&&typeof out_values==="object")out_values=out_values.ptr;return!!_emscripten_bind_MeshNodeV3_GetBBoxFloat32Array_2(self,out_size,out_values)};MeshNodeV3.prototype["num_components_matrix"]=MeshNodeV3.prototype.num_components_matrix=function(){var self=this.ptr;return _emscripten_bind_MeshNodeV3_num_components_matrix_0(self)};MeshNodeV3.prototype["byte_length_per_component_matrix"]=MeshNodeV3.prototype.byte_length_per_component_matrix=function(){var self=this.ptr;return _emscripten_bind_MeshNodeV3_byte_length_per_component_matrix_0(self)};MeshNodeV3.prototype["GetMatrixFloat32Array"]=MeshNodeV3.prototype.GetMatrixFloat32Array=function(out_size,out_values){var self=this.ptr;if(out_size&&typeof out_size==="object")out_size=out_size.ptr;if(out_values&&typeof out_values==="object")out_values=out_values.ptr;return!!_emscripten_bind_MeshNodeV3_GetMatrixFloat32Array_2(self,out_size,out_values)};MeshNodeV3.prototype["num_components_translation"]=MeshNodeV3.prototype.num_components_translation=function(){var self=this.ptr;return _emscripten_bind_MeshNodeV3_num_components_translation_0(self)};MeshNodeV3.prototype["byte_length_per_component_translation"]=MeshNodeV3.prototype.byte_length_per_component_translation=function(){var self=this.ptr;return _emscripten_bind_MeshNodeV3_byte_length_per_component_translation_0(self)};MeshNodeV3.prototype["GetTranslationFloat64Array"]=MeshNodeV3.prototype.GetTranslationFloat64Array=function(out_size,out_values){var self=this.ptr;if(out_size&&typeof out_size==="object")out_size=out_size.ptr;if(out_values&&typeof out_values==="object")out_values=out_values.ptr;return!!_emscripten_bind_MeshNodeV3_GetTranslationFloat64Array_2(self,out_size,out_values)};MeshNodeV3.prototype["__destroy__"]=MeshNodeV3.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_MeshNodeV3___destroy___0(self)};function UseridIndexInfo(){this.ptr=_emscripten_bind_UseridIndexInfo_UseridIndexInfo_0();getCache(UseridIndexInfo)[this.ptr]=this}UseridIndexInfo.prototype=Object.create(WrapperObject.prototype);UseridIndexInfo.prototype.constructor=UseridIndexInfo;UseridIndexInfo.prototype.__class__=UseridIndexInfo;UseridIndexInfo.__cache__={};Module["UseridIndexInfo"]=UseridIndexInfo;UseridIndexInfo.prototype["get_node_id"]=UseridIndexInfo.prototype.get_node_id=function(){var self=this.ptr;return _emscripten_bind_UseridIndexInfo_get_node_id_0(self)};UseridIndexInfo.prototype["get_userid_index"]=UseridIndexInfo.prototype.get_userid_index=function(){var self=this.ptr;return _emscripten_bind_UseridIndexInfo_get_userid_index_0(self)};UseridIndexInfo.prototype["get_index_start"]=UseridIndexInfo.prototype.get_index_start=function(){var self=this.ptr;return _emscripten_bind_UseridIndexInfo_get_index_start_0(self)};UseridIndexInfo.prototype["get_index_end"]=UseridIndexInfo.prototype.get_index_end=function(){var self=this.ptr;return _emscripten_bind_UseridIndexInfo_get_index_end_0(self)};UseridIndexInfo.prototype["get_position_start"]=UseridIndexInfo.prototype.get_position_start=function(){var self=this.ptr;return _emscripten_bind_UseridIndexInfo_get_position_start_0(self)};UseridIndexInfo.prototype["get_position_end"]=UseridIndexInfo.prototype.get_position_end=function(){var self=this.ptr;return _emscripten_bind_UseridIndexInfo_get_position_end_0(self)};UseridIndexInfo.prototype["__destroy__"]=UseridIndexInfo.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_UseridIndexInfo___destroy___0(self)};function BimTilesMeshV3(){this.ptr=_emscripten_bind_BimTilesMeshV3_BimTilesMeshV3_0();getCache(BimTilesMeshV3)[this.ptr]=this}BimTilesMeshV3.prototype=Object.create(WrapperObject.prototype);BimTilesMeshV3.prototype.constructor=BimTilesMeshV3;BimTilesMeshV3.prototype.__class__=BimTilesMeshV3;BimTilesMeshV3.__cache__={};Module["BimTilesMeshV3"]=BimTilesMeshV3;BimTilesMeshV3.prototype["get_tile_id"]=BimTilesMeshV3.prototype.get_tile_id=function(){var self=this.ptr;return _emscripten_bind_BimTilesMeshV3_get_tile_id_0(self)};BimTilesMeshV3.prototype["get_block_id"]=BimTilesMeshV3.prototype.get_block_id=function(){var self=this.ptr;return _emscripten_bind_BimTilesMeshV3_get_block_id_0(self)};BimTilesMeshV3.prototype["get_buffer_block_id"]=BimTilesMeshV3.prototype.get_buffer_block_id=function(){var self=this.ptr;return _emscripten_bind_BimTilesMeshV3_get_buffer_block_id_0(self)};BimTilesMeshV3.prototype["get_material_block_id"]=BimTilesMeshV3.prototype.get_material_block_id=function(){var self=this.ptr;return _emscripten_bind_BimTilesMeshV3_get_material_block_id_0(self)};BimTilesMeshV3.prototype["get_userdata_block_id"]=BimTilesMeshV3.prototype.get_userdata_block_id=function(){var self=this.ptr;return _emscripten_bind_BimTilesMeshV3_get_userdata_block_id_0(self)};BimTilesMeshV3.prototype["GetNode"]=BimTilesMeshV3.prototype.GetNode=function(node_id){var self=this.ptr;if(node_id&&typeof node_id==="object")node_id=node_id.ptr;return wrapPointer(_emscripten_bind_BimTilesMeshV3_GetNode_1(self,node_id),MeshNodeV3)};BimTilesMeshV3.prototype["num_mesh_nodes"]=BimTilesMeshV3.prototype.num_mesh_nodes=function(){var self=this.ptr;return _emscripten_bind_BimTilesMeshV3_num_mesh_nodes_0(self)};BimTilesMeshV3.prototype["GetUseridIndexInfo"]=BimTilesMeshV3.prototype.GetUseridIndexInfo=function(index_info_id){var self=this.ptr;if(index_info_id&&typeof index_info_id==="object")index_info_id=index_info_id.ptr;return wrapPointer(_emscripten_bind_BimTilesMeshV3_GetUseridIndexInfo_1(self,index_info_id),UseridIndexInfo)};BimTilesMeshV3.prototype["num_userid_index_infos"]=BimTilesMeshV3.prototype.num_userid_index_infos=function(){var self=this.ptr;return _emscripten_bind_BimTilesMeshV3_num_userid_index_infos_0(self)};BimTilesMeshV3.prototype["__destroy__"]=BimTilesMeshV3.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_BimTilesMeshV3___destroy___0(self)};function Decoder(){this.ptr=_emscripten_bind_Decoder_Decoder_0();getCache(Decoder)[this.ptr]=this}Decoder.prototype=Object.create(WrapperObject.prototype);Decoder.prototype.constructor=Decoder;Decoder.prototype.__class__=Decoder;Decoder.__cache__={};Module["Decoder"]=Decoder;Decoder.prototype["DecodeFromArray"]=Decoder.prototype.DecodeFromArray=function(data,data_size,data_version){var self=this.ptr;ensureCache.prepare();if(typeof data=="object"){data=ensureInt8(data)}if(data_size&&typeof data_size==="object")data_size=data_size.ptr;if(data_version&&typeof data_version==="object")data_version=data_version.ptr;return wrapPointer(_emscripten_bind_Decoder_DecodeFromArray_3(self,data,data_size,data_version),Status)};Decoder.prototype["ReleaseAllBuffers"]=Decoder.prototype.ReleaseAllBuffers=function(){var self=this.ptr;_emscripten_bind_Decoder_ReleaseAllBuffers_0(self)};Decoder.prototype["GetBlockTypeCount"]=Decoder.prototype.GetBlockTypeCount=function(){var self=this.ptr;return _emscripten_bind_Decoder_GetBlockTypeCount_0(self)};Decoder.prototype["GetBlockType"]=Decoder.prototype.GetBlockType=function(index){var self=this.ptr;if(index&&typeof index==="object")index=index.ptr;return _emscripten_bind_Decoder_GetBlockType_1(self,index)};Decoder.prototype["GetBlockCount"]=Decoder.prototype.GetBlockCount=function(type,data_version){var self=this.ptr;if(type&&typeof type==="object")type=type.ptr;if(data_version&&typeof data_version==="object")data_version=data_version.ptr;return _emscripten_bind_Decoder_GetBlockCount_2(self,type,data_version)};Decoder.prototype["GetMesh"]=Decoder.prototype.GetMesh=function(mesh_id){var self=this.ptr;if(mesh_id&&typeof mesh_id==="object")mesh_id=mesh_id.ptr;return wrapPointer(_emscripten_bind_Decoder_GetMesh_1(self,mesh_id),BimTilesMesh)};Decoder.prototype["GetInstance"]=Decoder.prototype.GetInstance=function(index){var self=this.ptr;if(index&&typeof index==="object")index=index.ptr;return wrapPointer(_emscripten_bind_Decoder_GetInstance_1(self,index),BimTilesInstance)};Decoder.prototype["GetMaterial"]=Decoder.prototype.GetMaterial=function(index){var self=this.ptr;if(index&&typeof index==="object")index=index.ptr;return wrapPointer(_emscripten_bind_Decoder_GetMaterial_1(self,index),BimTilesMaterial)};Decoder.prototype["GetUserId"]=Decoder.prototype.GetUserId=function(index){var self=this.ptr;if(index&&typeof index==="object")index=index.ptr;return wrapPointer(_emscripten_bind_Decoder_GetUserId_1(self,index),BimTilesUserId)};Decoder.prototype["GetUserData"]=Decoder.prototype.GetUserData=function(index){var self=this.ptr;if(index&&typeof index==="object")index=index.ptr;return wrapPointer(_emscripten_bind_Decoder_GetUserData_1(self,index),BimTilesUserData)};Decoder.prototype["GetTileId"]=Decoder.prototype.GetTileId=function(index){var self=this.ptr;if(index&&typeof index==="object")index=index.ptr;return wrapPointer(_emscripten_bind_Decoder_GetTileId_1(self,index),BimTilesTileId)};Decoder.prototype["GetSearchTree"]=Decoder.prototype.GetSearchTree=function(index){var self=this.ptr;if(index&&typeof index==="object")index=index.ptr;return wrapPointer(_emscripten_bind_Decoder_GetSearchTree_1(self,index),BimTilesSearchTree)};Decoder.prototype["GetViewTree"]=Decoder.prototype.GetViewTree=function(index){var self=this.ptr;if(index&&typeof index==="object")index=index.ptr;return wrapPointer(_emscripten_bind_Decoder_GetViewTree_1(self,index),BimTilesViewTree)};Decoder.prototype["GetSearchTreeV2"]=Decoder.prototype.GetSearchTreeV2=function(index){var self=this.ptr;if(index&&typeof index==="object")index=index.ptr;return wrapPointer(_emscripten_bind_Decoder_GetSearchTreeV2_1(self,index),BimTilesSearchTreeV2)};Decoder.prototype["GetUserIdV2"]=Decoder.prototype.GetUserIdV2=function(index){var self=this.ptr;if(index&&typeof index==="object")index=index.ptr;return wrapPointer(_emscripten_bind_Decoder_GetUserIdV2_1(self,index),BimTilesUserIdV2)};Decoder.prototype["GetNodeId"]=Decoder.prototype.GetNodeId=function(index){var self=this.ptr;if(index&&typeof index==="object")index=index.ptr;return wrapPointer(_emscripten_bind_Decoder_GetNodeId_1(self,index),BimTilesNodeId)};Decoder.prototype["GetNodeBox"]=Decoder.prototype.GetNodeBox=function(index){var self=this.ptr;if(index&&typeof index==="object")index=index.ptr;return wrapPointer(_emscripten_bind_Decoder_GetNodeBox_1(self,index),BimTilesNodeBox)};Decoder.prototype["GetUserIdV3"]=Decoder.prototype.GetUserIdV3=function(index){var self=this.ptr;if(index&&typeof index==="object")index=index.ptr;return wrapPointer(_emscripten_bind_Decoder_GetUserIdV3_1(self,index),BimTilesUserIdV3)};Decoder.prototype["GetMeshV3"]=Decoder.prototype.GetMeshV3=function(index){var self=this.ptr;if(index&&typeof index==="object")index=index.ptr;return wrapPointer(_emscripten_bind_Decoder_GetMeshV3_1(self,index),BimTilesMeshV3)};Decoder.prototype["GetUserIdBoxV3"]=Decoder.prototype.GetUserIdBoxV3=function(index){var self=this.ptr;if(index&&typeof index==="object")index=index.ptr;return wrapPointer(_emscripten_bind_Decoder_GetUserIdBoxV3_1(self,index),BimTilesUserIdBoxV3)};Decoder.prototype["GetAttributeDataArrayWithDataType"]=Decoder.prototype.GetAttributeDataArrayWithDataType=function(m,ga,data_type,out_size,out_values){var self=this.ptr;if(m&&typeof m==="object")m=m.ptr;if(ga&&typeof ga==="object")ga=ga.ptr;if(data_type&&typeof data_type==="object")data_type=data_type.ptr;if(out_size&&typeof out_size==="object")out_size=out_size.ptr;if(out_values&&typeof out_values==="object")out_values=out_values.ptr;return!!_emscripten_bind_Decoder_GetAttributeDataArrayWithDataType_5(self,m,ga,data_type,out_size,out_values)};Decoder.prototype["GetAttributeDataArray"]=Decoder.prototype.GetAttributeDataArray=function(m,ga,out_size,out_values){var self=this.ptr;if(m&&typeof m==="object")m=m.ptr;if(ga&&typeof ga==="object")ga=ga.ptr;if(out_size&&typeof out_size==="object")out_size=out_size.ptr;if(out_values&&typeof out_values==="object")out_values=out_values.ptr;return!!_emscripten_bind_Decoder_GetAttributeDataArray_4(self,m,ga,out_size,out_values)};Decoder.prototype["GetAttributeDataArrayV3WithDataType"]=Decoder.prototype.GetAttributeDataArrayV3WithDataType=function(m,ga,data_type,out_size,out_values){var self=this.ptr;if(m&&typeof m==="object")m=m.ptr;if(ga&&typeof ga==="object")ga=ga.ptr;if(data_type&&typeof data_type==="object")data_type=data_type.ptr;if(out_size&&typeof out_size==="object")out_size=out_size.ptr;if(out_values&&typeof out_values==="object")out_values=out_values.ptr;return!!_emscripten_bind_Decoder_GetAttributeDataArrayV3WithDataType_5(self,m,ga,data_type,out_size,out_values)};Decoder.prototype["GetAttributeDataArrayV3"]=Decoder.prototype.GetAttributeDataArrayV3=function(m,ga,out_size,out_values){var self=this.ptr;if(m&&typeof m==="object")m=m.ptr;if(ga&&typeof ga==="object")ga=ga.ptr;if(out_size&&typeof out_size==="object")out_size=out_size.ptr;if(out_values&&typeof out_values==="object")out_values=out_values.ptr;return!!_emscripten_bind_Decoder_GetAttributeDataArrayV3_4(self,m,ga,out_size,out_values)};Decoder.prototype["__destroy__"]=Decoder.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_Decoder___destroy___0(self)};(function(){function setupEnums(){Module["DT_INVALID"]=_emscripten_enum_bimtiles_DataType_DT_INVALID();Module["DT_INT8"]=_emscripten_enum_bimtiles_DataType_DT_INT8();Module["DT_UINT8"]=_emscripten_enum_bimtiles_DataType_DT_UINT8();Module["DT_INT16"]=_emscripten_enum_bimtiles_DataType_DT_INT16();Module["DT_UINT16"]=_emscripten_enum_bimtiles_DataType_DT_UINT16();Module["DT_INT32"]=_emscripten_enum_bimtiles_DataType_DT_INT32();Module["DT_UINT32"]=_emscripten_enum_bimtiles_DataType_DT_UINT32();Module["DT_INT64"]=_emscripten_enum_bimtiles_DataType_DT_INT64();Module["DT_UINT64"]=_emscripten_enum_bimtiles_DataType_DT_UINT64();Module["DT_FLOAT32"]=_emscripten_enum_bimtiles_DataType_DT_FLOAT32();Module["DT_FLOAT64"]=_emscripten_enum_bimtiles_DataType_DT_FLOAT64();Module["DT_BOOL"]=_emscripten_enum_bimtiles_DataType_DT_BOOL();Module["DT_TYPES_COUNT"]=_emscripten_enum_bimtiles_DataType_DT_TYPES_COUNT();Module["BT_UNKNOWN"]=_emscripten_enum_bimtiles_BlockType_BT_UNKNOWN();Module["BT_MESH"]=_emscripten_enum_bimtiles_BlockType_BT_MESH();Module["BT_LINE"]=_emscripten_enum_bimtiles_BlockType_BT_LINE();Module["BT_POINT"]=_emscripten_enum_bimtiles_BlockType_BT_POINT();Module["BT_SYMBOL"]=_emscripten_enum_bimtiles_BlockType_BT_SYMBOL();Module["BT_INSTANCE"]=_emscripten_enum_bimtiles_BlockType_BT_INSTANCE();Module["BT_INDEX"]=_emscripten_enum_bimtiles_BlockType_BT_INDEX();Module["BT_BUFFER"]=_emscripten_enum_bimtiles_BlockType_BT_BUFFER();Module["BT_MATERIAL"]=_emscripten_enum_bimtiles_BlockType_BT_MATERIAL();Module["BT_USERDATA"]=_emscripten_enum_bimtiles_BlockType_BT_USERDATA();Module["BT_USERID"]=_emscripten_enum_bimtiles_BlockType_BT_USERID();Module["BT_TILEID"]=_emscripten_enum_bimtiles_BlockType_BT_TILEID();Module["BT_VIEWTREE"]=_emscripten_enum_bimtiles_BlockType_BT_VIEWTREE();Module["BT_NODEID"]=_emscripten_enum_bimtiles_BlockType_BT_NODEID();Module["BT_NODEBOX"]=_emscripten_enum_bimtiles_BlockType_BT_NODEBOX();Module["BT_USERIDBOX"]=_emscripten_enum_bimtiles_BlockType_BT_USERIDBOX();Module["BT_NUM_BLOCK_TYPES"]=_emscripten_enum_bimtiles_BlockType_BT_NUM_BLOCK_TYPES();Module["DV_V1"]=_emscripten_enum_bimtiles_DataVersion_DV_V1();Module["DV_V2"]=_emscripten_enum_bimtiles_DataVersion_DV_V2();Module["DV_V3"]=_emscripten_enum_bimtiles_DataVersion_DV_V3();Module["GAT_INVALID"]=_emscripten_enum_bimtiles_GeometryAttributeType_GAT_INVALID();Module["GAT_POSITION"]=_emscripten_enum_bimtiles_GeometryAttributeType_GAT_POSITION();Module["GAT_INDEX"]=_emscripten_enum_bimtiles_GeometryAttributeType_GAT_INDEX();Module["GAT_NORMAL"]=_emscripten_enum_bimtiles_GeometryAttributeType_GAT_NORMAL();Module["GAT_COLOR"]=_emscripten_enum_bimtiles_GeometryAttributeType_GAT_COLOR();Module["GAT_TEX_COORD"]=_emscripten_enum_bimtiles_GeometryAttributeType_GAT_TEX_COORD();Module["GAT_TEX_COORD2"]=_emscripten_enum_bimtiles_GeometryAttributeType_GAT_TEX_COORD2();Module["GAT_TEX_COORD3"]=_emscripten_enum_bimtiles_GeometryAttributeType_GAT_TEX_COORD3();Module["GAT_USERID"]=_emscripten_enum_bimtiles_GeometryAttributeType_GAT_USERID();Module["GAT_BARYCENTRIC"]=_emscripten_enum_bimtiles_GeometryAttributeType_GAT_BARYCENTRIC();Module["GAT_ATTRIBUTES_COUNT"]=_emscripten_enum_bimtiles_GeometryAttributeType_GAT_ATTRIBUTES_COUNT();Module["PM_POINTS"]=_emscripten_enum_bimtiles_PrimitiveMode_PM_POINTS();Module["PM_LINES"]=_emscripten_enum_bimtiles_PrimitiveMode_PM_LINES();Module["PM_LINE_STRIP"]=_emscripten_enum_bimtiles_PrimitiveMode_PM_LINE_STRIP();Module["PM_TRIANGLES"]=_emscripten_enum_bimtiles_PrimitiveMode_PM_TRIANGLES();Module["PM_TRIANGLE_STRIP"]=_emscripten_enum_bimtiles_PrimitiveMode_PM_TRIANGLE_STRIP();Module["PM_TRIANGLE_FAN"]=_emscripten_enum_bimtiles_PrimitiveMode_PM_TRIANGLE_FAN();Module["NT_UNKNOW_NODE"]=_emscripten_enum_bimtiles_NodeType_NT_UNKNOW_NODE();Module["NT_COMMON_NODE"]=_emscripten_enum_bimtiles_NodeType_NT_COMMON_NODE();Module["NT_BATCHED_MAIN_NODE"]=_emscripten_enum_bimtiles_NodeType_NT_BATCHED_MAIN_NODE();Module["NT_BATCHED_SUB_NODE"]=_emscripten_enum_bimtiles_NodeType_NT_BATCHED_SUB_NODE();Module["CD_VEC2"]=_emscripten_enum_bimtiles_ComponentDataType_CD_VEC2();Module["CD_VEC3"]=_emscripten_enum_bimtiles_ComponentDataType_CD_VEC3();Module["CD_VEC4"]=_emscripten_enum_bimtiles_ComponentDataType_CD_VEC4();Module["CD_MAT2"]=_emscripten_enum_bimtiles_ComponentDataType_CD_MAT2();Module["CD_MAT3"]=_emscripten_enum_bimtiles_ComponentDataType_CD_MAT3();Module["CD_MAT4"]=_emscripten_enum_bimtiles_ComponentDataType_CD_MAT4();Module["CD_SCALAR"]=_emscripten_enum_bimtiles_ComponentDataType_CD_SCALAR();Module["CD_BUFFER"]=_emscripten_enum_bimtiles_ComponentDataType_CD_BUFFER();Module["OK"]=_emscripten_enum_bimtiles_StatusCode_OK();Module["ERROR"]=_emscripten_enum_bimtiles_StatusCode_ERROR();Module["IO_ERROR"]=_emscripten_enum_bimtiles_StatusCode_IO_ERROR();Module["INVALID_PARAMETER"]=_emscripten_enum_bimtiles_StatusCode_INVALID_PARAMETER();Module["UNSUPPORTED_VERSION"]=_emscripten_enum_bimtiles_StatusCode_UNSUPPORTED_VERSION();Module["UNKNOWN_VERSION"]=_emscripten_enum_bimtiles_StatusCode_UNKNOWN_VERSION()}if(runtimeInitialized)setupEnums();else addOnInit(setupEnums)})();if(typeof Module["onModuleParsed"]==="function"){Module["onModuleParsed"]()}Module["Decoder"].prototype.GetEncodedGeometryType=function(array){if(array.__class__&&array.__class__===Module.DecoderBuffer){return Module.Decoder.prototype.GetEncodedGeometryType_Deprecated(array)}if(array.byteLength<8)return Module.INVALID_GEOMETRY_TYPE;switch(array[7]){case 0:return Module.POINT_CLOUD;case 1:return Module.TRIANGULAR_MESH;default:return Module.INVALID_GEOMETRY_TYPE}};


  return ProtobufDecoderModule.ready
}
);
})();
if (typeof exports === 'object' && typeof module === 'object')
  module.exports = ProtobufDecoderModule;
else if (typeof define === 'function' && define['amd'])
  define([], function() { return ProtobufDecoderModule; });
else if (typeof exports === 'object')
  exports["ProtobufDecoderModule"] = ProtobufDecoderModule;
