!function(){const t=/^[og]\s*(.+)?/,e=/^mtllib /,s=/^usemtl /,i=/^usemap /,o=new THREE.Vector3,r=new THREE.Vector3,n=new THREE.Vector3,a=new THREE.Vector3,l=new THREE.Vector3;function c(){const t={objects:[],object:{},vertices:[],normals:[],colors:[],uvs:[],materials:{},materialLibraries:[],startObject:function(t,e){if(this.object&&!1===this.object.fromDeclaration)return this.object.name=t,void(this.object.fromDeclaration=!1!==e);const s=this.object&&"function"==typeof this.object.currentMaterial?this.object.currentMaterial():void 0;if(this.object&&"function"==typeof this.object._finalize&&this.object._finalize(!0),this.object={name:t||"",fromDeclaration:!1!==e,geometry:{vertices:[],normals:[],colors:[],uvs:[],hasUVIndices:!1},materials:[],smooth:!0,startMaterial:function(t,e){const s=this._finalize(!1);s&&(s.inherited||s.groupCount<=0)&&this.materials.splice(s.index,1);const i={index:this.materials.length,name:t||"",mtllib:Array.isArray(e)&&e.length>0?e[e.length-1]:"",smooth:void 0!==s?s.smooth:this.smooth,groupStart:void 0!==s?s.groupEnd:0,groupEnd:-1,groupCount:-1,inherited:!1,clone:function(t){const e={index:"number"==typeof t?t:this.index,name:this.name,mtllib:this.mtllib,smooth:this.smooth,groupStart:0,groupEnd:-1,groupCount:-1,inherited:!1};return e.clone=this.clone.bind(e),e}};return this.materials.push(i),i},currentMaterial:function(){if(this.materials.length>0)return this.materials[this.materials.length-1]},_finalize:function(t){const e=this.currentMaterial();if(e&&-1===e.groupEnd&&(e.groupEnd=this.geometry.vertices.length/3,e.groupCount=e.groupEnd-e.groupStart,e.inherited=!1),t&&this.materials.length>1)for(let t=this.materials.length-1;t>=0;t--)this.materials[t].groupCount<=0&&this.materials.splice(t,1);return t&&0===this.materials.length&&this.materials.push({name:"",smooth:this.smooth}),e}},s&&s.name&&"function"==typeof s.clone){const t=s.clone(0);t.inherited=!0,this.object.materials.push(t)}this.objects.push(this.object)},finalize:function(){this.object&&"function"==typeof this.object._finalize&&this.object._finalize(!0)},parseVertexIndex:function(t,e){const s=parseInt(t,10);return 3*(s>=0?s-1:s+e/3)},parseNormalIndex:function(t,e){const s=parseInt(t,10);return 3*(s>=0?s-1:s+e/3)},parseUVIndex:function(t,e){const s=parseInt(t,10);return 2*(s>=0?s-1:s+e/2)},addVertex:function(t,e,s){const i=this.vertices,o=this.object.geometry.vertices;o.push(i[t+0],i[t+1],i[t+2]),o.push(i[e+0],i[e+1],i[e+2]),o.push(i[s+0],i[s+1],i[s+2])},addVertexPoint:function(t){const e=this.vertices;this.object.geometry.vertices.push(e[t+0],e[t+1],e[t+2])},addVertexLine:function(t){const e=this.vertices;this.object.geometry.vertices.push(e[t+0],e[t+1],e[t+2])},addNormal:function(t,e,s){const i=this.normals,o=this.object.geometry.normals;o.push(i[t+0],i[t+1],i[t+2]),o.push(i[e+0],i[e+1],i[e+2]),o.push(i[s+0],i[s+1],i[s+2])},addFaceNormal:function(t,e,s){const i=this.vertices,c=this.object.geometry.normals;o.fromArray(i,t),r.fromArray(i,e),n.fromArray(i,s),l.subVectors(n,r),a.subVectors(o,r),l.cross(a),l.normalize(),c.push(l.x,l.y,l.z),c.push(l.x,l.y,l.z),c.push(l.x,l.y,l.z)},addColor:function(t,e,s){const i=this.colors,o=this.object.geometry.colors;void 0!==i[t]&&o.push(i[t+0],i[t+1],i[t+2]),void 0!==i[e]&&o.push(i[e+0],i[e+1],i[e+2]),void 0!==i[s]&&o.push(i[s+0],i[s+1],i[s+2])},addUV:function(t,e,s){const i=this.uvs,o=this.object.geometry.uvs;o.push(i[t+0],i[t+1]),o.push(i[e+0],i[e+1]),o.push(i[s+0],i[s+1])},addDefaultUV:function(){const t=this.object.geometry.uvs;t.push(0,0),t.push(0,0),t.push(0,0)},addUVLine:function(t){const e=this.uvs;this.object.geometry.uvs.push(e[t+0],e[t+1])},addFace:function(t,e,s,i,o,r,n,a,l){const c=this.vertices.length;let h=this.parseVertexIndex(t,c),u=this.parseVertexIndex(e,c),m=this.parseVertexIndex(s,c);if(this.addVertex(h,u,m),this.addColor(h,u,m),void 0!==n&&""!==n){const t=this.normals.length;h=this.parseNormalIndex(n,t),u=this.parseNormalIndex(a,t),m=this.parseNormalIndex(l,t),this.addNormal(h,u,m)}else this.addFaceNormal(h,u,m);if(void 0!==i&&""!==i){const t=this.uvs.length;h=this.parseUVIndex(i,t),u=this.parseUVIndex(o,t),m=this.parseUVIndex(r,t),this.addUV(h,u,m),this.object.geometry.hasUVIndices=!0}else this.addDefaultUV()},addPointGeometry:function(t){this.object.geometry.type="Points";const e=this.vertices.length;for(let s=0,i=t.length;s<i;s++){const i=this.parseVertexIndex(t[s],e);this.addVertexPoint(i),this.addColor(i)}},addLineGeometry:function(t,e){this.object.geometry.type="Line";const s=this.vertices.length,i=this.uvs.length;for(let e=0,i=t.length;e<i;e++)this.addVertexLine(this.parseVertexIndex(t[e],s));for(let t=0,s=e.length;t<s;t++)this.addUVLine(this.parseUVIndex(e[t],i))}};return t.startObject("",!1),t}class h extends THREE.Loader{constructor(t){super(t),this.materials=null}load(t,e,s,i){const o=this,r=new THREE.FileLoader(this.manager);r.setPath(this.path),r.setRequestHeader(this.requestHeader),r.setWithCredentials(this.withCredentials),r.load(t,(function(s){try{e(o.parse(s))}catch(e){i?i(e):console.error(e),o.manager.itemError(t)}}),s,i)}setPath(t){return this.path=t,this}setMaterials(t){return this.materials=t,this}parse(o){const r=new c;-1!==o.indexOf("\r\n")&&(o=o.replace(/\r\n/g,"\n")),-1!==o.indexOf("\\\n")&&(o=o.replace(/\\\n/g,""));const n=o.split("\n");let a="",l="",h=0,u=[];const m="function"==typeof"".trimLeft;for(let o=0,c=n.length;o<c;o++)if(a=n[o],a=m?a.trimLeft():a.trim(),h=a.length,0!==h&&(l=a.charAt(0),"#"!==l))if("v"===l){const t=a.split(/\s+/);switch(t[0]){case"v":r.vertices.push(parseFloat(t[1]),parseFloat(t[2]),parseFloat(t[3])),t.length>=7?r.colors.push(parseFloat(t[4]),parseFloat(t[5]),parseFloat(t[6])):r.colors.push(void 0,void 0,void 0);break;case"vn":r.normals.push(parseFloat(t[1]),parseFloat(t[2]),parseFloat(t[3]));break;case"vt":r.uvs.push(parseFloat(t[1]),parseFloat(t[2]))}}else if("f"===l){const t=a.substr(1).trim().split(/\s+/),e=[];for(let s=0,i=t.length;s<i;s++){const i=t[s];if(i.length>0){const t=i.split("/");e.push(t)}}const s=e[0];for(let t=1,i=e.length-1;t<i;t++){const i=e[t],o=e[t+1];r.addFace(s[0],i[0],o[0],s[1],i[1],o[1],s[2],i[2],o[2])}}else if("l"===l){const t=a.substring(1).trim().split(" ");let e=[];const s=[];if(-1===a.indexOf("/"))e=t;else for(let i=0,o=t.length;i<o;i++){const o=t[i].split("/");""!==o[0]&&e.push(o[0]),""!==o[1]&&s.push(o[1])}r.addLineGeometry(e,s)}else if("p"===l){const t=a.substr(1).trim().split(" ");r.addPointGeometry(t)}else if(null!==(u=t.exec(a))){const t=(" "+u[0].substr(1).trim()).substr(1);r.startObject(t)}else if(s.test(a))r.object.startMaterial(a.substring(7).trim(),r.materialLibraries);else if(e.test(a))r.materialLibraries.push(a.substring(7).trim());else if(i.test(a))console.warn('THREE.OBJLoader: Rendering identifier "usemap" not supported. Textures must be defined in MTL files.');else if("s"===l){if(u=a.split(" "),u.length>1){const t=u[1].trim().toLowerCase();r.object.smooth="0"!==t&&"off"!==t}else r.object.smooth=!0;const t=r.object.currentMaterial();t&&(t.smooth=r.object.smooth)}else if("\0"===a)continue;r.finalize();const p=new THREE.Group;p.materialLibraries=[].concat(r.materialLibraries);if(!0===!(1===r.objects.length&&0===r.objects[0].geometry.vertices.length))for(let t=0,e=r.objects.length;t<e;t++){const e=r.objects[t],s=e.geometry,i=e.materials,o="Line"===s.type,n="Points"===s.type;let a=!1;if(0===s.vertices.length)continue;const l=new THREE.BufferGeometry;l.setAttribute("position",new THREE.Float32BufferAttribute(s.vertices,3)),s.normals.length>0&&l.setAttribute("normal",new THREE.Float32BufferAttribute(s.normals,3)),s.colors.length>0&&(a=!0,l.setAttribute("color",new THREE.Float32BufferAttribute(s.colors,3))),!0===s.hasUVIndices&&l.setAttribute("uv",new THREE.Float32BufferAttribute(s.uvs,2));const c=[];for(let t=0,e=i.length;t<e;t++){const e=i[t],s=e.name+"_"+e.smooth+"_"+a;let l=r.materials[s];if(null!==this.materials)if(l=this.materials.create(e.name),!o||!l||l instanceof THREE.LineBasicMaterial){if(n&&l&&!(l instanceof THREE.PointsMaterial)){const t=new THREE.PointsMaterial({size:10,sizeAttenuation:!1});THREE.Material.prototype.copy.call(t,l),t.color.copy(l.color),t.map=l.map,l=t}}else{const t=new THREE.LineBasicMaterial;THREE.Material.prototype.copy.call(t,l),t.color.copy(l.color),t.lights=!1,l=t}void 0===l&&(l=o?new THREE.LineBasicMaterial:n?new THREE.PointsMaterial({size:1,sizeAttenuation:!1}):new THREE.MeshPhongMaterial,l.name=e.name,l.flatShading=!e.smooth,l.vertexColors=a,r.materials[s]=l),c.push(l)}let h;if(c.length>1){for(let t=0,e=i.length;t<e;t++){const e=i[t];l.addGroup(e.groupStart,e.groupCount,t)}h=o?new THREE.LineSegments(l,c):n?new THREE.Points(l,c):new THREE.Mesh(l,c)}else h=o?new THREE.LineSegments(l,c[0]):n?new THREE.Points(l,c[0]):new THREE.Mesh(l,c[0]);h.name=e.name,p.add(h)}else if(r.vertices.length>0){const t=new THREE.PointsMaterial({size:1,sizeAttenuation:!1}),e=new THREE.BufferGeometry;e.setAttribute("position",new THREE.Float32BufferAttribute(r.vertices,3)),r.colors.length>0&&void 0!==r.colors[0]&&(e.setAttribute("color",new THREE.Float32BufferAttribute(r.colors,3)),t.vertexColors=!0);const s=new THREE.Points(e,t);p.add(s)}return p}}THREE.OBJLoader=h}();