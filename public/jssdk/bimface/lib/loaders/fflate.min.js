/*!
fflate - fast JavaScript compression/decompression
<https://101arrowz.github.io/fflate>
Licensed under MIT. https://github.com/101arrowz/fflate/blob/master/LICENSE
*/
!function(t){"undefined"!=typeof module&&"object"==typeof exports?module.exports=t():"undefined"!=typeof define&&define.amd?define(["fflate",t]):this.fflate=t()}((function(){var t={__esModule:!0},n=("undefined"!=typeof module&&"object"==typeof exports?function(t){"use strict";var n;try{n=require("worker_threads").Worker}catch(n){}return exports.default=n?function(t,r,e,i,o){var a=!1,s=new n(t+";var __w=require('worker_threads');__w.parentPort.on('message',function(m){onmessage({data:m})}),postMessage=function(m,t){__w.parentPort.postMessage(m,t)},close=process.exit;self=global",{eval:!0}).on("error",(function(t){return o(t,null)})).on("message",(function(t){return o(null,t)})).on("exit",(function(t){t&&!a&&o(Error("exited with code "+t),null)}));return s.postMessage(e,i),s.terminate=function(){return a=!0,n.prototype.terminate.call(s)},s}:function(t,n,r,e,i){setImmediate((function(){return i(Error("async operations unsupported - update to Node 12+ (or Node 10-11 with the --experimental-worker CLI flag)"),null)}));var o=function(){};return{terminate:o,postMessage:o}},t}:function(t){"use strict";var n={};return t.default=function(t,r,e,i,o){var a=n[r]||(n[r]=URL.createObjectURL(new Blob([t],{type:"text/javascript"}))),s=new Worker(a);return s.onerror=function(t){return o(t.error,null)},s.onmessage=function(t){return o(null,t.data)},s.postMessage(e,i),s},t})({}),r=Uint8Array,e=Uint16Array,i=Uint32Array,o=new r([0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0]),a=new r([0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0]),s=new r([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),f=function(t,n){for(var r=new e(31),o=0;o<31;++o)r[o]=n+=1<<t[o-1];var a=new i(r[30]);for(o=1;o<30;++o)for(var s=r[o];s<r[o+1];++s)a[s]=s-r[o]<<5|o;return[r,a]},u=f(o,2),h=u[0],c=u[1];h[28]=258,c[258]=28;for(var l=f(a,0),p=l[0],v=l[1],d=new e(32768),g=0;g<32768;++g){var m=(43690&g)>>>1|(21845&g)<<1;d[g]=((65280&(m=(61680&(m=(52428&m)>>>2|(13107&m)<<2))>>>4|(3855&m)<<4))>>>8|(255&m)<<8)>>>1}var w=function(t,n,r){for(var i=t.length,o=0,a=new e(n);o<i;++o)++a[t[o]-1];var s,f=new e(n);for(o=0;o<n;++o)f[o]=f[o-1]+a[o-1]<<1;if(r){s=new e(1<<n);var u=15-n;for(o=0;o<i;++o)if(t[o])for(var h=o<<4|t[o],c=n-t[o],l=f[t[o]-1]++<<c,p=l|(1<<c)-1;l<=p;++l)s[d[l]>>>u]=h}else for(s=new e(i),o=0;o<i;++o)t[o]&&(s[o]=d[f[t[o]-1]++]>>>15-t[o]);return s},y=new r(288);for(g=0;g<144;++g)y[g]=8;for(g=144;g<256;++g)y[g]=9;for(g=256;g<280;++g)y[g]=7;for(g=280;g<288;++g)y[g]=8;var b=new r(32);for(g=0;g<32;++g)b[g]=5;var z=w(y,9,0),k=w(y,9,1),x=w(b,5,0),M=w(b,5,1),A=function(t){for(var n=t[0],r=1;r<t.length;++r)t[r]>n&&(n=t[r]);return n},S=function(t,n,r){var e=n/8|0;return(t[e]|t[e+1]<<8)>>(7&n)&r},C=function(t,n){var r=n/8|0;return(t[r]|t[r+1]<<8|t[r+2]<<16)>>(7&n)},D=function(t){return(t/8|0)+(7&t&&1)},U=function(t,n,o){(null==n||n<0)&&(n=0),(null==o||o>t.length)&&(o=t.length);var a=new(t instanceof e?e:t instanceof i?i:r)(o-n);return a.set(t.subarray(n,o)),a},I=function(t,n,e){var i=t.length;if(!i||e&&!e.l&&i<5)return n||new r(0);var f=!n||e,u=!e||e.i;e||(e={}),n||(n=new r(3*i));var c=function(t){var e=n.length;if(t>e){var i=new r(Math.max(2*e,t));i.set(n),n=i}},l=e.f||0,v=e.p||0,d=e.b||0,g=e.l,m=e.d,y=e.m,b=e.n,z=8*i;do{if(!g){e.f=l=S(t,v,1);var x=S(t,v+1,3);if(v+=3,!x){var I=t[(q=D(v)+4)-4]|t[q-3]<<8,O=q+I;if(O>i){if(u)throw"unexpected EOF";break}f&&c(d+I),n.set(t.subarray(q,O),d),e.b=d+=I,e.p=v=8*O;continue}if(1==x)g=k,m=M,y=9,b=5;else{if(2!=x)throw"invalid block type";var T=S(t,v,31)+257,Z=S(t,v+10,15)+4,_=T+S(t,v+5,31)+1;v+=14;for(var E=new r(_),F=new r(19),G=0;G<Z;++G)F[s[G]]=S(t,v+3*G,7);v+=3*Z;var j=A(F),P=(1<<j)-1;if(!u&&v+_*(j+7)>z)break;var L=w(F,j,1);for(G=0;G<_;){var q,N=L[S(t,v,P)];if(v+=15&N,(q=N>>>4)<16)E[G++]=q;else{var R=0,W=0;for(16==q?(W=3+S(t,v,3),v+=2,R=E[G-1]):17==q?(W=3+S(t,v,7),v+=3):18==q&&(W=11+S(t,v,127),v+=7);W--;)E[G++]=R}}var B=E.subarray(0,T),H=E.subarray(T);y=A(B),b=A(H),g=w(B,y,1),m=w(H,b,1)}if(v>z)throw"unexpected EOF"}f&&c(d+131072);for(var Y=(1<<y)-1,J=(1<<b)-1,K=y+b+18;u||v+K<z;){var Q=(R=g[C(t,v)&Y])>>>4;if((v+=15&R)>z)throw"unexpected EOF";if(!R)throw"invalid length/literal";if(Q<256)n[d++]=Q;else{if(256==Q){g=null;break}var V=Q-254;Q>264&&(V=S(t,v,(1<<(tt=o[G=Q-257]))-1)+h[G],v+=tt);var X=m[C(t,v)&J],$=X>>>4;if(!X)throw"invalid distance";if(v+=15&X,H=p[$],$>3){var tt=a[$];H+=C(t,v)&(1<<tt)-1,v+=tt}if(v>z)throw"unexpected EOF";f&&c(d+131072);for(var nt=d+V;d<nt;d+=4)n[d]=n[d-H],n[d+1]=n[d+1-H],n[d+2]=n[d+2-H],n[d+3]=n[d+3-H];d=nt}}e.l=g,e.p=v,e.b=d,g&&(l=1,e.m=y,e.d=m,e.n=b)}while(!l);return d==n.length?n:U(n,0,d)},O=function(t,n,r){var e=n/8|0;t[e]|=r<<=7&n,t[e+1]|=r>>>8},T=function(t,n,r){var e=n/8|0;t[e]|=r<<=7&n,t[e+1]|=r>>>8,t[e+2]|=r>>>16},Z=function(t,n){for(var i=[],o=0;o<t.length;++o)t[o]&&i.push({s:o,f:t[o]});var a=i.length,s=i.slice();if(!a)return[L,0];if(1==a){var f=new r(i[0].s+1);return f[i[0].s]=1,[f,1]}i.sort((function(t,n){return t.f-n.f})),i.push({s:-1,f:25001});var u=i[0],h=i[1],c=0,l=1,p=2;for(i[0]={s:-1,f:u.f+h.f,l:u,r:h};l!=a-1;)u=i[i[c].f<i[p].f?c++:p++],h=i[c!=l&&i[c].f<i[p].f?c++:p++],i[l++]={s:-1,f:u.f+h.f,l:u,r:h};var v=s[0].s;for(o=1;o<a;++o)s[o].s>v&&(v=s[o].s);var d=new e(v+1),g=_(i[l-1],d,0);if(g>n){o=0;var m=0,w=g-n,y=1<<w;for(s.sort((function(t,n){return d[n.s]-d[t.s]||t.f-n.f}));o<a;++o){var b=s[o].s;if(!(d[b]>n))break;m+=y-(1<<g-d[b]),d[b]=n}for(m>>>=w;m>0;){var z=s[o].s;d[z]<n?m-=1<<n-d[z]++-1:++o}for(;o>=0&&m;--o){var k=s[o].s;d[k]==n&&(--d[k],++m)}g=n}return[new r(d),g]},_=function(t,n,r){return-1==t.s?Math.max(_(t.l,n,r+1),_(t.r,n,r+1)):n[t.s]=r},E=function(t){for(var n=t.length;n&&!t[--n];);for(var r=new e(++n),i=0,o=t[0],a=1,s=function(t){r[i++]=t},f=1;f<=n;++f)if(t[f]==o&&f!=n)++a;else{if(!o&&a>2){for(;a>138;a-=138)s(32754);a>2&&(s(a>10?a-11<<5|28690:a-3<<5|12305),a=0)}else if(a>3){for(s(o),--a;a>6;a-=6)s(8304);a>2&&(s(a-3<<5|8208),a=0)}for(;a--;)s(o);a=1,o=t[f]}return[r.subarray(0,i),n]},F=function(t,n){for(var r=0,e=0;e<n.length;++e)r+=t[e]*n[e];return r},G=function(t,n,r){var e=r.length,i=D(n+2);t[i]=255&e,t[i+1]=e>>>8,t[i+2]=255^t[i],t[i+3]=255^t[i+1];for(var o=0;o<e;++o)t[i+o+4]=r[o];return 8*(i+4+e)},j=function(t,n,r,i,f,u,h,c,l,p,v){O(n,v++,r),++f[256];for(var d=Z(f,15),g=d[0],m=d[1],k=Z(u,15),M=k[0],A=k[1],S=E(g),C=S[0],D=S[1],U=E(M),I=U[0],_=U[1],j=new e(19),P=0;P<C.length;++P)j[31&C[P]]++;for(P=0;P<I.length;++P)j[31&I[P]]++;for(var L=Z(j,7),q=L[0],N=L[1],R=19;R>4&&!q[s[R-1]];--R);var W,B,H,Y,J=p+5<<3,K=F(f,y)+F(u,b)+h,Q=F(f,g)+F(u,M)+h+14+3*R+F(j,q)+(2*j[16]+3*j[17]+7*j[18]);if(J<=K&&J<=Q)return G(n,v,t.subarray(l,l+p));if(O(n,v,1+(Q<K)),v+=2,Q<K){W=w(g,m,0),B=g,H=w(M,A,0),Y=M;var V=w(q,N,0);for(O(n,v,D-257),O(n,v+5,_-1),O(n,v+10,R-4),v+=14,P=0;P<R;++P)O(n,v+3*P,q[s[P]]);v+=3*R;for(var X=[C,I],$=0;$<2;++$){var tt=X[$];for(P=0;P<tt.length;++P)O(n,v,V[nt=31&tt[P]]),v+=q[nt],nt>15&&(O(n,v,tt[P]>>>5&127),v+=tt[P]>>>12)}}else W=z,B=y,H=x,Y=b;for(P=0;P<c;++P)if(i[P]>255){var nt;T(n,v,W[257+(nt=i[P]>>>18&31)]),v+=B[nt+257],nt>7&&(O(n,v,i[P]>>>23&31),v+=o[nt]);var rt=31&i[P];T(n,v,H[rt]),v+=Y[rt],rt>3&&(T(n,v,i[P]>>>5&8191),v+=a[rt])}else T(n,v,W[i[P]]),v+=B[i[P]];return T(n,v,W[256]),v+B[256]},P=new i([65540,131080,131088,131104,262176,1048704,1048832,2114560,2117632]),L=new r(0),q=function(t,n,s,f,u,h){var l=t.length,p=new r(f+l+5*(1+Math.ceil(l/7e3))+u),d=p.subarray(f,p.length-u),g=0;if(!n||l<8)for(var m=0;m<=l;m+=65535){var w=m+65535;w<l?g=G(d,g,t.subarray(m,w)):(d[m]=h,g=G(d,g,t.subarray(m,l)))}else{for(var y=P[n-1],b=y>>>13,z=8191&y,k=(1<<s)-1,x=new e(32768),M=new e(k+1),A=Math.ceil(s/3),S=2*A,C=function(n){return(t[n]^t[n+1]<<A^t[n+2]<<S)&k},I=new i(25e3),O=new e(288),T=new e(32),Z=0,_=0,E=(m=0,0),F=0,q=0;m<l;++m){var N=C(m),R=32767&m,W=M[N];if(x[R]=W,M[N]=R,F<=m){var B=l-m;if((Z>7e3||E>24576)&&B>423){g=j(t,d,0,I,O,T,_,E,q,m-q,g),E=Z=_=0,q=m;for(var H=0;H<286;++H)O[H]=0;for(H=0;H<30;++H)T[H]=0}var Y=2,J=0,K=z,Q=R-W&32767;if(B>2&&N==C(m-Q))for(var V=Math.min(b,B)-1,X=Math.min(32767,m),$=Math.min(258,B);Q<=X&&--K&&R!=W;){if(t[m+Y]==t[m+Y-Q]){for(var tt=0;tt<$&&t[m+tt]==t[m+tt-Q];++tt);if(tt>Y){if(Y=tt,J=Q,tt>V)break;var nt=Math.min(Q,tt-2),rt=0;for(H=0;H<nt;++H){var et=m-Q+H+32768&32767,it=et-x[et]+32768&32767;it>rt&&(rt=it,W=et)}}}Q+=(R=W)-(W=x[R])+32768&32767}if(J){I[E++]=268435456|c[Y]<<18|v[J];var ot=31&c[Y],at=31&v[J];_+=o[ot]+a[at],++O[257+ot],++T[at],F=m+Y,++Z}else I[E++]=t[m],++O[t[m]]}}g=j(t,d,h,I,O,T,_,E,q,m-q,g),!h&&7&g&&(g=G(d,g+1,L))}return U(p,0,f+D(g)+u)},N=function(){for(var t=new i(256),n=0;n<256;++n){for(var r=n,e=9;--e;)r=(1&r&&3988292384)^r>>>1;t[n]=r}return t}(),R=function(){var t=-1;return{p:function(n){for(var r=t,e=0;e<n.length;++e)r=N[255&r^n[e]]^r>>>8;t=r},d:function(){return~t}}},W=function(){var t=1,n=0;return{p:function(r){for(var e=t,i=n,o=r.length,a=0;a!=o;){for(var s=Math.min(a+2655,o);a<s;++a)i+=e+=r[a];e=(65535&e)+15*(e>>16),i=(65535&i)+15*(i>>16)}t=e,n=i},d:function(){return((t%=65521)>>>8<<16|(255&(n%=65521))<<8|n>>>8)+2*((255&t)<<23)}}},B=function(t,n,r,e,i){return q(t,null==n.level?6:n.level,null==n.mem?Math.ceil(1.5*Math.max(8,Math.min(13,Math.log(t.length)))):12+n.mem,r,e,!i)},H=function(t,n){var r={};for(var e in t)r[e]=t[e];for(var e in n)r[e]=n[e];return r},Y=function(t,n,r){for(var e=t(),i=""+t,o=i.slice(i.indexOf("[")+1,i.lastIndexOf("]")).replace(/ /g,"").split(","),a=0;a<e.length;++a){var s=e[a],f=o[a];if("function"==typeof s){n+=";"+f+"=";var u=""+s;if(s.prototype)if(-1!=u.indexOf("[native code]")){var h=u.indexOf(" ",8)+1;n+=u.slice(h,u.indexOf("(",h))}else for(var c in n+=u,s.prototype)n+=";"+f+".prototype."+c+"="+s.prototype[c];else n+=u}else r[f]=s}return[n,r]},J=[],K=function(t){var n=[];for(var o in t)(t[o]instanceof r||t[o]instanceof e||t[o]instanceof i)&&n.push((t[o]=new t[o].constructor(t[o])).buffer);return n},Q=function(t,r,e,i){var o;if(!J[e]){for(var a="",s={},f=t.length-1,u=0;u<f;++u)a=(o=Y(t[u],a,s))[0],s=o[1];J[e]=Y(t[f],a,s)}var h=H({},J[e][1]);return n.default(J[e][0]+";onmessage=function(e){for(var k in e.data)self[k]=e.data[k];onmessage="+r+"}",e,h,K(h),i)},V=function(){return[r,e,i,o,a,s,h,p,k,M,d,w,A,S,C,D,U,I,St,et,it]},X=function(){return[r,e,i,o,a,s,c,v,z,y,x,b,d,P,L,w,O,T,Z,_,E,F,G,j,D,U,q,B,kt,et]},$=function(){return[lt,dt,ct,R,N]},tt=function(){return[pt,vt]},nt=function(){return[gt,ct,W]},rt=function(){return[mt]},et=function(t){return postMessage(t,[t.buffer])},it=function(t){return t&&t.size&&new r(t.size)},ot=function(t,n,r,e,i,o){var a=Q(r,e,i,(function(t,n){a.terminate(),o(t,n)}));return a.postMessage([t,n],n.consume?[t.buffer]:[]),function(){a.terminate()}},at=function(t){return t.ondata=function(t,n){return postMessage([t,n],[t.buffer])},function(n){return t.push(n.data[0],n.data[1])}},st=function(t,n,r,e,i){var o,a=Q(t,e,i,(function(t,r){t?(a.terminate(),n.ondata.call(n,t)):(r[1]&&a.terminate(),n.ondata.call(n,t,r[0],r[1]))}));a.postMessage(r),n.push=function(t,r){if(o)throw"stream finished";if(!n.ondata)throw"no stream handler";a.postMessage([t,o=r],[t.buffer])},n.terminate=function(){a.terminate()}},ft=function(t,n){return t[n]|t[n+1]<<8},ut=function(t,n){return(t[n]|t[n+1]<<8|t[n+2]<<16)+2*(t[n+3]<<23)},ht=function(t,n){return ut(t,n)|4294967296*ut(t,n)},ct=function(t,n,r){for(;r;++n)t[n]=r,r>>>=8},lt=function(t,n){var r=n.filename;if(t[0]=31,t[1]=139,t[2]=8,t[8]=n.level<2?4:9==n.level?2:0,t[9]=3,0!=n.mtime&&ct(t,4,Math.floor(new Date(n.mtime||Date.now())/1e3)),r){t[3]=8;for(var e=0;e<=r.length;++e)t[e+10]=r.charCodeAt(e)}},pt=function(t){if(31!=t[0]||139!=t[1]||8!=t[2])throw"invalid gzip data";var n=t[3],r=10;4&n&&(r+=t[10]|2+(t[11]<<8));for(var e=(n>>3&1)+(n>>4&1);e>0;e-=!t[r++]);return r+(2&n)},vt=function(t){var n=t.length;return(t[n-4]|t[n-3]<<8|t[n-2]<<16)+2*(t[n-1]<<23)},dt=function(t){return 10+(t.filename&&t.filename.length+1||0)},gt=function(t,n){var r=n.level,e=0==r?0:r<6?1:9==r?3:2;t[0]=120,t[1]=e<<6|(e?32-2*e:1)},mt=function(t){if(8!=(15&t[0])||t[0]>>>4>7||(t[0]<<8|t[1])%31)throw"invalid zlib data";if(32&t[1])throw"invalid zlib data: preset dictionaries not supported"};function wt(t,n){return n||"function"!=typeof t||(n=t,t={}),this.ondata=n,t}var yt=function(){function t(t,n){n||"function"!=typeof t||(n=t,t={}),this.ondata=n,this.o=t||{}}return t.prototype.p=function(t,n){this.ondata(B(t,this.o,0,0,!n),n)},t.prototype.push=function(t,n){if(this.d)throw"stream finished";if(!this.ondata)throw"no stream handler";this.d=n,this.p(t,n||!1)},t}();t.Deflate=yt;var bt=function(t,n){st([X,function(){return[at,yt]}],this,wt.call(this,t,n),(function(t){var n=new yt(t.data);onmessage=at(n)}),6)};function zt(t,n,r){if(r||(r=n,n={}),"function"!=typeof r)throw"no callback";return ot(t,n,[X],(function(t){return et(kt(t.data[0],t.data[1]))}),0,r)}function kt(t,n){return B(t,n||{},0,0)}t.AsyncDeflate=bt,t.deflate=zt,t.deflateSync=kt;var xt=function(){function t(t){this.s={},this.p=new r(0),this.ondata=t}return t.prototype.e=function(t){if(this.d)throw"stream finished";if(!this.ondata)throw"no stream handler";var n=this.p.length,e=new r(n+t.length);e.set(this.p),e.set(t,n),this.p=e},t.prototype.c=function(t){this.d=this.s.i=t||!1;var n=this.s.b,r=I(this.p,this.o,this.s);this.ondata(U(r,n,this.s.b),this.d),this.o=U(r,this.s.b-32768),this.s.b=this.o.length,this.p=U(this.p,this.s.p/8|0),this.s.p&=7},t.prototype.push=function(t,n){this.e(t),this.c(n)},t}();t.Inflate=xt;var Mt=function(t){this.ondata=t,st([V,function(){return[at,xt]}],this,0,(function(){var t=new xt;onmessage=at(t)}),7)};function At(t,n,r){if(r||(r=n,n={}),"function"!=typeof r)throw"no callback";return ot(t,n,[V],(function(t){return et(St(t.data[0],it(t.data[1])))}),1,r)}function St(t,n){return I(t,n)}t.AsyncInflate=Mt,t.inflate=At,t.inflateSync=St;var Ct=function(){function t(t,n){this.c=R(),this.l=0,this.v=1,yt.call(this,t,n)}return t.prototype.push=function(t,n){yt.prototype.push.call(this,t,n)},t.prototype.p=function(t,n){this.c.p(t),this.l+=t.length;var r=B(t,this.o,this.v&&dt(this.o),n&&8,!n);this.v&&(lt(r,this.o),this.v=0),n&&(ct(r,r.length-8,this.c.d()),ct(r,r.length-4,this.l)),this.ondata(r,n)},t}();t.Gzip=Ct,t.Compress=Ct;var Dt=function(t,n){st([X,$,function(){return[at,yt,Ct]}],this,wt.call(this,t,n),(function(t){var n=new Ct(t.data);onmessage=at(n)}),8)};function Ut(t,n,r){if(r||(r=n,n={}),"function"!=typeof r)throw"no callback";return ot(t,n,[X,$,function(){return[It]}],(function(t){return et(It(t.data[0],t.data[1]))}),2,r)}function It(t,n){n||(n={});var r=R(),e=t.length;r.p(t);var i=B(t,n,dt(n),8),o=i.length;return lt(i,n),ct(i,o-8,r.d()),ct(i,o-4,e),i}t.AsyncGzip=Dt,t.AsyncCompress=Dt,t.gzip=Ut,t.compress=Ut,t.gzipSync=It,t.compressSync=It;var Ot=function(){function t(t){this.v=1,xt.call(this,t)}return t.prototype.push=function(t,n){if(xt.prototype.e.call(this,t),this.v){var r=this.p.length>3?pt(this.p):4;if(r>=this.p.length&&!n)return;this.p=this.p.subarray(r),this.v=0}if(n){if(this.p.length<8)throw"invalid gzip stream";this.p=this.p.subarray(0,-8)}xt.prototype.c.call(this,n)},t}();t.Gunzip=Ot;var Tt=function(t){this.ondata=t,st([V,tt,function(){return[at,xt,Ot]}],this,0,(function(){var t=new Ot;onmessage=at(t)}),9)};function Zt(t,n,r){if(r||(r=n,n={}),"function"!=typeof r)throw"no callback";return ot(t,n,[V,tt,function(){return[_t]}],(function(t){return et(_t(t.data[0]))}),3,r)}function _t(t,n){return I(t.subarray(pt(t),-8),n||new r(vt(t)))}t.AsyncGunzip=Tt,t.gunzip=Zt,t.gunzipSync=_t;var Et=function(){function t(t,n){this.c=W(),this.v=1,yt.call(this,t,n)}return t.prototype.push=function(t,n){yt.prototype.push.call(this,t,n)},t.prototype.p=function(t,n){this.c.p(t);var r=B(t,this.o,this.v&&2,n&&4,!n);this.v&&(gt(r,this.o),this.v=0),n&&ct(r,r.length-4,this.c.d()),this.ondata(r,n)},t}();t.Zlib=Et;var Ft=function(t,n){st([X,nt,function(){return[at,yt,Et]}],this,wt.call(this,t,n),(function(t){var n=new Et(t.data);onmessage=at(n)}),10)};function Gt(t,n){n||(n={});var r=W();r.p(t);var e=B(t,n,2,4);return gt(e,n),ct(e,e.length-4,r.d()),e}t.AsyncZlib=Ft,t.zlib=function(t,n,r){if(r||(r=n,n={}),"function"!=typeof r)throw"no callback";return ot(t,n,[X,nt,function(){return[Gt]}],(function(t){return et(Gt(t.data[0],t.data[1]))}),4,r)},t.zlibSync=Gt;var jt=function(){function t(t){this.v=1,xt.call(this,t)}return t.prototype.push=function(t,n){if(xt.prototype.e.call(this,t),this.v){if(this.p.length<2&&!n)return;this.p=this.p.subarray(2),this.v=0}if(n){if(this.p.length<4)throw"invalid zlib stream";this.p=this.p.subarray(0,-4)}xt.prototype.c.call(this,n)},t}();t.Unzlib=jt;var Pt=function(t){this.ondata=t,st([V,rt,function(){return[at,xt,jt]}],this,0,(function(){var t=new jt;onmessage=at(t)}),11)};function Lt(t,n,r){if(r||(r=n,n={}),"function"!=typeof r)throw"no callback";return ot(t,n,[V,rt,function(){return[qt]}],(function(t){return et(qt(t.data[0],it(t.data[1])))}),5,r)}function qt(t,n){return I((mt(t),t.subarray(2,-4)),n)}t.AsyncUnzlib=Pt,t.unzlib=Lt,t.unzlibSync=qt;var Nt=function(){function t(t){this.G=Ot,this.I=xt,this.Z=jt,this.ondata=t}return t.prototype.push=function(t,n){if(!this.ondata)throw"no stream handler";if(this.s)this.s.push(t,n);else{if(this.p&&this.p.length){var e=new r(this.p.length+t.length);e.set(this.p),e.set(t,this.p.length)}else this.p=t;if(this.p.length>2){var i=this,o=function(){i.ondata.apply(i,arguments)};this.s=31==this.p[0]&&139==this.p[1]&&8==this.p[2]?new this.G(o):8!=(15&this.p[0])||this.p[0]>>4>7||(this.p[0]<<8|this.p[1])%31?new this.I(o):new this.Z(o),this.s.push(this.p,n),this.p=null}}},t}();t.Decompress=Nt;var Rt=function(){function t(t){this.G=Tt,this.I=Mt,this.Z=Pt,this.ondata=t}return t.prototype.push=function(t,n){Nt.prototype.push.call(this,t,n)},t}();t.AsyncDecompress=Rt,t.decompress=function(t,n,r){if(r||(r=n,n={}),"function"!=typeof r)throw"no callback";return 31==t[0]&&139==t[1]&&8==t[2]?Zt(t,n,r):8!=(15&t[0])||t[0]>>4>7||(t[0]<<8|t[1])%31?At(t,n,r):Lt(t,n,r)},t.decompressSync=function(t,n){return 31==t[0]&&139==t[1]&&8==t[2]?_t(t,n):8!=(15&t[0])||t[0]>>4>7||(t[0]<<8|t[1])%31?St(t,n):qt(t,n)};var Wt=function(t,n,e,i){for(var o in t){var a=t[o],s=n+o;a instanceof r?e[s]=[a,i]:Array.isArray(a)?e[s]=[a[0],H(i,a[1])]:Wt(a,s+"/",e,i)}},Bt="undefined"!=typeof TextEncoder&&new TextEncoder,Ht="undefined"!=typeof TextDecoder&&new TextDecoder,Yt=0;try{Ht.decode(L,{stream:!0}),Yt=1}catch(n){}var Jt=function(t){for(var n="",r=0;;){var e=t[r++],i=(e>127)+(e>223)+(e>239);if(r+i>t.length)return[n,U(t,r-1)];i?3==i?(e=((15&e)<<18|(63&t[r++])<<12|(63&t[r++])<<6|63&t[r++])-65536,n+=String.fromCharCode(55296|e>>10,56320|1023&e)):n+=String.fromCharCode(1&i?(31&e)<<6|63&t[r++]:(15&e)<<12|(63&t[r++])<<6|63&t[r++]):n+=String.fromCharCode(e)}},Kt=function(){function t(t){this.ondata=t,Yt?this.t=new TextDecoder:this.p=L}return t.prototype.push=function(t,n){if(!this.ondata)throw"no callback";if(n||(n=!1),this.t)return this.ondata(this.t.decode(t,{stream:!n}),n);var e=new r(this.p.length+t.length);e.set(this.p),e.set(t,this.p.length);var i=Jt(e),o=i[0],a=i[1];if(n&&a.length)throw"invalid utf-8 data";this.p=a,this.ondata(o,n)},t}();t.DecodeUTF8=Kt;var Qt=function(){function t(t){this.ondata=t}return t.prototype.push=function(t,n){if(!this.ondata)throw"no callback";this.ondata(Vt(t),n||!1)},t}();function Vt(t,n){if(n){for(var e=new r(t.length),i=0;i<t.length;++i)e[i]=t.charCodeAt(i);return e}if(Bt)return Bt.encode(t);var o=t.length,a=new r(t.length+(t.length>>1)),s=0,f=function(t){a[s++]=t};for(i=0;i<o;++i){if(s+5>a.length){var u=new r(s+8+(o-i<<1));u.set(a),a=u}var h=t.charCodeAt(i);h<128||n?f(h):h<2048?(f(192|h>>>6),f(128|63&h)):h>55295&&h<57344?(f(240|(h=65536+(1047552&h)|1023&t.charCodeAt(++i))>>>18),f(128|h>>>12&63),f(128|h>>>6&63),f(128|63&h)):(f(224|h>>>12),f(128|h>>>6&63),f(128|63&h))}return U(a,0,s)}function Xt(t,n){if(n){for(var r="",e=0;e<t.length;e+=16384)r+=String.fromCharCode.apply(null,t.subarray(e,e+16384));return r}if(Ht)return Ht.decode(t);var i=Jt(t);if(i[1].length)throw"invalid utf-8 data";return i[0]}t.EncodeUTF8=Qt,t.strToU8=Vt,t.strFromU8=Xt;var $t=function(t){return 1==t?3:t<6?2:9==t?1:0},tn=function(t,n){return n+30+ft(t,n+26)+ft(t,n+28)},nn=function(t,n,r){var e=ft(t,n+28),i=Xt(t.subarray(n+46,n+46+e),!(2048&ft(t,n+8))),o=n+46+e,a=ut(t,n+20),s=r&&4294967295==a?rn(t,o):[a,ut(t,n+24),ut(t,n+42)],f=s[0],u=s[1],h=s[2];return[ft(t,n+10),f,u,i,o+ft(t,n+30)+ft(t,n+32),h]},rn=function(t,n){for(;1!=ft(t,n);n+=4+ft(t,n+2));return[ht(t,n+12),ht(t,n+4),ht(t,n+20)]},en=function(t){var n=0;if(t)for(var r in t){var e=t[r].length;if(e>65535)throw"extra field too long";n+=e+4}return n},on=function(t,n,r,e,i,o,a,s){var f=e.length,u=r.extra,h=s&&s.length,c=en(u);ct(t,n,null!=a?33639248:67324752),n+=4,null!=a&&(t[n++]=20,t[n++]=r.os),t[n]=20,n+=2,t[n++]=r.flag<<1|(null==o&&8),t[n++]=i&&8,t[n++]=255&r.compression,t[n++]=r.compression>>8;var l=new Date(null==r.mtime?Date.now():r.mtime),p=l.getFullYear()-1980;if(p<0||p>119)throw"date not in range 1980-2099";if(ct(t,n,2*(p<<24)|l.getMonth()+1<<21|l.getDate()<<16|l.getHours()<<11|l.getMinutes()<<5|l.getSeconds()>>>1),n+=4,null!=o&&(ct(t,n,r.crc),ct(t,n+4,o),ct(t,n+8,r.size)),ct(t,n+12,f),ct(t,n+14,c),n+=16,null!=a&&(ct(t,n,h),ct(t,n+6,r.attrs),ct(t,n+10,a),n+=14),t.set(e,n),n+=f,c)for(var v in u){var d=u[v],g=d.length;ct(t,n,+v),ct(t,n+2,g),t.set(d,n+4),n+=4+g}return h&&(t.set(s,n),n+=h),n},an=function(t,n,r,e,i){ct(t,n,101010256),ct(t,n+8,r),ct(t,n+10,r),ct(t,n+12,e),ct(t,n+16,i)},sn=function(){function t(t){this.filename=t,this.c=R(),this.size=0,this.compression=0}return t.prototype.process=function(t,n){this.ondata(null,t,n)},t.prototype.push=function(t,n){if(!this.ondata)throw"no callback - add to ZIP archive before pushing";this.c.p(t),this.size+=t.length,n&&(this.crc=this.c.d()),this.process(t,n||!1)},t}();t.ZipPassThrough=sn;var fn=function(){function t(t,n){var r=this;n||(n={}),sn.call(this,t),this.d=new yt(n,(function(t,n){r.ondata(null,t,n)})),this.compression=8,this.flag=$t(n.level)}return t.prototype.process=function(t,n){try{this.d.push(t,n)}catch(t){this.ondata(t,null,n)}},t.prototype.push=function(t,n){sn.prototype.push.call(this,t,n)},t}();t.ZipDeflate=fn;var un=function(){function t(t,n){var r=this;n||(n={}),sn.call(this,t),this.d=new bt(n,(function(t,n,e){r.ondata(t,n,e)})),this.compression=8,this.flag=$t(n.level),this.terminate=this.d.terminate}return t.prototype.process=function(t,n){this.d.push(t,n)},t.prototype.push=function(t,n){sn.prototype.push.call(this,t,n)},t}();t.AsyncZipDeflate=un;var hn=function(){function t(t){this.ondata=t,this.u=[],this.d=1}return t.prototype.add=function(t){var n=this;if(2&this.d)throw"stream finished";var e=Vt(t.filename),i=e.length,o=t.comment,a=o&&Vt(o),s=i!=t.filename.length||a&&o.length!=a.length,f=i+en(t.extra)+30;if(i>65535)throw"filename too long";var u=new r(f);on(u,0,t,e,s);var h=[u],c=function(){for(var t=0,r=h;t<r.length;t++)n.ondata(null,r[t],!1);h=[]},l=this.d;this.d=0;var p=this.u.length,v=H(t,{f:e,u:s,o:a,t:function(){t.terminate&&t.terminate()},r:function(){if(c(),l){var t=n.u[p+1];t?t.r():n.d=1}l=1}}),d=0;t.ondata=function(e,i,o){if(e)n.ondata(e,i,o),n.terminate();else if(d+=i.length,h.push(i),o){var a=new r(16);ct(a,0,134695760),ct(a,4,t.crc),ct(a,8,d),ct(a,12,t.size),h.push(a),v.c=d,v.b=f+d+16,v.crc=t.crc,v.size=t.size,l&&v.r(),l=1}else l&&c()},this.u.push(v)},t.prototype.end=function(){var t=this;if(2&this.d){if(1&this.d)throw"stream finishing";throw"stream finished"}this.d?this.e():this.u.push({r:function(){1&t.d&&(t.u.splice(-1,1),t.e())},t:function(){}}),this.d=3},t.prototype.e=function(){for(var t=0,n=0,e=0,i=0,o=this.u;i<o.length;i++)e+=46+(u=o[i]).f.length+en(u.extra)+(u.o?u.o.length:0);for(var a=new r(e+22),s=0,f=this.u;s<f.length;s++){var u;on(a,t,u=f[s],u.f,u.u,u.c,n,u.o),t+=46+u.f.length+en(u.extra)+(u.o?u.o.length:0),n+=u.b}an(a,t,this.u.length,e,n),this.ondata(null,a,!0),this.d=2},t.prototype.terminate=function(){for(var t=0,n=this.u;t<n.length;t++)n[t].t();this.d=2},t}();t.Zip=hn,t.zip=function(t,n,e){if(e||(e=n,n={}),"function"!=typeof e)throw"no callback";var i={};Wt(t,"",i,n);var o=Object.keys(i),a=o.length,s=0,f=0,u=a,h=Array(a),c=[],l=function(){for(var t=0;t<c.length;++t)c[t]()},p=function(){var t=new r(f+22),n=s,i=f-s;f=0;for(var o=0;o<u;++o){var a=h[o];try{var c=a.c.length;on(t,f,a,a.f,a.u,c);var l=30+a.f.length+en(a.extra),p=f+l;t.set(a.c,p),on(t,s,a,a.f,a.u,c,f,a.m),s+=16+l+(a.m?a.m.length:0),f=p+c}catch(t){return e(t,null)}}an(t,s,h.length,i,n),e(null,t)};a||p();for(var v=function(t){var n=o[t],r=i[n],u=r[0],v=r[1],d=R(),g=u.length;d.p(u);var m=Vt(n),w=m.length,y=v.comment,b=y&&Vt(y),z=b&&b.length,k=en(v.extra),x=0==v.level?0:8,M=function(r,i){if(r)l(),e(r,null);else{var o=i.length;h[t]=H(v,{size:g,crc:d.d(),c:i,f:m,m:b,u:w!=n.length||b&&y.length!=z,compression:x}),s+=30+w+k+o,f+=76+2*(w+k)+(z||0)+o,--a||p()}};if(w>65535&&M("filename too long",null),x)if(g<16e4)try{M(null,kt(u,v))}catch(t){M(t,null)}else c.push(zt(u,v,M));else M(null,u)},d=0;d<u;++d)v(d);return l},t.zipSync=function(t,n){n||(n={});var e={},i=[];Wt(t,"",e,n);var o=0,a=0;for(var s in e){var f=e[s],u=f[0],h=f[1],c=0==h.level?0:8,l=(M=Vt(s)).length,p=h.comment,v=p&&Vt(p),d=v&&v.length,g=en(h.extra);if(l>65535)throw"filename too long";var m=c?kt(u,h):u,w=m.length,y=R();y.p(u),i.push(H(h,{size:u.length,crc:y.d(),c:m,f:M,m:v,u:l!=s.length||v&&p.length!=d,o:o,compression:c})),o+=30+l+g+w,a+=76+2*(l+g)+(d||0)+w}for(var b=new r(a+22),z=o,k=a-o,x=0;x<i.length;++x){var M;on(b,(M=i[x]).o,M,M.f,M.u,M.c.length);var A=30+M.f.length+en(M.extra);b.set(M.c,M.o+A),on(b,o,M,M.f,M.u,M.c.length,M.o,M.m),o+=16+A+(M.m?M.m.length:0)}return an(b,o,i.length,k,z),b};var cn=function(){function t(){}return t.prototype.push=function(t,n){this.ondata(null,t,n)},t.compression=0,t}();t.UnzipPassThrough=cn;var ln=function(){function t(){var t=this;this.i=new xt((function(n,r){t.ondata(null,n,r)}))}return t.prototype.push=function(t,n){try{this.i.push(t,n)}catch(r){this.ondata(r,t,n)}},t.compression=8,t}();t.UnzipInflate=ln;var pn=function(){function t(t,n){var r=this;n<32e4?this.i=new xt((function(t,n){r.ondata(null,t,n)})):(this.i=new Mt((function(t,n,e){r.ondata(t,n,e)})),this.terminate=this.i.terminate)}return t.prototype.push=function(t,n){this.i.terminate&&(t=U(t,0)),this.i.push(t,n)},t.compression=8,t}();t.AsyncUnzipInflate=pn;var vn=function(){function t(t){this.onfile=t,this.k=[],this.o={0:cn},this.p=L}return t.prototype.push=function(t,n){var e=this;if(!this.onfile)throw"no callback";if(this.c>0){var i=Math.min(this.c,t.length),o=t.subarray(0,i);if(this.c-=i,this.d?this.d.push(o,!this.c):this.k[0].push(o),(t=t.subarray(i)).length)return this.push(t,n)}else{var a=0,s=0,f=void 0,u=void 0;this.p.length?t.length?((u=new r(this.p.length+t.length)).set(this.p),u.set(t,this.p.length)):u=this.p:u=t;for(var h=u.length,c=this.c,l=c&&this.d,p=function(){var t,n=ut(u,s);if(67324752==n){a=1,f=s,v.d=null,v.c=0;var r=ft(u,s+6),i=ft(u,s+8),o=2048&r,l=8&r,p=ft(u,s+26),d=ft(u,s+28);if(h>s+30+p+d){var g=[];v.k.unshift(g),a=2;var m=ut(u,s+18),w=ut(u,s+22),y=Xt(u.subarray(s+30,s+=30+p),!o);4294967295==m?(t=l?[-2]:rn(u,s),m=t[0],w=t[1]):l&&(m=-1),s+=d,v.c=m;var b={name:y,compression:i,start:function(){if(!b.ondata)throw"no callback";if(m){var t=e.o[i];if(!t)throw"unknown compression type "+i;var n=m<0?new t(y):new t(y,m,w);n.ondata=function(t,n,r){b.ondata(t,n,r)};for(var r=0,o=g;r<o.length;r++)n.push(o[r],!1);e.k[0]==g?e.d=n:n.push(L,!0)}else b.ondata(null,L,!0)},terminate:function(){e.k[0]==g&&e.d.terminate&&e.d.terminate()}};m>=0&&(b.size=m,b.originalSize=w),v.onfile(b)}return"break"}if(c){if(134695760==n)return f=s+=12+(-2==c&&8),a=2,v.c=0,"break";if(33639248==n)return f=s-=4,a=2,v.c=0,"break"}},v=this;s<h-4&&"break"!==p();++s);if(this.p=L,c<0){var d=u.subarray(0,a?f-12-(-2==c&&8)-(134695760==ut(u,f-16)&&4):s);l?l.push(d,!!a):this.k[+(2==a)].push(d)}if(2&a)return this.push(u.subarray(s),n);this.p=u.subarray(s)}if(n&&this.c)throw"invalid zip file"},t.prototype.register=function(t){this.o[t.compression]=t},t}();return t.Unzip=vn,t.unzip=function(t,n){if("function"!=typeof n)throw"no callback";for(var e=[],i=function(){for(var t=0;t<e.length;++t)e[t]()},o={},a=t.length-22;101010256!=ut(t,a);--a)if(!a||t.length-a>65558)return void n("invalid zip file",null);var s=ft(t,a+8);s||n(null,{});var f=s,u=ut(t,a+16),h=4294967295==u;if(h){if(a=ut(t,a-12),101075792!=ut(t,a))return void n("invalid zip file",null);f=s=ut(t,a+32),u=ut(t,a+48)}for(var c=function(a){var f=nn(t,u,h),c=f[0],l=f[1],p=f[2],v=f[3],d=f[4],g=tn(t,f[5]);u=d;var m=function(t,r){t?(i(),n(t,null)):(o[v]=r,--s||n(null,o))};if(c)if(8==c){var w=t.subarray(g,g+l);if(l<32e4)try{m(null,St(w,new r(p)))}catch(t){m(t,null)}else e.push(At(w,{size:p},m))}else m("unknown compression type "+c,null);else m(null,U(t,g,g+l))},l=0;l<f;++l)c();return i},t.unzipSync=function(t){for(var n={},e=t.length-22;101010256!=ut(t,e);--e)if(!e||t.length-e>65558)throw"invalid zip file";var i=ft(t,e+8);if(!i)return{};var o=ut(t,e+16),a=4294967295==o;if(a){if(e=ut(t,e-12),101075792!=ut(t,e))throw"invalid zip file";i=ut(t,e+32),o=ut(t,e+48)}for(var s=0;s<i;++s){var f=nn(t,o,a),u=f[0],h=f[1],c=f[2],l=f[3],p=f[4],v=tn(t,f[5]);if(o=p,u){if(8!=u)throw"unknown compression type "+u;n[l]=St(t.subarray(v,v+h),new r(c))}else n[l]=U(t,v,v+h)}return n},t}));