var CLOUD=CLOUD||{};!function(){"use strict";function e(e,t,n){return t<=e&&e<=n}function t(e){if(void 0===e)return{};if(e===Object(e))return e;throw TypeError("Could not convert argument to dictionary")}function n(e){this.tokens=[].slice.call(e)}n.prototype={endOfStream:function(){return!this.tokens.length},read:function(){return this.tokens.length?this.tokens.shift():-1},prepend:function(e){if(Array.isArray(e))for(var t=e;t.length;)this.tokens.unshift(t.pop());else this.tokens.unshift(e)},push:function(e){if(Array.isArray(e))for(var t=e;t.length;)this.tokens.push(t.shift());else this.tokens.push(e)}};var r=-1;function o(e,t){if(e)throw TypeError("Decoder error");return t||65533}var i="utf-8";function s(e,n){if(!(this instanceof s))return new s(e,n);if((e=void 0!==e?String(e).toLowerCase():i)!==i)throw new Error("Encoding not supported. Only utf-8 is supported");n=t(n),this._streaming=!1,this._BOMseen=!1,this._decoder=null,this._fatal=Boolean(n.fatal),this._ignoreBOM=Boolean(n.ignoreBOM),Object.defineProperty(this,"encoding",{value:"utf-8"}),Object.defineProperty(this,"fatal",{value:this._fatal}),Object.defineProperty(this,"ignoreBOM",{value:this._ignoreBOM})}function a(e,n){if(!(this instanceof a))return new a(e,n);if((e=void 0!==e?String(e).toLowerCase():i)!==i)throw new Error("Encoding not supported. Only utf-8 is supported");n=t(n),this._streaming=!1,this._encoder=null,this._options={fatal:Boolean(n.fatal)},Object.defineProperty(this,"encoding",{value:"utf-8"})}function l(t){var n=t.fatal,i=0,s=0,a=0,l=128,d=191;this.handler=function(t,f){if(-1===f&&0!==a)return a=0,o(n);if(-1===f)return r;if(0===a){if(e(f,0,127))return f;if(e(f,194,223))a=1,i=f-192;else if(e(f,224,239))224===f&&(l=160),237===f&&(d=159),a=2,i=f-224;else{if(!e(f,240,244))return o(n);240===f&&(l=144),244===f&&(d=143),a=3,i=f-240}return i<<=6*a,null}if(!e(f,l,d))return i=a=s=0,l=128,d=191,t.prepend(f),o(n);if(l=128,d=191,i+=f-128<<6*(a-(s+=1)),s!==a)return null;var u=i;return i=a=s=0,u}}function d(t){t.fatal;this.handler=function(t,n){if(-1===n)return r;if(e(n,0,127))return n;var o,i;e(n,128,2047)?(o=1,i=192):e(n,2048,65535)?(o=2,i=224):e(n,65536,1114111)&&(o=3,i=240);for(var s=[(n>>6*o)+i];o>0;){var a=n>>6*(o-1);s.push(128|63&a),o-=1}return s}}function f(){if("undefined"!=typeof self)return self;if("undefined"!=typeof global)return global;throw new Error("No global found")}s.prototype={decode:function(e,o){var i;i="object"==typeof e&&e instanceof ArrayBuffer?new Uint8Array(e):"object"==typeof e&&"buffer"in e&&e.buffer instanceof ArrayBuffer?new Uint8Array(e.buffer,e.byteOffset,e.byteLength):new Uint8Array(0),o=t(o),this._streaming||(this._decoder=new l({fatal:this._fatal}),this._BOMseen=!1),this._streaming=Boolean(o.stream);for(var s,a=new n(i),d=[];!a.endOfStream()&&(s=this._decoder.handler(a,a.read()))!==r;)null!==s&&(Array.isArray(s)?d.push.apply(d,s):d.push(s));if(!this._streaming){do{if((s=this._decoder.handler(a,a.read()))===r)break;null!==s&&(Array.isArray(s)?d.push.apply(d,s):d.push(s))}while(!a.endOfStream());this._decoder=null}return d.length&&(-1===["utf-8"].indexOf(this.encoding)||this._ignoreBOM||this._BOMseen||(65279===d[0]?(this._BOMseen=!0,d.shift()):this._BOMseen=!0)),function(e){for(var t="",n=0;n<e.length;++n){var r=e[n];r<=65535?t+=String.fromCharCode(r):(r-=65536,t+=String.fromCharCode(55296+(r>>10),56320+(1023&r)))}return t}(d)}},a.prototype={encode:function(e,o){e=e?String(e):"",o=t(o),this._streaming||(this._encoder=new d(this._options)),this._streaming=Boolean(o.stream);for(var i,s=[],a=new n(function(e){for(var t=String(e),n=t.length,r=0,o=[];r<n;){var i=t.charCodeAt(r);if(i<55296||i>57343)o.push(i);else if(56320<=i&&i<=57343)o.push(65533);else if(55296<=i&&i<=56319)if(r===n-1)o.push(65533);else{var s=e.charCodeAt(r+1);if(56320<=s&&s<=57343){var a=1023&i,l=1023&s;o.push(65536+(a<<10)+l),r+=1}else o.push(65533)}r+=1}return o}(e));!a.endOfStream()&&(i=this._encoder.handler(a,a.read()))!==r;)Array.isArray(i)?s.push.apply(s,i):s.push(i);if(!this._streaming){for(;(i=this._encoder.handler(a,a.read()))!==r;)Array.isArray(i)?s.push.apply(s,i):s.push(i);this._encoder=null}return new Uint8Array(s)}},"function"!=typeof TextDecoder&&(f().TextDecoder=s),"function"!=typeof TextEncoder&&(f().TextEncoder=a)}();class PntsParser{constructor(){this.utf8Decoder=new TextDecoder("utf-8")}parse(e){if(!e)return Promise.reject(e);const t=new DataView(e);let n=0;const r={};let o={},i={};if(r.magic=this.utf8Decoder.decode(new Uint8Array(e,n,4)),n+=4,r.magic){if(r.version=t.getUint32(n,!0),n+=Uint32Array.BYTES_PER_ELEMENT,r.byteLength=t.getUint32(n,!0),n+=Uint32Array.BYTES_PER_ELEMENT,r.FTJSONLength=t.getUint32(n,!0),n+=Uint32Array.BYTES_PER_ELEMENT,r.FTBinaryLength=t.getUint32(n,!0),n+=Uint32Array.BYTES_PER_ELEMENT,r.BTJSONLength=t.getUint32(n,!0),n+=Uint32Array.BYTES_PER_ELEMENT,r.BTBinaryLength=t.getUint32(n,!0),n+=Uint32Array.BYTES_PER_ELEMENT,r.FTBinaryLength>0&&(i=this.parseFeatureBinary(e,n,r.FTJSONLength)),r.BTJSONLength>0){const t=28+r.FTJSONLength+r.FTBinaryLength;o=BatchTableParser.parse(e.slice(t,r.BTJSONLength+t))}const s={point:i,batchTable:o};return Promise.resolve(s)}throw new Error("Invalid pnts file.")}parseFeatureBinary(e,t,n){const r=new THREE.BufferGeometry,o=this.utf8Decoder.decode(new Uint8Array(e,t,n)),i=JSON.parse(o);let s;if(i.POINTS_LENGTH&&(s=i.POINTS_LENGTH),i.POSITION){const n=i.POSITION.byteOffset+o.length+t,a=new Float32Array(e,n,3*s);r.setAttribute("position",new THREE.BufferAttribute(a,3))}if(i.RGB){const n=i.RGB.byteOffset+o.length+t,a=new Uint8Array(e,n,3*s);r.setAttribute("color",new THREE.BufferAttribute(a,3,!0))}if(i.POSITION_QUANTIZED)throw new Error("For pnts loader, POSITION_QUANTIZED: not yet managed");if(i.RGBA)throw new Error("For pnts loader, RGBA: not yet managed");if(i.RGB565)throw new Error("For pnts loader, RGB565: not yet managed");if(i.NORMAL){const n=i.RGB.byteOffset+o.length+t,a=new Float32Array(e,n,3*s);r.setAttribute("normal",new THREE.BufferAttribute(a,3))}if(i.NORMAL_OCT16P)throw new Error("For pnts loader, NORMAL_OCT16P: not yet managed");if(i.BATCH_ID)throw new Error("For pnts loader, BATCH_ID: not yet managed");return{geometry:r,offset:i.RTC_CENTER?(new THREE.Vector3).fromArray(i.RTC_CENTER):void 0}}}CLOUD.PntsParser=PntsParser;class PntLoader{constructor(e,t){this.unitScale=t,this.pntParser=new PntsParser,this.utf8Decoder=new TextDecoder("utf-8"),this.tileIndexMap={},this.rootPntUrl=void 0,this.maximumScreenSpaceError=void 0===e?5:e,this.loadPromises=[],this.camera=void 0,this.globalInverseMatrix=new THREE.Matrix4,this.box=null,this.loadJsonPromises=[],this.pntFileList={}}loadPntTotalJson(e,t){let n=this;n.loadPromises=[],this.rootPntUrl=e.slice(0,e.lastIndexOf("/")+1);return new Promise((function(r,o){(new THREE.FileLoader).load(e,(function(e){let o=JSON.parse(e);n.box=o.root.boundingVolume.box,t&&t(n.box),n.parseTilesJson(o.root);var i=Promise.all(n.loadJsonPromises.map((function(e){return e.catch((function(e){return e}))}))),s=new CLOUD.TaskManager;i.then((e=>{let t=0;for(let e in n.tileIndexMap){let r=n.tileIndexMap[e];const o=e.slice(0,e.lastIndexOf("/")+1);for(let i in r.index){let a=o+r.index[i].content.url;s.addTask(t),n.pntFileList[t]={},n.pntFileList[t].jsonUrl=e,n.pntFileList[t].pntUrl=a,n.pntFileList[t].index=i,t++}}s.processTasks(n.loadPnt.bind(n),void 0,(function(e){var t={};for(let e in n.pntFileList){if(!((i=n.pntFileList[e].jsonUrl)in t)){let e=new CLOUD.ObjectGroup(CLOUD.ObjectGroupType.EXTRUDEBODYMANAGER,{pickableType:CLOUD.PICKABLETYPE.Geometry,globalSpace:!0});t[i]=e}n.tileIndexMap[i].index[n.pntFileList[e].index].points=n.pntFileList[e].result,t[i].add(n.pntFileList[e].result)}var o=[];for(var i in t)o.push(t[i]);r(o)}))}))}))}))}parseTilesJson(e){let t=this;if(e.content&&e.content.url){let n=this.rootPntUrl+e.content.url;t.loadTileSetJson(n)}if(e.children)for(let t of e.children)this.parseTilesJson(t)}loadTileSetJson(e){let t=this;const n=new Promise((function(n,r){var o=new THREE.FileLoader;o.setResponseType("json"),o.load(e,(function(o){if(null==o)r(o);else{let r=new ExtendTileset(o,"");t.tileIndexMap[e]=r;r.index[1],e.slice(0,e.lastIndexOf("/")+1);n(r)}}),void 0,(function(t){r(data),console.error(`Load ${e} failed, ${t}`)}))}));return t.loadJsonPromises.push(n),n}ProcessTileIndexs(e,t){this.camera=e,this.globalInverseMatrix=t;for(let e in this.tileIndexMap){const t=this.tileIndexMap[e].index[1];this.ProcessNode(t,e)}}ProcessNode(e,t,n){e.visible=!1;let r=this.tileIndexMap[t].index[e.tileId].points;if(null==r)return;r.visible=!1;if(this.$3dTilesSubdivisionControl(this.camera,e)&&(r.visible=!0,e.visible=!0),null!=n)if("ADD"===n.refine.toUpperCase());else{n.visible=!1,this.tileIndexMap[t].index[n.tileId].visible=!1}if(e.children)for(let n of e.children)this.ProcessNode(n,t,e)}$3dTilesSubdivisionControl(e,t){return this.computeNodeSSE(e,t)>this.maximumScreenSpaceError}computeNodeSSE(e,t){const n=new THREE.Box3,r=new THREE.Sphere;let o=e.position.clone();if(o.multiplyScalar(1/this.unitScale),o.applyMatrix4(this.globalInverseMatrix),t.distance=0,t.boundingVolume.region)n.copy(t.boundingVolume.region.box3D),n.applyMatrix4(t.boundingVolume.region.matrixWorld),t.distance=n.distanceToPoint(o);else if(t.boundingVolume.box)n.copy(t.boundingVolume.box),t.matrixWorld&&n.applyMatrix4(t.matrixWorld),t.distance=n.distanceToPoint(o);else{if(!t.boundingVolume.sphere)return 1/0;r.copy(t.boundingVolume.sphere),r.applyMatrix4(t.matrixWorld),t.distance=Math.max(0,r.distanceToPoint(o))}return 0===t.distance?1/0:(t.geometricError-0<1e-4&&(t.geometricError=1),e._preSSE*(t.geometricError/t.distance))}updatePreSse(e,t){const n=e.fov,r=THREE.Math.degToRad(n),o=t/(2*Math.tan(.5*r));e._preSSE=o}loadPnt(e,t){let n=this;var r=n.pntFileList[e].pntUrl;return new Promise((function(o,i){var s=new THREE.FileLoader;s.setResponseType("arraybuffer"),s.load(r,(function(o){if(void 0!==o){const i=n.utf8Decoder.decode(new Uint8Array(o,0,4));if("{"===i[0]){o=JSON.parse(n.utf8Decoder.decode(new Uint8Array(o)));r.slice(0,r.lastIndexOf("/")+1)}else if("b3dm"==i)console.log("b3dm is load");else{if("pnts"!=i)return Promise.reject(`Unsupported magic code ${i}`);console.log("pnts is load")}n.pntParser.parse(o).then((o=>{const i=new THREE.PointsMaterial({size:.05,vertexColors:THREE.VertexColors}),s=new THREE.Points(o.point.geometry,i);o.point.offset&&s.position.copy(o.point.offset),s.visible=!1,s.name=r,n.pntFileList[e].result=s,t()}))}}),void 0,(function(e){i(void 0)}))}))}loadPntByPromise(e){let t=this;return new Promise((function(n,r){var o=new THREE.FileLoader;o.setResponseType("arraybuffer"),o.load(e,(function(r){if(void 0!==r){const o=t.utf8Decoder.decode(new Uint8Array(r,0,4));if("{"===o[0]){r=JSON.parse(t.utf8Decoder.decode(new Uint8Array(r)));e.slice(0,e.lastIndexOf("/")+1)}else if("b3dm"==o)console.log("b3dm is load");else{if("pnts"!=o)return Promise.reject(`Unsupported magic code ${o}`);console.log("pnts is load")}t.pntParser.parse(r).then((t=>{const r=new THREE.PointsMaterial({size:.05,vertexColors:THREE.VertexColors}),o=new THREE.Points(t.point.geometry,r);t.point.offset&&o.position.copy(t.point.offset),o.visible=!1,o.name=e,n(o)}))}}),void 0,(function(e){r(void 0)}))}))}loadPntTotalJsonByPromise(e,t){let n=this;n.loadPromises=[],this.rootPntUrl=e.slice(0,e.lastIndexOf("/")+1);return new Promise((function(r,o){(new THREE.FileLoader).load(e,(function(e){let o=JSON.parse(e);n.box=o.root.boundingVolume.box,t&&t(n.box),n.parseTilesJson(o.root),Promise.all(n.loadJsonPromises.map((function(e){return e.catch((function(e){return e}))}))).then((e=>{var t=[];for(let e in n.tileIndexMap){n.urlPointGroupMap[e]={};let r=n.tileIndexMap[e];const o=e.slice(0,e.lastIndexOf("/")+1);for(let i in r.index){let s=o+r.index[i].content.url;t.push(n.loadPnt(s).then((t=>({url:e,context:{index:i,result:t}}))))}}Promise.all(t.map((function(e){return e.catch((function(e){return e}))}))).then((e=>{for(let e in n.urlPointGroupMap){let t=new CLOUD.ObjectGroup(CLOUD.ObjectGroupType.EXTRUDEBODYMANAGER,{pickableType:CLOUD.PICKABLETYPE.Geometry,globalSpace:!0});n.urlPointGroupMap[e]=t}for(const t of e){if(void 0!==t)n.tileIndexMap[t.url].index[t.context.index].points=t.context.result,n.urlPointGroupMap[t.url].add(t.context.result)}var t=[];for(var o in n.urlPointGroupMap)t.push(n.urlPointGroupMap[o]);r(t)}))})).catch((function(e){console.log("promise reject failed reason",e)}))}))}))}}CLOUD.PntLoader=PntLoader;