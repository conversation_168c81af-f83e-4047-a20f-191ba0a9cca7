!function(){if(void 0===THREE.SkinnedMeshEx){const e=new THREE.Vector3,t=new THREE.Vector4,r=new THREE.Vector4,a=new THREE.Vector3,n=new THREE.Matrix4;class i extends THREE.SkinnedMesh{constructor(e,t){super(e,t),this.isSkinnedMeshEx=!0,this.bindMatrixInverseReal=new THREE.Matrix4}getBindMatrixInverse(){return this.bindMatrixInverseReal}copy(e){return super.copy(e),this.isSkinnedMeshEx=e.isSkinnedMeshEx,this.bindMatrixInverseReal.copy(e.bindMatrixInverseReal),this}bind(e,t){this.skeleton&&(this.skeleton.associatedSkinnedMesh=null),super.bind(e,t),this.bindMatrixInverseReal.copy(this.bindMatrix).invert(),this.skeleton.associatedSkinnedMesh=this}updateMatrixWorld(e){THREE.Mesh.prototype.updateMatrixWorld.call(this,e),"attached"===this.bindMode?(this.bindMatrixInverseReal.copy(this.matrixWorld).invert(),this.bindMatrixInverse.identity()):"detached"===this.bindMode?(this.bindMatrixInverse.copy(this.bindMatrix).invert(),this.bindMatrixInverseReal.copy(this.bindMatrix).invert()):console.warn("THREE.SkinnedMesh: Unrecognized bindMode: "+this.bindMode)}boneTransform(i,o){const s=this.skeleton,u=this.geometry;t.fromBufferAttribute(u.attributes.skinIndex,i),r.fromBufferAttribute(u.attributes.skinWeight,i),e.fromBufferAttribute(u.attributes.position,i).applyMatrix4(this.bindMatrix),o.set(0,0,0);for(let i=0;i<4;i++){const u=r.getComponent(i);if(0!==u){const r=t.getComponent(i);n.multiplyMatrices(s.bones[r].matrixWorld,s.boneInverses[r]),o.addScaledVector(a.copy(e).applyMatrix4(n),u)}}return o.applyMatrix4(this.bindMatrixInverseReal)}}THREE.SkinnedMeshEx=i}if(void 0===THREE.SkeletonEx){const e=new THREE.Matrix4,t=new THREE.Matrix4;class r extends THREE.Skeleton{constructor(e=[],t=[]){super(e,t),this.associatedSkinnedMesh=null}clone(){return new r(this.bones,this.boneInverses)}getBindMatrixInverse(){return this.associatedSkinnedMesh?this.associatedSkinnedMesh.getBindMatrixInverse():void 0}update(){const r=this.bones,a=this.boneInverses,n=this.boneMatrices,i=this.boneTexture,o=this.getBindMatrixInverse();for(let i=0,s=r.length;i<s;i++){const s=r[i]?r[i].matrixWorld:t;e.multiplyMatrices(s,a[i]),o&&e.premultiply(o),e.toArray(n,16*i)}null!==i&&(i.needsUpdate=!0)}}THREE.SkeletonEx=r}}(),THREE.FBXLoader=function(){var e,t,r;function a(e){THREE.Loader.call(this,e)}function n(e,t){this.textureLoader=e,this.manager=t,this.imageNum=0,this.loadTextureCount=0,this.onLoad=null}function i(){}function o(){}function s(){}function u(){}function l(e,t){this.dv=new DataView(e),this.offset=0,this.littleEndian=void 0===t||t}function c(){}function p(e){var t=e.match(/FBXVersion: (\d+)/);if(t)return parseInt(t[1]);throw new Error("THREE.FBXLoader: Cannot find the version number for the file given.")}function h(e){return e/46186158e3}a.prototype=Object.assign(Object.create(THREE.Loader.prototype),{constructor:a,load:function(e,t,r,a){var n=this,i=""===n.path?THREE.LoaderUtils.extractUrlBase(e):n.path,o=new THREE.FileLoader(this.manager);o.setPath(n.path),o.setResponseType("arraybuffer"),o.setRequestHeader(n.requestHeader),o.setWithCredentials(n.withCredentials),o.load(e,(function(r){try{n.parse(r,i,t)}catch(t){a?a(t):console.error(t),n.manager.itemError(e)}}),r,a)},parse:function(t,r,a){if(l="Kaydara FBX Binary  \0",(o=t).byteLength>=l.length&&l===T(o,0,l.length))e=(new u).parse(t);else{var i=T(t);if(!function(e){var t=["K","a","y","d","a","r","a","\\","F","B","X","\\","B","i","n","a","r","y","\\","\\"],r=0;function a(t){var a=e[t-1];return e=e.slice(r+t),r++,a}for(var n=0;n<t.length;++n){if(a(1)===t[n])return!1}return!0}(i))throw new Error("THREE.FBXLoader: Unknown format.");if(p(i)<7e3)throw new Error("THREE.FBXLoader: FBX version not supported, FileVersion: "+p(i));e=(new s).parse(i)}var o,l;return new n(new THREE.TextureLoader(this.manager).setPath(this.resourcePath||r).setCrossOrigin(this.crossOrigin),this.manager).parse(e,a)}}),n.prototype={constructor:n,parse:function(e,r){t=this.parseConnections(),this.onLoad=r;var a=this.parseImages();0==this.imageNum?this.onLoadTexture():this.parseTextures(a)},onLoadTexture:function(){if(this.loadTextureCount++,this.loadTextureCount>=this.imageNum){var e=this.parseMaterials(this.textures),t=this.parseDeformers(),a=(new i).parse(t);this.parseScene(t,a,e),this.onLoad&&this.onLoad(r)}},parseConnections:function(){var t=new Map;"Connections"in e&&e.Connections.connections.forEach((function(e){var r=e[0],a=e[1],n=e[2];t.has(r)||t.set(r,{parents:[],children:[]});var i={ID:a,relationship:n};t.get(r).parents.push(i),t.has(a)||t.set(a,{parents:[],children:[]});var o={ID:r,relationship:n};t.get(a).children.push(o)}));return t},parseImages:function(){var t={},r={};if(this.imageNum=0,"Video"in e.Objects){var a=e.Objects.Video;for(var n in a){var i=a[n];if(t[l=parseInt(n)]=i.RelativeFilename||i.Filename,this.imageNum++,"Content"in i){var o=i.Content instanceof ArrayBuffer&&i.Content.byteLength>0,s="string"==typeof i.Content&&""!==i.Content;if(o||s){var u=this.parseImage(a[n]);r[i.RelativeFilename||i.Filename]=u}}}}for(var l in t){var c=t[l];void 0!==r[c]?t[l]=r[c]:t[l]=t[l].split("\\").pop()}return t},parseImage:function(e){var t,r=e.Content,a=e.RelativeFilename||e.Filename,n=a.slice(a.lastIndexOf(".")+1).toLowerCase();switch(n){case"bmp":t="image/bmp";break;case"jpg":case"jpeg":t="image/jpeg";break;case"png":t="image/png";break;case"tif":t="image/tiff";break;case"tga":null===this.manager.getHandler(".tga")&&console.warn("FBXLoader: TGA loader not found, skipping ",a),t="image/tga";break;default:return void console.warn('FBXLoader: Image type "'+n+'" is not supported.')}if("string"==typeof r)return"data:"+t+";base64,"+r;var i=new Uint8Array(r);return window.URL.createObjectURL(new Blob([i],{type:t}))},parseTextures:function(t){if(this.textures=new Map,"Texture"in e.Objects){var r=e.Objects.Texture;for(var a in r){var n=this.parseTexture(r[a],t);this.textures.set(parseInt(a),n)}}return this.textures},parseTexture:function(e,t){var r=this.loadTexture(e,t);r.ID=e.id,r.name=e.attrName;var a=e.WrapModeU,n=e.WrapModeV,i=void 0!==a?a.value:0,o=void 0!==n?n.value:0;if(r.wrapS=0===i?THREE.RepeatWrapping:THREE.ClampToEdgeWrapping,r.wrapT=0===o?THREE.RepeatWrapping:THREE.ClampToEdgeWrapping,"Scaling"in e){var s=e.Scaling.value;r.repeat.x=s[0],r.repeat.y=s[1]}return r},loadTexture:function(e,r){var a,n,i=this.textureLoader.path,o=this,s=t.get(e.id).children;void 0!==s&&s.length>0&&void 0!==r[s[0].ID]&&(0!==(a=r[s[0].ID]).indexOf("blob:")&&0!==a.indexOf("data:")||this.textureLoader.setPath(void 0));var u=e.FileName.slice(-3).toLowerCase();if("tga"===u){var l=this.manager.getHandler(".tga");null===l?(console.warn("FBXLoader: TGA loader not found, creating placeholder texture for",e.RelativeFilename),n=null,o.onLoadTexture()):n=l.load(a,(function(){o.onLoadTexture()}),void 0,(function(){o.textures.set(e.id,null),o.onLoadTexture()}))}else"psd"===u?(console.warn("FBXLoader: PSD textures are not supported, creating placeholder texture for",e.RelativeFilename),n=null,o.onLoadTexture()):n=this.textureLoader.load(a,(function(){o.onLoadTexture()}),void 0,(function(){o.textures.set(e.id,null),o.onLoadTexture()}));return this.textureLoader.setPath(i),n},parseMaterials:function(t){var r=new Map;if("Material"in e.Objects){var a=e.Objects.Material;for(var n in a){var i=this.parseMaterial(a[n],t);null!==i&&r.set(parseInt(n),i)}}return r},parseMaterial:function(e,r){var a=e.id,n=e.attrName,i=e.ShadingModel;if("object"==typeof i&&(i=i.value),!t.has(a))return null;var o,s=this.parseParameters(e,r,a);switch(i.toLowerCase()){case"phong":o=new THREE.MeshPhongMaterial;break;case"lambert":o=new THREE.MeshLambertMaterial;break;default:console.warn('THREE.FBXLoader: unknown material type "%s". Defaulting to MeshPhongMaterial.',i),o=new THREE.MeshPhongMaterial}return o.setValues(s),o.name=n,o},parseParameters:function(e,r,a){var n={};e.BumpFactor&&(n.bumpScale=e.BumpFactor.value),e.Diffuse?n.color=(new THREE.Color).fromArray(e.Diffuse.value):!e.DiffuseColor||"Color"!==e.DiffuseColor.type&&"ColorRGB"!==e.DiffuseColor.type||(n.color=(new THREE.Color).fromArray(e.DiffuseColor.value)),e.DisplacementFactor&&(n.displacementScale=e.DisplacementFactor.value),e.Emissive?n.emissive=(new THREE.Color).fromArray(e.Emissive.value):!e.EmissiveColor||"Color"!==e.EmissiveColor.type&&"ColorRGB"!==e.EmissiveColor.type||(n.emissive=(new THREE.Color).fromArray(e.EmissiveColor.value)),e.EmissiveFactor&&(n.emissiveIntensity=parseFloat(e.EmissiveFactor.value)),e.Opacity&&(n.opacity=parseFloat(e.Opacity.value)),n.opacity<1&&(n.transparent=!0),e.ReflectionFactor&&(n.reflectivity=e.ReflectionFactor.value),e.Shininess&&(n.shininess=e.Shininess.value),e.Specular?n.specular=(new THREE.Color).fromArray(e.Specular.value):e.SpecularColor&&"Color"===e.SpecularColor.type&&(n.specular=(new THREE.Color).fromArray(e.SpecularColor.value));var i=this;return t.get(a).children.forEach((function(e){var t=e.relationship;switch(t){case"Bump":n.bumpMap=i.getTexture(r,e.ID);break;case"Maya|TEX_ao_map":n.aoMap=i.getTexture(r,e.ID);break;case"DiffuseColor":case"Maya|TEX_color_map":n.map=i.getTexture(r,e.ID),n.map&&(n.map.encoding=THREE.sRGBEncoding);break;case"DisplacementColor":n.displacementMap=i.getTexture(r,e.ID);break;case"EmissiveColor":n.emissiveMap=i.getTexture(r,e.ID),n.emissiveMap&&(n.emissiveMap.encoding=THREE.sRGBEncoding);break;case"NormalMap":case"Maya|TEX_normal_map":n.normalMap=i.getTexture(r,e.ID);break;case"ReflectionColor":n.envMap=i.getTexture(r,e.ID),n.envMap&&(n.envMap.mapping=THREE.EquirectangularReflectionMapping,n.envMap.encoding=THREE.sRGBEncoding);break;case"SpecularColor":n.specularMap=i.getTexture(r,e.ID),n.specularMap&&(n.specularMap.encoding=THREE.sRGBEncoding);break;case"TransparentColor":case"TransparencyFactor":n.alphaMap=i.getTexture(r,e.ID),n.transparent=!0;break;default:console.warn("THREE.FBXLoader: %s map is not supported in three.js, skipping texture.",t)}})),n},getTexture:function(r,a){return r?("LayeredTexture"in e.Objects&&a in e.Objects.LayeredTexture&&(console.warn("THREE.FBXLoader: layered textures are not supported in three.js. Discarding all but first layer."),a=t.get(a).children[0].ID),r.get(a)):null},parseDeformers:function(){var r={},a={};if("Deformer"in e.Objects){var n=e.Objects.Deformer;for(var i in n){var o=n[i],s=t.get(parseInt(i));if("Skin"===o.attrType){var u=this.parseSkeleton(s,n);u.ID=i,s.parents.length>1&&console.warn("THREE.FBXLoader: skeleton attached to more than one geometry is not supported."),u.geometryID=s.parents[0].ID,r[i]=u}else if("BlendShape"===o.attrType){var l={id:i};l.rawTargets=this.parseMorphTargets(s,n),l.id=i,s.parents.length>1&&console.warn("THREE.FBXLoader: morph target attached to more than one geometry is not supported."),a[i]=l}}}return{skeletons:r,morphTargets:a}},parseSkeleton:function(e,t){var r=[];return e.children.forEach((function(e){var a=t[e.ID];if("Cluster"===a.attrType){var n={ID:e.ID,indices:[],weights:[],transformLink:(new THREE.Matrix4).fromArray(a.TransformLink.a)};"Indexes"in a&&(n.indices=a.Indexes.a,n.weights=a.Weights.a),r.push(n)}})),{rawBones:r,bones:[]}},parseMorphTargets:function(e,r){for(var a=[],n=0;n<e.children.length;n++){var i=e.children[n],o=r[i.ID],s={name:o.attrName,initialWeight:o.DeformPercent,id:o.id,fullWeights:o.FullWeights.a};if("BlendShapeChannel"!==o.attrType)return;s.geoID=t.get(parseInt(i.ID)).children.filter((function(e){return void 0===e.relationship}))[0].ID,a.push(s)}return a},parseScene:function(a,n,i){r=new THREE.Group;var s=this.parseModels(a.skeletons,n,i),u=e.Objects.Model,l=this;s.forEach((function(e){var a=u[e.ID];l.setLookAtProperties(e,a),t.get(e.ID).parents.forEach((function(t){var r=s.get(t.ID);void 0!==r&&r.add(e)})),null===e.parent&&r.add(e)})),this.bindSkeleton(a.skeletons,n,s),this.createAmbientLight(),this.setupMorphMaterials(),r.traverse((function(e){if(e.userData.transformData){e.parent&&(e.userData.transformData.parentMatrix=e.parent.matrix,e.userData.transformData.parentMatrixWorld=e.parent.matrixWorld);var t=g(e.userData.transformData);e.applyMatrix4(t),e.updateWorldMatrix()}}));var c=(new o).parse();1===r.children.length&&r.children[0].isGroup&&(r.children[0].animations=c,r=r.children[0]),r.animations=c},parseModels:function(r,a,n){var i=new Map,o=e.Objects.Model;for(var s in o){var u=parseInt(s),l=o[s],c=t.get(u),p=this.buildSkeleton(c,r,u,l.attrName);if(!p){switch(l.attrType){case"Camera":p=this.createCamera(c);break;case"Light":p=this.createLight(c);break;case"Mesh":p=this.createMesh(c,a,n);break;case"NurbsCurve":p=this.createCurve(c,a);break;case"LimbNode":case"Root":p=new THREE.Bone;break;default:p=new THREE.Group}p.name=l.attrName?THREE.PropertyBinding.sanitizeNodeName(l.attrName):"",p.ID=u}this.getTransformData(p,l),i.set(u,p)}return i},buildSkeleton:function(e,t,r,a){var n=null;return e.parents.forEach((function(e){for(var i in t){var o=t[i];o.rawBones.forEach((function(t,i){if(t.ID===e.ID){var s=n;(n=new THREE.Bone).matrixWorld.copy(t.transformLink),n.name=a?THREE.PropertyBinding.sanitizeNodeName(a):"",n.ID=r,o.bones[i]=n,null!==s&&n.add(s)}}))}})),n},createCamera:function(t){var r,a;if(t.children.forEach((function(t){var r=e.Objects.NodeAttribute[t.ID];void 0!==r&&(a=r)})),void 0===a)r=new THREE.Object3D;else{var n=0;void 0!==a.CameraProjectionType&&1===a.CameraProjectionType.value&&(n=1);var i=1;void 0!==a.NearPlane&&(i=a.NearPlane.value/1e3);var o=1e3;void 0!==a.FarPlane&&(o=a.FarPlane.value/1e3);var s=window.innerWidth,u=window.innerHeight;void 0!==a.AspectWidth&&void 0!==a.AspectHeight&&(s=a.AspectWidth.value,u=a.AspectHeight.value);var l=s/u,c=45;void 0!==a.FieldOfView&&(c=a.FieldOfView.value);var p=a.FocalLength?a.FocalLength.value:null;switch(n){case 0:r=new THREE.PerspectiveCamera(c,l,i,o),null!==p&&r.setFocalLength(p);break;case 1:r=new THREE.OrthographicCamera(-s/2,s/2,u/2,-u/2,i,o);break;default:console.warn("THREE.FBXLoader: Unknown camera type "+n+"."),r=new THREE.Object3D}}return r},createLight:function(t){var r,a;if(t.children.forEach((function(t){var r=e.Objects.NodeAttribute[t.ID];void 0!==r&&(a=r)})),void 0===a)r=new THREE.Object3D;else{var n;n=void 0===a.LightType?0:a.LightType.value;var i=16777215;void 0!==a.Color&&(i=(new THREE.Color).fromArray(a.Color.value));var o=void 0===a.Intensity?1:a.Intensity.value/100;void 0!==a.CastLightOnObject&&0===a.CastLightOnObject.value&&(o=0);var s=0;void 0!==a.FarAttenuationEnd&&(s=void 0!==a.EnableFarAttenuation&&0===a.EnableFarAttenuation.value?0:a.FarAttenuationEnd.value);switch(n){case 0:r=new THREE.PointLight(i,o,s,1);break;case 1:r=new THREE.DirectionalLight(i,o);break;case 2:var u=Math.PI/3;void 0!==a.InnerAngle&&(u=THREE.MathUtils.degToRad(a.InnerAngle.value));var l=0;void 0!==a.OuterAngle&&(l=THREE.MathUtils.degToRad(a.OuterAngle.value),l=Math.max(l,1)),r=new THREE.SpotLight(i,o,s,u,l,1);break;default:console.warn("THREE.FBXLoader: Unknown light type "+a.LightType.value+", defaulting to a THREE.PointLight."),r=new THREE.PointLight(i,o)}void 0!==a.CastShadows&&1===a.CastShadows.value&&(r.castShadow=!0)}return r},createMesh:function(e,t,r){var a,n=null,i=null,o=[];return e.children.forEach((function(e){t.has(e.ID)&&(n=t.get(e.ID)),r.has(e.ID)&&o.push(r.get(e.ID))})),o.length>1?i=o:o.length>0?i=o[0]:(i=new THREE.MeshPhongMaterial({color:13421772}),o.push(i)),"color"in n.attributes&&o.forEach((function(e){e.vertexColors=!0})),n.FBX_Deformer?(o.forEach((function(e){e.skinning=!0})),(a=new THREE.SkinnedMeshEx(n,i)).normalizeSkinWeights()):a=new THREE.Mesh(n,i),a},createCurve:function(e,t){var r=e.children.reduce((function(e,r){return t.has(r.ID)&&(e=t.get(r.ID)),e}),null),a=new THREE.LineBasicMaterial({color:3342591,linewidth:1});return new THREE.Line(r,a)},getTransformData:function(e,t){var r={};"InheritType"in t&&(r.inheritType=parseInt(t.InheritType.value)),r.eulerOrder="RotationOrder"in t?E(t.RotationOrder.value):"ZYX","Lcl_Translation"in t&&(r.translation=t.Lcl_Translation.value),"PreRotation"in t&&(r.preRotation=t.PreRotation.value),"Lcl_Rotation"in t&&(r.rotation=t.Lcl_Rotation.value),"PostRotation"in t&&(r.postRotation=t.PostRotation.value),"Lcl_Scaling"in t&&(r.scale=t.Lcl_Scaling.value),"ScalingOffset"in t&&(r.scalingOffset=t.ScalingOffset.value),"ScalingPivot"in t&&(r.scalingPivot=t.ScalingPivot.value),"RotationOffset"in t&&(r.rotationOffset=t.RotationOffset.value),"RotationPivot"in t&&(r.rotationPivot=t.RotationPivot.value),e.userData.transformData=r},setLookAtProperties:function(a,n){"LookAtProperty"in n&&t.get(a.ID).children.forEach((function(t){if("LookAtProperty"===t.relationship){var n=e.Objects.Model[t.ID];if("Lcl_Translation"in n){var i=n.Lcl_Translation.value;void 0!==a.target?(a.target.position.fromArray(i),r.add(a.target)):a.lookAt((new THREE.Vector3).fromArray(i))}}}))},bindSkeleton:function(e,r,a){var n=this.parsePoseNodes();for(var i in e){var o=e[i];t.get(parseInt(o.ID)).parents.forEach((function(e){if(r.has(e.ID)){var i=e.ID;t.get(i).parents.forEach((function(e){a.has(e.ID)&&a.get(e.ID).bind(new THREE.SkeletonEx(o.bones),n[e.ID])}))}}))}},parsePoseNodes:function(){var t={};if("Pose"in e.Objects){var r=e.Objects.Pose;for(var a in r)if("BindPose"===r[a].attrType){var n=r[a].PoseNode;Array.isArray(n)?n.forEach((function(e){t[e.Node]=(new THREE.Matrix4).fromArray(e.Matrix.a)})):t[n.Node]=(new THREE.Matrix4).fromArray(n.Matrix.a)}}return t},createAmbientLight:function(){if("GlobalSettings"in e&&"AmbientColor"in e.GlobalSettings){var t=e.GlobalSettings.AmbientColor.value,a=t[0],n=t[1],i=t[2];if(0!==a||0!==n||0!==i){var o=new THREE.Color(a,n,i);r.add(new THREE.AmbientLight(o,1))}}},setupMorphMaterials:function(){var e=this;r.traverse((function(t){t.isMesh&&t.geometry.morphAttributes.position&&t.geometry.morphAttributes.position.length&&(Array.isArray(t.material)?t.material.forEach((function(r,a){e.setupMorphMaterial(t,r,a)})):e.setupMorphMaterial(t,t.material))}))},setupMorphMaterial:function(e,t,a){var n=e.uuid,i=t.uuid,o=!1;if(r.traverse((function(e){e.isMesh&&(Array.isArray(e.material)?e.material.forEach((function(t){t.uuid===i&&e.uuid!==n&&(o=!0)})):e.material.uuid===i&&e.uuid!==n&&(o=!0))})),!0===o){var s=t.clone();s.morphTargets=!0,void 0===a?e.material=s:e.material[a]=s}else t.morphTargets=!0}},i.prototype={constructor:i,parse:function(r){var a=new Map;if("Geometry"in e.Objects){var n=e.Objects.Geometry;for(var i in n){var o=t.get(parseInt(i)),s=this.parseGeometry(o,n[i],r);a.set(parseInt(i),s)}}return a},parseGeometry:function(e,t,r){switch(t.attrType){case"Mesh":return this.parseMeshGeometry(e,t,r);case"NurbsCurve":return this.parseNurbsGeometry(t)}},parseMeshGeometry:function(t,r,a){var n=a.skeletons,i=[],o=t.parents.map((function(t){return e.Objects.Model[t.ID]}));if(0!==o.length){var s=t.children.reduce((function(e,t){return void 0!==n[t.ID]&&(e=n[t.ID]),e}),null);t.children.forEach((function(e){void 0!==a.morphTargets[e.ID]&&i.push(a.morphTargets[e.ID])}));var u=o[0],l={};"RotationOrder"in u&&(l.eulerOrder=E(u.RotationOrder.value)),"InheritType"in u&&(l.inheritType=parseInt(u.InheritType.value)),"GeometricTranslation"in u&&(l.translation=u.GeometricTranslation.value),"GeometricRotation"in u&&(l.rotation=u.GeometricRotation.value),"GeometricScaling"in u&&(l.scale=u.GeometricScaling.value);var c=g(l);return this.genGeometry(r,s,i,c)}},genGeometry:function(e,t,r,a){var n=new THREE.BufferGeometry;e.attrName&&(n.name=e.attrName);var i=this.parseGeoNode(e,t),o=this.genBuffers(i),s=new THREE.Float32BufferAttribute(o.vertex,3);if(s.applyMatrix4(a),n.setAttribute("position",s),o.colors.length>0&&n.setAttribute("color",new THREE.Float32BufferAttribute(o.colors,3)),t&&(n.setAttribute("skinIndex",new THREE.Uint16BufferAttribute(o.weightsIndices,4)),n.setAttribute("skinWeight",new THREE.Float32BufferAttribute(o.vertexWeights,4)),n.FBX_Deformer=t),o.normal.length>0){var u=(new THREE.Matrix3).getNormalMatrix(a),l=new THREE.Float32BufferAttribute(o.normal,3);l.applyNormalMatrix(u),n.setAttribute("normal",l)}if(o.uvs.forEach((function(e,t){var r="uv"+(t+1).toString();0===t&&(r="uv"),n.setAttribute(r,new THREE.Float32BufferAttribute(o.uvs[t],2))})),i.material&&"AllSame"!==i.material.mappingType){var c=o.materialIndex[0],p=0;if(o.materialIndex.forEach((function(e,t){e!==c&&(n.addGroup(p,t-p,c),c=e,p=t)})),n.groups.length>0){var h=n.groups[n.groups.length-1],d=h.start+h.count;d!==o.materialIndex.length&&n.addGroup(d,o.materialIndex.length-d,c)}0===n.groups.length&&n.addGroup(0,o.materialIndex.length,o.materialIndex[0])}return this.addMorphTargets(n,e,r,a),n},parseGeoNode:function(e,t){var r={};if(r.vertexPositions=void 0!==e.Vertices?e.Vertices.a:[],r.vertexIndices=void 0!==e.PolygonVertexIndex?e.PolygonVertexIndex.a:[],e.LayerElementColor&&(r.color=this.parseVertexColors(e.LayerElementColor[0])),e.LayerElementMaterial&&(r.material=this.parseMaterialIndices(e.LayerElementMaterial[0])),e.LayerElementNormal&&(r.normal=this.parseNormals(e.LayerElementNormal[0])),e.LayerElementUV){r.uv=[];for(var a=0;e.LayerElementUV[a];)e.LayerElementUV[a].UV&&r.uv.push(this.parseUVs(e.LayerElementUV[a])),a++}return r.weightTable={},null!==t&&(r.skeleton=t,t.rawBones.forEach((function(e,t){e.indices.forEach((function(a,n){void 0===r.weightTable[a]&&(r.weightTable[a]=[]),r.weightTable[a].push({id:t,weight:e.weights[n]})}))}))),r},genBuffers:function(e){var t={vertex:[],normal:[],colors:[],uvs:[],materialIndex:[],vertexWeights:[],weightsIndices:[]},r=0,a=0,n=!1,i=[],o=[],s=[],u=[],l=[],c=[],p=this;return e.vertexIndices.forEach((function(h,d){var v=!1;h<0&&(h^=-1,v=!0);var m=[],g=[];if(i.push(3*h,3*h+1,3*h+2),e.color){var E=f(d,r,h,e.color);s.push(E[0],E[1],E[2])}if(e.skeleton){if(void 0!==e.weightTable[h]&&e.weightTable[h].forEach((function(e){g.push(e.weight),m.push(e.id)})),g.length>4){n||(console.warn("THREE.FBXLoader: Vertex has more than 4 skinning weights assigned to vertex. Deleting additional weights."),n=!0);var y=[0,0,0,0],T=[0,0,0,0];g.forEach((function(e,t){var r=e,a=m[t];T.forEach((function(e,t,n){if(r>e){n[t]=r,r=e;var i=y[t];y[t]=a,a=i}}))})),m=y,g=T}for(;g.length<4;)g.push(0),m.push(0);for(var R=0;R<4;++R)l.push(g[R]),c.push(m[R])}if(e.normal){E=f(d,r,h,e.normal);o.push(E[0],E[1],E[2])}if(e.material&&"AllSame"!==e.material.mappingType)var x=f(d,r,h,e.material)[0];e.uv&&e.uv.forEach((function(e,t){var a=f(d,r,h,e);void 0===u[t]&&(u[t]=[]),u[t].push(a[0]),u[t].push(a[1])})),a++,v&&(p.genFace(t,e,i,x,o,s,u,l,c,a),r++,a=0,i=[],o=[],s=[],u=[],l=[],c=[])})),t},genFace:function(e,t,r,a,n,i,o,s,u,l){for(var c=2;c<l;c++)e.vertex.push(t.vertexPositions[r[0]]),e.vertex.push(t.vertexPositions[r[1]]),e.vertex.push(t.vertexPositions[r[2]]),e.vertex.push(t.vertexPositions[r[3*(c-1)]]),e.vertex.push(t.vertexPositions[r[3*(c-1)+1]]),e.vertex.push(t.vertexPositions[r[3*(c-1)+2]]),e.vertex.push(t.vertexPositions[r[3*c]]),e.vertex.push(t.vertexPositions[r[3*c+1]]),e.vertex.push(t.vertexPositions[r[3*c+2]]),t.skeleton&&(e.vertexWeights.push(s[0]),e.vertexWeights.push(s[1]),e.vertexWeights.push(s[2]),e.vertexWeights.push(s[3]),e.vertexWeights.push(s[4*(c-1)]),e.vertexWeights.push(s[4*(c-1)+1]),e.vertexWeights.push(s[4*(c-1)+2]),e.vertexWeights.push(s[4*(c-1)+3]),e.vertexWeights.push(s[4*c]),e.vertexWeights.push(s[4*c+1]),e.vertexWeights.push(s[4*c+2]),e.vertexWeights.push(s[4*c+3]),e.weightsIndices.push(u[0]),e.weightsIndices.push(u[1]),e.weightsIndices.push(u[2]),e.weightsIndices.push(u[3]),e.weightsIndices.push(u[4*(c-1)]),e.weightsIndices.push(u[4*(c-1)+1]),e.weightsIndices.push(u[4*(c-1)+2]),e.weightsIndices.push(u[4*(c-1)+3]),e.weightsIndices.push(u[4*c]),e.weightsIndices.push(u[4*c+1]),e.weightsIndices.push(u[4*c+2]),e.weightsIndices.push(u[4*c+3])),t.color&&(e.colors.push(i[0]),e.colors.push(i[1]),e.colors.push(i[2]),e.colors.push(i[3*(c-1)]),e.colors.push(i[3*(c-1)+1]),e.colors.push(i[3*(c-1)+2]),e.colors.push(i[3*c]),e.colors.push(i[3*c+1]),e.colors.push(i[3*c+2])),t.material&&"AllSame"!==t.material.mappingType&&(e.materialIndex.push(a),e.materialIndex.push(a),e.materialIndex.push(a)),t.normal&&(e.normal.push(n[0]),e.normal.push(n[1]),e.normal.push(n[2]),e.normal.push(n[3*(c-1)]),e.normal.push(n[3*(c-1)+1]),e.normal.push(n[3*(c-1)+2]),e.normal.push(n[3*c]),e.normal.push(n[3*c+1]),e.normal.push(n[3*c+2])),t.uv&&t.uv.forEach((function(t,r){void 0===e.uvs[r]&&(e.uvs[r]=[]),e.uvs[r].push(o[r][0]),e.uvs[r].push(o[r][1]),e.uvs[r].push(o[r][2*(c-1)]),e.uvs[r].push(o[r][2*(c-1)+1]),e.uvs[r].push(o[r][2*c]),e.uvs[r].push(o[r][2*c+1])}))},addMorphTargets:function(t,r,a,n){if(0!==a.length){t.morphTargetsRelative=!0,t.morphAttributes.position=[];var i=this;a.forEach((function(a){a.rawTargets.forEach((function(a){var o=e.Objects.Geometry[a.geoID];void 0!==o&&i.genMorphGeometry(t,r,o,n,a.name)}))}))}},genMorphGeometry:function(e,t,r,a,n){for(var i=void 0!==t.PolygonVertexIndex?t.PolygonVertexIndex.a:[],o=void 0!==r.Vertices?r.Vertices.a:[],s=void 0!==r.Indexes?r.Indexes.a:[],u=3*e.attributes.position.count,l=new Float32Array(u),c=0;c<s.length;c++){var p=3*s[c];l[p]=o[3*c],l[p+1]=o[3*c+1],l[p+2]=o[3*c+2]}var h={vertexIndices:i,vertexPositions:l},d=this.genBuffers(h),f=new THREE.Float32BufferAttribute(d.vertex,3);f.name=n||r.attrName,f.applyMatrix4(a),e.morphAttributes.position.push(f)},parseNormals:function(e){var t=e.MappingInformationType,r=e.ReferenceInformationType,a=e.Normals.a,n=[];return"IndexToDirect"===r&&("NormalIndex"in e?n=e.NormalIndex.a:"NormalsIndex"in e&&(n=e.NormalsIndex.a)),{dataSize:3,buffer:a,indices:n,mappingType:t,referenceType:r}},parseUVs:function(e){var t=e.MappingInformationType,r=e.ReferenceInformationType,a=e.UV.a,n=[];return"IndexToDirect"===r&&(n=e.UVIndex.a),{dataSize:2,buffer:a,indices:n,mappingType:t,referenceType:r}},parseVertexColors:function(e){var t=e.MappingInformationType,r=e.ReferenceInformationType,a=e.Colors.a,n=[];return"IndexToDirect"===r&&(n=e.ColorIndex.a),{dataSize:4,buffer:a,indices:n,mappingType:t,referenceType:r}},parseMaterialIndices:function(e){var t=e.MappingInformationType,r=e.ReferenceInformationType;if("NoMappingInformation"===t)return{dataSize:1,buffer:[0],indices:[0],mappingType:"AllSame",referenceType:r};for(var a=e.Materials.a,n=[],i=0;i<a.length;++i)n.push(i);return{dataSize:1,buffer:a,indices:n,mappingType:t,referenceType:r}},parseNurbsGeometry:function(e){if(void 0===THREE.NURBSCurve)return console.error("THREE.FBXLoader: The loader relies on THREE.NURBSCurve for any nurbs present in the model. Nurbs will show up as empty geometry."),new THREE.BufferGeometry;var t=parseInt(e.Order);if(isNaN(t))return console.error("THREE.FBXLoader: Invalid Order %s given for geometry ID: %s",e.Order,e.id),new THREE.BufferGeometry;for(var r,a,n=t-1,i=e.KnotVector.a,o=[],s=e.Points.a,u=0,l=s.length;u<l;u+=4)o.push((new THREE.Vector4).fromArray(s,u));if("Closed"===e.Form)o.push(o[0]);else if("Periodic"===e.Form){r=n,a=i.length-1-r;for(u=0;u<n;++u)o.push(o[u])}var c=new THREE.NURBSCurve(n,i,o,r,a).getPoints(7*o.length),p=new Float32Array(3*c.length);c.forEach((function(e,t){e.toArray(p,3*t)}));var h=new THREE.BufferGeometry;return h.setAttribute("position",new THREE.BufferAttribute(p,3)),h}},o.prototype={constructor:o,parse:function(){var e=[],t=this.parseClips();if(void 0!==t)for(var r in t){var a=t[r],n=this.addClip(a);e.push(n)}return e},parseClips:function(){if(void 0!==e.Objects.AnimationCurve){var t=this.parseAnimationCurveNodes();this.parseAnimationCurves(t);var r=this.parseAnimationLayers(t);return this.parseAnimStacks(r)}},parseAnimationCurveNodes:function(){var t=e.Objects.AnimationCurveNode,r=new Map;for(var a in t){var n=t[a];if(null!==n.attrName.match(/S|R|T|DeformPercent/)){var i={id:n.id,attr:n.attrName,curves:{}};r.set(i.id,i)}}return r},parseAnimationCurves:function(r){var a=e.Objects.AnimationCurve;for(var n in a){var i={id:a[n].id,times:a[n].KeyTime.a.map(h),values:a[n].KeyValueFloat.a},o=t.get(i.id);if(void 0!==o){var s=o.parents[0].ID,u=o.parents[0].relationship;u.match(/X/)?r.get(s).curves.x=i:u.match(/Y/)?r.get(s).curves.y=i:u.match(/Z/)?r.get(s).curves.z=i:u.match(/d|DeformPercent/)&&r.has(s)&&(r.get(s).curves.morph=i)}}},parseAnimationLayers:function(a){var n=e.Objects.AnimationLayer,i=new Map;for(var o in n){var s=[],u=t.get(parseInt(o));if(void 0!==u)u.children.forEach((function(n,i){if(a.has(n.ID)){var o=a.get(n.ID);if(void 0!==o.curves.x||void 0!==o.curves.y||void 0!==o.curves.z){if(void 0===s[i])if(void 0!==(d=t.get(n.ID).parents.filter((function(e){return void 0!==e.relationship}))[0].ID)){if(void 0===(l=e.Objects.Model[d.toString()]))return void console.warn("THREE.FBXLoader: Encountered a unused curve.",n);var u={modelName:l.attrName?THREE.PropertyBinding.sanitizeNodeName(l.attrName):"",ID:l.id,initialPosition:[0,0,0],initialRotation:[0,0,0],initialScale:[1,1,1]};r.traverse((function(e){e.ID===l.id&&(u.transform=e.matrix,e.userData.transformData&&(u.eulerOrder=e.userData.transformData.eulerOrder))})),u.transform||(u.transform=new THREE.Matrix4),"PreRotation"in l&&(u.preRotation=l.PreRotation.value),"PostRotation"in l&&(u.postRotation=l.PostRotation.value),s[i]=u}s[i]&&(s[i][o.attr]=o)}else if(void 0!==o.curves.morph){if(void 0===s[i]){var l,c=t.get(n.ID).parents.filter((function(e){return void 0!==e.relationship}))[0].ID,p=t.get(c).parents[0].ID,h=t.get(p).parents[0].ID,d=t.get(h).parents[0].ID;u={modelName:(l=e.Objects.Model[d]).attrName?THREE.PropertyBinding.sanitizeNodeName(l.attrName):"",morphName:e.Objects.Deformer[c].attrName};s[i]=u}s[i][o.attr]=o}}})),i.set(parseInt(o),s)}return i},parseAnimStacks:function(r){var a=e.Objects.AnimationStack,n={};for(var i in a){var o=t.get(parseInt(i)).children;o.length>1&&console.warn("THREE.FBXLoader: Encountered an animation stack with multiple layers, this is currently not supported. Ignoring subsequent layers.");var s=r.get(o[0].ID);n[i]={name:a[i].attrName,layer:s}}return n},addClip:function(e){var t=[],r=this;return e.layer.forEach((function(e){t=t.concat(r.generateTracks(e))})),new THREE.AnimationClip(e.name,-1,t)},generateTracks:function(e){var t=[],r=new THREE.Vector3,a=new THREE.Quaternion,n=new THREE.Vector3;if(e.transform&&e.transform.decompose(r,a,n),r=r.toArray(),a=(new THREE.Euler).setFromQuaternion(a,e.eulerOrder).toArray(),n=n.toArray(),void 0!==e.T&&Object.keys(e.T.curves).length>0){var i=this.generateVectorTrack(e.modelName,e.T.curves,r,"position");void 0!==i&&t.push(i)}if(void 0!==e.R&&Object.keys(e.R.curves).length>0){var o=this.generateRotationTrack(e.modelName,e.R.curves,a,e.preRotation,e.postRotation,e.eulerOrder);void 0!==o&&t.push(o)}if(void 0!==e.S&&Object.keys(e.S.curves).length>0){var s=this.generateVectorTrack(e.modelName,e.S.curves,n,"scale");void 0!==s&&t.push(s)}if(void 0!==e.DeformPercent){var u=this.generateMorphTrack(e);void 0!==u&&t.push(u)}return t},generateVectorTrack:function(e,t,r,a){var n=this.getTimesForAllAxes(t),i=this.getKeyframeTrackValues(n,t,r);return new THREE.VectorKeyframeTrack(e+"."+a,n,i)},generateRotationTrack:function(e,t,r,a,n,i){void 0!==t.x&&(this.interpolateRotations(t.x),t.x.values=t.x.values.map(THREE.MathUtils.degToRad)),void 0!==t.y&&(this.interpolateRotations(t.y),t.y.values=t.y.values.map(THREE.MathUtils.degToRad)),void 0!==t.z&&(this.interpolateRotations(t.z),t.z.values=t.z.values.map(THREE.MathUtils.degToRad));var o=this.getTimesForAllAxes(t),s=this.getKeyframeTrackValues(o,t,r);void 0!==a&&((a=a.map(THREE.MathUtils.degToRad)).push(i),a=(new THREE.Euler).fromArray(a),a=(new THREE.Quaternion).setFromEuler(a)),void 0!==n&&((n=n.map(THREE.MathUtils.degToRad)).push(i),n=(new THREE.Euler).fromArray(n),n=(new THREE.Quaternion).setFromEuler(n).invert());for(var u=new THREE.Quaternion,l=new THREE.Euler,c=[],p=0;p<s.length;p+=3)l.set(s[p],s[p+1],s[p+2],i),u.setFromEuler(l),void 0!==a&&u.premultiply(a),void 0!==n&&u.multiply(n),u.toArray(c,p/3*4);return new THREE.QuaternionKeyframeTrack(e+".quaternion",o,c)},generateMorphTrack:function(e){var t=e.DeformPercent.curves.morph,a=t.values.map((function(e){return e/100})),n=r.getObjectByName(e.modelName).morphTargetDictionary[e.morphName];return new THREE.NumberKeyframeTrack(e.modelName+".morphTargetInfluences["+n+"]",t.times,a)},getTimesForAllAxes:function(e){var t=[];if(void 0!==e.x&&(t=t.concat(e.x.times)),void 0!==e.y&&(t=t.concat(e.y.times)),void 0!==e.z&&(t=t.concat(e.z.times)),(t=t.sort((function(e,t){return e-t}))).length>1){for(var r=1,a=t[0],n=1;n<t.length;n++){var i=t[n];i!==a&&(t[r]=i,a=i,r++)}t=t.slice(0,r)}return t},getKeyframeTrackValues:function(e,t,r){var a=r,n=[],i=-1,o=-1,s=-1;return e.forEach((function(e){if(t.x&&(i=t.x.times.indexOf(e)),t.y&&(o=t.y.times.indexOf(e)),t.z&&(s=t.z.times.indexOf(e)),-1!==i){var r=t.x.values[i];n.push(r),a[0]=r}else n.push(a[0]);if(-1!==o){var u=t.y.values[o];n.push(u),a[1]=u}else n.push(a[1]);if(-1!==s){var l=t.z.values[s];n.push(l),a[2]=l}else n.push(a[2])})),n},interpolateRotations:function(e){for(var t=1;t<e.values.length;t++){var r=e.values[t-1],a=e.values[t]-r,n=Math.abs(a);if(n>=180){for(var i=n/180,o=a/i,s=r+o,u=e.times[t-1],l=(e.times[t]-u)/i,c=u+l,p=[],h=[];c<e.times[t];)p.push(c),c+=l,h.push(s),s+=o;e.times=R(e.times,t,p),e.values=R(e.values,t,h)}}}},s.prototype={constructor:s,getPrevNode:function(){return this.nodeStack[this.currentIndent-2]},getCurrentNode:function(){return this.nodeStack[this.currentIndent-1]},getCurrentProp:function(){return this.currentProp},pushStack:function(e){this.nodeStack.push(e),this.currentIndent+=1},popStack:function(){this.nodeStack.pop(),this.currentIndent-=1},setCurrentProp:function(e,t){this.currentProp=e,this.currentPropName=t},parse:function(e){this.currentIndent=0,this.allNodes=new c,this.nodeStack=[],this.currentProp=[],this.currentPropName="";var t=this,r=e.split(/[\r\n]+/);return r.forEach((function(e,a){var n=e.match(/^[\s\t]*;/),i=e.match(/^[\s\t]*$/);if(!n&&!i){var o=e.match("^\\t{"+t.currentIndent+"}(\\w+):(.*){",""),s=e.match("^\\t{"+t.currentIndent+"}(\\w+):[\\s\\t\\r\\n](.*)"),u=e.match("^\\t{"+(t.currentIndent-1)+"}}");o?t.parseNodeBegin(e,o):s?t.parseNodeProperty(e,s,r[++a]):u?t.popStack():e.match(/^[^\s\t}]/)&&t.parseNodePropertyContinued(e)}})),this.allNodes},parseNodeBegin:function(e,t){var r=t[1].trim().replace(/^"/,"").replace(/"$/,""),a=t[2].split(",").map((function(e){return e.trim().replace(/^"/,"").replace(/"$/,"")})),n={name:r},i=this.parseNodeAttr(a),o=this.getCurrentNode();0===this.currentIndent?this.allNodes.add(r,n):r in o?("PoseNode"===r?o.PoseNode.push(n):void 0!==o[r].id&&(o[r]={},o[r][o[r].id]=o[r]),""!==i.id&&(o[r][i.id]=n)):"number"==typeof i.id?(o[r]={},o[r][i.id]=n):"Properties70"!==r&&(o[r]="PoseNode"===r?[n]:n),"number"==typeof i.id&&(n.id=i.id),""!==i.name&&(n.attrName=i.name),""!==i.type&&(n.attrType=i.type),this.pushStack(n)},parseNodeAttr:function(e){var t=e[0];""!==e[0]&&(t=parseInt(e[0]),isNaN(t)&&(t=e[0]));var r="",a="";return e.length>1&&(r=e[1].replace(/^(\w+)::/,""),a=e[2]),{id:t,name:r,type:a}},parseNodeProperty:function(e,t,r){var a=t[1].replace(/^"/,"").replace(/"$/,"").trim(),n=t[2].replace(/^"/,"").replace(/"$/,"").trim();"Content"===a&&","===n&&(n=r.replace(/"/g,"").replace(/,$/,"").trim());var i=this.getCurrentNode();if("Properties70"!==i.name){if("C"===a){var o=n.split(",").slice(1),s=parseInt(o[0]),u=parseInt(o[1]),l=n.split(",").slice(3);a="connections",function(e,t){for(var r=0,a=e.length,n=t.length;r<n;r++,a++)e[a]=t[r]}(n=[s,u],l=l.map((function(e){return e.trim().replace(/^"/,"")}))),void 0===i[a]&&(i[a]=[])}"Node"===a&&(i.id=n),a in i&&Array.isArray(i[a])?i[a].push(n):"a"!==a?i[a]=n:i.a=n,this.setCurrentProp(i,a),"a"===a&&","!==n.slice(-1)&&(i.a=y(n))}else this.parseNodeSpecialProperty(e,a,n)},parseNodePropertyContinued:function(e){var t=this.getCurrentNode();t.a+=e,","!==e.slice(-1)&&(t.a=y(t.a))},parseNodeSpecialProperty:function(e,t,r){var a=r.split('",').map((function(e){return e.trim().replace(/^\"/,"").replace(/\s/,"_")})),n=a[0],i=a[1],o=a[2],s=a[3],u=a[4];switch(i){case"int":case"enum":case"bool":case"ULongLong":case"double":case"Number":case"FieldOfView":u=parseFloat(u);break;case"Color":case"ColorRGB":case"Vector3D":case"Lcl_Translation":case"Lcl_Rotation":case"Lcl_Scaling":u=y(u)}this.getPrevNode()[n]={type:i,type2:o,flag:s,value:u},this.setCurrentProp(this.getPrevNode(),n)}},u.prototype={constructor:u,parse:function(e){var t=new l(e);t.skip(23);var r=t.getUint32();if(r<6400)throw new Error("THREE.FBXLoader: FBX version not supported, FileVersion: "+r);for(var a=new c;!this.endOfContent(t);){var n=this.parseNode(t,r);null!==n&&a.add(n.name,n)}return a},endOfContent:function(e){return e.size()%16==0?(e.getOffset()+160+16&-16)>=e.size():e.getOffset()+160+16>=e.size()},parseNode:function(e,t){var r={},a=t>=7500?e.getUint64():e.getUint32(),n=t>=7500?e.getUint64():e.getUint32();t>=7500?e.getUint64():e.getUint32();var i=e.getUint8(),o=e.getString(i);if(0===a)return null;for(var s=[],u=0;u<n;u++)s.push(this.parseProperty(e));var l=s.length>0?s[0]:"",c=s.length>1?s[1]:"",p=s.length>2?s[2]:"";for(r.singleProperty=1===n&&e.getOffset()===a;a>e.getOffset();){var h=this.parseNode(e,t);null!==h&&this.parseSubNode(o,r,h)}return r.propertyList=s,"number"==typeof l&&(r.id=l),""!==c&&(r.attrName=c),""!==p&&(r.attrType=p),""!==o&&(r.name=o),r},parseSubNode:function(e,t,r){if(!0===r.singleProperty){var a=r.propertyList[0];Array.isArray(a)?(t[r.name]=r,r.a=a):t[r.name]=a}else if("Connections"===e&&"C"===r.name){var n=[];r.propertyList.forEach((function(e,t){0!==t&&n.push(e)})),void 0===t.connections&&(t.connections=[]),t.connections.push(n)}else if("Properties70"===r.name){Object.keys(r).forEach((function(e){t[e]=r[e]}))}else if("Properties70"===e&&"P"===r.name){var i,o=r.propertyList[0],s=r.propertyList[1],u=r.propertyList[2],l=r.propertyList[3];0===o.indexOf("Lcl ")&&(o=o.replace("Lcl ","Lcl_")),0===s.indexOf("Lcl ")&&(s=s.replace("Lcl ","Lcl_")),i="Color"===s||"ColorRGB"===s||"Vector"===s||"Vector3D"===s||0===s.indexOf("Lcl_")?[r.propertyList[4],r.propertyList[5],r.propertyList[6]]:r.propertyList[4],t[o]={type:s,type2:u,flag:l,value:i}}else void 0===t[r.name]?"number"==typeof r.id?(t[r.name]={},t[r.name][r.id]=r):t[r.name]=r:"PoseNode"===r.name?(Array.isArray(t[r.name])||(t[r.name]=[t[r.name]]),t[r.name].push(r)):void 0===t[r.name][r.id]&&(t[r.name][r.id]=r)},parseProperty:function(e){var t=e.getString(1);switch(t){case"C":return e.getBoolean();case"D":return e.getFloat64();case"F":return e.getFloat32();case"I":return e.getInt32();case"L":return e.getInt64();case"R":var r=e.getUint32();return e.getArrayBuffer(r);case"S":r=e.getUint32();return e.getString(r);case"Y":return e.getInt16();case"b":case"c":case"d":case"f":case"i":case"l":var a=e.getUint32(),n=e.getUint32(),i=e.getUint32();if(0===n)switch(t){case"b":case"c":return e.getBooleanArray(a);case"d":return e.getFloat64Array(a);case"f":return e.getFloat32Array(a);case"i":return e.getInt32Array(a);case"l":return e.getInt64Array(a)}"undefined"==typeof fflate&&console.error("THREE.FBXLoader: External library fflate.min.js required.");var o=new l(fflate.unzlibSync(new Uint8Array(e.getArrayBuffer(i))).buffer);switch(t){case"b":case"c":return o.getBooleanArray(a);case"d":return o.getFloat64Array(a);case"f":return o.getFloat32Array(a);case"i":return o.getInt32Array(a);case"l":return o.getInt64Array(a)}default:throw new Error("THREE.FBXLoader: Unknown property type "+t)}}},l.prototype={constructor:l,getOffset:function(){return this.offset},size:function(){return this.dv.buffer.byteLength},skip:function(e){this.offset+=e},getBoolean:function(){return 1==(1&this.getUint8())},getBooleanArray:function(e){for(var t=[],r=0;r<e;r++)t.push(this.getBoolean());return t},getUint8:function(){var e=this.dv.getUint8(this.offset);return this.offset+=1,e},getInt16:function(){var e=this.dv.getInt16(this.offset,this.littleEndian);return this.offset+=2,e},getInt32:function(){var e=this.dv.getInt32(this.offset,this.littleEndian);return this.offset+=4,e},getInt32Array:function(e){for(var t=[],r=0;r<e;r++)t.push(this.getInt32());return t},getUint32:function(){var e=this.dv.getUint32(this.offset,this.littleEndian);return this.offset+=4,e},getInt64:function(){var e,t;return this.littleEndian?(e=this.getUint32(),t=this.getUint32()):(t=this.getUint32(),e=this.getUint32()),2147483648&t?(t=4294967295&~t,4294967295===(e=4294967295&~e)&&(t=t+1&4294967295),-(4294967296*t+(e=e+1&4294967295))):4294967296*t+e},getInt64Array:function(e){for(var t=[],r=0;r<e;r++)t.push(this.getInt64());return t},getUint64:function(){var e,t;return this.littleEndian?(e=this.getUint32(),t=this.getUint32()):(t=this.getUint32(),e=this.getUint32()),4294967296*t+e},getFloat32:function(){var e=this.dv.getFloat32(this.offset,this.littleEndian);return this.offset+=4,e},getFloat32Array:function(e){for(var t=[],r=0;r<e;r++)t.push(this.getFloat32());return t},getFloat64:function(){var e=this.dv.getFloat64(this.offset,this.littleEndian);return this.offset+=8,e},getFloat64Array:function(e){for(var t=[],r=0;r<e;r++)t.push(this.getFloat64());return t},getArrayBuffer:function(e){var t=this.dv.buffer.slice(this.offset,this.offset+e);return this.offset+=e,t},getString:function(e){for(var t=[],r=0;r<e;r++)t[r]=this.getUint8();var a=t.indexOf(0);return a>=0&&(t=t.slice(0,a)),THREE.LoaderUtils.decodeText(new Uint8Array(t))}},c.prototype={constructor:c,add:function(e,t){this[e]=t}};var d=[];function f(e,t,r,a){var n;switch(a.mappingType){case"ByPolygonVertex":n=e;break;case"ByPolygon":n=t;break;case"ByVertice":n=r;break;case"AllSame":n=a.indices[0];break;default:console.warn("THREE.FBXLoader: unknown attribute mapping type "+a.mappingType)}"IndexToDirect"===a.referenceType&&(n=a.indices[n]);var i=n*a.dataSize,o=i+a.dataSize;return function(e,t,r,a){for(var n=r,i=0;n<a;n++,i++)e[i]=t[n];return e}(d,a.buffer,i,o)}var v=new THREE.Euler,m=new THREE.Vector3;function g(e){var t,r=new THREE.Matrix4,a=new THREE.Matrix4,n=new THREE.Matrix4,i=new THREE.Matrix4,o=new THREE.Matrix4,s=new THREE.Matrix4,u=new THREE.Matrix4,l=new THREE.Matrix4,c=new THREE.Matrix4,p=new THREE.Matrix4,h=new THREE.Matrix4,d=new THREE.Matrix4,f=e.inheritType?e.inheritType:0;(e.translation&&r.setPosition(m.fromArray(e.translation)),e.preRotation)&&((t=e.preRotation.map(THREE.MathUtils.degToRad)).push(e.eulerOrder),a.makeRotationFromEuler(v.fromArray(t)));e.rotation&&((t=e.rotation.map(THREE.MathUtils.degToRad)).push(e.eulerOrder),n.makeRotationFromEuler(v.fromArray(t)));e.postRotation&&((t=e.postRotation.map(THREE.MathUtils.degToRad)).push(e.eulerOrder),i.makeRotationFromEuler(v.fromArray(t)),i.invert());e.scale&&o.scale(m.fromArray(e.scale)),e.scalingOffset&&u.setPosition(m.fromArray(e.scalingOffset)),e.scalingPivot&&s.setPosition(m.fromArray(e.scalingPivot)),e.rotationOffset&&l.setPosition(m.fromArray(e.rotationOffset)),e.rotationPivot&&c.setPosition(m.fromArray(e.rotationPivot)),e.parentMatrixWorld&&(h.copy(e.parentMatrix),p.copy(e.parentMatrixWorld));var g=(new THREE.Matrix4).copy(a).multiply(n).multiply(i),E=new THREE.Matrix4;E.extractRotation(p);var y=new THREE.Matrix4;y.copyPosition(p);var T=new THREE.Matrix4,R=(new THREE.Matrix4).copy(y).invert().multiply(p);T.copy(E).invert().multiply(R);var x=o,w=new THREE.Matrix4;if(0===f)w.copy(E).multiply(g).multiply(T).multiply(x);else if(1===f)w.copy(E).multiply(T).multiply(g).multiply(x);else{var b=(new THREE.Matrix4).scale((new THREE.Vector3).setFromMatrixScale(h)),I=(new THREE.Matrix4).copy(b).invert(),M=(new THREE.Matrix4).copy(T).multiply(I);w.copy(E).multiply(g).multiply(M).multiply(x)}var H=new THREE.Matrix4;H.copy(c).invert();var A=new THREE.Matrix4;A.copy(s).invert();var L=new THREE.Matrix4;L.copy(r).multiply(l).multiply(c).multiply(a).multiply(n).multiply(i).multiply(H).multiply(u).multiply(s).multiply(o).multiply(A);var k=(new THREE.Matrix4).copyPosition(L),D=(new THREE.Matrix4).copy(p).multiply(k);return d.copyPosition(D),(L=(new THREE.Matrix4).copy(d).multiply(w)).premultiply(p.invert()),L}function E(e){var t=["ZYX","YZX","XZY","ZXY","YXZ","XYZ"];return 6===(e=e||0)?(console.warn("THREE.FBXLoader: unsupported Euler Order: Spherical XYZ. Animations and rotations may be incorrect."),t[0]):t[e]}function y(e){return e.split(",").map((function(e){return parseFloat(e)}))}function T(e,t,r){return void 0===t&&(t=0),void 0===r&&(r=e.byteLength),THREE.LoaderUtils.decodeText(new Uint8Array(e,t,r))}function R(e,t,r){return e.slice(0,t).concat(r).concat(e.slice(t))}return a}();