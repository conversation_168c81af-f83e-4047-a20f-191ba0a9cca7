var CLOUD=CLOUD||{};function BDImageryProvider(e){this._url="https://api.map.baidu.com/customimage/tile?udt=20181205&scale=1&ak=1XjLLEhZhQNUzd93EjU5nOGQ&customid=white",this._tileWidth=256,this._tileHeight=256,this._maximumLevel=18,this._height=33746824,this._width=33554054;var i=new Cesium.Cartesian2(-this._width,-this._height),t=new Cesium.Cartesian2(this._width,this._height);this._tilingScheme=new Cesium.WebMercatorTilingScheme({rectangleSouthwestInMeters:i,rectangleNortheastInMeters:t}),this._credit=void 0,this._rectangle=this._tilingScheme.rectangle,this._ready=!0}function buildImageUrl(e,i,t,r){var a=e._url+"&x={x}&y={y}&z={z}",s=e._tilingScheme.getNumberOfXTilesAtLevel(r),n=e._tilingScheme.getNumberOfYTilesAtLevel(r);return a=a.replace("{x}",i-s/2).replace("{y}",n/2-t-1).replace("{z}",r)}function createAMapByUrl(e,i){i=e.defaultValue(i,{});var t=e.defaultValue(i.url,"//webst01.is.autonavi.com/appmaptile?style=6&x={x}&y={y}&z={z}"),r=new e.Credit("AMap"),a=new e.WebMercatorTilingScheme({ellipsoid:i.ellipsoid}),s=e.defaultValue(i.minimumLevel,0),n=e.defaultValue(i.minimumLevel,18),o=e.defaultValue(i.rectangle,a.rectangle),l=a.positionToTileXY(e.Rectangle.southwest(o),s),u=a.positionToTileXY(e.Rectangle.northeast(o),s),m=(Math.abs(u.x-l.x)+1)*(Math.abs(u.y-l.y)+1);if(m>4)throw new e.DeveloperError("The rectangle and minimumLevel indicate that there are "+m+" tiles at the minimum level. Imagery providers with more than four tiles at the minimum level are not supported.");var c=e.defaultValue(i.credit,r);return"string"==typeof c&&(c=new e.Credit(c)),new e.UrlTemplateImageryProvider({url:t,proxy:i.proxy,credit:c,tilingScheme:a,tileWidth:256,tileHeight:256,minimumLevel:s,maximumLevel:n,rectangle:o})}Cesium.defineProperties(BDImageryProvider.prototype,{url:{get:function(){return this._url}},token:{get:function(){return this._token}},proxy:{get:function(){return this._proxy}},tileWidth:{get:function(){if(!this._ready)throw new DeveloperError("tileWidth must not be called before the imagery provider is ready.");return this._tileWidth}},tileHeight:{get:function(){if(!this._ready)throw new DeveloperError("tileHeight must not be called before the imagery provider is ready.");return this._tileHeight}},maximumLevel:{get:function(){if(!this._ready)throw new DeveloperError("maximumLevel must not be called before the imagery provider is ready.");return this._maximumLevel}},minimumLevel:{get:function(){if(!this._ready)throw new DeveloperError("minimumLevel must not be called before the imagery provider is ready.");return 0}},tilingScheme:{get:function(){if(!this._ready)throw new DeveloperError("tilingScheme must not be called before the imagery provider is ready.");return this._tilingScheme}},rectangle:{get:function(){if(!this._ready)throw new DeveloperError("rectangle must not be called before the imagery provider is ready.");return this._rectangle}},tileDiscardPolicy:{get:function(){if(!this._ready)throw new DeveloperError("tileDiscardPolicy must not be called before the imagery provider is ready.");return this._tileDiscardPolicy}},errorEvent:{get:function(){return this._errorEvent}},ready:{get:function(){return this._ready}},readyPromise:{get:function(){return this._readyPromise.promise}},credit:{get:function(){return this._credit}},usingPrecachedTiles:{get:function(){return this._useTiles}},hasAlphaChannel:{get:function(){return!0}},layers:{get:function(){return this._layers}}}),BDImageryProvider.prototype.getTileCredits=function(e,i,t){},BDImageryProvider.prototype.requestImage=function(e,i,t){if(!this._ready)throw new DeveloperError("requestImage must not be called before the imagery provider is ready.");var r=buildImageUrl(this,e,i,t);return Cesium.ImageryProvider.loadImage(this,r)};var fixGltf=function(e){if(e.extensionsUsed){var i=e.extensionsUsed.indexOf("KHR_technique_webgl");if(-1!==i){e.extensionsRequired.splice(r,1,"KHR_techniques_webgl"),e.extensionsUsed.splice(i,1,"KHR_techniques_webgl"),e.extensions=e.extensions||{},e.extensions.KHR_techniques_webgl={},e.extensions.KHR_techniques_webgl.programs=e.programs,e.extensions.KHR_techniques_webgl.shaders=e.shaders,e.extensions.KHR_techniques_webgl.techniques=e.techniques;var t=e.extensions.KHR_techniques_webgl.techniques;if(e.materials.forEach((function(i,r){e.materials[r].extensions||(e.materials[r].extensions={KHR_technique_webgl:{}}),e.materials[r].extensions.KHR_technique_webgl.values=e.materials[r].values,e.materials[r].extensions.KHR_techniques_webgl=e.materials[r].extensions.KHR_technique_webgl;var a=e.materials[r].extensions.KHR_techniques_webgl;for(var s in a.technique||(a.technique=e.materials[r].technique),a.values){var n=t[a.technique].uniforms;for(var o in n)if(n[o]===s){a.values[o]=a.values[s],delete a.values[s];break}}})),Cesium.defined(e.extensionsRequired)){var r=e.extensionsRequired.indexOf("KHR_technique_webgl");t.forEach((function(e){for(var i in e.attributes){var t=e.attributes[i];e.attributes[i]=e.parameters[t]}for(var r in e.uniforms){t=e.uniforms[r];e.uniforms[r]=e.parameters[t]}}))}}}};Object.defineProperties(Cesium.Model.prototype,{_cachedGltf:{set:function(e){this._vtxf_cachedGltf=e,this._vtxf_cachedGltf&&this._vtxf_cachedGltf._gltf&&fixGltf(this._vtxf_cachedGltf._gltf)},get:function(){return this._vtxf_cachedGltf}}});class CesiumLoader{constructor(e){this.container=e.container,this.worldCoordinates=[0,0],this.worldCoordinates[0]=null==e.longitude?121.32403:e.longitude,this.worldCoordinates[1]=null==e.latitude?31.198192:e.latitude,this.useTerrain=null!=e.useTerrain&&e.useTerrain,this.imageryLayer=null==e.imageryLayer?CLOUD.EnumCesiumImageryLayerType.AUTO:e.imageryLayer,this.clipHeight=null==e.clipHeight?0:e.clipHeight,this.duration=null==e.duration?5:e.duration,this.accessToken=e.accessToken,this.globalPlane=void 0,this.viewer=void 0,this.terrainProvider=void 0,this.terrainHeight={},this.globeMatrix=void 0,this.cameraScale=.001,this.viewport=void 0,this.originBimCameraTar=void 0,this.closeToModel=0,this.gdRoadNoLabel=void 0}init(e,i){let t=this;this.accessToken&&(Cesium.Ion.defaultAccessToken=this.accessToken),this.useTerrain&&!this.accessToken&&console.warn("when useTerrain,the accessToken can not be null"),this.terrainProvider=Cesium.createWorldTerrain();var r=[Cesium.Cartographic.fromDegrees(this.worldCoordinates[0],this.worldCoordinates[1])],a=Cesium.sampleTerrainMostDetailed(this.terrainProvider,r),s=void 0;switch(t.imageryLayer){case CLOUD.EnumCesiumImageryLayerType.AUTO:s=null==t.accessToken&&new Cesium.TileMapServiceImageryProvider({url:Cesium.buildModuleUrl("Assets/Textures/NaturalEarthII")});break;case CLOUD.EnumCesiumImageryLayerType.BAIDU:s=new BDImageryProvider;break;case CLOUD.EnumCesiumImageryLayerType.GAODE:var n=createAMapByUrl(Cesium,{url:"//webst01.is.autonavi.com/appmaptile?style=7&x={x}&y={y}&z={z}"});createAMapByUrl(Cesium,{url:"//wprd04.is.autonavi.com/appmaptile?x={x}&y={y}&z={z}&lang=zh_cn&size=1&scl=1&style=8&ltype=11"}),createAMapByUrl(Cesium,{url:"//wprd04.is.autonavi.com/appmaptile?x={x}&y={y}&z={z}&lang=zh_cn&size=1&scl=1&style=8&ltype=12"});s=n;break;case CLOUD.EnumCesiumImageryLayerType.MAPBOX:s=new Cesium.MapboxImageryProvider({mapId:"mapbox.streets-satellite"});break;case CLOUD.EnumCesiumImageryLayerType.GOOGLE:s=s=new Cesium.UrlTemplateImageryProvider({url:"//www.google.cn/maps/vt?lyrs=s&gl=CN&x={x}&y={y}&z={z}",tilingScheme:new Cesium.WebMercatorTilingScheme,minimumLevel:1,maximumLevel:20});break;case CLOUD.EnumCesiumImageryLayerType.ARCGIS:s=new Cesium.ArcGisMapServerImageryProvider({url:"//nationalmap.gov.au/proxy/http://services.ga.gov.au/site_3/rest/services/Electricity_Infrastructure/MapServer"});break;default:s=t.createGoogleMapsByUrl(Cesium,{url:"//mt1.google.cn/vt/lyrs=s&hl=zh-CN&x={x}&y={y}&z={z}"})}!0===t.useTerrain?Cesium.when(a,(function(r){t.terrainHeight=r[0].height,t.viewer.terrainProvider=Cesium.createWorldTerrain(),t.initView(s,e,i)})):(t.terrainHeight=0,t.initView(s,e,i))}initView(e,i,t){let r=this;r.viewer=new Cesium.Viewer(r.container,{useDefaultRenderLoop:!0,selectionIndicator:!1,homeButton:!1,sceneModePicker:!1,navigationHelpButton:!1,infoBox:!1,navigationHelpButton:!1,navigationInstructionsInitiallyVisible:!1,animation:!1,timeline:!1,fullscreenButton:!1,allowTextureFilterAnisotropic:!1,contextOptions:{webgl:{alpha:!0,antialias:!0,preserveDrawingBuffer:!0,failIfMajorPerformanceCaveat:!1,depth:!0,stencil:!1,anialias:!0}},targetFrameRate:60,resolutionScale:.1,orderIndependentTranslucency:!0,imageryProvider:e,baseLayerPicker:!1,geocoder:!1,automaticallyTrackDataSourceClocks:!1,dataSources:null,clock:null,terrainShadows:Cesium.ShadowMode.DISABLED}),r.viewer._cesiumWidget._creditContainer.style.display="none",r.viewer.scene.skyBox.show=!1,r.viewer.scene.backgroundColor=new Cesium.Color(0,0,0,0),r.updateCesiumOriginPos();var a=new Cesium.UrlTemplateImageryProvider({url:"//webst02.is.autonavi.com/appmaptile?x={x}&y={y}&z={z}&lang=zh_cn&style=8&scale=1",minimumLevel:3,maximumLevel:18});r.gdRoad=r.viewer.imageryLayers.addImageryProvider(a),r.gdRoad.alpha=.5,r.gdRoad.show=!1,new Cesium.ScreenSpaceEventHandler(r.viewer.scene.canvas).setInputAction((function(e){let i=r.viewer.scene.pickPosition(e.position);console.log(i),i=r.kadierTollh(i),console.log(i)}),Cesium.ScreenSpaceEventType.LEFT_CLICK),r.viewer.camera.flyTo({destination:null!=t.cesiumDestination?t.cesiumDestination:r.cesiumOriginPos,orientation:null!=t.cesiumOrientation?t.cesiumOrientation:{heading:Cesium.Math.toRadians(175),pitch:Cesium.Math.toRadians(-35),roll:0},duration:r.duration,complete:function(){i&&i(t)}}),this.viewer.scene.globe.depthTestAgainstTerrain=!0}createGoogleMapsByUrl(e,i){i=e.defaultValue(i,{});var t=e.defaultValue(i.url,"https://mt1.google.cn/vt/lyrs=s&hl=zh-CN&x={x}&y={y}&z={z}"),r=new e.Credit("Google Maps"),a=new e.WebMercatorTilingScheme({ellipsoid:i.ellipsoid}),s=e.defaultValue(i.minimumLevel,0),n=e.defaultValue(i.minimumLevel,17),o=e.defaultValue(i.rectangle,a.rectangle),l=a.positionToTileXY(e.Rectangle.southwest(o),s),u=a.positionToTileXY(e.Rectangle.northeast(o),s),m=(Math.abs(u.x-l.x)+1)*(Math.abs(u.y-l.y)+1);if(m>4)throw new e.DeveloperError("The rectangle and minimumLevel indicate that there are "+m+" tiles at the minimum level. Imagery providers with more than four tiles at the minimum level are not supported.");var c=e.defaultValue(i.credit,r);return"string"==typeof c&&(c=new e.Credit(c)),new e.UrlTemplateImageryProvider({url:t,proxy:i.proxy,credit:c,tilingScheme:a,tileWidth:256,tileHeight:256,minimumLevel:s,maximumLevel:n,rectangle:o})}updateCesiumOriginPos(){this.cesiumOriginPos=Cesium.Cartesian3.fromDegrees(this.worldCoordinates[0],this.worldCoordinates[1],this.terrainHeight+100)}kadierTollh(e){if(Cesium.defined(e)){var i=Cesium.Cartographic.fromCartesian(e);return{lon:Cesium.Math.toDegrees(i.longitude),lat:Cesium.Math.toDegrees(i.latitude),alt:i.height}}}llhToKadier(e,i,t){return Cesium.Cartesian3.fromDegrees(e,i,t)}loadGltf(e,i,t,r,a,s,n,o){Cesium.Transforms.eastNorthUpToFixedFrame(Cesium.Cartesian3.fromDegrees(i,t,r));var l=Cesium.Cartesian3.fromDegrees(i,t,r),u=Cesium.Math.toRadians(a),m=Cesium.Math.toRadians(s),c=Cesium.Math.toRadians(n),h=new Cesium.HeadingPitchRoll(u,m,c),d=Cesium.Transforms.headingPitchRollQuaternion(l,h),g=this.viewer.entities.add({name:e,position:l,orientation:d,model:{uri:e,minimumPixelSize:128,maximumScale:2e4}});o&&o(g)}load3dTiles(e,i,t,r){var a=this;new Cesium.Cesium3DTileset({url:e,maximumScreenSpaceError:t}).readyPromise.then((function(e){a.viewer.scene.primitives.add(e);var t=e.boundingSphere,s=Cesium.Cartographic.fromCartesian(t.center),n=Cesium.Cartesian3.fromRadians(s.longitude,s.latitude,0),o=(Cesium.Cartographic.fromDegrees(s.longitude,s.latitude),Cesium.Cartesian3.fromRadians(s.longitude,s.latitude,i)),l=Cesium.Cartesian3.subtract(o,n,new Cesium.Cartesian3);e.modelMatrix=Cesium.Matrix4.fromTranslation(l),r&&r(e)})).otherwise((function(e){window.alert(e)}))}load3dTilesByPosition(e,i,t,r,a,s){var n=this.viewer.scene.primitives.add(new Cesium.Cesium3DTileset({url:e,maximumScreenSpaceError:a,scale:1}));n.readyPromise.then((function(e){let a=new Cesium.Matrix3,o=new Cesium.HeadingPitchRoll(Math.PI,Math.PI,Math.PI);a=Cesium.Matrix3.fromHeadingPitchRoll(o,a);let l=Cesium.Matrix4.multiplyByTranslation(Cesium.Transforms.eastNorthUpToFixedFrame(Cesium.Cartesian3.fromDegrees(i,t,r)),new Cesium.Cartesian3,new Cesium.Matrix4);Cesium.Matrix4.multiplyByMatrix3(l,a,l),n._root.transform=l,s&&s(n)}))}loadSkyBox(){this.viewer.scene.skyBox=new Cesium.SkyBox({sources:{positiveX:"../cesium/ThirdParty/Cesium/Apps/SampleData/px.jpg",negativeX:"../cesium/ThirdParty/Cesium/Apps/SampleData/nx.jpg",positiveY:"../cesium/ThirdParty/Cesium/Apps/SampleData/py.jpg",negativeY:"../cesium/ThirdParty/Cesium/Apps/SampleData/ny.jpg",positiveZ:"../cesium/ThirdParty/Cesium/Apps/SampleData/pz.jpg",negativeZ:"../cesium/ThirdParty/Cesium/Apps/SampleData/nz.jpg"}})}loadGeoJson(e){Cesium.GeoJsonDataSource.load(e).then((function(e){self.viewer.dataSources.add(e);e.entities.values})).otherwise((function(e){window.alert(e)}))}setUpGDRoad(e,i){this.gdRoad.alpha=i,this.gdRoad.show=e}}CLOUD.CesiumLoader=CesiumLoader;