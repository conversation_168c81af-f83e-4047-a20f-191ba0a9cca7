!function(){class t extends THREE.Loader{constructor(t){super(t)}load(t,s,e,a){const r=this,i=""===this.path?THREE.LoaderUtils.extractUrlBase(t):this.path,o=new THREE.FileLoader(this.manager);o.setPath(this.path),o.setRequestHeader(this.requestHeader),o.setWithCredentials(this.withCredentials),o.load(t,(function(e){try{s(r.parse(e,i))}catch(s){a?a(s):console.error(s),r.manager.itemError(t)}}),e,a)}setPath(t){return this.path=t,this}setMaterialOptions(t){return this.materialOptions=t,this}parse(t,e){const a=t.split("\n");let r={};const i=/\s+/,o={};for(let t=0;t<a.length;t++){let s=a[t];if(s=s.trim(),0===s.length||"#"===s.charAt(0))continue;const e=s.indexOf(" ");let n=e>=0?s.substring(0,e):s;n=n.toLowerCase();let l=e>=0?s.substring(e+1):"";if(l=l.trim(),"newmtl"===n)r={name:l},o[l]=r;else if("ka"===n||"kd"===n||"ks"===n||"ke"===n){const t=l.split(i,3);r[n]=[parseFloat(t[0]),parseFloat(t[1]),parseFloat(t[2])]}else r[n]=l}const n=new s(this.resourcePath||e,this.materialOptions);return n.setCrossOrigin(this.crossOrigin),n.setManager(this.manager),n.setMaterials(o),n}}class s{constructor(t="",s={}){this.baseUrl=t,this.options=s,this.materialsInfo={},this.materials={},this.materialsArray=[],this.nameLookup={},this.crossOrigin="anonymous",this.side=void 0!==this.options.side?this.options.side:THREE.FrontSide,this.wrap=void 0!==this.options.wrap?this.options.wrap:THREE.RepeatWrapping}setCrossOrigin(t){return this.crossOrigin=t,this}setManager(t){this.manager=t}setMaterials(t){this.materialsInfo=this.convert(t),this.materials={},this.materialsArray=[],this.nameLookup={}}convert(t){if(!this.options)return t;const s={};for(const e in t){const a=t[e],r={};s[e]=r;for(const t in a){let s=!0,e=a[t];const i=t.toLowerCase();switch(i){case"kd":case"ka":case"ks":this.options&&this.options.normalizeRGB&&(e=[e[0]/255,e[1]/255,e[2]/255]),this.options&&this.options.ignoreZeroRGBs&&0===e[0]&&0===e[1]&&0===e[2]&&(s=!1)}s&&(r[i]=e)}}return s}preload(){for(const t in this.materialsInfo)this.create(t)}getIndex(t){return this.nameLookup[t]}getAsArray(){let t=0;for(const s in this.materialsInfo)this.materialsArray[t]=this.create(s),this.nameLookup[s]=t,t++;return this.materialsArray}create(t){return void 0===this.materials[t]&&this.createMaterial_(t),this.materials[t]}createMaterial_(t){const s=this,e=this.materialsInfo[t],a={name:t,side:this.side};function r(t,e){if(a[t])return;const r=s.getTextureParams(e,a),i=s.loadTexture((o=s.baseUrl,"string"!=typeof(n=r.url)||""===n?"":/^https?:\/\//i.test(n)?n:o+n));var o,n;i.repeat.copy(r.scale),i.offset.copy(r.offset),i.wrapS=s.wrap,i.wrapT=s.wrap,a[t]=i}for(const t in e){const s=e[t];let i;if(""!==s)switch(t.toLowerCase()){case"kd":a.color=(new THREE.Color).fromArray(s);break;case"ks":a.specular=(new THREE.Color).fromArray(s);break;case"ke":a.emissive=(new THREE.Color).fromArray(s);break;case"map_kd":r("map",s);break;case"map_ks":r("specularMap",s);break;case"map_ke":r("emissiveMap",s);break;case"norm":r("normalMap",s);break;case"map_bump":case"bump":r("bumpMap",s);break;case"map_d":r("alphaMap",s),a.transparent=!0;break;case"ns":a.shininess=parseFloat(s);break;case"d":i=parseFloat(s),i<1&&(a.opacity=i,a.transparent=!0);break;case"tr":i=parseFloat(s),this.options&&this.options.invertTrProperty&&(i=1-i),i>0&&(a.opacity=1-i,a.transparent=!0)}}return this.materials[t]=new THREE.MeshPhongMaterial(a),this.materials[t]}getTextureParams(t,s){const e={scale:new THREE.Vector2(1,1),offset:new THREE.Vector2(0,0)},a=t.split(/\s+/);let r;return r=a.indexOf("-bm"),r>=0&&(s.bumpScale=parseFloat(a[r+1]),a.splice(r,2)),r=a.indexOf("-s"),r>=0&&(e.scale.set(parseFloat(a[r+1]),parseFloat(a[r+2])),a.splice(r,4)),r=a.indexOf("-o"),r>=0&&(e.offset.set(parseFloat(a[r+1]),parseFloat(a[r+2])),a.splice(r,4)),e.url=a.join(" ").trim(),e}loadTexture(t,s,e,a,r){const i=void 0!==this.manager?this.manager:THREE.DefaultLoadingManager;let o=i.getHandler(t);null===o&&(o=new THREE.TextureLoader(i)),o.setCrossOrigin&&o.setCrossOrigin(this.crossOrigin);const n=o.load(t,e,a,r);return void 0!==s&&(n.mapping=s),n}}THREE.MTLLoader=t}();