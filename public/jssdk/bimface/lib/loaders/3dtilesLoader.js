THREE.GLTFLoader=function(){function e(e){this.manager=void 0!==e?e:THREE.DefaultLoadingManager}function r(){var e={};return{get:function(r){return e[r]},add:function(r,t){e[r]=t},remove:function(r){delete e[r]},removeAll:function(){e={}},update:function(r,t){for(var a in e){var n=e[a];n.update&&n.update(r,t)}}}}function t(e,r){var t={},a=e.material.uniforms;for(var n in a){var i=a[n];if(i.semantic){var s=i.node,o=e;s&&(o=r[s]),t[n]={semantic:i.semantic,sourceNode:o,targetNode:e,uniform:i}}}this.boundUniforms=t,this._m4=new THREE.Matrix4}e.prototype={constructor:e,load:function(e,r,t,a){var n=this,i=this.path&&"string"==typeof this.path?this.path:THREE.LoaderUtils.extractUrlBase(e),s=new THREE.FileLoader(n.manager);s.setResponseType("arraybuffer"),s.load(e,(function(e){n.parse(e,r,i)}),t,a)},setCrossOrigin:function(e){this.crossOrigin=e},setPath:function(e){this.path=e},parse:function(e,r,t){var o,c={};y(new Uint8Array(e,0,4))===i.magic?(c[a.KHR_BINARY_GLTF]=new s(e),o=c[a.KHR_BINARY_GLTF].content):o=y(new Uint8Array(e));var u=JSON.parse(o);u.extensionsUsed&&u.extensionsUsed.indexOf(a.KHR_MATERIALS_COMMON)>=0&&(c[a.KHR_MATERIALS_COMMON]=new n(u)),new M(u,c,{path:t||this.path,crossOrigin:this.crossOrigin}).parse((function(e,t,a,n){r({scene:e,scenes:t,cameras:a,animations:n})}))}},e.Shaders={update:function(){console.warn("THREE.GLTFLoader.Shaders has been deprecated, and now updates automatically.")}},t.prototype.update=function(e,r){var t=this.boundUniforms;for(var a in t){var n=t[a];switch(n.semantic){case"MODELVIEW":n.uniform.value.multiplyMatrices(r.matrixWorldInverse,n.sourceNode.matrixWorld);break;case"MODELVIEWINVERSETRANSPOSE":var i=n.uniform.value;this._m4.multiplyMatrices(r.matrixWorldInverse,n.sourceNode.matrixWorld),i.getNormalMatrix(this._m4);break;case"PROJECTION":n.uniform.value.copy(r.projectionMatrix);break;case"JOINTMATRIX":for(var s=n.uniform.value,o=0;o<s.length;o++)s[o].copy(n.sourceNode.matrixWorld).invert().multiply(n.targetNode.skeleton.bones[o].matrixWorld).multiply(n.targetNode.skeleton.boneInverses[o]).multiply(n.targetNode.bindMatrix);break;default:console.warn("Unhandled shader semantic: "+n.semantic)}}},e.Animations={update:function(){console.warn("THREE.GLTFLoader.Animation has been deprecated. Use THREE.AnimationMixer instead.")}};var a={KHR_BINARY_GLTF:"KHR_binary_glTF",KHR_MATERIALS_COMMON:"KHR_materials_common"};function n(e){this.name=a.KHR_MATERIALS_COMMON,this.lights={};var r=(e.extensions&&e.extensions[a.KHR_MATERIALS_COMMON]||{}).lights||{};for(var t in r){var n,i=r[t],s=i[i.type],o=(new THREE.Color).fromArray(s.color);switch(i.type){case"directional":(n=new THREE.DirectionalLight(o)).position.set(0,0,1);break;case"point":n=new THREE.PointLight(o);break;case"spot":(n=new THREE.SpotLight(o)).position.set(0,0,1);break;case"ambient":n=new THREE.AmbientLight(o)}n&&(this.lights[t]=n)}}var i={magic:"glTF",version:1,contentFormat:0};function s(e){this.name=a.KHR_BINARY_GLTF;var r=new DataView(e,0,20),t={magic:y(new Uint8Array(e.slice(0,4))),version:r.getUint32(4,!0),length:r.getUint32(8,!0),contentLength:r.getUint32(12,!0),contentFormat:r.getUint32(16,!0)};for(var n in i){var s=i[n];if(t[n]!==s)throw new Error('Unsupported glTF-Binary header: Expected "%s" to be "%s".',n,s)}var o=new Uint8Array(e,20,t.contentLength);this.header=t,this.content=y(o),this.body=e.slice(20+t.contentLength,t.length)}s.prototype.loadShader=function(e,r){var t=r[e.extensions[a.KHR_BINARY_GLTF].bufferView];return y(new Uint8Array(t))},s.prototype.loadTextureSourceUri=function(e,r){var t=e.extensions[a.KHR_BINARY_GLTF],n=r[t.bufferView],i=y(new Uint8Array(n));return"data:"+t.mimeType+";base64,"+btoa(i)};var o={FLOAT:5126,FLOAT_MAT3:35675,FLOAT_MAT4:35676,FLOAT_VEC2:35664,FLOAT_VEC3:35665,FLOAT_VEC4:35666,LINEAR:9729,REPEAT:10497,SAMPLER_2D:35678,TRIANGLES:4,LINES:1,UNSIGNED_BYTE:5121,UNSIGNED_SHORT:5123,VERTEX_SHADER:35633,FRAGMENT_SHADER:35632},c={5126:Number,35675:THREE.Matrix3,35676:THREE.Matrix4,35664:THREE.Vector2,35665:THREE.Vector3,35666:THREE.Vector4,35678:THREE.Texture},u={5120:Int8Array,5121:Uint8Array,5122:Int16Array,5123:Uint16Array,5125:Uint32Array,5126:Float32Array},E={9728:THREE.NearestFilter,9729:THREE.LinearFilter,9984:THREE.NearestMipMapNearestFilter,9985:THREE.LinearMipMapNearestFilter,9986:THREE.NearestMipMapLinearFilter,9987:THREE.LinearMipMapLinearFilter},d={33071:THREE.ClampToEdgeWrapping,33648:THREE.MirroredRepeatWrapping,10497:THREE.RepeatWrapping},l={6406:THREE.AlphaFormat,6407:THREE.RGBFormat,6408:THREE.RGBAFormat,6409:THREE.LuminanceFormat,6410:THREE.LuminanceAlphaFormat},p={5121:THREE.UnsignedByteType,32819:THREE.UnsignedShort4444Type,32820:THREE.UnsignedShort5551Type,33635:THREE.UnsignedShort565Type},f={1028:THREE.BackSide,1029:THREE.FrontSide},h={512:THREE.NeverDepth,513:THREE.LessDepth,514:THREE.EqualDepth,515:THREE.LessEqualDepth,516:THREE.GreaterEqualDepth,517:THREE.NotEqualDepth,518:THREE.GreaterEqualDepth,519:THREE.AlwaysDepth},m={32774:THREE.AddEquation,32778:THREE.SubtractEquation,32779:THREE.ReverseSubtractEquation},v={0:THREE.ZeroFactor,1:THREE.OneFactor,768:THREE.SrcColorFactor,769:THREE.OneMinusSrcColorFactor,770:THREE.SrcAlphaFactor,771:THREE.OneMinusSrcAlphaFactor,772:THREE.DstAlphaFactor,773:THREE.OneMinusDstAlphaFactor,774:THREE.DstColorFactor,775:THREE.OneMinusDstColorFactor,776:THREE.SrcAlphaSaturateFactor},T={SCALAR:1,VEC2:2,VEC3:3,VEC4:4,MAT2:4,MAT3:9,MAT4:16},R={scale:"scale",translation:"position",rotation:"quaternion"},H={LINEAR:THREE.InterpolateLinear,STEP:THREE.InterpolateDiscrete},A={2884:"CULL_FACE",2929:"DEPTH_TEST",3042:"BLEND",3089:"SCISSOR_TEST",32823:"POLYGON_OFFSET_FILL",32926:"SAMPLE_ALPHA_TO_COVERAGE"};function b(e,r,t){if(!e)return Promise.resolve();var a,n=[];if("[object Array]"===Object.prototype.toString.call(e)){a=[];for(var i=e.length,s=0;s<i;s++){(c=r.call(t||this,e[s],s))&&(n.push(c),c instanceof Promise?c.then(function(e,r){a[e]=r}.bind(this,s)):a[s]=c)}}else for(var o in a={},e){var c;if(e.hasOwnProperty(o))(c=r.call(t||this,e[o],o))&&(n.push(c),c instanceof Promise?c.then(function(e,r){a[e]=r}.bind(this,o)):a[o]=c)}return Promise.all(n).then((function(){return a}))}function w(e,r){return"string"!=typeof e||""===e?"":/^(https?:)?\/\//i.test(e)||/^data:.*,.*$/i.test(e)?e:(r||"")+e}function y(e){for(var r="",t=0;t<e.length;t++)r+=String.fromCharCode(e[t]);return r}function L(e){this.isDeferredShaderMaterial=!0,this.params=e}function M(e,t,a){this.json=e||{},this.extensions=t||{},this.options=a||{},this.cache=new r}return L.prototype.create=function(){var e=THREE.UniformsUtils.clone(this.params.uniforms);for(var r in this.params.uniforms){var t=this.params.uniforms[r];t.value instanceof THREE.Texture&&(e[r].value=t.value,e[r].value.needsUpdate=!0),e[r].semantic=t.semantic,e[r].node=t.node}return this.params.uniforms=e,new THREE.RawShaderMaterial(this.params)},M.prototype._withDependencies=function(e){for(var r={},t=0;t<e.length;t++){var a=e[t],n="load"+a.charAt(0).toUpperCase()+a.slice(1),i=this.cache.get(a);if(void 0!==i)r[a]=i;else if(this[n]){var s=this[n]();this.cache.add(a,s),r[a]=s}}return b(r,(function(e){return e}))},M.prototype.parse=function(e){var r=this.json;this.cache.removeAll(),this._withDependencies(["scenes","cameras","animations"]).then((function(t){var a=[];for(var n in t.scenes)a.push(t.scenes[n]);var i=void 0!==r.scene?t.scenes[r.scene]:a[0],s=[];for(var n in t.cameras){var o=t.cameras[n];s.push(o)}var c=[];for(var n in t.animations)c.push(t.animations[n]);e(i,a,s,c)}))},M.prototype.loadShaders=function(){var e=this.json,r=this.extensions,t=this.options;return this._withDependencies(["bufferViews"]).then((function(n){return b(e.shaders,(function(e){return e.extensions&&e.extensions[a.KHR_BINARY_GLTF]?r[a.KHR_BINARY_GLTF].loadShader(e,n.bufferViews):new Promise((function(r){var a=new THREE.FileLoader;a.setResponseType("text"),a.load(w(e.uri,t.path),(function(e){r(e)}))}))}))}))},M.prototype.loadBuffers=function(){var e=this.json,r=this.extensions,t=this.options;return b(e.buffers,(function(e,n){return"binary_glTF"===n?r[a.KHR_BINARY_GLTF].body:"arraybuffer"===e.type||void 0===e.type?new Promise((function(r){var a=new THREE.FileLoader;a.setResponseType("arraybuffer"),a.load(w(e.uri,t.path),(function(e){r(e)}))})):void console.warn("THREE.GLTFLoader: "+e.type+" buffer type is not supported")}))},M.prototype.loadBufferViews=function(){var e=this.json;return this._withDependencies(["buffers"]).then((function(r){return b(e.bufferViews,(function(e){var t=r.buffers[e.buffer],a=void 0!==e.byteLength?e.byteLength:0;return t.slice(e.byteOffset,e.byteOffset+a)}))}))},M.prototype.loadAccessors=function(){var e=this.json;return this._withDependencies(["bufferViews"]).then((function(r){return b(e.accessors,(function(e){var t=r.bufferViews[e.bufferView],a=T[e.type],n=u[e.componentType],i=n.BYTES_PER_ELEMENT,s=i*a;if(e.byteStride&&e.byteStride!==s){var o=new n(t),c=new THREE.InterleavedBuffer(o,e.byteStride/i);return new THREE.InterleavedBufferAttribute(c,a,e.byteOffset/i)}return o=new n(t,e.byteOffset,e.count*a),new THREE.BufferAttribute(o,a)}))}))},M.prototype.loadTextures=function(){var e=this.json,r=this.extensions,t=this.options;return this._withDependencies(["bufferViews"]).then((function(n){return b(e.textures,(function(i){if(i.source)return new Promise((function(s){var o=e.images[i.source],c=o.uri;o.extensions&&o.extensions[a.KHR_BINARY_GLTF]&&(c=r[a.KHR_BINARY_GLTF].loadTextureSourceUri(o,n.bufferViews));var u=THREE.Loader.Handlers.get(c);null===u&&(u=new THREE.TextureLoader),u.setCrossOrigin(t.crossOrigin),u.load(w(c,t.path),(function(r){if(r.flipY=!1,void 0!==i.name&&(r.name=i.name),r.format=void 0!==i.format?l[i.format]:THREE.RGBAFormat,void 0!==i.internalFormat&&r.format!==l[i.internalFormat]&&console.warn("THREE.GLTFLoader: Three.js doesn't support texture internalFormat which is different from texture format. internalFormat will be forced to be the same value as format."),r.type=void 0!==i.type?p[i.type]:THREE.UnsignedByteType,i.sampler){var t=e.samplers[i.sampler];r.magFilter=E[t.magFilter]||THREE.LinearFilter,r.minFilter=E[t.minFilter]||THREE.NearestMipMapLinearFilter,r.wrapS=d[t.wrapS]||THREE.RepeatWrapping,r.wrapT=d[t.wrapT]||THREE.RepeatWrapping}s(r)}),void 0,(function(){s()}))}))}))}))},M.prototype.loadMaterials=function(){var e=this.json;return this._withDependencies(["shaders","textures"]).then((function(r){return b(e.materials,(function(t){var n,i,s={},u={};if(t.extensions&&t.extensions[a.KHR_MATERIALS_COMMON]&&(i=t.extensions[a.KHR_MATERIALS_COMMON]),i){var E=["ambient","emission","transparent","transparency","doubleSided"];switch(i.technique){case"BLINN":case"PHONG":n=THREE.MeshPhongMaterial,E.push("diffuse","specular","shininess");break;case"LAMBERT":n=THREE.MeshLambertMaterial,E.push("diffuse");break;default:n=THREE.MeshBasicMaterial}E.forEach((function(e){void 0!==i.values[e]&&(s[e]=i.values[e])})),(i.doubleSided||s.doubleSided)&&(u.side=THREE.DoubleSide),(i.transparent||s.transparent)&&(u.transparent=!0,u.opacity=void 0!==s.transparency?s.transparency:1)}else if(void 0===t.technique)n=THREE.MeshPhongMaterial,Object.assign(s,t.values);else{n=L;var d=e.techniques[t.technique];u.uniforms={};var l=e.programs[d.program];if(l){u.fragmentShader=r.shaders[l.fragmentShader],u.fragmentShader||(console.warn("ERROR: Missing fragment shader definition:",l.fragmentShader),n=THREE.MeshPhongMaterial);var p=r.shaders[l.vertexShader];p||(console.warn("ERROR: Missing vertex shader definition:",l.vertexShader),n=THREE.MeshPhongMaterial),u.vertexShader=function(e,r){var t={};for(var a in r.attributes){var n=r.attributes[a],i=(d=r.parameters[n]).type,s=d.semantic;t[a]={type:i,semantic:s}}var o=r.parameters,c=r.attributes,u={};for(var a in t){var E=o[n=c[a]];(s=E.semantic)&&(u[a]=E)}for(var n in u){s=(d=u[n]).semantic;var d,l=new RegExp("\\b"+n+"\\b","g");switch(s){case"POSITION":e=e.replace(l,"position");break;case"NORMAL":e=e.replace(l,"normal");break;case"TEXCOORD_0":case"TEXCOORD0":case"TEXCOORD":e=e.replace(l,"uv");break;case"TEXCOORD_1":e=e.replace(l,"uv2");break;case"COLOR_0":case"COLOR0":case"COLOR":e=e.replace(l,"color");break;case"WEIGHT":e=e.replace(l,"skinWeight");break;case"JOINT":e=e.replace(l,"skinIndex")}}return e}(p,d);var T=d.uniforms;for(var R in T){var H=T[R],b=d.parameters[H],w=b.type;if(!c[w])throw new Error("Unknown shader uniform param type: "+w);var y,M=b.count;void 0!==t.values&&(y=t.values[H]);var O=new c[w],g=b.semantic,S=b.node;switch(w){case o.FLOAT:O=b.value,"transparency"==H&&(u.transparent=!0),void 0!==y&&(O=y);break;case o.FLOAT_VEC2:case o.FLOAT_VEC3:case o.FLOAT_VEC4:case o.FLOAT_MAT3:b&&b.value&&O.fromArray(b.value),y&&O.fromArray(y);break;case o.FLOAT_MAT2:console.warn("FLOAT_MAT2 is not a supported uniform type");break;case o.FLOAT_MAT4:if(M){O=new Array(M);for(var _=0;_<M;_++)O[_]=new c[w];if(b&&b.value){var x=b.value;O.fromArray(x)}y&&O.fromArray(y)}else{if(b&&b.value){var F=b.value;O.fromArray(F)}y&&O.fromArray(y)}break;case o.SAMPLER_2D:O=void 0!==y?r.textures[y]:void 0!==b.value?r.textures[b.value]:null}u.uniforms[R]={value:O,semantic:g,node:S}}for(var N=d.states||{},C=N.enable||[],D=N.functions||{},I=!1,k=!1,U=!1,B=0,G=C.length;B<G;B++){var P=C[B];switch(A[P]){case"CULL_FACE":I=!0;break;case"DEPTH_TEST":k=!0;break;case"BLEND":U=!0;break;case"SCISSOR_TEST":case"POLYGON_OFFSET_FILL":case"SAMPLE_ALPHA_TO_COVERAGE":break;default:throw new Error("Unknown technique.states.enable: "+P)}}u.side=I?void 0!==D.cullFace?f[D.cullFace]:THREE.FrontSide:THREE.DoubleSide,u.depthTest=k,u.depthFunc=void 0!==D.depthFunc?h[D.depthFunc]:THREE.LessDepth,u.depthWrite=void 0===D.depthMask||D.depthMask[0],u.blending=U?THREE.CustomBlending:THREE.NoBlending,u.transparent=U;var V=D.blendEquationSeparate;void 0!==V?(u.blendEquation=m[V[0]],u.blendEquationAlpha=m[V[1]]):(u.blendEquation=THREE.AddEquation,u.blendEquationAlpha=THREE.AddEquation);var j=D.blendFuncSeparate;void 0!==j?(u.blendSrc=v[j[0]],u.blendDst=v[j[1]],u.blendSrcAlpha=v[j[2]],u.blendDstAlpha=v[j[3]]):(u.blendSrc=THREE.OneFactor,u.blendDst=THREE.ZeroFactor,u.blendSrcAlpha=THREE.OneFactor,u.blendDstAlpha=THREE.ZeroFactor)}}Array.isArray(s.diffuse)?u.color=(new THREE.Color).fromArray(s.diffuse):"string"==typeof s.diffuse&&(u.map=r.textures[s.diffuse]),delete u.diffuse,"string"==typeof s.reflective&&(u.envMap=r.textures[s.reflective]),"string"==typeof s.bump&&(u.bumpMap=r.textures[s.bump]),Array.isArray(s.emission)?n===THREE.MeshBasicMaterial?u.color=(new THREE.Color).fromArray(s.emission):u.emissive=(new THREE.Color).fromArray(s.emission):"string"==typeof s.emission&&(n===THREE.MeshBasicMaterial?u.map=r.textures[s.emission]:u.emissiveMap=r.textures[s.emission]),Array.isArray(s.specular)?u.specular=(new THREE.Color).fromArray(s.specular):"string"==typeof s.specular&&(u.specularMap=r.textures[s.specular]),void 0!==s.shininess&&(u.shininess=s.shininess);var K=new n(u);return void 0!==t.name&&(K.name=t.name),K}))}))},M.prototype.loadMeshes=function(){var e=this.json;return this._withDependencies(["accessors","materials"]).then((function(r){return b(e.meshes,(function(e){var t=new THREE.Group;void 0!==e.name&&(t.name=e.name),e.extras&&(t.userData=e.extras);var a=e.primitives||[];for(var n in a){var i=a[n];if(i.mode===o.TRIANGLES||void 0===i.mode){var s=new THREE.BufferGeometry,c=i.attributes;for(var u in c){if(!(l=c[u]))return;var E=r.accessors[l];switch(u){case"POSITION":s.setAttribute("position",E);break;case"NORMAL":s.setAttribute("normal",E);break;case"TEXCOORD_0":case"TEXCOORD0":case"TEXCOORD":s.setAttribute("uv",E);break;case"TEXCOORD_1":s.setAttribute("uv2",E);break;case"COLOR_0":case"COLOR0":case"COLOR":s.setAttribute("color",E);break;case"WEIGHT":s.setAttribute("skinWeight",E);break;case"JOINT":s.setAttribute("skinIndex",E)}}i.indices&&s.setIndex(r.accessors[i.indices]);var d=void 0!==r.materials?r.materials[i.material]:new THREE.MeshPhongMaterial({color:0,emissive:8947848,specular:0,shininess:0,transparent:!1,depthTest:!0,side:THREE.FrontSide});(p=new THREE.Mesh(s,d)).castShadow=!0,p.name="0"===n?t.name:t.name+n,i.extras&&(p.userData=i.extras),t.add(p)}else if(i.mode===o.LINES){s=new THREE.BufferGeometry,c=i.attributes;for(var u in c){var l;if(!(l=c[u]))return;E=r.accessors[l];switch(u){case"POSITION":s.setAttribute("position",E);break;case"COLOR_0":case"COLOR0":case"COLOR":s.setAttribute("color",E)}}var p;d=r.materials[i.material];i.indices?(s.setIndex(r.accessors[i.indices]),p=new THREE.LineSegments(s,d)):p=new THREE.Line(s,d),p.name="0"===n?t.name:t.name+n,i.extras&&(p.userData=i.extras),t.add(p)}else console.warn("Only triangular and line primitives are supported")}return t}))}))},M.prototype.loadCameras=function(){return b(this.json.cameras,(function(e){if("perspective"==e.type&&e.perspective){var r=e.perspective.yfov,t=void 0!==e.perspective.aspectRatio?e.perspective.aspectRatio:1,a=r*t,n=new THREE.PerspectiveCamera(THREE.Math.radToDeg(a),t,e.perspective.znear||1,e.perspective.zfar||2e6);return void 0!==e.name&&(n.name=e.name),e.extras&&(n.userData=e.extras),n}if("orthographic"==e.type&&e.orthographic){n=new THREE.OrthographicCamera(window.innerWidth/-2,window.innerWidth/2,window.innerHeight/2,window.innerHeight/-2,e.orthographic.znear,e.orthographic.zfar);return void 0!==e.name&&(n.name=e.name),e.extras&&(n.userData=e.extras),n}}))},M.prototype.loadSkins=function(){var e=this.json;return this._withDependencies(["accessors"]).then((function(r){return b(e.skins,(function(e){var t=new THREE.Matrix4;return void 0!==e.bindShapeMatrix&&t.fromArray(e.bindShapeMatrix),{bindShapeMatrix:t,jointNames:e.jointNames,inverseBindMatrices:r.accessors[e.inverseBindMatrices]}}))}))},M.prototype.loadAnimations=function(){var e=this.json;return this._withDependencies(["accessors","nodes"]).then((function(r){return b(e.animations,(function(e,t){var a=[];for(var n in e.channels){var i=e.channels[n],s=e.samplers[i.sampler];if(s){var o=i.target,c=o.id,u=void 0!==e.parameters?e.parameters[s.input]:s.input,E=void 0!==e.parameters?e.parameters[s.output]:s.output,d=r.accessors[u],l=r.accessors[E],p=r.nodes[c];if(p){p.updateMatrix(),p.matrixAutoUpdate=!0;var f=R[o.path]===R.rotation?THREE.QuaternionKeyframeTrack:THREE.VectorKeyframeTrack,h=p.name?p.name:p.uuid,m=void 0!==s.interpolation?H[s.interpolation]:THREE.InterpolateLinear;a.push(new f(h+"."+R[o.path],THREE.AnimationUtils.arraySlice(d.array,0),THREE.AnimationUtils.arraySlice(l.array,0),m))}}}c=void 0!==e.name?e.name:"animation_"+t;return new THREE.AnimationClip(c,void 0,a)}))}))},M.prototype.loadNodes=function(){var e=this.json,r=this.extensions,t=this;return b(e.nodes,(function(e){var r,t=new THREE.Matrix4;return e.jointName?((r=new THREE.Bone).name=void 0!==e.name?e.name:e.jointName,r.jointName=e.jointName):(r=new THREE.Object3D,void 0!==e.name&&(r.name=e.name)),e.extras&&(r.userData=e.extras),void 0!==e.matrix?(t.fromArray(e.matrix),r.applyMatrix4(t)):(void 0!==e.translation&&r.position.fromArray(e.translation),void 0!==e.rotation&&r.quaternion.fromArray(e.rotation),void 0!==e.scale&&r.scale.fromArray(e.scale)),r})).then((function(n){return t._withDependencies(["meshes","skins","cameras"]).then((function(t){return b(n,(function(i,s){var o=e.nodes[s];if(void 0!==o.meshes)for(var c in o.meshes){var u=o.meshes[c],E=t.meshes[u];if(void 0!==E)for(var d in E.children){var l,p=E.children[d],f=p.material,h=p.geometry,m=p.userData,v=p.name;switch(f.isDeferredShaderMaterial?f=T=f.create():T=f,p.type){case"LineSegments":p=new THREE.LineSegments(h,T);break;case"LineLoop":p=new THREE.LineLoop(h,T);break;case"Line":p=new THREE.Line(h,T);break;default:p=new THREE.Mesh(h,T)}if(p.castShadow=!0,p.userData=m,p.name=v,o.skin&&(l=t.skins[o.skin]),l){var T,R=function(e){for(var r=Object.keys(n),t=0,a=r.length;t<a;t++){var i=n[r[t]];if(i.jointName===e)return i}return null},H=h;(T=f).skinning=!0,(p=new THREE.SkinnedMesh(H,T,!1)).castShadow=!0,p.userData=m,p.name=v;for(var A=[],b=[],w=0,y=l.jointNames.length;w<y;w++){var L=l.jointNames[w],M=R(L);if(M){A.push(M);var O=l.inverseBindMatrices.array,g=(new THREE.Matrix4).fromArray(O,16*w);b.push(g)}else console.warn("WARNING: joint: '"+L+"' could not be found")}p.bind(new THREE.Skeleton(A,b,!1),l.bindShapeMatrix);var S=function(r,t,a){var i=r[a];if(void 0!==i)for(var s=0,o=i.length;s<o;s++){var c=i[s],u=n[c],E=e.nodes[c];void 0!==u&&!0===u.isBone&&void 0!==E&&(t.add(u),S(E,u,"children"))}};S(o,p,"skeletons")}i.add(p)}else console.warn("GLTFLoader: Couldn't find node \""+u+'".')}if(void 0!==o.camera){var _=t.cameras[o.camera];i.add(_)}if(o.extensions&&o.extensions[a.KHR_MATERIALS_COMMON]&&o.extensions[a.KHR_MATERIALS_COMMON].light){var x=r[a.KHR_MATERIALS_COMMON].lights[o.extensions[a.KHR_MATERIALS_COMMON].light];i.add(x)}return i}))}))}))},M.prototype.loadScenes=function(){var e=this.json;function r(t,a,n){var i=n[t];a.add(i);var s=e.nodes[t];if(s.children)for(var o=s.children,c=0,u=o.length;c<u;c++){r(o[c],i,n)}}return this._withDependencies(["nodes"]).then((function(a){return b(e.scenes,(function(e){var n=new THREE.Scene;void 0!==e.name&&(n.name=e.name),e.extras&&(n.userData=e.extras);for(var i=e.nodes||[],s=0,o=i.length;s<o;s++){r(i[s],n,a.nodes)}return n.traverse((function(e){e.material&&e.material.isRawShaderMaterial&&(e.gltfShader=new t(e,a.nodes),e.onBeforeRender=function(e,r,t){this.gltfShader.update(r,t)})})),n}))}))},e}(),THREE.GLTF2Loader=function(){function e(e){this.manager=void 0!==e?e:THREE.DefaultLoadingManager}function r(){var e={};return{get:function(r){return e[r]},add:function(r,t){e[r]=t},remove:function(r){delete e[r]},removeAll:function(){e={}},update:function(r,t){for(var a in e){var n=e[a];n.update&&n.update(r,t)}}}}function t(e,r){var t={},a=e.material.uniforms;for(var n in a){var i=a[n];if(i.semantic){var s=i.node,o=e;s&&(o=r[s]),t[n]={semantic:i.semantic,sourceNode:o,targetNode:e,uniform:i}}}this.boundUniforms=t,this._m4=new THREE.Matrix4}e.prototype={constructor:e,load:function(e,r,t,a){var n=this,i=this.path&&"string"==typeof this.path?this.path:THREE.LoaderUtils.extractUrlBase(e),s=new THREE.FileLoader(n.manager);s.setResponseType("arraybuffer"),s.load(e,(function(e){n.parse(e,r,i)}),t,a)},setCrossOrigin:function(e){this.crossOrigin=e},setPath:function(e){this.path=e},parse:function(e,r,t){var s,o={};M(new Uint8Array(e,0,4))===i?(o[a.KHR_BINARY_GLTF]=new c(e),s=o[a.KHR_BINARY_GLTF].content):s=M(new Uint8Array(e));var u=JSON.parse(s);u.extensionsUsed&&u.extensionsUsed.indexOf(a.KHR_MATERIALS_COMMON)>=0&&(o[a.KHR_MATERIALS_COMMON]=new n(u)),new g(u,o,{path:t||this.path,crossOrigin:this.crossOrigin}).parse((function(e,t,a,n){r({scene:e,scenes:t,cameras:a,animations:n})}))}},t.prototype.update=function(e,r){var t=this.boundUniforms;for(var a in t){var n=t[a];switch(n.semantic){case"MODELVIEW":n.uniform.value.multiplyMatrices(r.matrixWorldInverse,n.sourceNode.matrixWorld);break;case"MODELVIEWINVERSETRANSPOSE":var i=n.uniform.value;this._m4.multiplyMatrices(r.matrixWorldInverse,n.sourceNode.matrixWorld),i.getNormalMatrix(this._m4);break;case"PROJECTION":n.uniform.value.copy(r.projectionMatrix);break;case"JOINTMATRIX":for(var s=n.uniform.value,o=0;o<s.length;o++)clone[o].copy(n.sourceNode.matrixWorld).invert().multiply(n.targetNode.skeleton.bones[o].matrixWorld).multiply(n.targetNode.skeleton.boneInverses[o]).multiply(n.targetNode.bindMatrix);break;default:console.warn("Unhandled shader semantic: "+n.semantic)}}};var a={KHR_BINARY_GLTF:"KHR_binary_glTF",KHR_MATERIALS_COMMON:"KHR_materials_common"};function n(e){this.name=a.KHR_MATERIALS_COMMON,this.lights={};var r=(e.extensions&&e.extensions[a.KHR_MATERIALS_COMMON]||{}).lights||{};for(var t in r){var n,i=r[t],s=i[i.type],o=(new THREE.Color).fromArray(s.color);switch(i.type){case"directional":(n=new THREE.DirectionalLight(o)).position.set(0,0,1);break;case"point":n=new THREE.PointLight(o);break;case"spot":(n=new THREE.SpotLight(o)).position.set(0,0,1);break;case"ambient":n=new THREE.AmbientLight(o)}n&&(this.lights[t]=n)}}var i="glTF",s=1313821514,o=5130562;function c(e){this.name=a.KHR_BINARY_GLTF,this.content=null,this.body=null;var r=new DataView(e,0,12);if(this.header={magic:M(new Uint8Array(e.slice(0,4))),version:r.getUint32(4,!0),length:r.getUint32(8,!0)},this.header.magic!==i)throw new Error("GLTF2Loader: Unsupported glTF-Binary header.");if(this.header.version<2)throw new Error("GLTF2Loader: Legacy binary file detected. Use GLTFLoader instead.");for(var t=new DataView(e,12),n=0;n<t.byteLength;){var c=t.getUint32(n,!0);n+=4;var u=t.getUint32(n,!0);if(n+=4,u===s){var E=new Uint8Array(e,12+n,c);this.content=M(E)}else if(u===o){var d=12+n;this.body=e.slice(d,d+c)}n+=c}if(null===this.content)throw new Error("GLTF2Loader: JSON content not found.")}var u={FLOAT:5126,FLOAT_MAT3:35675,FLOAT_MAT4:35676,FLOAT_VEC2:35664,FLOAT_VEC3:35665,FLOAT_VEC4:35666,LINEAR:9729,REPEAT:10497,SAMPLER_2D:35678,TRIANGLES:4,LINES:1,UNSIGNED_BYTE:5121,UNSIGNED_SHORT:5123,VERTEX_SHADER:35633,FRAGMENT_SHADER:35632},E={5126:Number,35675:THREE.Matrix3,35676:THREE.Matrix4,35664:THREE.Vector2,35665:THREE.Vector3,35666:THREE.Vector4,35678:THREE.Texture},d={5120:Int8Array,5121:Uint8Array,5122:Int16Array,5123:Uint16Array,5125:Uint32Array,5126:Float32Array},l={9728:THREE.NearestFilter,9729:THREE.LinearFilter,9984:THREE.NearestMipMapNearestFilter,9985:THREE.LinearMipMapNearestFilter,9986:THREE.NearestMipMapLinearFilter,9987:THREE.LinearMipMapLinearFilter},p={33071:THREE.ClampToEdgeWrapping,33648:THREE.MirroredRepeatWrapping,10497:THREE.RepeatWrapping},f={6406:THREE.AlphaFormat,6407:THREE.RGBFormat,6408:THREE.RGBAFormat,6409:THREE.LuminanceFormat,6410:THREE.LuminanceAlphaFormat},h={5121:THREE.UnsignedByteType,32819:THREE.UnsignedShort4444Type,32820:THREE.UnsignedShort5551Type,33635:THREE.UnsignedShort565Type},m={1028:THREE.BackSide,1029:THREE.FrontSide},v={512:THREE.NeverDepth,513:THREE.LessDepth,514:THREE.EqualDepth,515:THREE.LessEqualDepth,516:THREE.GreaterEqualDepth,517:THREE.NotEqualDepth,518:THREE.GreaterEqualDepth,519:THREE.AlwaysDepth},T={32774:THREE.AddEquation,32778:THREE.SubtractEquation,32779:THREE.ReverseSubtractEquation},R={0:THREE.ZeroFactor,1:THREE.OneFactor,768:THREE.SrcColorFactor,769:THREE.OneMinusSrcColorFactor,770:THREE.SrcAlphaFactor,771:THREE.OneMinusSrcAlphaFactor,772:THREE.DstAlphaFactor,773:THREE.OneMinusDstAlphaFactor,774:THREE.DstColorFactor,775:THREE.OneMinusDstColorFactor,776:THREE.SrcAlphaSaturateFactor},H={SCALAR:1,VEC2:2,VEC3:3,VEC4:4,MAT2:4,MAT3:9,MAT4:16},A={scale:"scale",translation:"position",rotation:"quaternion"},b={LINEAR:THREE.InterpolateLinear,STEP:THREE.InterpolateDiscrete},w={2884:"CULL_FACE",2929:"DEPTH_TEST",3042:"BLEND",3089:"SCISSOR_TEST",32823:"POLYGON_OFFSET_FILL",32926:"SAMPLE_ALPHA_TO_COVERAGE"};function y(e,r,t){if(!e)return Promise.resolve();var a,n=[];if("[object Array]"===Object.prototype.toString.call(e)){a=[];for(var i=e.length,s=0;s<i;s++){(c=r.call(t||this,e[s],s))&&(n.push(c),c instanceof Promise?c.then(function(e,r){a[e]=r}.bind(this,s)):a[s]=c)}}else for(var o in a={},e){var c;if(e.hasOwnProperty(o))(c=r.call(t||this,e[o],o))&&(n.push(c),c instanceof Promise?c.then(function(e,r){a[e]=r}.bind(this,o)):a[o]=c)}return Promise.all(n).then((function(){return a}))}function L(e,r){return"string"!=typeof e||""===e?"":/^(https?:)?\/\//i.test(e)||/^data:.*,.*$/i.test(e)||/^blob:.*$/i.test(e)?e:(r||"")+e}function M(e){for(var r="",t=0;t<e.length;t++)r+=String.fromCharCode(e[t]);return r}function O(e){this.isDeferredShaderMaterial=!0,this.params=e}function g(e,t,a){this.json=e||{},this.extensions=t||{},this.options=a||{},this.cache=new r}return O.prototype.create=function(){var e=THREE.UniformsUtils.clone(this.params.uniforms);for(var r in this.params.uniforms){var t=this.params.uniforms[r];t.value instanceof THREE.Texture&&(e[r].value=t.value,e[r].value.needsUpdate=!0),e[r].semantic=t.semantic,e[r].node=t.node}return this.params.uniforms=e,new THREE.RawShaderMaterial(this.params)},g.prototype._withDependencies=function(e){for(var r={},t=0;t<e.length;t++){var a=e[t],n="load"+a.charAt(0).toUpperCase()+a.slice(1),i=this.cache.get(a);if(void 0!==i)r[a]=i;else if(this[n]){var s=this[n]();this.cache.add(a,s),r[a]=s}}return y(r,(function(e){return e}))},g.prototype.parse=function(e){var r=this.json;this.cache.removeAll(),this._withDependencies(["scenes","cameras","animations"]).then((function(t){var a=[];for(var n in t.scenes)a.push(t.scenes[n]);var i=void 0!==r.scene?t.scenes[r.scene]:a[0],s=[];for(var n in t.cameras){var o=t.cameras[n];s.push(o)}var c=[];for(var n in t.animations)c.push(t.animations[n]);e(i,a,s,c)}))},g.prototype.loadShaders=function(){var e=this.json,r=this.options;return this._withDependencies(["bufferViews"]).then((function(t){return y(e.shaders,(function(e){if(void 0!==e.bufferView){var a=t.bufferViews[e.bufferView];return M(new Uint8Array(a))}return new Promise((function(t){var a=new THREE.FileLoader;a.setResponseType("text"),a.load(L(e.uri,r.path),(function(e){t(e)}))}))}))}))},g.prototype.loadBuffers=function(){var e=this.json,r=this.extensions,t=this.options;return y(e.buffers,(function(e,n){if("arraybuffer"===e.type||void 0===e.type)return void 0===e.uri&&0===n?r[a.KHR_BINARY_GLTF].body:new Promise((function(r){var a=new THREE.FileLoader;a.setResponseType("arraybuffer"),a.load(L(e.uri,t.path),(function(e){r(e)}))}));console.warn("THREE.GLTF2Loader: "+e.type+" buffer type is not supported")}))},g.prototype.loadBufferViews=function(){var e=this.json;return this._withDependencies(["buffers"]).then((function(r){return y(e.bufferViews,(function(e){var t=r.buffers[e.buffer],a=void 0!==e.byteLength?e.byteLength:0;return null==e.byteOffset&&(e.byteOffset=0),t.slice(e.byteOffset,e.byteOffset+a)}))}))},g.prototype.loadAccessors=function(){var e=this.json;return this._withDependencies(["bufferViews"]).then((function(r){return y(e.accessors,(function(e){var t,a=r.bufferViews[e.bufferView],n=H[e.type],i=d[e.componentType],s=i.BYTES_PER_ELEMENT,o=s*n;if(e.byteStride&&e.byteStride!==o){t=new i(a);var c=new THREE.InterleavedBuffer(t,e.byteStride/s);return new THREE.InterleavedBufferAttribute(c,n,e.byteOffset/s)}return t=new i(a,e.byteOffset,e.count*n),new THREE.BufferAttribute(t,n)}))}))},g.prototype.loadTextures=function(){var e=this.json,r=this.options;return this._withDependencies(["bufferViews"]).then((function(t){return y(e.textures,(function(a){if(void 0!==a.source)return new Promise((function(n){var i,s=e.images[a.source],o=s.uri;if(void 0!==s.bufferView){var c=t.bufferViews[s.bufferView],u=new Blob([c],{type:s.mimeType});i=window.URL||window.webkitURL,o=i.createObjectURL(u)}var E=THREE.Loader.Handlers.get(o);null===E&&(E=new THREE.TextureLoader),E.setCrossOrigin(r.crossOrigin),E.load(L(o,r.path),(function(r){if(void 0!==i&&i.revokeObjectURL(o),r.flipY=!1,void 0!==a.name&&(r.name=a.name),r.format=void 0!==a.format?f[a.format]:THREE.RGBAFormat,void 0!==a.internalFormat&&r.format!==f[a.internalFormat]&&console.warn("THREE.GLTF2Loader: Three.js doesn't support texture internalFormat which is different from texture format. internalFormat will be forced to be the same value as format."),r.type=void 0!==a.type?h[a.type]:THREE.UnsignedByteType,void 0!==a.sampler){var t=e.samplers[a.sampler];r.magFilter=l[t.magFilter]||THREE.LinearFilter,r.minFilter=l[t.minFilter]||THREE.NearestMipMapLinearFilter,r.wrapS=p[t.wrapS]||THREE.RepeatWrapping,r.wrapT=p[t.wrapT]||THREE.RepeatWrapping}n(r)}),void 0,(function(){n()}))}))}))}))},g.prototype.loadMaterials=function(){var e=this.json;return this._withDependencies(["shaders","textures"]).then((function(r){return y(e.materials,(function(t){var n,i,s={},o={};if(t.extensions&&t.extensions[a.KHR_MATERIALS_COMMON]&&(i=t.extensions[a.KHR_MATERIALS_COMMON]),i){var c=["ambient","emission","transparent","transparency","doubleSided"];switch(i.technique){case"BLINN":case"PHONG":n=THREE.MeshPhongMaterial,c.push("diffuse","specular","shininess");break;case"LAMBERT":n=THREE.MeshLambertMaterial,c.push("diffuse");break;default:n=THREE.MeshBasicMaterial}c.forEach((function(e){void 0!==i.values[e]&&(s[e]=i.values[e])})),(i.doubleSided||s.doubleSided)&&(o.side=THREE.DoubleSide),(i.transparent||s.transparent)&&(o.transparent=!0,o.opacity=void 0!==s.transparency?s.transparency:1)}else if(void 0===t.technique){if(void 0!==t.pbrMetallicRoughness){if(n=THREE.MeshStandardMaterial,void 0!==t.pbrMetallicRoughness){var d=t.pbrMetallicRoughness;if(o.color=new THREE.Color(1,1,1),o.opacity=1,Array.isArray(d.baseColorFactor)){var l=d.baseColorFactor;o.color.fromArray(l),o.opacity=l[3]}if(void 0!==d.baseColorTexture&&(o.map=r.textures[d.baseColorTexture.index]),(o.opacity<1||void 0!==o.map&&(o.map.format===THREE.AlphaFormat||o.map.format===THREE.RGBAFormat||o.map.format===THREE.LuminanceAlphaFormat))&&(o.transparent=!0),o.metalness=void 0!==d.metallicFactor?d.metallicFactor:1,o.roughness=void 0!==d.roughnessFactor?d.roughnessFactor:1,void 0!==d.metallicRoughnessTexture){var p=d.metallicRoughnessTexture.index;o.metalnessMap=r.textures[p],o.roughnessMap=r.textures[p]}}}else n=THREE.MeshPhongMaterial;void 0!==t.normalTexture&&(o.normalMap=r.textures[t.normalTexture.index]),void 0!==t.occlusionTexture&&(o.aoMap=r.textures[t.occlusionTexture.index]),void 0!==t.emissiveTexture&&(o.emissiveMap=r.textures[t.emissiveTexture.index]),o.emissive=new THREE.Color(0,0,0),void 0!==t.emissiveFactor&&o.emissive.fromArray(t.emissiveFactor),Object.assign(s,t.values)}else{n=O;var f=e.techniques[t.technique];o.uniforms={};var h=e.programs[f.program];if(h){o.fragmentShader=r.shaders[h.fragmentShader],o.fragmentShader||(console.warn("ERROR: Missing fragment shader definition:",h.fragmentShader),n=THREE.MeshPhongMaterial);var H=r.shaders[h.vertexShader];H||(console.warn("ERROR: Missing vertex shader definition:",h.vertexShader),n=THREE.MeshPhongMaterial),o.vertexShader=function(e,r){var t={};for(var a in r.attributes){var n=r.attributes[a],i=(d=r.parameters[n]).type,s=d.semantic;t[a]={type:i,semantic:s}}var o=r.parameters,c=r.attributes,u={};for(var a in t){var E=o[n=c[a]];(s=E.semantic)&&(u[a]=E)}for(var n in u){s=(d=u[n]).semantic;var d,l=new RegExp("\\b"+n+"\\b","g");switch(s){case"POSITION":e=e.replace(l,"position");break;case"NORMAL":e=e.replace(l,"normal");break;case"TEXCOORD_0":case"TEXCOORD0":case"TEXCOORD":e=e.replace(l,"uv");break;case"TEXCOORD_1":e=e.replace(l,"uv2");break;case"COLOR_0":case"COLOR0":case"COLOR":e=e.replace(l,"color");break;case"WEIGHTS_0":case"WEIGHT":e=e.replace(l,"skinWeight");break;case"JOINTS_0":case"JOINT":e=e.replace(l,"skinIndex")}}return e}(H,f);var A=f.uniforms;for(var b in A){var y=A[b],L=f.parameters[y],M=L.type;if(!E[M])throw new Error("Unknown shader uniform param type: "+M);var g,S=L.count;void 0!==t.values&&(g=t.values[y]);var _=new E[M],x=L.semantic,F=L.node;switch(M){case u.FLOAT:_=L.value,"transparency"==y&&(o.transparent=!0),void 0!==g&&(_=g);break;case u.FLOAT_VEC2:case u.FLOAT_VEC3:case u.FLOAT_VEC4:case u.FLOAT_MAT3:L&&L.value&&_.fromArray(L.value),g&&_.fromArray(g);break;case u.FLOAT_MAT2:console.warn("FLOAT_MAT2 is not a supported uniform type");break;case u.FLOAT_MAT4:if(S){_=new Array(S);for(var N=0;N<S;N++)_[N]=new E[M];if(L&&L.value){var C=L.value;_.fromArray(C)}g&&_.fromArray(g)}else{if(L&&L.value){var D=L.value;_.fromArray(D)}g&&_.fromArray(g)}break;case u.SAMPLER_2D:_=void 0!==g?r.textures[g.index]:void 0!==L.value?r.textures[L.value]:null}o.uniforms[b]={value:_,semantic:x,node:F}}for(var I=f.states||{},k=I.enable||[],U=I.functions||{},B=!1,G=!1,P=!1,V=0,j=k.length;V<j;V++){var K=k[V];switch(w[K]){case"CULL_FACE":B=!0;break;case"DEPTH_TEST":G=!0;break;case"BLEND":P=!0;break;case"SCISSOR_TEST":case"POLYGON_OFFSET_FILL":case"SAMPLE_ALPHA_TO_COVERAGE":break;default:throw new Error("Unknown technique.states.enable: "+K)}}o.side=B?void 0!==U.cullFace?m[U.cullFace]:THREE.FrontSide:THREE.DoubleSide,o.depthTest=G,o.depthFunc=void 0!==U.depthFunc?v[U.depthFunc]:THREE.LessDepth,o.depthWrite=void 0===U.depthMask||U.depthMask[0],o.blending=P?THREE.CustomBlending:THREE.NoBlending,o.transparent=P;var q=U.blendEquationSeparate;void 0!==q?(o.blendEquation=T[q[0]],o.blendEquationAlpha=T[q[1]]):(o.blendEquation=THREE.AddEquation,o.blendEquationAlpha=THREE.AddEquation);var W=U.blendFuncSeparate;void 0!==W?(o.blendSrc=R[W[0]],o.blendDst=R[W[1]],o.blendSrcAlpha=R[W[2]],o.blendDstAlpha=R[W[3]]):(o.blendSrc=THREE.OneFactor,o.blendDst=THREE.ZeroFactor,o.blendSrcAlpha=THREE.OneFactor,o.blendDstAlpha=THREE.ZeroFactor)}}t.extensions&&t.extensions.KHR_techniques_webgl&&null!=t.extensions.KHR_techniques_webgl.values.u_diffuse.index&&(o.map=r.textures[t.extensions.KHR_techniques_webgl.values.u_diffuse.index]),Array.isArray(s.diffuse)?o.color=(new THREE.Color).fromArray(s.diffuse):"string"==typeof s.diffuse&&(o.map=r.textures[s.diffuse]),delete o.diffuse,"string"==typeof s.reflective&&(o.envMap=r.textures[s.reflective]),"string"==typeof s.bump&&(o.bumpMap=r.textures[s.bump]),Array.isArray(s.emission)?n===THREE.MeshBasicMaterial?o.color=(new THREE.Color).fromArray(s.emission):o.emissive=(new THREE.Color).fromArray(s.emission):"string"==typeof s.emission&&(n===THREE.MeshBasicMaterial?o.map=r.textures[s.emission]:o.emissiveMap=r.textures[s.emission]),Array.isArray(s.specular)?o.specular=(new THREE.Color).fromArray(s.specular):"string"==typeof s.specular&&(o.specularMap=r.textures[s.specular]),void 0!==s.shininess&&(o.shininess=s.shininess);var Y=new n(o);return void 0!==t.name&&(Y.name=t.name),Y}))}))},g.prototype.loadMeshes=function(){var e=this.json;return this._withDependencies(["accessors","materials"]).then((function(r){return y(e.meshes,(function(e){var t=new THREE.Group;void 0!==e.name&&(t.name=e.name),e.extras&&(t.userData=e.extras);var a=e.primitives||[];for(var n in a){var i,s,o=a[n],c=void 0!==o.material?r.materials[o.material]:new THREE.MeshPhongMaterial({color:0,emissive:8947848,specular:0,shininess:0,transparent:!1,depthTest:!0,side:THREE.FrontSide});if(o.mode===u.TRIANGLES||void 0===o.mode){i=new THREE.BufferGeometry;var E=o.attributes;for(var d in E){if(void 0===(p=E[d]))return;var l=r.accessors[p];switch(d){case"POSITION":i.setAttribute("position",l);break;case"NORMAL":i.setAttribute("normal",l);break;case"TEXCOORD_0":case"TEXCOORD0":case"TEXCOORD":i.setAttribute("uv",l);break;case"TEXCOORD_1":i.setAttribute("uv2",l);break;case"COLOR_0":case"COLOR0":case"COLOR":i.setAttribute("color",l);break;case"WEIGHTS_0":case"WEIGHT":i.setAttribute("skinWeight",l);break;case"JOINTS_0":case"JOINT":i.setAttribute("skinIndex",l)}}void 0!==o.indices&&i.setIndex(r.accessors[o.indices]),(s=new THREE.Mesh(i,c)).castShadow=!0}else{if(o.mode!==u.LINES)throw new Error("Only triangular and line primitives are supported");i=new THREE.BufferGeometry;E=o.attributes;for(var d in E){var p;if(!(p=E[d]))return;l=r.accessors[p];switch(d){case"POSITION":i.setAttribute("position",l);break;case"COLOR_0":case"COLOR0":case"COLOR":i.setAttribute("color",l)}}void 0!==o.indices?(i.setIndex(r.accessors[o.indices]),s=new THREE.LineSegments(i,c)):s=new THREE.Line(i,c)}void 0!==i.attributes.color&&(c.vertexColors=THREE.VertexColors,c.needsUpdate=!0),s.name="0"===n?t.name:t.name+n,o.extras&&(s.userData=o.extras),t.add(s)}return t}))}))},g.prototype.loadCameras=function(){return y(this.json.cameras,(function(e){if("perspective"==e.type&&e.perspective){var r=e.perspective.yfov,t=void 0!==e.perspective.aspectRatio?e.perspective.aspectRatio:1,a=r*t,n=new THREE.PerspectiveCamera(THREE.Math.radToDeg(a),t,e.perspective.znear||1,e.perspective.zfar||2e6);return void 0!==e.name&&(n.name=e.name),e.extras&&(n.userData=e.extras),n}if("orthographic"==e.type&&e.orthographic){n=new THREE.OrthographicCamera(window.innerWidth/-2,window.innerWidth/2,window.innerHeight/2,window.innerHeight/-2,e.orthographic.znear,e.orthographic.zfar);return void 0!==e.name&&(n.name=e.name),e.extras&&(n.userData=e.extras),n}}))},g.prototype.loadSkins=function(){var e=this.json;return this._withDependencies(["accessors"]).then((function(r){return y(e.skins,(function(e){var t=new THREE.Matrix4;return void 0!==e.bindShapeMatrix&&t.fromArray(e.bindShapeMatrix),{bindShapeMatrix:t,jointNames:e.jointNames,inverseBindMatrices:r.accessors[e.inverseBindMatrices]}}))}))},g.prototype.loadAnimations=function(){var e=this.json;return this._withDependencies(["accessors","nodes"]).then((function(r){return y(e.animations,(function(e,t){var a=[];for(var n in e.channels){var i=e.channels[n],s=e.samplers[i.sampler];if(s){var o=i.target,c=o.node||o.id,u=void 0!==e.parameters?e.parameters[s.input]:s.input,E=void 0!==e.parameters?e.parameters[s.output]:s.output,d=r.accessors[u],l=r.accessors[E],p=r.nodes[c];if(p){p.updateMatrix(),p.matrixAutoUpdate=!0;var f=A[o.path]===A.rotation?THREE.QuaternionKeyframeTrack:THREE.VectorKeyframeTrack,h=p.name?p.name:p.uuid,m=void 0!==s.interpolation?b[s.interpolation]:THREE.InterpolateLinear;a.push(new f(h+"."+A[o.path],THREE.AnimationUtils.arraySlice(d.array,0),THREE.AnimationUtils.arraySlice(l.array,0),m))}}}c=void 0!==e.name?e.name:"animation_"+t;return new THREE.AnimationClip(c,void 0,a)}))}))},g.prototype.loadNodes=function(){var e=this.json,r=this.extensions,t=this;return y(e.nodes,(function(e){var r,t=new THREE.Matrix4;return e.jointName?((r=new THREE.Bone).name=void 0!==e.name?e.name:e.jointName,r.jointName=e.jointName):(r=new THREE.Object3D,void 0!==e.name&&(r.name=e.name)),e.extras&&(r.userData=e.extras),void 0!==e.matrix?(t.fromArray(e.matrix),r.applyMatrix4(t)):(void 0!==e.translation&&r.position.fromArray(e.translation),void 0!==e.rotation&&r.quaternion.fromArray(e.rotation),void 0!==e.scale&&r.scale.fromArray(e.scale)),r})).then((function(n){return t._withDependencies(["meshes","skins","cameras"]).then((function(t){return y(n,(function(i,s){var o,c=e.nodes[s];if(void 0!==c.mesh?o=[c.mesh]:void 0!==c.meshes&&(console.warn("GLTF2Loader: Legacy glTF file detected. Nodes may have no more than 1 mesh."),o=c.meshes),void 0!==o)for(var u in o){var E=o[u],d=t.meshes[E];if(void 0!==d)for(var l in d.children){var p,f=d.children[l],h=f.material,m=f.geometry,v=f.userData,T=f.name;switch(h.isDeferredShaderMaterial?h=R=h.create():R=h,f.type){case"LineSegments":f=new THREE.LineSegments(m,R);break;case"LineLoop":f=new THREE.LineLoop(m,R);break;case"Line":f=new THREE.Line(m,R);break;default:f=new THREE.Mesh(m,R)}if(f.castShadow=!0,f.userData=v,f.name=T,void 0!==c.skin&&(p=t.skins[c.skin]),p){var R,H=function(e){for(var r=Object.keys(n),t=0,a=r.length;t<a;t++){var i=n[r[t]];if(i.jointName===e)return i}return null},A=m;(R=h).skinning=!0,(f=new THREE.SkinnedMesh(A,R,!1)).castShadow=!0,f.userData=v,f.name=T;for(var b=[],w=[],y=0,L=p.jointNames.length;y<L;y++){var M=p.jointNames[y],O=H(M);if(O){b.push(O);var g=p.inverseBindMatrices.array,S=(new THREE.Matrix4).fromArray(g,16*y);w.push(S)}else console.warn("WARNING: joint: '"+M+"' could not be found")}f.bind(new THREE.Skeleton(b,w,!1),p.bindShapeMatrix);var _=function(r,t,a){var i=r[a];if(void 0!==i)for(var s=0,o=i.length;s<o;s++){var c=i[s],u=n[c],E=e.nodes[c];void 0!==u&&!0===u.isBone&&void 0!==E&&(t.add(u),_(E,u,"children"))}};_(c,f,"skeletons")}i.add(f)}else console.warn("GLTF2Loader: Couldn't find node \""+E+'".')}if(void 0!==c.camera){var x=t.cameras[c.camera];i.add(x)}if(c.extensions&&c.extensions[a.KHR_MATERIALS_COMMON]&&c.extensions[a.KHR_MATERIALS_COMMON].light){var F=r[a.KHR_MATERIALS_COMMON].lights[c.extensions[a.KHR_MATERIALS_COMMON].light];i.add(F)}return i}))}))}))},g.prototype.loadScenes=function(){var e=this.json;function r(t,a,n){var i=n[t];a.add(i);var s=e.nodes[t];if(s.children)for(var o=s.children,c=0,u=o.length;c<u;c++){r(o[c],i,n)}}return this._withDependencies(["nodes"]).then((function(a){return y(e.scenes,(function(e){var n=new THREE.Scene;void 0!==e.name&&(n.name=e.name),e.extras&&(n.userData=e.extras);for(var i=e.nodes||[],s=0,o=i.length;s<o;s++){r(i[s],n,a.nodes)}return n.traverse((function(e){e.material&&e.material.isRawShaderMaterial&&(e.gltfShader=new t(e,a.nodes),e.onBeforeRender=function(e,r,t){this.gltfShader.update(r,t)})})),n}))}))},e}();