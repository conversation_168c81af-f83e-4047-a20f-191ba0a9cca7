!function(t,s){"object"==typeof exports&&"undefined"!=typeof module?module.exports=s():"function"==typeof define&&define.amd?define(s):t.proj4=s()}(this,(function(){"use strict";var t=484813681109536e-20,s=Math.PI/2,i=1e-10,a=.017453292519943295,h=57.29577951308232,e=Math.PI/4,n=2*Math.PI,r=3.14159265359,o={greenwich:0,lisbon:-9.131906111111,paris:2.337229166667,bogota:-74.080916666667,madrid:-3.687938888889,rome:12.452333333333,bern:7.439583333333,jakarta:106.807719444444,ferro:-17.666666666667,brussels:4.367975,stockholm:18.058277777778,athens:23.7163375,oslo:10.722916666667},l={ft:{to_meter:.3048},"us-ft":{to_meter:1200/3937}},c=/[\s_\-\/\(\)]/g;function M(t,s){if(t[s])return t[s];for(var i,a=Object.keys(t),h=s.toLowerCase().replace(c,""),e=-1;++e<a.length;)if((i=a[e]).toLowerCase().replace(c,"")===h)return t[i]}var u=function(t){var s,i,h,e={},n=t.split("+").map((function(t){return t.trim()})).filter((function(t){return t})).reduce((function(t,s){var i=s.split("=");return i.push(!0),t[i[0].toLowerCase()]=i[1],t}),{}),r={proj:"projName",datum:"datumCode",rf:function(t){e.rf=parseFloat(t)},lat_0:function(t){e.lat0=t*a},lat_1:function(t){e.lat1=t*a},lat_2:function(t){e.lat2=t*a},lat_ts:function(t){e.lat_ts=t*a},lon_0:function(t){e.long0=t*a},lon_1:function(t){e.long1=t*a},lon_2:function(t){e.long2=t*a},alpha:function(t){e.alpha=parseFloat(t)*a},lonc:function(t){e.longc=t*a},x_0:function(t){e.x0=parseFloat(t)},y_0:function(t){e.y0=parseFloat(t)},k_0:function(t){e.k0=parseFloat(t)},k:function(t){e.k0=parseFloat(t)},a:function(t){e.a=parseFloat(t)},b:function(t){e.b=parseFloat(t)},r_a:function(){e.R_A=!0},zone:function(t){e.zone=parseInt(t,10)},south:function(){e.utmSouth=!0},towgs84:function(t){e.datum_params=t.split(",").map((function(t){return parseFloat(t)}))},to_meter:function(t){e.to_meter=parseFloat(t)},units:function(t){e.units=t;var s=M(l,t);s&&(e.to_meter=s.to_meter)},from_greenwich:function(t){e.from_greenwich=t*a},pm:function(t){var s=M(o,t);e.from_greenwich=(s||parseFloat(t))*a},nadgrids:function(t){"@null"===t?e.datumCode="none":e.nadgrids=t},axis:function(t){var s="ewnsud";3===t.length&&-1!==s.indexOf(t.substr(0,1))&&-1!==s.indexOf(t.substr(1,1))&&-1!==s.indexOf(t.substr(2,1))&&(e.axis=t)}};for(s in n)i=n[s],s in r?"function"==typeof(h=r[s])?h(i):e[h]=i:e[s]=i;return"string"==typeof e.datumCode&&"WGS84"!==e.datumCode&&(e.datumCode=e.datumCode.toLowerCase()),e},f=/\s/,m=/[A-Za-z]/,p=/[A-Za-z84]/,d=/[,\]]/,y=/[\d\.E\-\+]/;function _(t){if("string"!=typeof t)throw new Error("not a string");this.text=t.trim(),this.level=0,this.place=0,this.root=null,this.stack=[],this.currentObject=null,this.state=1}function x(t,s,i){Array.isArray(s)&&(i.unshift(s),s=null);var a=s?{}:t,h=i.reduce((function(t,s){return v(s,t),t}),a);s&&(t[s]=h)}function v(t,s){if(Array.isArray(t)){var i=t.shift();if("PARAMETER"===i&&(i=t.shift()),1===t.length)return Array.isArray(t[0])?(s[i]={},void v(t[0],s[i])):void(s[i]=t[0]);if(t.length)if("TOWGS84"!==i){if("AXIS"===i)return i in s||(s[i]=[]),void s[i].push(t);var a;switch(Array.isArray(i)||(s[i]={}),i){case"UNIT":case"PRIMEM":case"VERT_DATUM":return s[i]={name:t[0].toLowerCase(),convert:t[1]},void(3===t.length&&v(t[2],s[i]));case"SPHEROID":case"ELLIPSOID":return s[i]={name:t[0],a:t[1],rf:t[2]},void(4===t.length&&v(t[3],s[i]));case"PROJECTEDCRS":case"PROJCRS":case"GEOGCS":case"GEOCCS":case"PROJCS":case"LOCAL_CS":case"GEODCRS":case"GEODETICCRS":case"GEODETICDATUM":case"EDATUM":case"ENGINEERINGDATUM":case"VERT_CS":case"VERTCRS":case"VERTICALCRS":case"COMPD_CS":case"COMPOUNDCRS":case"ENGINEERINGCRS":case"ENGCRS":case"FITTED_CS":case"LOCAL_DATUM":case"DATUM":return t[0]=["name",t[0]],void x(s,i,t);default:for(a=-1;++a<t.length;)if(!Array.isArray(t[a]))return v(t,s[i]);return x(s,i,t)}}else s[i]=t;else s[i]=!0}else s[t]=!0}_.prototype.readCharicter=function(){var t=this.text[this.place++];if(4!==this.state)for(;f.test(t);){if(this.place>=this.text.length)return;t=this.text[this.place++]}switch(this.state){case 1:return this.neutral(t);case 2:return this.keyword(t);case 4:return this.quoted(t);case 5:return this.afterquote(t);case 3:return this.number(t);case-1:return}},_.prototype.afterquote=function(t){if('"'===t)return this.word+='"',void(this.state=4);if(d.test(t))return this.word=this.word.trim(),void this.afterItem(t);throw new Error("havn't handled \""+t+'" in afterquote yet, index '+this.place)},_.prototype.afterItem=function(t){return","===t?(null!==this.word&&this.currentObject.push(this.word),this.word=null,void(this.state=1)):"]"===t?(this.level--,null!==this.word&&(this.currentObject.push(this.word),this.word=null),this.state=1,this.currentObject=this.stack.pop(),void(this.currentObject||(this.state=-1))):void 0},_.prototype.number=function(t){if(!y.test(t)){if(d.test(t))return this.word=parseFloat(this.word),void this.afterItem(t);throw new Error("havn't handled \""+t+'" in number yet, index '+this.place)}this.word+=t},_.prototype.quoted=function(t){'"'!==t?this.word+=t:this.state=5},_.prototype.keyword=function(t){if(p.test(t))this.word+=t;else{if("["===t){var s=[];return s.push(this.word),this.level++,null===this.root?this.root=s:this.currentObject.push(s),this.stack.push(this.currentObject),this.currentObject=s,void(this.state=1)}if(!d.test(t))throw new Error("havn't handled \""+t+'" in keyword yet, index '+this.place);this.afterItem(t)}},_.prototype.neutral=function(t){if(m.test(t))return this.word=t,void(this.state=2);if('"'===t)return this.word="",void(this.state=4);if(y.test(t))return this.word=t,void(this.state=3);if(!d.test(t))throw new Error("havn't handled \""+t+'" in neutral yet, index '+this.place);this.afterItem(t)},_.prototype.output=function(){for(;this.place<this.text.length;)this.readCharicter();if(-1===this.state)return this.root;throw new Error('unable to parse string "'+this.text+'". State is '+this.state)};function g(t){return.017453292519943295*t}var b=function(t){var s=new _(t).output(),i=s.shift(),a=s.shift();s.unshift(["name",a]),s.unshift(["type",i]);var h={};return v(s,h),function(t){if("GEOGCS"===t.type?t.projName="longlat":"LOCAL_CS"===t.type?(t.projName="identity",t.local=!0):"object"==typeof t.PROJECTION?t.projName=Object.keys(t.PROJECTION)[0]:t.projName=t.PROJECTION,t.AXIS){for(var s="",i=0,a=t.AXIS.length;i<a;++i){var h=t.AXIS[i][0].toLowerCase();-1!==h.indexOf("north")?s+="n":-1!==h.indexOf("south")?s+="s":-1!==h.indexOf("east")?s+="e":-1!==h.indexOf("west")&&(s+="w")}2===s.length&&(s+="u"),3===s.length&&(t.axis=s)}t.UNIT&&(t.units=t.UNIT.name.toLowerCase(),"metre"===t.units&&(t.units="meter"),t.UNIT.convert&&("GEOGCS"===t.type?t.DATUM&&t.DATUM.SPHEROID&&(t.to_meter=t.UNIT.convert*t.DATUM.SPHEROID.a):t.to_meter=t.UNIT.convert));var e=t.GEOGCS;function n(s){return s*(t.to_meter||1)}"GEOGCS"===t.type&&(e=t),e&&(e.DATUM?t.datumCode=e.DATUM.name.toLowerCase():t.datumCode=e.name.toLowerCase(),"d_"===t.datumCode.slice(0,2)&&(t.datumCode=t.datumCode.slice(2)),"new_zealand_geodetic_datum_1949"!==t.datumCode&&"new_zealand_1949"!==t.datumCode||(t.datumCode="nzgd49"),"wgs_1984"!==t.datumCode&&"world_geodetic_system_1984"!==t.datumCode||("Mercator_Auxiliary_Sphere"===t.PROJECTION&&(t.sphere=!0),t.datumCode="wgs84"),"_ferro"===t.datumCode.slice(-6)&&(t.datumCode=t.datumCode.slice(0,-6)),"_jakarta"===t.datumCode.slice(-8)&&(t.datumCode=t.datumCode.slice(0,-8)),~t.datumCode.indexOf("belge")&&(t.datumCode="rnb72"),e.DATUM&&e.DATUM.SPHEROID&&(t.ellps=e.DATUM.SPHEROID.name.replace("_19","").replace(/[Cc]larke\_18/,"clrk"),"international"===t.ellps.toLowerCase().slice(0,13)&&(t.ellps="intl"),t.a=e.DATUM.SPHEROID.a,t.rf=parseFloat(e.DATUM.SPHEROID.rf,10)),e.DATUM&&e.DATUM.TOWGS84&&(t.datum_params=e.DATUM.TOWGS84),~t.datumCode.indexOf("osgb_1936")&&(t.datumCode="osgb36"),~t.datumCode.indexOf("osni_1952")&&(t.datumCode="osni52"),(~t.datumCode.indexOf("tm65")||~t.datumCode.indexOf("geodetic_datum_of_1965"))&&(t.datumCode="ire65"),"ch1903+"===t.datumCode&&(t.datumCode="ch1903"),~t.datumCode.indexOf("israel")&&(t.datumCode="isr93")),t.b&&!isFinite(t.b)&&(t.b=t.a),[["standard_parallel_1","Standard_Parallel_1"],["standard_parallel_2","Standard_Parallel_2"],["false_easting","False_Easting"],["false_northing","False_Northing"],["central_meridian","Central_Meridian"],["latitude_of_origin","Latitude_Of_Origin"],["latitude_of_origin","Central_Parallel"],["scale_factor","Scale_Factor"],["k0","scale_factor"],["latitude_of_center","Latitude_Of_Center"],["latitude_of_center","Latitude_of_center"],["lat0","latitude_of_center",g],["longitude_of_center","Longitude_Of_Center"],["longitude_of_center","Longitude_of_center"],["longc","longitude_of_center",g],["x0","false_easting",n],["y0","false_northing",n],["long0","central_meridian",g],["lat0","latitude_of_origin",g],["lat0","standard_parallel_1",g],["lat1","standard_parallel_1",g],["lat2","standard_parallel_2",g],["azimuth","Azimuth"],["alpha","azimuth",g],["srsCode","name"]].forEach((function(s){return function(t,s){var i=s[0],a=s[1];!(i in t)&&a in t&&(t[i]=t[a],3===s.length&&(t[i]=s[2](t[i])))}(t,s)})),t.long0||!t.longc||"Albers_Conic_Equal_Area"!==t.projName&&"Lambert_Azimuthal_Equal_Area"!==t.projName||(t.long0=t.longc),t.lat_ts||!t.lat1||"Stereographic_South_Pole"!==t.projName&&"Polar Stereographic (variant B)"!==t.projName||(t.lat0=g(t.lat1>0?90:-90),t.lat_ts=t.lat1)}(h),h};function w(t){var s=this;if(2===arguments.length){var i=arguments[1];"string"==typeof i?"+"===i.charAt(0)?w[t]=u(arguments[1]):w[t]=b(arguments[1]):w[t]=i}else if(1===arguments.length){if(Array.isArray(t))return t.map((function(t){Array.isArray(t)?w.apply(s,t):w(t)}));if("string"==typeof t){if(t in w)return w[t]}else"EPSG"in t?w["EPSG:"+t.EPSG]=t:"ESRI"in t?w["ESRI:"+t.ESRI]=t:"IAU2000"in t?w["IAU2000:"+t.IAU2000]=t:console.log(t);return}}function C(t){return t in w}!function(t){t("EPSG:4326","+title=WGS 84 (long/lat) +proj=longlat +ellps=WGS84 +datum=WGS84 +units=degrees"),t("CGCS_GAUSS_3_ZONE_40","+proj=tmerc +lat_0=0 +lon_0=120 +k=1 +x_0=40500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"),t("EPSG:4269","+title=NAD83 (long/lat) +proj=longlat +a=6378137.0 +b=6356752.31414036 +ellps=GRS80 +datum=NAD83 +units=degrees"),t("EPSG:3857","+title=WGS 84 / Pseudo-Mercator +proj=merc +a=6378137 +b=6378137 +lat_ts=0.0 +lon_0=0.0 +x_0=0.0 +y_0=0 +k=1.0 +units=m +nadgrids=@null +no_defs"),t.WGS84=t["EPSG:4326"],t["EPSG:3785"]=t["EPSG:3857"],t.GOOGLE=t["EPSG:3857"],t["EPSG:900913"]=t["EPSG:3857"],t["EPSG:102113"]=t["EPSG:3857"]}(w);var P=["PROJECTEDCRS","PROJCRS","GEOGCS","GEOCCS","PROJCS","LOCAL_CS","GEODCRS","GEODETICCRS","GEODETICDATUM","ENGCRS","ENGINEERINGCRS"];var S=["3857","900913","3785","102113"];function N(t){if(!function(t){return"string"==typeof t}(t))return t;if(C(t))return w[t];if(function(t){return P.some((function(s){return t.indexOf(s)>-1}))}(t)){var s=b(t);if(function(t){var s=M(t,"authority");if(s){var i=M(s,"epsg");return i&&S.indexOf(i)>-1}}(s))return w["EPSG:3857"];var i=function(t){var s=M(t,"extension");if(s)return M(s,"proj4")}(s);return i?u(i):s}return function(t){return"+"===t[0]}(t)?u(t):void 0}var k=function(t,s){var i,a;if(t=t||{},!s)return t;for(a in s)void 0!==(i=s[a])&&(t[a]=i);return t},E=function(t,s,i){var a=t*s;return i/Math.sqrt(1-a*a)},q=function(t){return t<0?-1:1},I=function(t){return Math.abs(t)<=r?t:t-q(t)*n},O=function(t,i,a){var h=t*a,e=.5*t;return h=Math.pow((1-h)/(1+h),e),Math.tan(.5*(s-i))/h},A=function(t,i){for(var a,h,e=.5*t,n=s-2*Math.atan(i),r=0;r<=15;r++)if(a=t*Math.sin(n),n+=h=s-2*Math.atan(i*Math.pow((1-a)/(1+a),e))-n,Math.abs(h)<=1e-10)return n;return-9999};function G(t){return t}var j=[{init:function(){var t=this.b/this.a;this.es=1-t*t,"x0"in this||(this.x0=0),"y0"in this||(this.y0=0),this.e=Math.sqrt(this.es),this.lat_ts?this.sphere?this.k0=Math.cos(this.lat_ts):this.k0=E(this.e,Math.sin(this.lat_ts),Math.cos(this.lat_ts)):this.k0||(this.k?this.k0=this.k:this.k0=1)},forward:function(t){var a,n,r=t.x,o=t.y;if(o*h>90&&o*h<-90&&r*h>180&&r*h<-180)return null;if(Math.abs(Math.abs(o)-s)<=i)return null;if(this.sphere)a=this.x0+this.a*this.k0*I(r-this.long0),n=this.y0+this.a*this.k0*Math.log(Math.tan(e+.5*o));else{var l=Math.sin(o),c=O(this.e,o,l);a=this.x0+this.a*this.k0*I(r-this.long0),n=this.y0-this.a*this.k0*Math.log(c)}return t.x=a,t.y=n,t},inverse:function(t){var i,a,h=t.x-this.x0,e=t.y-this.y0;if(this.sphere)a=s-2*Math.atan(Math.exp(-e/(this.a*this.k0)));else{var n=Math.exp(-e/(this.a*this.k0));if(-9999===(a=A(this.e,n)))return null}return i=I(this.long0+h/(this.a*this.k0)),t.x=i,t.y=a,t},names:["Mercator","Popular Visualisation Pseudo Mercator","Mercator_1SP","Mercator_Auxiliary_Sphere","merc"]},{init:function(){},forward:G,inverse:G,names:["longlat","identity"]}],z={},R=[];function L(t,s){var i=R.length;return t.names?(R[i]=t,t.names.forEach((function(t){z[t.toLowerCase()]=i})),this):(console.log(s),!0)}var T={start:function(){j.forEach(L)},add:L,get:function(t){if(!t)return!1;var s=t.toLowerCase();return void 0!==z[s]&&R[z[s]]?R[z[s]]:void 0}},D={MERIT:{a:6378137,rf:298.257,ellipseName:"MERIT 1983"},SGS85:{a:6378136,rf:298.257,ellipseName:"Soviet Geodetic System 85"},GRS80:{a:6378137,rf:298.*********,ellipseName:"GRS 1980(IUGG, 1980)"},IAU76:{a:6378140,rf:298.257,ellipseName:"IAU 1976"},airy:{a:6377563.396,b:6356256.91,ellipseName:"Airy 1830"},APL4:{a:6378137,rf:298.25,ellipseName:"Appl. Physics. 1965"},NWL9D:{a:6378145,rf:298.25,ellipseName:"Naval Weapons Lab., 1965"},mod_airy:{a:6377340.189,b:6356034.446,ellipseName:"Modified Airy"},andrae:{a:6377104.43,rf:300,ellipseName:"Andrae 1876 (Den., Iclnd.)"},aust_SA:{a:6378160,rf:298.25,ellipseName:"Australian Natl & S. Amer. 1969"},GRS67:{a:6378160,rf:298.*********,ellipseName:"GRS 67(IUGG 1967)"},bessel:{a:6377397.155,rf:299.1528128,ellipseName:"Bessel 1841"},bess_nam:{a:6377483.865,rf:299.1528128,ellipseName:"Bessel 1841 (Namibia)"},clrk66:{a:6378206.4,b:6356583.8,ellipseName:"Clarke 1866"},clrk80:{a:6378249.145,rf:293.4663,ellipseName:"Clarke 1880 mod."},clrk58:{a:6378293.*********,rf:294.2606763692654,ellipseName:"Clarke 1858"},CPM:{a:6375738.7,rf:334.29,ellipseName:"Comm. des Poids et Mesures 1799"},delmbr:{a:6376428,rf:311.5,ellipseName:"Delambre 1810 (Belgium)"},engelis:{a:6378136.05,rf:298.2566,ellipseName:"Engelis 1985"},evrst30:{a:6377276.345,rf:300.8017,ellipseName:"Everest 1830"},evrst48:{a:6377304.063,rf:300.8017,ellipseName:"Everest 1948"},evrst56:{a:6377301.243,rf:300.8017,ellipseName:"Everest 1956"},evrst69:{a:6377295.664,rf:300.8017,ellipseName:"Everest 1969"},evrstSS:{a:6377298.556,rf:300.8017,ellipseName:"Everest (Sabah & Sarawak)"},fschr60:{a:6378166,rf:298.3,ellipseName:"Fischer (Mercury Datum) 1960"},fschr60m:{a:6378155,rf:298.3,ellipseName:"Fischer 1960"},fschr68:{a:6378150,rf:298.3,ellipseName:"Fischer 1968"},helmert:{a:6378200,rf:298.3,ellipseName:"Helmert 1906"},hough:{a:6378270,rf:297,ellipseName:"Hough"},intl:{a:6378388,rf:297,ellipseName:"International 1909 (Hayford)"},kaula:{a:6378163,rf:298.24,ellipseName:"Kaula 1961"},lerch:{a:6378139,rf:298.257,ellipseName:"Lerch 1979"},mprts:{a:6397300,rf:191,ellipseName:"Maupertius 1738"},new_intl:{a:6378157.5,b:6356772.2,ellipseName:"New International 1967"},plessis:{a:6376523,rf:6355863,ellipseName:"Plessis 1817 (France)"},krass:{a:6378245,rf:298.3,ellipseName:"Krassovsky, 1942"},SEasia:{a:6378155,b:6356773.3205,ellipseName:"Southeast Asia"},walbeck:{a:6376896,b:6355834.8467,ellipseName:"Walbeck"},WGS60:{a:6378165,rf:298.3,ellipseName:"WGS 60"},WGS66:{a:6378145,rf:298.25,ellipseName:"WGS 66"},WGS7:{a:6378135,rf:298.26,ellipseName:"WGS 72"}},U=D.WGS84={a:6378137,rf:298.257223563,ellipseName:"WGS 84"};D.sphere={a:6370997,b:6370997,ellipseName:"Normal Sphere (r=6370997)"};var B={};function F(s,a){if(!(this instanceof F))return new F(s);a=a||function(t){if(t)throw t};var h=N(s);if("object"==typeof h){var e=F.projections.get(h.projName);if(e){if(h.datumCode&&"none"!==h.datumCode){var n=M(B,h.datumCode);n&&(h.datum_params=n.towgs84?n.towgs84.split(","):null,h.ellps=n.ellipse,h.datumName=n.datumName?n.datumName:h.datumCode)}h.k0=h.k0||1,h.axis=h.axis||"enu",h.ellps=h.ellps||"wgs84";var r,o,l,c,u,f,m,p=function(t,s,a,h,e){if(!t){var n=M(D,h);n||(n=U),t=n.a,s=n.b,a=n.rf}return a&&!s&&(s=(1-1/a)*t),(0===a||Math.abs(t-s)<i)&&(e=!0,s=t),{a:t,b:s,rf:a,sphere:e}}(h.a,h.b,h.rf,h.ellps,h.sphere),d=(r=p.a,o=p.b,p.rf,l=h.R_A,f=((c=r*r)-(u=o*o))/c,m=0,l?(c=(r*=1-f*(.16666666666666666+f*(.04722222222222222+.022156084656084655*f)))*r,f=0):m=Math.sqrt(f),{es:f,e:m,ep2:(c-u)/u}),y=h.datum||function(s,i,a,h,e,n){var r={};return r.datum_type=void 0===s||"none"===s?5:4,i&&(r.datum_params=i.map(parseFloat),0===r.datum_params[0]&&0===r.datum_params[1]&&0===r.datum_params[2]||(r.datum_type=1),r.datum_params.length>3&&(0===r.datum_params[3]&&0===r.datum_params[4]&&0===r.datum_params[5]&&0===r.datum_params[6]||(r.datum_type=2,r.datum_params[3]*=t,r.datum_params[4]*=t,r.datum_params[5]*=t,r.datum_params[6]=r.datum_params[6]/1e6+1))),r.a=a,r.b=h,r.es=e,r.ep2=n,r}(h.datumCode,h.datum_params,p.a,p.b,d.es,d.ep2);k(this,h),k(this,e),this.a=p.a,this.b=p.b,this.rf=p.rf,this.sphere=p.sphere,this.es=d.es,this.e=d.e,this.ep2=d.ep2,this.datum=y,this.init(),a(null,this)}else a(s)}else a(s)}function Q(t,i,a){var h,e,n,r,o=t.x,l=t.y,c=t.z?t.z:0;if(l<-s&&l>-1.001*s)l=-s;else if(l>s&&l<1.001*s)l=s;else{if(l<-s)return{x:-1/0,y:-1/0,z:t.z};if(l>s)return{x:1/0,y:1/0,z:t.z}}return o>Math.PI&&(o-=2*Math.PI),e=Math.sin(l),r=Math.cos(l),n=e*e,{x:((h=a/Math.sqrt(1-i*n))+c)*r*Math.cos(o),y:(h+c)*r*Math.sin(o),z:(h*(1-i)+c)*e}}function W(t,i,a,h){var e,n,r,o,l,c,M,u,f,m,p,d,y,_,x,v=1e-12,g=t.x,b=t.y,w=t.z?t.z:0;if(e=Math.sqrt(g*g+b*b),n=Math.sqrt(g*g+b*b+w*w),e/a<v){if(_=0,n/a<v)return s,x=-h,{x:t.x,y:t.y,z:t.z}}else _=Math.atan2(b,g);r=w/n,u=(o=e/n)*(1-i)*(l=1/Math.sqrt(1-i*(2-i)*o*o)),f=r*l,y=0;do{y++,c=i*(M=a/Math.sqrt(1-i*f*f))/(M+(x=e*u+w*f-M*(1-i*f*f))),d=(p=r*(l=1/Math.sqrt(1-c*(2-c)*o*o)))*u-(m=o*(1-c)*l)*f,u=m,f=p}while(d*d>1e-24&&y<30);return{x:_,y:Math.atan(p/Math.abs(m)),z:x}}function X(t){return 1===t||2===t}B.wgs84={towgs84:"0,0,0",ellipse:"WGS84",datumName:"WGS84"},B.ch1903={towgs84:"674.374,15.056,405.346",ellipse:"bessel",datumName:"swiss"},B.ggrs87={towgs84:"-199.87,74.79,246.62",ellipse:"GRS80",datumName:"Greek_Geodetic_Reference_System_1987"},B.nad83={towgs84:"0,0,0",ellipse:"GRS80",datumName:"North_American_Datum_1983"},B.nad27={nadgrids:"@conus,@alaska,@ntv2_0.gsb,@ntv1_can.dat",ellipse:"clrk66",datumName:"North_American_Datum_1927"},B.potsdam={towgs84:"606.0,23.0,413.0",ellipse:"bessel",datumName:"Potsdam Rauenberg 1950 DHDN"},B.carthage={towgs84:"-263.0,6.0,431.0",ellipse:"clark80",datumName:"Carthage 1934 Tunisia"},B.hermannskogel={towgs84:"653.0,-212.0,449.0",ellipse:"bessel",datumName:"Hermannskogel"},B.osni52={towgs84:"482.530,-130.596,564.557,-1.042,-0.214,-0.631,8.15",ellipse:"airy",datumName:"Irish National"},B.ire65={towgs84:"482.530,-130.596,564.557,-1.042,-0.214,-0.631,8.15",ellipse:"mod_airy",datumName:"Ireland 1965"},B.rassadiran={towgs84:"-133.63,-157.5,-158.62",ellipse:"intl",datumName:"Rassadiran"},B.nzgd49={towgs84:"59.47,-5.04,187.44,0.47,-0.1,1.024,-4.5993",ellipse:"intl",datumName:"New Zealand Geodetic Datum 1949"},B.osgb36={towgs84:"446.448,-125.157,542.060,0.1502,0.2470,0.8421,-20.4894",ellipse:"airy",datumName:"Airy 1830"},B.s_jtsk={towgs84:"589,76,480",ellipse:"bessel",datumName:"S-JTSK (Ferro)"},B.beduaram={towgs84:"-106,-87,188",ellipse:"clrk80",datumName:"Beduaram"},B.gunung_segara={towgs84:"-403,684,41",ellipse:"bessel",datumName:"Gunung Segara Jakarta"},B.rnb72={towgs84:"106.869,-52.2978,103.724,-0.33657,0.456955,-1.84218,1",ellipse:"intl",datumName:"Reseau National Belge 1972"},F.projections=T,F.projections.start();var H=function(t,s,i){return function(t,s){return t.datum_type===s.datum_type&&!(t.a!==s.a||Math.abs(t.es-s.es)>5e-11)&&(1===t.datum_type?t.datum_params[0]===s.datum_params[0]&&t.datum_params[1]===s.datum_params[1]&&t.datum_params[2]===s.datum_params[2]:2!==t.datum_type||t.datum_params[0]===s.datum_params[0]&&t.datum_params[1]===s.datum_params[1]&&t.datum_params[2]===s.datum_params[2]&&t.datum_params[3]===s.datum_params[3]&&t.datum_params[4]===s.datum_params[4]&&t.datum_params[5]===s.datum_params[5]&&t.datum_params[6]===s.datum_params[6])}(t,s)||5===t.datum_type||5===s.datum_type?i:t.es!==s.es||t.a!==s.a||X(t.datum_type)||X(s.datum_type)?(i=Q(i,t.es,t.a),X(t.datum_type)&&(i=function(t,s,i){if(1===s)return{x:t.x+i[0],y:t.y+i[1],z:t.z+i[2]};if(2===s){var a=i[0],h=i[1],e=i[2],n=i[3],r=i[4],o=i[5],l=i[6];return{x:l*(t.x-o*t.y+r*t.z)+a,y:l*(o*t.x+t.y-n*t.z)+h,z:l*(-r*t.x+n*t.y+t.z)+e}}}(i,t.datum_type,t.datum_params)),X(s.datum_type)&&(i=function(t,s,i){if(1===s)return{x:t.x-i[0],y:t.y-i[1],z:t.z-i[2]};if(2===s){var a=i[0],h=i[1],e=i[2],n=i[3],r=i[4],o=i[5],l=i[6],c=(t.x-a)/l,M=(t.y-h)/l,u=(t.z-e)/l;return{x:c+o*M-r*u,y:-o*c+M+n*u,z:r*c-n*M+u}}}(i,s.datum_type,s.datum_params)),W(i,s.es,s.a,s.b)):i},J=function(t,s,i){var a,h,e,n=i.x,r=i.y,o=i.z||0,l={};for(e=0;e<3;e++)if(!s||2!==e||void 0!==i.z)switch(0===e?(a=n,h=-1!=="ew".indexOf(t.axis[e])?"x":"y"):1===e?(a=r,h=-1!=="ns".indexOf(t.axis[e])?"y":"x"):(a=o,h="z"),t.axis[e]){case"e":case"n":l[h]=a;break;case"w":case"s":l[h]=-a;break;case"u":void 0!==i[h]&&(l.z=a);break;case"d":void 0!==i[h]&&(l.z=-a);break;default:return null}return l},K=function(t){var s={x:t[0],y:t[1]};return t.length>2&&(s.z=t[2]),t.length>3&&(s.m=t[3]),s};function V(t){if("function"==typeof Number.isFinite){if(Number.isFinite(t))return;throw new TypeError("coordinates must be finite numbers")}if("number"!=typeof t||t!=t||!isFinite(t))throw new TypeError("coordinates must be finite numbers")}function Z(t,s,i){var e;if(Array.isArray(i)&&(i=K(i)),function(t){V(t.x),V(t.y)}(i),t.datum&&s.datum&&function(t,s){return(1===t.datum.datum_type||2===t.datum.datum_type)&&"WGS84"!==s.datumCode||(1===s.datum.datum_type||2===s.datum.datum_type)&&"WGS84"!==t.datumCode}(t,s)&&(i=Z(t,e=new F("WGS84"),i),t=e),"enu"!==t.axis&&(i=J(t,!1,i)),"longlat"===t.projName)i={x:i.x*a,y:i.y*a,z:i.z||0};else if(t.to_meter&&(i={x:i.x*t.to_meter,y:i.y*t.to_meter,z:i.z||0}),!(i=t.inverse(i)))return;return t.from_greenwich&&(i.x+=t.from_greenwich),i=H(t.datum,s.datum,i),s.from_greenwich&&(i={x:i.x-s.from_greenwich,y:i.y,z:i.z||0}),"longlat"===s.projName?i={x:i.x*h,y:i.y*h,z:i.z||0}:(i=s.forward(i),s.to_meter&&(i={x:i.x/s.to_meter,y:i.y/s.to_meter,z:i.z||0})),"enu"!==s.axis?J(s,!0,i):i}var Y=F("WGS84");function $(t,s,i){var a,h,e;return Array.isArray(i)?(a=Z(t,s,i)||{x:NaN,y:NaN},i.length>2?void 0!==t.name&&"geocent"===t.name||void 0!==s.name&&"geocent"===s.name?"number"==typeof a.z?[a.x,a.y,a.z].concat(i.splice(3)):[a.x,a.y,i[2]].concat(i.splice(3)):[a.x,a.y].concat(i.splice(2)):[a.x,a.y]):(h=Z(t,s,i),2===(e=Object.keys(i)).length||e.forEach((function(a){if(void 0!==t.name&&"geocent"===t.name||void 0!==s.name&&"geocent"===s.name){if("x"===a||"y"===a||"z"===a)return}else if("x"===a||"y"===a)return;h[a]=i[a]})),h)}function tt(t){return t instanceof F?t:t.oProj?t.oProj:F(t)}function st(t,s,i){t=tt(t);var a,h=!1;return void 0===s?(s=t,t=Y,h=!0):(void 0!==s.x||Array.isArray(s))&&(i=s,s=t,t=Y,h=!0),s=tt(s),i?$(t,s,i):(a={forward:function(i){return $(t,s,i)},inverse:function(i){return $(s,t,i)}},h&&(a.oProj=s),a)}var it="AJSAJS",at="AFAFAF",ht=65,et=73,nt=79,rt=86,ot=90,lt={forward:ct,inverse:function(t){var s=mt(yt(t.toUpperCase()));if(s.lat&&s.lon)return[s.lon,s.lat,s.lon,s.lat];return[s.left,s.bottom,s.right,s.top]},toPoint:Mt};function ct(t,s){return s=s||5,function(t,s){var i="00000"+t.easting,a="00000"+t.northing;return t.zoneNumber+t.zoneLetter+(f=t.easting,m=t.northing,p=t.zoneNumber,d=dt(p),y=Math.floor(f/1e5),_=Math.floor(m/1e5)%20,h=y,e=_,n=d,r=n-1,o=it.charCodeAt(r),l=at.charCodeAt(r),c=o+h-1,M=l+e,u=!1,c>ot&&(c=c-ot+ht-1,u=!0),(c===et||o<et&&c>et||(c>et||o<et)&&u)&&c++,(c===nt||o<nt&&c>nt||(c>nt||o<nt)&&u)&&++c===et&&c++,c>ot&&(c=c-ot+ht-1),M>rt?(M=M-rt+ht-1,u=!0):u=!1,(M===et||l<et&&M>et||(M>et||l<et)&&u)&&M++,(M===nt||l<nt&&M>nt||(M>nt||l<nt)&&u)&&++M===et&&M++,M>rt&&(M=M-rt+ht-1),String.fromCharCode(c)+String.fromCharCode(M))+i.substr(i.length-5,s)+a.substr(a.length-5,s);var h,e,n,r,o,l,c,M,u;var f,m,p,d,y,_}(function(t){var s,i,a,h,e,n,r,o,l=t.lat,c=t.lon,M=6378137,u=.00669438,f=.9996,m=ut(l),p=ut(c);o=Math.floor((c+180)/6)+1,180===c&&(o=60);l>=56&&l<64&&c>=3&&c<12&&(o=32);l>=72&&l<84&&(c>=0&&c<9?o=31:c>=9&&c<21?o=33:c>=21&&c<33?o=35:c>=33&&c<42&&(o=37));r=ut(6*(o-1)-180+3),s=u/(1-u),i=M/Math.sqrt(1-u*Math.sin(m)*Math.sin(m)),a=Math.tan(m)*Math.tan(m),h=s*Math.cos(m)*Math.cos(m),e=Math.cos(m)*(p-r),n=M*((1-u/4-3*u*u/64-5*u*u*u/256)*m-(3*u/8+3*u*u/32+45*u*u*u/1024)*Math.sin(2*m)+(15*u*u/256+45*u*u*u/1024)*Math.sin(4*m)-35*u*u*u/3072*Math.sin(6*m));var d=f*i*(e+(1-a+h)*e*e*e/6+(5-18*a+a*a+72*h-58*s)*e*e*e*e*e/120)+5e5,y=f*(n+i*Math.tan(m)*(e*e/2+(5-a+9*h+4*h*h)*e*e*e*e/24+(61-58*a+a*a+600*h-330*s)*e*e*e*e*e*e/720));l<0&&(y+=1e7);return{northing:Math.round(y),easting:Math.round(d),zoneNumber:o,zoneLetter:pt(l)}}({lat:t[1],lon:t[0]}),s)}function Mt(t){var s=mt(yt(t.toUpperCase()));return s.lat&&s.lon?[s.lon,s.lat]:[(s.left+s.right)/2,(s.top+s.bottom)/2]}function ut(t){return t*(Math.PI/180)}function ft(t){return t/Math.PI*180}function mt(t){var s=t.northing,i=t.easting,a=t.zoneLetter,h=t.zoneNumber;if(h<0||h>60)return null;var e,n,r,o,l,c,M,u,f,m=.9996,p=6378137,d=.00669438,y=(1-Math.sqrt(.99330562))/(1+Math.sqrt(.99330562)),_=i-5e5,x=s;a<"N"&&(x-=1e7),M=6*(h-1)-180+3,e=.006739496752268451,f=(u=x/m/6367449.145945056)+(3*y/2-27*y*y*y/32)*Math.sin(2*u)+(21*y*y/16-55*y*y*y*y/32)*Math.sin(4*u)+151*y*y*y/96*Math.sin(6*u),n=p/Math.sqrt(1-d*Math.sin(f)*Math.sin(f)),r=Math.tan(f)*Math.tan(f),o=e*Math.cos(f)*Math.cos(f),l=.99330562*p/Math.pow(1-d*Math.sin(f)*Math.sin(f),1.5),c=_/(n*m);var v=f-n*Math.tan(f)/l*(c*c/2-(5+3*r+10*o-4*o*o-9*e)*c*c*c*c/24+(61+90*r+298*o+45*r*r-1.6983531815716497-3*o*o)*c*c*c*c*c*c/720);v=ft(v);var g,b=(c-(1+2*r+o)*c*c*c/6+(5-2*o+28*r-3*o*o+8*e+24*r*r)*c*c*c*c*c/120)/Math.cos(f);if(b=M+ft(b),t.accuracy){var w=mt({northing:t.northing+t.accuracy,easting:t.easting+t.accuracy,zoneLetter:t.zoneLetter,zoneNumber:t.zoneNumber});g={top:w.lat,right:w.lon,bottom:v,left:b}}else g={lat:v,lon:b};return g}function pt(t){var s="Z";return 84>=t&&t>=72?s="X":72>t&&t>=64?s="W":64>t&&t>=56?s="V":56>t&&t>=48?s="U":48>t&&t>=40?s="T":40>t&&t>=32?s="S":32>t&&t>=24?s="R":24>t&&t>=16?s="Q":16>t&&t>=8?s="P":8>t&&t>=0?s="N":0>t&&t>=-8?s="M":-8>t&&t>=-16?s="L":-16>t&&t>=-24?s="K":-24>t&&t>=-32?s="J":-32>t&&t>=-40?s="H":-40>t&&t>=-48?s="G":-48>t&&t>=-56?s="F":-56>t&&t>=-64?s="E":-64>t&&t>=-72?s="D":-72>t&&t>=-80&&(s="C"),s}function dt(t){var s=t%6;return 0===s&&(s=6),s}function yt(t){if(t&&0===t.length)throw"MGRSPoint coverting from nothing";for(var s,i=t.length,a=null,h="",e=0;!/[A-Z]/.test(s=t.charAt(e));){if(e>=2)throw"MGRSPoint bad conversion from: "+t;h+=s,e++}var n=parseInt(h,10);if(0===e||e+3>i)throw"MGRSPoint bad conversion from: "+t;var r=t.charAt(e++);if(r<="A"||"B"===r||"Y"===r||r>="Z"||"I"===r||"O"===r)throw"MGRSPoint zone letter "+r+" not handled: "+t;a=t.substring(e,e+=2);for(var o=dt(n),l=function(t,s){var i=it.charCodeAt(s-1),a=1e5,h=!1;for(;i!==t.charCodeAt(0);){if(++i===et&&i++,i===nt&&i++,i>ot){if(h)throw"Bad character: "+t;i=ht,h=!0}a+=1e5}return a}(a.charAt(0),o),c=function(t,s){if(t>"V")throw"MGRSPoint given invalid Northing "+t;var i=at.charCodeAt(s-1),a=0,h=!1;for(;i!==t.charCodeAt(0);){if(++i===et&&i++,i===nt&&i++,i>rt){if(h)throw"Bad character: "+t;i=ht,h=!0}a+=1e5}return a}(a.charAt(1),o);c<_t(r);)c+=2e6;var M=i-e;if(M%2!=0)throw"MGRSPoint has to have an even number \nof digits after the zone letter and two 100km letters - front \nhalf for easting meters, second half for \nnorthing meters"+t;var u,f,m,p=M/2,d=0,y=0;return p>0&&(u=1e5/Math.pow(10,p),f=t.substring(e,e+p),d=parseFloat(f)*u,m=t.substring(e+p),y=parseFloat(m)*u),{easting:d+l,northing:y+c,zoneLetter:r,zoneNumber:n,accuracy:u}}function _t(t){var s;switch(t){case"C":s=11e5;break;case"D":s=2e6;break;case"E":s=28e5;break;case"F":s=37e5;break;case"G":s=46e5;break;case"H":s=55e5;break;case"J":s=64e5;break;case"K":s=73e5;break;case"L":s=82e5;break;case"M":s=91e5;break;case"N":s=0;break;case"P":s=8e5;break;case"Q":s=17e5;break;case"R":s=26e5;break;case"S":s=35e5;break;case"T":s=44e5;break;case"U":s=53e5;break;case"V":s=62e5;break;case"W":s=7e6;break;case"X":s=79e5;break;default:s=-1}if(s>=0)return s;throw"Invalid zone letter: "+t}function xt(t,s,i){if(!(this instanceof xt))return new xt(t,s,i);if(Array.isArray(t))this.x=t[0],this.y=t[1],this.z=t[2]||0;else if("object"==typeof t)this.x=t.x,this.y=t.y,this.z=t.z||0;else if("string"==typeof t&&void 0===s){var a=t.split(",");this.x=parseFloat(a[0],10),this.y=parseFloat(a[1],10),this.z=parseFloat(a[2],10)||0}else this.x=t,this.y=s,this.z=i||0;console.warn("proj4.Point will be removed in version 3, use proj4.toPoint")}xt.fromMGRS=function(t){return new xt(Mt(t))},xt.prototype.toMGRS=function(t){return ct([this.x,this.y],t)};var vt=.046875,gt=.01953125,bt=.01068115234375,wt=function(t){var s=[];s[0]=1-t*(.25+t*(vt+t*(gt+t*bt))),s[1]=t*(.75-t*(vt+t*(gt+t*bt)));var i=t*t;return s[2]=i*(.46875-t*(.013020833333333334+.007120768229166667*t)),i*=t,s[3]=i*(.3645833333333333-.005696614583333333*t),s[4]=i*t*.3076171875,s},Ct=function(t,s,i,a){return i*=s,s*=s,a[0]*t-i*(a[1]+s*(a[2]+s*(a[3]+s*a[4])))},Pt=function(t,s,a){for(var h=1/(1-s),e=t,n=20;n;--n){var r=Math.sin(e),o=1-s*r*r;if(e-=o=(Ct(e,r,Math.cos(e),a)-t)*(o*Math.sqrt(o))*h,Math.abs(o)<i)return e}return e};var St={init:function(){this.x0=void 0!==this.x0?this.x0:0,this.y0=void 0!==this.y0?this.y0:0,this.long0=void 0!==this.long0?this.long0:0,this.lat0=void 0!==this.lat0?this.lat0:0,this.es&&(this.en=wt(this.es),this.ml0=Ct(this.lat0,Math.sin(this.lat0),Math.cos(this.lat0),this.en))},forward:function(t){var s,a,h,e=t.x,n=t.y,r=I(e-this.long0),o=Math.sin(n),l=Math.cos(n);if(this.es){var c=l*r,M=Math.pow(c,2),u=this.ep2*Math.pow(l,2),f=Math.pow(u,2),m=Math.abs(l)>i?Math.tan(n):0,p=Math.pow(m,2),d=Math.pow(p,2);s=1-this.es*Math.pow(o,2),c/=Math.sqrt(s);var y=Ct(n,o,l,this.en);a=this.a*(this.k0*c*(1+M/6*(1-p+u+M/20*(5-18*p+d+14*u-58*p*u+M/42*(61+179*d-d*p-479*p)))))+this.x0,h=this.a*(this.k0*(y-this.ml0+o*r*c/2*(1+M/12*(5-p+9*u+4*f+M/30*(61+d-58*p+270*u-330*p*u+M/56*(1385+543*d-d*p-3111*p))))))+this.y0}else{var _=l*Math.sin(r);if(Math.abs(Math.abs(_)-1)<i)return 93;if(a=.5*this.a*this.k0*Math.log((1+_)/(1-_))+this.x0,h=l*Math.cos(r)/Math.sqrt(1-Math.pow(_,2)),(_=Math.abs(h))>=1){if(_-1>i)return 93;h=0}else h=Math.acos(h);n<0&&(h=-h),h=this.a*this.k0*(h-this.lat0)+this.y0}return t.x=a,t.y=h,t},inverse:function(t){var a,h,e,n,r=(t.x-this.x0)*(1/this.a),o=(t.y-this.y0)*(1/this.a);if(this.es)if(a=this.ml0+o/this.k0,h=Pt(a,this.es,this.en),Math.abs(h)<s){var l=Math.sin(h),c=Math.cos(h),M=Math.abs(c)>i?Math.tan(h):0,u=this.ep2*Math.pow(c,2),f=Math.pow(u,2),m=Math.pow(M,2),p=Math.pow(m,2);a=1-this.es*Math.pow(l,2);var d=r*Math.sqrt(a)/this.k0,y=Math.pow(d,2);e=h-(a*=M)*y/(1-this.es)*.5*(1-y/12*(5+3*m-9*u*m+u-4*f-y/30*(61+90*m-252*u*m+45*p+46*u-y/56*(1385+3633*m+4095*p+1574*p*m)))),n=I(this.long0+d*(1-y/6*(1+2*m+u-y/20*(5+28*m+24*p+8*u*m+6*u-y/42*(61+662*m+1320*p+720*p*m))))/c)}else e=s*q(o),n=0;else{var _=Math.exp(r/this.k0),x=.5*(_-1/_),v=this.lat0+o/this.k0,g=Math.cos(v);a=Math.sqrt((1-Math.pow(g,2))/(1+Math.pow(x,2))),e=Math.asin(a),o<0&&(e=-e),n=0===x&&0===g?0:I(Math.atan2(x,g)+this.long0)}return t.x=n,t.y=e,t},names:["Transverse_Mercator","Transverse Mercator","tmerc"]},Nt=function(t){var s=Math.exp(t);return s=(s-1/s)/2},kt=function(t,s){t=Math.abs(t),s=Math.abs(s);var i=Math.max(t,s),a=Math.min(t,s)/(i||1);return i*Math.sqrt(1+Math.pow(a,2))},Et=function(t){var s=Math.abs(t);return s=function(t){var s=1+t,i=s-1;return 0===i?t:t*Math.log(s)/i}(s*(1+s/(kt(1,s)+1))),t<0?-s:s},qt=function(t,s){for(var i,a=2*Math.cos(2*s),h=t.length-1,e=t[h],n=0;--h>=0;)i=a*e-n+t[h],n=e,e=i;return s+i*Math.sin(2*s)},It=function(t,s,i){for(var a,h,e=Math.sin(s),n=Math.cos(s),r=Nt(i),o=function(t){var s=Math.exp(t);return(s+1/s)/2}(i),l=2*n*o,c=-2*e*r,M=t.length-1,u=t[M],f=0,m=0,p=0;--M>=0;)a=m,h=f,u=l*(m=u)-a-c*(f=p)+t[M],p=c*m-h+l*f;return[(l=e*o)*u-(c=n*r)*p,l*p+c*u]};var Ot={init:function(){if(void 0===this.es||this.es<=0)throw new Error("incorrect elliptical usage");this.x0=void 0!==this.x0?this.x0:0,this.y0=void 0!==this.y0?this.y0:0,this.long0=void 0!==this.long0?this.long0:0,this.lat0=void 0!==this.lat0?this.lat0:0,this.cgb=[],this.cbg=[],this.utg=[],this.gtu=[];var t=this.es/(1+Math.sqrt(1-this.es)),s=t/(2-t),i=s;this.cgb[0]=s*(2+s*(-2/3+s*(s*(116/45+s*(26/45+s*(-2854/675)))-2))),this.cbg[0]=s*(s*(2/3+s*(4/3+s*(-82/45+s*(32/45+s*(4642/4725)))))-2),i*=s,this.cgb[1]=i*(7/3+s*(s*(-227/45+s*(2704/315+s*(2323/945)))-1.6)),this.cbg[1]=i*(5/3+s*(-16/15+s*(-13/9+s*(904/315+s*(-1522/945))))),i*=s,this.cgb[2]=i*(56/15+s*(-136/35+s*(-1262/105+s*(73814/2835)))),this.cbg[2]=i*(-26/15+s*(34/21+s*(1.6+s*(-12686/2835)))),i*=s,this.cgb[3]=i*(4279/630+s*(-332/35+s*(-399572/14175))),this.cbg[3]=i*(1237/630+s*(s*(-24832/14175)-2.4)),i*=s,this.cgb[4]=i*(4174/315+s*(-144838/6237)),this.cbg[4]=i*(-734/315+s*(109598/31185)),i*=s,this.cgb[5]=i*(601676/22275),this.cbg[5]=i*(444337/155925),i=Math.pow(s,2),this.Qn=this.k0/(1+s)*(1+i*(1/4+i*(1/64+i/256))),this.utg[0]=s*(s*(2/3+s*(-37/96+s*(1/360+s*(81/512+s*(-96199/604800)))))-.5),this.gtu[0]=s*(.5+s*(-2/3+s*(5/16+s*(41/180+s*(-127/288+s*(7891/37800)))))),this.utg[1]=i*(-1/48+s*(-1/15+s*(437/1440+s*(-46/105+s*(1118711/3870720))))),this.gtu[1]=i*(13/48+s*(s*(557/1440+s*(281/630+s*(-1983433/1935360)))-.6)),i*=s,this.utg[2]=i*(-17/480+s*(37/840+s*(209/4480+s*(-5569/90720)))),this.gtu[2]=i*(61/240+s*(-103/140+s*(15061/26880+s*(167603/181440)))),i*=s,this.utg[3]=i*(-4397/161280+s*(11/504+s*(830251/7257600))),this.gtu[3]=i*(49561/161280+s*(-179/168+s*(6601661/7257600))),i*=s,this.utg[4]=i*(-4583/161280+s*(108847/3991680)),this.gtu[4]=i*(34729/80640+s*(-3418889/1995840)),i*=s,this.utg[5]=i*(-20648693/638668800),this.gtu[5]=.6650675310896665*i;var a=qt(this.cbg,this.lat0);this.Zb=-this.Qn*(a+function(t,s){for(var i,a=2*Math.cos(s),h=t.length-1,e=t[h],n=0;--h>=0;)i=a*e-n+t[h],n=e,e=i;return Math.sin(s)*i}(this.gtu,2*a))},forward:function(t){var s=I(t.x-this.long0),i=t.y;i=qt(this.cbg,i);var a=Math.sin(i),h=Math.cos(i),e=Math.sin(s),n=Math.cos(s);i=Math.atan2(a,n*h),s=Math.atan2(e*h,kt(a,h*n)),s=Et(Math.tan(s));var r,o,l=It(this.gtu,2*i,2*s);return i+=l[0],s+=l[1],Math.abs(s)<=2.623395162778?(r=this.a*(this.Qn*s)+this.x0,o=this.a*(this.Qn*i+this.Zb)+this.y0):(r=1/0,o=1/0),t.x=r,t.y=o,t},inverse:function(t){var s,i,a=(t.x-this.x0)*(1/this.a),h=(t.y-this.y0)*(1/this.a);if(h=(h-this.Zb)/this.Qn,a/=this.Qn,Math.abs(a)<=2.623395162778){var e=It(this.utg,2*h,2*a);h+=e[0],a+=e[1],a=Math.atan(Nt(a));var n=Math.sin(h),r=Math.cos(h),o=Math.sin(a),l=Math.cos(a);h=Math.atan2(n*l,kt(o,l*r)),a=Math.atan2(o,l*r),s=I(a+this.long0),i=qt(this.cgb,h)}else s=1/0,i=1/0;return t.x=s,t.y=i,t},names:["Extended_Transverse_Mercator","Extended Transverse Mercator","etmerc"]};var At={init:function(){var t=function(t,s){if(void 0===t){if((t=Math.floor(30*(I(s)+Math.PI)/Math.PI)+1)<0)return 0;if(t>60)return 60}return t}(this.zone,this.long0);if(void 0===t)throw new Error("unknown utm zone");this.lat0=0,this.long0=(6*Math.abs(t)-183)*a,this.x0=5e5,this.y0=this.utmSouth?1e7:0,this.k0=.9996,Ot.init.apply(this),this.forward=Ot.forward,this.inverse=Ot.inverse},names:["Universal Transverse Mercator System","utm"],dependsOn:"etmerc"},Gt=function(t,s){return Math.pow((1-t)/(1+t),s)};var jt={init:function(){var t=Math.sin(this.lat0),s=Math.cos(this.lat0);s*=s,this.rc=Math.sqrt(1-this.es)/(1-this.es*t*t),this.C=Math.sqrt(1+this.es*s*s/(1-this.es)),this.phic0=Math.asin(t/this.C),this.ratexp=.5*this.C*this.e,this.K=Math.tan(.5*this.phic0+e)/(Math.pow(Math.tan(.5*this.lat0+e),this.C)*Gt(this.e*t,this.ratexp))},forward:function(t){var i=t.x,a=t.y;return t.y=2*Math.atan(this.K*Math.pow(Math.tan(.5*a+e),this.C)*Gt(this.e*Math.sin(a),this.ratexp))-s,t.x=this.C*i,t},inverse:function(t){for(var i=t.x/this.C,a=t.y,h=Math.pow(Math.tan(.5*a+e)/this.K,1/this.C),n=20;n>0&&(a=2*Math.atan(h*Gt(this.e*Math.sin(t.y),-.5*this.e))-s,!(Math.abs(a-t.y)<1e-14));--n)t.y=a;return n?(t.x=i,t.y=a,t):null},names:["gauss"]};var zt={init:function(){jt.init.apply(this),this.rc&&(this.sinc0=Math.sin(this.phic0),this.cosc0=Math.cos(this.phic0),this.R2=2*this.rc,this.title||(this.title="Oblique Stereographic Alternative"))},forward:function(t){var s,i,a,h;return t.x=I(t.x-this.long0),jt.forward.apply(this,[t]),s=Math.sin(t.y),i=Math.cos(t.y),a=Math.cos(t.x),h=this.k0*this.R2/(1+this.sinc0*s+this.cosc0*i*a),t.x=h*i*Math.sin(t.x),t.y=h*(this.cosc0*s-this.sinc0*i*a),t.x=this.a*t.x+this.x0,t.y=this.a*t.y+this.y0,t},inverse:function(t){var s,i,a,h,e;if(t.x=(t.x-this.x0)/this.a,t.y=(t.y-this.y0)/this.a,t.x/=this.k0,t.y/=this.k0,e=Math.sqrt(t.x*t.x+t.y*t.y)){var n=2*Math.atan2(e,this.R2);s=Math.sin(n),i=Math.cos(n),h=Math.asin(i*this.sinc0+t.y*s*this.cosc0/e),a=Math.atan2(t.x*s,e*this.cosc0*i-t.y*this.sinc0*s)}else h=this.phic0,a=0;return t.x=a,t.y=h,jt.inverse.apply(this,[t]),t.x=I(t.x+this.long0),t},names:["Stereographic_North_Pole","Oblique_Stereographic","Polar_Stereographic","sterea","Oblique Stereographic Alternative","Double_Stereographic"]};var Rt={init:function(){this.coslat0=Math.cos(this.lat0),this.sinlat0=Math.sin(this.lat0),this.sphere?1===this.k0&&!isNaN(this.lat_ts)&&Math.abs(this.coslat0)<=i&&(this.k0=.5*(1+q(this.lat0)*Math.sin(this.lat_ts))):(Math.abs(this.coslat0)<=i&&(this.lat0>0?this.con=1:this.con=-1),this.cons=Math.sqrt(Math.pow(1+this.e,1+this.e)*Math.pow(1-this.e,1-this.e)),1===this.k0&&!isNaN(this.lat_ts)&&Math.abs(this.coslat0)<=i&&(this.k0=.5*this.cons*E(this.e,Math.sin(this.lat_ts),Math.cos(this.lat_ts))/O(this.e,this.con*this.lat_ts,this.con*Math.sin(this.lat_ts))),this.ms1=E(this.e,this.sinlat0,this.coslat0),this.X0=2*Math.atan(this.ssfn_(this.lat0,this.sinlat0,this.e))-s,this.cosX0=Math.cos(this.X0),this.sinX0=Math.sin(this.X0))},forward:function(t){var a,h,e,n,r,o,l=t.x,c=t.y,M=Math.sin(c),u=Math.cos(c),f=I(l-this.long0);return Math.abs(Math.abs(l-this.long0)-Math.PI)<=i&&Math.abs(c+this.lat0)<=i?(t.x=NaN,t.y=NaN,t):this.sphere?(a=2*this.k0/(1+this.sinlat0*M+this.coslat0*u*Math.cos(f)),t.x=this.a*a*u*Math.sin(f)+this.x0,t.y=this.a*a*(this.coslat0*M-this.sinlat0*u*Math.cos(f))+this.y0,t):(h=2*Math.atan(this.ssfn_(c,M,this.e))-s,n=Math.cos(h),e=Math.sin(h),Math.abs(this.coslat0)<=i?(r=O(this.e,c*this.con,this.con*M),o=2*this.a*this.k0*r/this.cons,t.x=this.x0+o*Math.sin(l-this.long0),t.y=this.y0-this.con*o*Math.cos(l-this.long0),t):(Math.abs(this.sinlat0)<i?(a=2*this.a*this.k0/(1+n*Math.cos(f)),t.y=a*e):(a=2*this.a*this.k0*this.ms1/(this.cosX0*(1+this.sinX0*e+this.cosX0*n*Math.cos(f))),t.y=a*(this.cosX0*e-this.sinX0*n*Math.cos(f))+this.y0),t.x=a*n*Math.sin(f)+this.x0,t))},inverse:function(t){var a,h,e,n,r;t.x-=this.x0,t.y-=this.y0;var o=Math.sqrt(t.x*t.x+t.y*t.y);if(this.sphere){var l=2*Math.atan(o/(2*this.a*this.k0));return a=this.long0,h=this.lat0,o<=i?(t.x=a,t.y=h,t):(h=Math.asin(Math.cos(l)*this.sinlat0+t.y*Math.sin(l)*this.coslat0/o),a=Math.abs(this.coslat0)<i?this.lat0>0?I(this.long0+Math.atan2(t.x,-1*t.y)):I(this.long0+Math.atan2(t.x,t.y)):I(this.long0+Math.atan2(t.x*Math.sin(l),o*this.coslat0*Math.cos(l)-t.y*this.sinlat0*Math.sin(l))),t.x=a,t.y=h,t)}if(Math.abs(this.coslat0)<=i){if(o<=i)return h=this.lat0,a=this.long0,t.x=a,t.y=h,t;t.x*=this.con,t.y*=this.con,e=o*this.cons/(2*this.a*this.k0),h=this.con*A(this.e,e),a=this.con*I(this.con*this.long0+Math.atan2(t.x,-1*t.y))}else n=2*Math.atan(o*this.cosX0/(2*this.a*this.k0*this.ms1)),a=this.long0,o<=i?r=this.X0:(r=Math.asin(Math.cos(n)*this.sinX0+t.y*Math.sin(n)*this.cosX0/o),a=I(this.long0+Math.atan2(t.x*Math.sin(n),o*this.cosX0*Math.cos(n)-t.y*this.sinX0*Math.sin(n)))),h=-1*A(this.e,Math.tan(.5*(s+r)));return t.x=a,t.y=h,t},names:["stere","Stereographic_South_Pole","Polar Stereographic (variant B)"],ssfn_:function(t,i,a){return i*=a,Math.tan(.5*(s+t))*Math.pow((1-i)/(1+i),.5*a)}};var Lt={init:function(){var t=this.lat0;this.lambda0=this.long0;var s=Math.sin(t),i=this.a,a=1/this.rf,h=2*a-Math.pow(a,2),e=this.e=Math.sqrt(h);this.R=this.k0*i*Math.sqrt(1-h)/(1-h*Math.pow(s,2)),this.alpha=Math.sqrt(1+h/(1-h)*Math.pow(Math.cos(t),4)),this.b0=Math.asin(s/this.alpha);var n=Math.log(Math.tan(Math.PI/4+this.b0/2)),r=Math.log(Math.tan(Math.PI/4+t/2)),o=Math.log((1+e*s)/(1-e*s));this.K=n-this.alpha*r+this.alpha*e/2*o},forward:function(t){var s=Math.log(Math.tan(Math.PI/4-t.y/2)),i=this.e/2*Math.log((1+this.e*Math.sin(t.y))/(1-this.e*Math.sin(t.y))),a=-this.alpha*(s+i)+this.K,h=2*(Math.atan(Math.exp(a))-Math.PI/4),e=this.alpha*(t.x-this.lambda0),n=Math.atan(Math.sin(e)/(Math.sin(this.b0)*Math.tan(h)+Math.cos(this.b0)*Math.cos(e))),r=Math.asin(Math.cos(this.b0)*Math.sin(h)-Math.sin(this.b0)*Math.cos(h)*Math.cos(e));return t.y=this.R/2*Math.log((1+Math.sin(r))/(1-Math.sin(r)))+this.y0,t.x=this.R*n+this.x0,t},inverse:function(t){for(var s=t.x-this.x0,i=t.y-this.y0,a=s/this.R,h=2*(Math.atan(Math.exp(i/this.R))-Math.PI/4),e=Math.asin(Math.cos(this.b0)*Math.sin(h)+Math.sin(this.b0)*Math.cos(h)*Math.cos(a)),n=Math.atan(Math.sin(a)/(Math.cos(this.b0)*Math.cos(a)-Math.sin(this.b0)*Math.tan(h))),r=this.lambda0+n/this.alpha,o=0,l=e,c=-1e3,M=0;Math.abs(l-c)>1e-7;){if(++M>20)return;o=1/this.alpha*(Math.log(Math.tan(Math.PI/4+e/2))-this.K)+this.e*Math.log(Math.tan(Math.PI/4+Math.asin(this.e*Math.sin(l))/2)),c=l,l=2*Math.atan(Math.exp(o))-Math.PI/2}return t.x=r,t.y=l,t},names:["somerc"]};var Tt={init:function(){this.no_off=this.no_off||!1,this.no_rot=this.no_rot||!1,isNaN(this.k0)&&(this.k0=1);var t=Math.sin(this.lat0),s=Math.cos(this.lat0),i=this.e*t;this.bl=Math.sqrt(1+this.es/(1-this.es)*Math.pow(s,4)),this.al=this.a*this.bl*this.k0*Math.sqrt(1-this.es)/(1-i*i);var a,h,e=O(this.e,this.lat0,t),n=this.bl/s*Math.sqrt((1-this.es)/(1-i*i));if(n*n<1&&(n=1),isNaN(this.longc)){var r=O(this.e,this.lat1,Math.sin(this.lat1)),o=O(this.e,this.lat2,Math.sin(this.lat2));this.lat0>=0?this.el=(n+Math.sqrt(n*n-1))*Math.pow(e,this.bl):this.el=(n-Math.sqrt(n*n-1))*Math.pow(e,this.bl);var l=Math.pow(r,this.bl),c=Math.pow(o,this.bl);h=.5*((a=this.el/l)-1/a);var M=(this.el*this.el-c*l)/(this.el*this.el+c*l),u=(c-l)/(c+l),f=I(this.long1-this.long2);this.long0=.5*(this.long1+this.long2)-Math.atan(M*Math.tan(.5*this.bl*f)/u)/this.bl,this.long0=I(this.long0);var m=I(this.long1-this.long0);this.gamma0=Math.atan(Math.sin(this.bl*m)/h),this.alpha=Math.asin(n*Math.sin(this.gamma0))}else a=this.lat0>=0?n+Math.sqrt(n*n-1):n-Math.sqrt(n*n-1),this.el=a*Math.pow(e,this.bl),h=.5*(a-1/a),this.gamma0=Math.asin(Math.sin(this.alpha)/n),this.long0=this.longc-Math.asin(h*Math.tan(this.gamma0))/this.bl;this.no_off?this.uc=0:this.lat0>=0?this.uc=this.al/this.bl*Math.atan2(Math.sqrt(n*n-1),Math.cos(this.alpha)):this.uc=-1*this.al/this.bl*Math.atan2(Math.sqrt(n*n-1),Math.cos(this.alpha))},forward:function(t){var a,h,n,r=t.x,o=t.y,l=I(r-this.long0);if(Math.abs(Math.abs(o)-s)<=i)n=o>0?-1:1,h=this.al/this.bl*Math.log(Math.tan(e+n*this.gamma0*.5)),a=-1*n*s*this.al/this.bl;else{var c=O(this.e,o,Math.sin(o)),M=this.el/Math.pow(c,this.bl),u=.5*(M-1/M),f=.5*(M+1/M),m=Math.sin(this.bl*l),p=(u*Math.sin(this.gamma0)-m*Math.cos(this.gamma0))/f;h=Math.abs(Math.abs(p)-1)<=i?Number.POSITIVE_INFINITY:.5*this.al*Math.log((1-p)/(1+p))/this.bl,a=Math.abs(Math.cos(this.bl*l))<=i?this.al*this.bl*l:this.al*Math.atan2(u*Math.cos(this.gamma0)+m*Math.sin(this.gamma0),Math.cos(this.bl*l))/this.bl}return this.no_rot?(t.x=this.x0+a,t.y=this.y0+h):(a-=this.uc,t.x=this.x0+h*Math.cos(this.alpha)+a*Math.sin(this.alpha),t.y=this.y0+a*Math.cos(this.alpha)-h*Math.sin(this.alpha)),t},inverse:function(t){var a,h;this.no_rot?(h=t.y-this.y0,a=t.x-this.x0):(h=(t.x-this.x0)*Math.cos(this.alpha)-(t.y-this.y0)*Math.sin(this.alpha),a=(t.y-this.y0)*Math.cos(this.alpha)+(t.x-this.x0)*Math.sin(this.alpha),a+=this.uc);var e=Math.exp(-1*this.bl*h/this.al),n=.5*(e-1/e),r=.5*(e+1/e),o=Math.sin(this.bl*a/this.al),l=(o*Math.cos(this.gamma0)+n*Math.sin(this.gamma0))/r,c=Math.pow(this.el/Math.sqrt((1+l)/(1-l)),1/this.bl);return Math.abs(l-1)<i?(t.x=this.long0,t.y=s):Math.abs(l+1)<i?(t.x=this.long0,t.y=-1*s):(t.y=A(this.e,c),t.x=I(this.long0-Math.atan2(n*Math.cos(this.gamma0)-o*Math.sin(this.gamma0),Math.cos(this.bl*a/this.al))/this.bl)),t},names:["Hotine_Oblique_Mercator","Hotine Oblique Mercator","Hotine_Oblique_Mercator_Azimuth_Natural_Origin","Hotine_Oblique_Mercator_Azimuth_Center","omerc"]};var Dt={init:function(){if(this.lat2||(this.lat2=this.lat1),this.k0||(this.k0=1),this.x0=this.x0||0,this.y0=this.y0||0,!(Math.abs(this.lat1+this.lat2)<i)){var t=this.b/this.a;this.e=Math.sqrt(1-t*t);var s=Math.sin(this.lat1),a=Math.cos(this.lat1),h=E(this.e,s,a),e=O(this.e,this.lat1,s),n=Math.sin(this.lat2),r=Math.cos(this.lat2),o=E(this.e,n,r),l=O(this.e,this.lat2,n),c=O(this.e,this.lat0,Math.sin(this.lat0));Math.abs(this.lat1-this.lat2)>i?this.ns=Math.log(h/o)/Math.log(e/l):this.ns=s,isNaN(this.ns)&&(this.ns=s),this.f0=h/(this.ns*Math.pow(e,this.ns)),this.rh=this.a*this.f0*Math.pow(c,this.ns),this.title||(this.title="Lambert Conformal Conic")}},forward:function(t){var a=t.x,h=t.y;Math.abs(2*Math.abs(h)-Math.PI)<=i&&(h=q(h)*(s-2e-10));var e,n,r=Math.abs(Math.abs(h)-s);if(r>i)e=O(this.e,h,Math.sin(h)),n=this.a*this.f0*Math.pow(e,this.ns);else{if((r=h*this.ns)<=0)return null;n=0}var o=this.ns*I(a-this.long0);return t.x=this.k0*(n*Math.sin(o))+this.x0,t.y=this.k0*(this.rh-n*Math.cos(o))+this.y0,t},inverse:function(t){var i,a,h,e,n,r=(t.x-this.x0)/this.k0,o=this.rh-(t.y-this.y0)/this.k0;this.ns>0?(i=Math.sqrt(r*r+o*o),a=1):(i=-Math.sqrt(r*r+o*o),a=-1);var l=0;if(0!==i&&(l=Math.atan2(a*r,a*o)),0!==i||this.ns>0){if(a=1/this.ns,h=Math.pow(i/(this.a*this.f0),a),-9999===(e=A(this.e,h)))return null}else e=-s;return n=I(l/this.ns+this.long0),t.x=n,t.y=e,t},names:["Lambert Tangential Conformal Conic Projection","Lambert_Conformal_Conic","Lambert_Conformal_Conic_2SP","lcc"]};var Ut={init:function(){this.a=6377397.155,this.es=.006674372230614,this.e=Math.sqrt(this.es),this.lat0||(this.lat0=.863937979737193),this.long0||(this.long0=.4334234309119251),this.k0||(this.k0=.9999),this.s45=.785398163397448,this.s90=2*this.s45,this.fi0=this.lat0,this.e2=this.es,this.e=Math.sqrt(this.e2),this.alfa=Math.sqrt(1+this.e2*Math.pow(Math.cos(this.fi0),4)/(1-this.e2)),this.uq=1.04216856380474,this.u0=Math.asin(Math.sin(this.fi0)/this.alfa),this.g=Math.pow((1+this.e*Math.sin(this.fi0))/(1-this.e*Math.sin(this.fi0)),this.alfa*this.e/2),this.k=Math.tan(this.u0/2+this.s45)/Math.pow(Math.tan(this.fi0/2+this.s45),this.alfa)*this.g,this.k1=this.k0,this.n0=this.a*Math.sqrt(1-this.e2)/(1-this.e2*Math.pow(Math.sin(this.fi0),2)),this.s0=1.37008346281555,this.n=Math.sin(this.s0),this.ro0=this.k1*this.n0/Math.tan(this.s0),this.ad=this.s90-this.uq},forward:function(t){var s,i,a,h,e,n,r,o=t.x,l=t.y,c=I(o-this.long0);return s=Math.pow((1+this.e*Math.sin(l))/(1-this.e*Math.sin(l)),this.alfa*this.e/2),i=2*(Math.atan(this.k*Math.pow(Math.tan(l/2+this.s45),this.alfa)/s)-this.s45),a=-c*this.alfa,h=Math.asin(Math.cos(this.ad)*Math.sin(i)+Math.sin(this.ad)*Math.cos(i)*Math.cos(a)),e=Math.asin(Math.cos(i)*Math.sin(a)/Math.cos(h)),n=this.n*e,r=this.ro0*Math.pow(Math.tan(this.s0/2+this.s45),this.n)/Math.pow(Math.tan(h/2+this.s45),this.n),t.y=r*Math.cos(n)/1,t.x=r*Math.sin(n)/1,this.czech||(t.y*=-1,t.x*=-1),t},inverse:function(t){var s,i,a,h,e,n,r,o=t.x;t.x=t.y,t.y=o,this.czech||(t.y*=-1,t.x*=-1),e=Math.sqrt(t.x*t.x+t.y*t.y),h=Math.atan2(t.y,t.x)/Math.sin(this.s0),a=2*(Math.atan(Math.pow(this.ro0/e,1/this.n)*Math.tan(this.s0/2+this.s45))-this.s45),s=Math.asin(Math.cos(this.ad)*Math.sin(a)-Math.sin(this.ad)*Math.cos(a)*Math.cos(h)),i=Math.asin(Math.cos(a)*Math.sin(h)/Math.cos(s)),t.x=this.long0-i/this.alfa,n=s,r=0;var l=0;do{t.y=2*(Math.atan(Math.pow(this.k,-1/this.alfa)*Math.pow(Math.tan(s/2+this.s45),1/this.alfa)*Math.pow((1+this.e*Math.sin(n))/(1-this.e*Math.sin(n)),this.e/2))-this.s45),Math.abs(n-t.y)<1e-10&&(r=1),n=t.y,l+=1}while(0===r&&l<15);return l>=15?null:t},names:["Krovak","krovak"]},Bt=function(t,s,i,a,h){return t*h-s*Math.sin(2*h)+i*Math.sin(4*h)-a*Math.sin(6*h)},Ft=function(t){return 1-.25*t*(1+t/16*(3+1.25*t))},Qt=function(t){return.375*t*(1+.25*t*(1+.46875*t))},Wt=function(t){return.05859375*t*t*(1+.75*t)},Xt=function(t){return t*t*t*(35/3072)},Ht=function(t,s,i){var a=s*i;return t/Math.sqrt(1-a*a)},Jt=function(t){return Math.abs(t)<s?t:t-q(t)*Math.PI},Kt=function(t,s,i,a,h){var e,n;e=t/s;for(var r=0;r<15;r++)if(e+=n=(t-(s*e-i*Math.sin(2*e)+a*Math.sin(4*e)-h*Math.sin(6*e)))/(s-2*i*Math.cos(2*e)+4*a*Math.cos(4*e)-6*h*Math.cos(6*e)),Math.abs(n)<=1e-10)return e;return NaN};var Vt={init:function(){this.sphere||(this.e0=Ft(this.es),this.e1=Qt(this.es),this.e2=Wt(this.es),this.e3=Xt(this.es),this.ml0=this.a*Bt(this.e0,this.e1,this.e2,this.e3,this.lat0))},forward:function(t){var s,i,a=t.x,h=t.y;if(a=I(a-this.long0),this.sphere)s=this.a*Math.asin(Math.cos(h)*Math.sin(a)),i=this.a*(Math.atan2(Math.tan(h),Math.cos(a))-this.lat0);else{var e=Math.sin(h),n=Math.cos(h),r=Ht(this.a,this.e,e),o=Math.tan(h)*Math.tan(h),l=a*Math.cos(h),c=l*l,M=this.es*n*n/(1-this.es);s=r*l*(1-c*o*(1/6-(8-o+8*M)*c/120)),i=this.a*Bt(this.e0,this.e1,this.e2,this.e3,h)-this.ml0+r*e/n*c*(.5+(5-o+6*M)*c/24)}return t.x=s+this.x0,t.y=i+this.y0,t},inverse:function(t){t.x-=this.x0,t.y-=this.y0;var a,h,e=t.x/this.a,n=t.y/this.a;if(this.sphere){var r=n+this.lat0;a=Math.asin(Math.sin(r)*Math.cos(e)),h=Math.atan2(Math.tan(e),Math.cos(r))}else{var o=this.ml0/this.a+n,l=Kt(o,this.e0,this.e1,this.e2,this.e3);if(Math.abs(Math.abs(l)-s)<=i)return t.x=this.long0,t.y=s,n<0&&(t.y*=-1),t;var c=Ht(this.a,this.e,Math.sin(l)),M=c*c*c/this.a/this.a*(1-this.es),u=Math.pow(Math.tan(l),2),f=e*this.a/c,m=f*f;a=l-c*Math.tan(l)/M*f*f*(.5-(1+3*u)*f*f/24),h=f*(1-m*(u/3+(1+3*u)*u*m/15))/Math.cos(l)}return t.x=I(h+this.long0),t.y=Jt(a),t},names:["Cassini","Cassini_Soldner","cass"]},Zt=function(t,s){var i;return t>1e-7?(1-t*t)*(s/(1-(i=t*s)*i)-.5/t*Math.log((1-i)/(1+i))):2*s};var Yt=.3333333333333333,$t=.17222222222222222,ts=.10257936507936508,ss=.06388888888888888,is=.0664021164021164,as=.016415012942191543;var hs={init:function(){var t,a=Math.abs(this.lat0);if(Math.abs(a-s)<i?this.mode=this.lat0<0?this.S_POLE:this.N_POLE:Math.abs(a)<i?this.mode=this.EQUIT:this.mode=this.OBLIQ,this.es>0)switch(this.qp=Zt(this.e,1),this.mmf=.5/(1-this.es),this.apa=function(t){var s,i=[];return i[0]=t*Yt,s=t*t,i[0]+=s*$t,i[1]=s*ss,s*=t,i[0]+=s*ts,i[1]+=s*is,i[2]=s*as,i}(this.es),this.mode){case this.N_POLE:case this.S_POLE:this.dd=1;break;case this.EQUIT:this.rq=Math.sqrt(.5*this.qp),this.dd=1/this.rq,this.xmf=1,this.ymf=.5*this.qp;break;case this.OBLIQ:this.rq=Math.sqrt(.5*this.qp),t=Math.sin(this.lat0),this.sinb1=Zt(this.e,t)/this.qp,this.cosb1=Math.sqrt(1-this.sinb1*this.sinb1),this.dd=Math.cos(this.lat0)/(Math.sqrt(1-this.es*t*t)*this.rq*this.cosb1),this.ymf=(this.xmf=this.rq)/this.dd,this.xmf*=this.dd}else this.mode===this.OBLIQ&&(this.sinph0=Math.sin(this.lat0),this.cosph0=Math.cos(this.lat0))},forward:function(t){var a,h,n,r,o,l,c,M,u,f,m=t.x,p=t.y;if(m=I(m-this.long0),this.sphere){if(o=Math.sin(p),f=Math.cos(p),n=Math.cos(m),this.mode===this.OBLIQ||this.mode===this.EQUIT){if((h=this.mode===this.EQUIT?1+f*n:1+this.sinph0*o+this.cosph0*f*n)<=i)return null;a=(h=Math.sqrt(2/h))*f*Math.sin(m),h*=this.mode===this.EQUIT?o:this.cosph0*o-this.sinph0*f*n}else if(this.mode===this.N_POLE||this.mode===this.S_POLE){if(this.mode===this.N_POLE&&(n=-n),Math.abs(p+this.lat0)<i)return null;h=e-.5*p,a=(h=2*(this.mode===this.S_POLE?Math.cos(h):Math.sin(h)))*Math.sin(m),h*=n}}else{switch(c=0,M=0,u=0,n=Math.cos(m),r=Math.sin(m),o=Math.sin(p),l=Zt(this.e,o),this.mode!==this.OBLIQ&&this.mode!==this.EQUIT||(c=l/this.qp,M=Math.sqrt(1-c*c)),this.mode){case this.OBLIQ:u=1+this.sinb1*c+this.cosb1*M*n;break;case this.EQUIT:u=1+M*n;break;case this.N_POLE:u=s+p,l=this.qp-l;break;case this.S_POLE:u=p-s,l=this.qp+l}if(Math.abs(u)<i)return null;switch(this.mode){case this.OBLIQ:case this.EQUIT:u=Math.sqrt(2/u),h=this.mode===this.OBLIQ?this.ymf*u*(this.cosb1*c-this.sinb1*M*n):(u=Math.sqrt(2/(1+M*n)))*c*this.ymf,a=this.xmf*u*M*r;break;case this.N_POLE:case this.S_POLE:l>=0?(a=(u=Math.sqrt(l))*r,h=n*(this.mode===this.S_POLE?u:-u)):a=h=0}}return t.x=this.a*a+this.x0,t.y=this.a*h+this.y0,t},inverse:function(t){t.x-=this.x0,t.y-=this.y0;var a,h,e,n,r,o,l,c,M,u,f=t.x/this.a,m=t.y/this.a;if(this.sphere){var p,d=0,y=0;if((h=.5*(p=Math.sqrt(f*f+m*m)))>1)return null;switch(h=2*Math.asin(h),this.mode!==this.OBLIQ&&this.mode!==this.EQUIT||(y=Math.sin(h),d=Math.cos(h)),this.mode){case this.EQUIT:h=Math.abs(p)<=i?0:Math.asin(m*y/p),f*=y,m=d*p;break;case this.OBLIQ:h=Math.abs(p)<=i?this.lat0:Math.asin(d*this.sinph0+m*y*this.cosph0/p),f*=y*this.cosph0,m=(d-Math.sin(h)*this.sinph0)*p;break;case this.N_POLE:m=-m,h=s-h;break;case this.S_POLE:h-=s}a=0!==m||this.mode!==this.EQUIT&&this.mode!==this.OBLIQ?Math.atan2(f,m):0}else{if(l=0,this.mode===this.OBLIQ||this.mode===this.EQUIT){if(f/=this.dd,m*=this.dd,(o=Math.sqrt(f*f+m*m))<i)return t.x=this.long0,t.y=this.lat0,t;n=2*Math.asin(.5*o/this.rq),e=Math.cos(n),f*=n=Math.sin(n),this.mode===this.OBLIQ?(l=e*this.sinb1+m*n*this.cosb1/o,r=this.qp*l,m=o*this.cosb1*e-m*this.sinb1*n):(l=m*n/o,r=this.qp*l,m=o*e)}else if(this.mode===this.N_POLE||this.mode===this.S_POLE){if(this.mode===this.N_POLE&&(m=-m),!(r=f*f+m*m))return t.x=this.long0,t.y=this.lat0,t;l=1-r/this.qp,this.mode===this.S_POLE&&(l=-l)}a=Math.atan2(f,m),c=Math.asin(l),M=this.apa,u=c+c,h=c+M[0]*Math.sin(u)+M[1]*Math.sin(u+u)+M[2]*Math.sin(u+u+u)}return t.x=I(this.long0+a),t.y=h,t},names:["Lambert Azimuthal Equal Area","Lambert_Azimuthal_Equal_Area","laea"],S_POLE:1,N_POLE:2,EQUIT:3,OBLIQ:4},es=function(t){return Math.abs(t)>1&&(t=t>1?1:-1),Math.asin(t)};var ns={init:function(){Math.abs(this.lat1+this.lat2)<i||(this.temp=this.b/this.a,this.es=1-Math.pow(this.temp,2),this.e3=Math.sqrt(this.es),this.sin_po=Math.sin(this.lat1),this.cos_po=Math.cos(this.lat1),this.t1=this.sin_po,this.con=this.sin_po,this.ms1=E(this.e3,this.sin_po,this.cos_po),this.qs1=Zt(this.e3,this.sin_po,this.cos_po),this.sin_po=Math.sin(this.lat2),this.cos_po=Math.cos(this.lat2),this.t2=this.sin_po,this.ms2=E(this.e3,this.sin_po,this.cos_po),this.qs2=Zt(this.e3,this.sin_po,this.cos_po),this.sin_po=Math.sin(this.lat0),this.cos_po=Math.cos(this.lat0),this.t3=this.sin_po,this.qs0=Zt(this.e3,this.sin_po,this.cos_po),Math.abs(this.lat1-this.lat2)>i?this.ns0=(this.ms1*this.ms1-this.ms2*this.ms2)/(this.qs2-this.qs1):this.ns0=this.con,this.c=this.ms1*this.ms1+this.ns0*this.qs1,this.rh=this.a*Math.sqrt(this.c-this.ns0*this.qs0)/this.ns0)},forward:function(t){var s=t.x,i=t.y;this.sin_phi=Math.sin(i),this.cos_phi=Math.cos(i);var a=Zt(this.e3,this.sin_phi,this.cos_phi),h=this.a*Math.sqrt(this.c-this.ns0*a)/this.ns0,e=this.ns0*I(s-this.long0),n=h*Math.sin(e)+this.x0,r=this.rh-h*Math.cos(e)+this.y0;return t.x=n,t.y=r,t},inverse:function(t){var s,i,a,h,e,n;return t.x-=this.x0,t.y=this.rh-t.y+this.y0,this.ns0>=0?(s=Math.sqrt(t.x*t.x+t.y*t.y),a=1):(s=-Math.sqrt(t.x*t.x+t.y*t.y),a=-1),h=0,0!==s&&(h=Math.atan2(a*t.x,a*t.y)),a=s*this.ns0/this.a,this.sphere?n=Math.asin((this.c-a*a)/(2*this.ns0)):(i=(this.c-a*a)/this.ns0,n=this.phi1z(this.e3,i)),e=I(h/this.ns0+this.long0),t.x=e,t.y=n,t},names:["Albers_Conic_Equal_Area","Albers","aea"],phi1z:function(t,s){var a,h,e,n,r=es(.5*s);if(t<i)return r;for(var o=t*t,l=1;l<=25;l++)if(r+=n=.5*(e=1-(h=t*(a=Math.sin(r)))*h)*e/Math.cos(r)*(s/(1-o)-a/e+.5/t*Math.log((1-h)/(1+h))),Math.abs(n)<=1e-7)return r;return null}};var rs={init:function(){this.sin_p14=Math.sin(this.lat0),this.cos_p14=Math.cos(this.lat0),this.infinity_dist=1e3*this.a,this.rc=1},forward:function(t){var s,a,h,e,n,r,o,l=t.x,c=t.y;return h=I(l-this.long0),s=Math.sin(c),a=Math.cos(c),e=Math.cos(h),1,(n=this.sin_p14*s+this.cos_p14*a*e)>0||Math.abs(n)<=i?(r=this.x0+1*this.a*a*Math.sin(h)/n,o=this.y0+1*this.a*(this.cos_p14*s-this.sin_p14*a*e)/n):(r=this.x0+this.infinity_dist*a*Math.sin(h),o=this.y0+this.infinity_dist*(this.cos_p14*s-this.sin_p14*a*e)),t.x=r,t.y=o,t},inverse:function(t){var s,i,a,h,e,n;return t.x=(t.x-this.x0)/this.a,t.y=(t.y-this.y0)/this.a,t.x/=this.k0,t.y/=this.k0,(s=Math.sqrt(t.x*t.x+t.y*t.y))?(h=Math.atan2(s,this.rc),i=Math.sin(h),a=Math.cos(h),n=es(a*this.sin_p14+t.y*i*this.cos_p14/s),e=Math.atan2(t.x*i,s*this.cos_p14*a-t.y*this.sin_p14*i),e=I(this.long0+e)):(n=this.phic0,e=0),t.x=e,t.y=n,t},names:["gnom"]};var os={init:function(){this.sphere||(this.k0=E(this.e,Math.sin(this.lat_ts),Math.cos(this.lat_ts)))},forward:function(t){var s,i,a=t.x,h=t.y,e=I(a-this.long0);if(this.sphere)s=this.x0+this.a*e*Math.cos(this.lat_ts),i=this.y0+this.a*Math.sin(h)/Math.cos(this.lat_ts);else{var n=Zt(this.e,Math.sin(h));s=this.x0+this.a*this.k0*e,i=this.y0+this.a*n*.5/this.k0}return t.x=s,t.y=i,t},inverse:function(t){var i,a;return t.x-=this.x0,t.y-=this.y0,this.sphere?(i=I(this.long0+t.x/this.a/Math.cos(this.lat_ts)),a=Math.asin(t.y/this.a*Math.cos(this.lat_ts))):(a=function(t,i){var a=1-(1-t*t)/(2*t)*Math.log((1-t)/(1+t));if(Math.abs(Math.abs(i)-a)<1e-6)return i<0?-1*s:s;for(var h,e,n,r,o=Math.asin(.5*i),l=0;l<30;l++)if(e=Math.sin(o),n=Math.cos(o),r=t*e,o+=h=Math.pow(1-r*r,2)/(2*n)*(i/(1-t*t)-e/(1-r*r)+.5/t*Math.log((1-r)/(1+r))),Math.abs(h)<=1e-10)return o;return NaN}(this.e,2*t.y*this.k0/this.a),i=I(this.long0+t.x/(this.a*this.k0))),t.x=i,t.y=a,t},names:["cea"]};var ls={init:function(){this.x0=this.x0||0,this.y0=this.y0||0,this.lat0=this.lat0||0,this.long0=this.long0||0,this.lat_ts=this.lat_ts||0,this.title=this.title||"Equidistant Cylindrical (Plate Carre)",this.rc=Math.cos(this.lat_ts)},forward:function(t){var s=t.x,i=t.y,a=I(s-this.long0),h=Jt(i-this.lat0);return t.x=this.x0+this.a*a*this.rc,t.y=this.y0+this.a*h,t},inverse:function(t){var s=t.x,i=t.y;return t.x=I(this.long0+(s-this.x0)/(this.a*this.rc)),t.y=Jt(this.lat0+(i-this.y0)/this.a),t},names:["Equirectangular","Equidistant_Cylindrical","eqc"]};var cs={init:function(){this.temp=this.b/this.a,this.es=1-Math.pow(this.temp,2),this.e=Math.sqrt(this.es),this.e0=Ft(this.es),this.e1=Qt(this.es),this.e2=Wt(this.es),this.e3=Xt(this.es),this.ml0=this.a*Bt(this.e0,this.e1,this.e2,this.e3,this.lat0)},forward:function(t){var s,a,h,e=t.x,n=t.y,r=I(e-this.long0);if(h=r*Math.sin(n),this.sphere)Math.abs(n)<=i?(s=this.a*r,a=-1*this.a*this.lat0):(s=this.a*Math.sin(h)/Math.tan(n),a=this.a*(Jt(n-this.lat0)+(1-Math.cos(h))/Math.tan(n)));else if(Math.abs(n)<=i)s=this.a*r,a=-1*this.ml0;else{var o=Ht(this.a,this.e,Math.sin(n))/Math.tan(n);s=o*Math.sin(h),a=this.a*Bt(this.e0,this.e1,this.e2,this.e3,n)-this.ml0+o*(1-Math.cos(h))}return t.x=s+this.x0,t.y=a+this.y0,t},inverse:function(t){var s,a,h,e,n,r,o,l,c;if(h=t.x-this.x0,e=t.y-this.y0,this.sphere)if(Math.abs(e+this.a*this.lat0)<=i)s=I(h/this.a+this.long0),a=0;else{var M;for(r=this.lat0+e/this.a,o=h*h/this.a/this.a+r*r,l=r,n=20;n;--n)if(l+=c=-1*(r*(l*(M=Math.tan(l))+1)-l-.5*(l*l+o)*M)/((l-r)/M-1),Math.abs(c)<=i){a=l;break}s=I(this.long0+Math.asin(h*Math.tan(l)/this.a)/Math.sin(a))}else if(Math.abs(e+this.ml0)<=i)a=0,s=I(this.long0+h/this.a);else{var u,f,m,p,d;for(r=(this.ml0+e)/this.a,o=h*h/this.a/this.a+r*r,l=r,n=20;n;--n)if(d=this.e*Math.sin(l),u=Math.sqrt(1-d*d)*Math.tan(l),f=this.a*Bt(this.e0,this.e1,this.e2,this.e3,l),m=this.e0-2*this.e1*Math.cos(2*l)+4*this.e2*Math.cos(4*l)-6*this.e3*Math.cos(6*l),l-=c=(r*(u*(p=f/this.a)+1)-p-.5*u*(p*p+o))/(this.es*Math.sin(2*l)*(p*p+o-2*r*p)/(4*u)+(r-p)*(u*m-2/Math.sin(2*l))-m),Math.abs(c)<=i){a=l;break}u=Math.sqrt(1-this.es*Math.pow(Math.sin(a),2))*Math.tan(a),s=I(this.long0+Math.asin(h*u/this.a)/Math.sin(a))}return t.x=s,t.y=a,t},names:["Polyconic","poly"]};var Ms={init:function(){this.A=[],this.A[1]=.6399175073,this.A[2]=-.1358797613,this.A[3]=.063294409,this.A[4]=-.02526853,this.A[5]=.0117879,this.A[6]=-.0055161,this.A[7]=.0026906,this.A[8]=-.001333,this.A[9]=67e-5,this.A[10]=-34e-5,this.B_re=[],this.B_im=[],this.B_re[1]=.7557853228,this.B_im[1]=0,this.B_re[2]=.249204646,this.B_im[2]=.003371507,this.B_re[3]=-.001541739,this.B_im[3]=.04105856,this.B_re[4]=-.10162907,this.B_im[4]=.01727609,this.B_re[5]=-.26623489,this.B_im[5]=-.36249218,this.B_re[6]=-.6870983,this.B_im[6]=-1.1651967,this.C_re=[],this.C_im=[],this.C_re[1]=1.3231270439,this.C_im[1]=0,this.C_re[2]=-.577245789,this.C_im[2]=-.007809598,this.C_re[3]=.508307513,this.C_im[3]=-.112208952,this.C_re[4]=-.15094762,this.C_im[4]=.18200602,this.C_re[5]=1.01418179,this.C_im[5]=1.64497696,this.C_re[6]=1.9660549,this.C_im[6]=2.5127645,this.D=[],this.D[1]=1.5627014243,this.D[2]=.5185406398,this.D[3]=-.03333098,this.D[4]=-.1052906,this.D[5]=-.0368594,this.D[6]=.007317,this.D[7]=.0122,this.D[8]=.00394,this.D[9]=-.0013},forward:function(s){var i,a=s.x,h=s.y-this.lat0,e=a-this.long0,n=h/t*1e-5,r=e,o=1,l=0;for(i=1;i<=10;i++)o*=n,l+=this.A[i]*o;var c,M=l,u=r,f=1,m=0,p=0,d=0;for(i=1;i<=6;i++)c=m*M+f*u,f=f*M-m*u,m=c,p=p+this.B_re[i]*f-this.B_im[i]*m,d=d+this.B_im[i]*f+this.B_re[i]*m;return s.x=d*this.a+this.x0,s.y=p*this.a+this.y0,s},inverse:function(s){var i,a,h=s.x,e=s.y,n=h-this.x0,r=(e-this.y0)/this.a,o=n/this.a,l=1,c=0,M=0,u=0;for(i=1;i<=6;i++)a=c*r+l*o,l=l*r-c*o,c=a,M=M+this.C_re[i]*l-this.C_im[i]*c,u=u+this.C_im[i]*l+this.C_re[i]*c;for(var f=0;f<this.iterations;f++){var m,p=M,d=u,y=r,_=o;for(i=2;i<=6;i++)m=d*M+p*u,p=p*M-d*u,d=m,y+=(i-1)*(this.B_re[i]*p-this.B_im[i]*d),_+=(i-1)*(this.B_im[i]*p+this.B_re[i]*d);p=1,d=0;var x=this.B_re[1],v=this.B_im[1];for(i=2;i<=6;i++)m=d*M+p*u,p=p*M-d*u,d=m,x+=i*(this.B_re[i]*p-this.B_im[i]*d),v+=i*(this.B_im[i]*p+this.B_re[i]*d);var g=x*x+v*v;M=(y*x+_*v)/g,u=(_*x-y*v)/g}var b=M,w=u,C=1,P=0;for(i=1;i<=9;i++)C*=b,P+=this.D[i]*C;var S=this.lat0+P*t*1e5,N=this.long0+w;return s.x=N,s.y=S,s},names:["New_Zealand_Map_Grid","nzmg"]};var us={init:function(){},forward:function(t){var s=t.x,i=t.y,a=I(s-this.long0),h=this.x0+this.a*a,e=this.y0+this.a*Math.log(Math.tan(Math.PI/4+i/2.5))*1.25;return t.x=h,t.y=e,t},inverse:function(t){t.x-=this.x0,t.y-=this.y0;var s=I(this.long0+t.x/this.a),i=2.5*(Math.atan(Math.exp(.8*t.y/this.a))-Math.PI/4);return t.x=s,t.y=i,t},names:["Miller_Cylindrical","mill"]};var fs={init:function(){this.sphere?(this.n=1,this.m=0,this.es=0,this.C_y=Math.sqrt((this.m+1)/this.n),this.C_x=this.C_y/(this.m+1)):this.en=wt(this.es)},forward:function(t){var s,a,h=t.x,e=t.y;if(h=I(h-this.long0),this.sphere){if(this.m)for(var n=this.n*Math.sin(e),r=20;r;--r){var o=(this.m*e+Math.sin(e)-n)/(this.m+Math.cos(e));if(e-=o,Math.abs(o)<i)break}else e=1!==this.n?Math.asin(this.n*Math.sin(e)):e;s=this.a*this.C_x*h*(this.m+Math.cos(e)),a=this.a*this.C_y*e}else{var l=Math.sin(e),c=Math.cos(e);a=this.a*Ct(e,l,c,this.en),s=this.a*h*c/Math.sqrt(1-this.es*l*l)}return t.x=s,t.y=a,t},inverse:function(t){var a,h,e,n;return t.x-=this.x0,e=t.x/this.a,t.y-=this.y0,a=t.y/this.a,this.sphere?(a/=this.C_y,e/=this.C_x*(this.m+Math.cos(a)),this.m?a=es((this.m*a+Math.sin(a))/this.n):1!==this.n&&(a=es(Math.sin(a)/this.n)),e=I(e+this.long0),a=Jt(a)):(a=Pt(t.y/this.a,this.es,this.en),(n=Math.abs(a))<s?(n=Math.sin(a),h=this.long0+t.x*Math.sqrt(1-this.es*n*n)/(this.a*Math.cos(a)),e=I(h)):n-i<s&&(e=this.long0)),t.x=e,t.y=a,t},names:["Sinusoidal","sinu"]};var ms={init:function(){},forward:function(t){for(var s=t.x,a=t.y,h=I(s-this.long0),e=a,n=Math.PI*Math.sin(a);;){var r=-(e+Math.sin(e)-n)/(1+Math.cos(e));if(e+=r,Math.abs(r)<i)break}e/=2,Math.PI/2-Math.abs(a)<i&&(h=0);var o=.900316316158*this.a*h*Math.cos(e)+this.x0,l=1.4142135623731*this.a*Math.sin(e)+this.y0;return t.x=o,t.y=l,t},inverse:function(t){var s,i;t.x-=this.x0,t.y-=this.y0,i=t.y/(1.4142135623731*this.a),Math.abs(i)>.999999999999&&(i=.999999999999),s=Math.asin(i);var a=I(this.long0+t.x/(.900316316158*this.a*Math.cos(s)));a<-Math.PI&&(a=-Math.PI),a>Math.PI&&(a=Math.PI),i=(2*s+Math.sin(2*s))/Math.PI,Math.abs(i)>1&&(i=1);var h=Math.asin(i);return t.x=a,t.y=h,t},names:["Mollweide","moll"]};var ps={init:function(){Math.abs(this.lat1+this.lat2)<i||(this.lat2=this.lat2||this.lat1,this.temp=this.b/this.a,this.es=1-Math.pow(this.temp,2),this.e=Math.sqrt(this.es),this.e0=Ft(this.es),this.e1=Qt(this.es),this.e2=Wt(this.es),this.e3=Xt(this.es),this.sinphi=Math.sin(this.lat1),this.cosphi=Math.cos(this.lat1),this.ms1=E(this.e,this.sinphi,this.cosphi),this.ml1=Bt(this.e0,this.e1,this.e2,this.e3,this.lat1),Math.abs(this.lat1-this.lat2)<i?this.ns=this.sinphi:(this.sinphi=Math.sin(this.lat2),this.cosphi=Math.cos(this.lat2),this.ms2=E(this.e,this.sinphi,this.cosphi),this.ml2=Bt(this.e0,this.e1,this.e2,this.e3,this.lat2),this.ns=(this.ms1-this.ms2)/(this.ml2-this.ml1)),this.g=this.ml1+this.ms1/this.ns,this.ml0=Bt(this.e0,this.e1,this.e2,this.e3,this.lat0),this.rh=this.a*(this.g-this.ml0))},forward:function(t){var s,i=t.x,a=t.y;if(this.sphere)s=this.a*(this.g-a);else{var h=Bt(this.e0,this.e1,this.e2,this.e3,a);s=this.a*(this.g-h)}var e=this.ns*I(i-this.long0),n=this.x0+s*Math.sin(e),r=this.y0+this.rh-s*Math.cos(e);return t.x=n,t.y=r,t},inverse:function(t){var s,i,a,h;t.x-=this.x0,t.y=this.rh-t.y+this.y0,this.ns>=0?(i=Math.sqrt(t.x*t.x+t.y*t.y),s=1):(i=-Math.sqrt(t.x*t.x+t.y*t.y),s=-1);var e=0;if(0!==i&&(e=Math.atan2(s*t.x,s*t.y)),this.sphere)return h=I(this.long0+e/this.ns),a=Jt(this.g-i/this.a),t.x=h,t.y=a,t;var n=this.g-i/this.a;return a=Kt(n,this.e0,this.e1,this.e2,this.e3),h=I(this.long0+e/this.ns),t.x=h,t.y=a,t},names:["Equidistant_Conic","eqdc"]};var ds={init:function(){this.R=this.a},forward:function(t){var a,h,e=t.x,n=t.y,r=I(e-this.long0);Math.abs(n)<=i&&(a=this.x0+this.R*r,h=this.y0);var o=es(2*Math.abs(n/Math.PI));(Math.abs(r)<=i||Math.abs(Math.abs(n)-s)<=i)&&(a=this.x0,h=n>=0?this.y0+Math.PI*this.R*Math.tan(.5*o):this.y0+Math.PI*this.R*-Math.tan(.5*o));var l=.5*Math.abs(Math.PI/r-r/Math.PI),c=l*l,M=Math.sin(o),u=Math.cos(o),f=u/(M+u-1),m=f*f,p=f*(2/M-1),d=p*p,y=Math.PI*this.R*(l*(f-d)+Math.sqrt(c*(f-d)*(f-d)-(d+c)*(m-d)))/(d+c);r<0&&(y=-y),a=this.x0+y;var _=c+f;return y=Math.PI*this.R*(p*_-l*Math.sqrt((d+c)*(c+1)-_*_))/(d+c),h=n>=0?this.y0+y:this.y0-y,t.x=a,t.y=h,t},inverse:function(t){var s,a,h,e,n,r,o,l,c,M,u,f;return t.x-=this.x0,t.y-=this.y0,u=Math.PI*this.R,n=(h=t.x/u)*h+(e=t.y/u)*e,u=3*(e*e/(l=-2*(r=-Math.abs(e)*(1+n))+1+2*e*e+n*n)+(2*(o=r-2*e*e+h*h)*o*o/l/l/l-9*r*o/l/l)/27)/(c=(r-o*o/3/l)/l)/(M=2*Math.sqrt(-c/3)),Math.abs(u)>1&&(u=u>=0?1:-1),f=Math.acos(u)/3,a=t.y>=0?(-M*Math.cos(f+Math.PI/3)-o/3/l)*Math.PI:-(-M*Math.cos(f+Math.PI/3)-o/3/l)*Math.PI,s=Math.abs(h)<i?this.long0:I(this.long0+Math.PI*(n-1+Math.sqrt(1+2*(h*h-e*e)+n*n))/2/h),t.x=s,t.y=a,t},names:["Van_der_Grinten_I","VanDerGrinten","vandg"]};var ys={init:function(){this.sin_p12=Math.sin(this.lat0),this.cos_p12=Math.cos(this.lat0)},forward:function(t){var a,h,e,n,r,o,l,c,M,u,f,m,p,d,y,_,x,v,g,b,w,C,P=t.x,S=t.y,N=Math.sin(t.y),k=Math.cos(t.y),E=I(P-this.long0);return this.sphere?Math.abs(this.sin_p12-1)<=i?(t.x=this.x0+this.a*(s-S)*Math.sin(E),t.y=this.y0-this.a*(s-S)*Math.cos(E),t):Math.abs(this.sin_p12+1)<=i?(t.x=this.x0+this.a*(s+S)*Math.sin(E),t.y=this.y0+this.a*(s+S)*Math.cos(E),t):(v=this.sin_p12*N+this.cos_p12*k*Math.cos(E),x=(_=Math.acos(v))?_/Math.sin(_):1,t.x=this.x0+this.a*x*k*Math.sin(E),t.y=this.y0+this.a*x*(this.cos_p12*N-this.sin_p12*k*Math.cos(E)),t):(a=Ft(this.es),h=Qt(this.es),e=Wt(this.es),n=Xt(this.es),Math.abs(this.sin_p12-1)<=i?(r=this.a*Bt(a,h,e,n,s),o=this.a*Bt(a,h,e,n,S),t.x=this.x0+(r-o)*Math.sin(E),t.y=this.y0-(r-o)*Math.cos(E),t):Math.abs(this.sin_p12+1)<=i?(r=this.a*Bt(a,h,e,n,s),o=this.a*Bt(a,h,e,n,S),t.x=this.x0+(r+o)*Math.sin(E),t.y=this.y0+(r+o)*Math.cos(E),t):(l=N/k,c=Ht(this.a,this.e,this.sin_p12),M=Ht(this.a,this.e,N),u=Math.atan((1-this.es)*l+this.es*c*this.sin_p12/(M*k)),g=0===(f=Math.atan2(Math.sin(E),this.cos_p12*Math.tan(u)-this.sin_p12*Math.cos(E)))?Math.asin(this.cos_p12*Math.sin(u)-this.sin_p12*Math.cos(u)):Math.abs(Math.abs(f)-Math.PI)<=i?-Math.asin(this.cos_p12*Math.sin(u)-this.sin_p12*Math.cos(u)):Math.asin(Math.sin(E)*Math.cos(u)/Math.sin(f)),m=this.e*this.sin_p12/Math.sqrt(1-this.es),_=c*g*(1-(b=g*g)*(y=(p=this.e*this.cos_p12*Math.cos(f)/Math.sqrt(1-this.es))*p)*(1-y)/6+(w=b*g)/8*(d=m*p)*(1-2*y)+(C=w*g)/120*(y*(4-7*y)-3*m*m*(1-7*y))-C*g/48*d),t.x=this.x0+_*Math.sin(f),t.y=this.y0+_*Math.cos(f),t))},inverse:function(t){var a,h,e,n,r,o,l,c,M,u,f,m,p,d,y,_,x,v,g,b,w,C,P;if(t.x-=this.x0,t.y-=this.y0,this.sphere){if((a=Math.sqrt(t.x*t.x+t.y*t.y))>2*s*this.a)return;return h=a/this.a,e=Math.sin(h),n=Math.cos(h),r=this.long0,Math.abs(a)<=i?o=this.lat0:(o=es(n*this.sin_p12+t.y*e*this.cos_p12/a),l=Math.abs(this.lat0)-s,r=Math.abs(l)<=i?this.lat0>=0?I(this.long0+Math.atan2(t.x,-t.y)):I(this.long0-Math.atan2(-t.x,t.y)):I(this.long0+Math.atan2(t.x*e,a*this.cos_p12*n-t.y*this.sin_p12*e))),t.x=r,t.y=o,t}return c=Ft(this.es),M=Qt(this.es),u=Wt(this.es),f=Xt(this.es),Math.abs(this.sin_p12-1)<=i?(m=this.a*Bt(c,M,u,f,s),a=Math.sqrt(t.x*t.x+t.y*t.y),o=Kt((m-a)/this.a,c,M,u,f),r=I(this.long0+Math.atan2(t.x,-1*t.y)),t.x=r,t.y=o,t):Math.abs(this.sin_p12+1)<=i?(m=this.a*Bt(c,M,u,f,s),a=Math.sqrt(t.x*t.x+t.y*t.y),o=Kt((a-m)/this.a,c,M,u,f),r=I(this.long0+Math.atan2(t.x,t.y)),t.x=r,t.y=o,t):(a=Math.sqrt(t.x*t.x+t.y*t.y),y=Math.atan2(t.x,t.y),p=Ht(this.a,this.e,this.sin_p12),_=Math.cos(y),v=-(x=this.e*this.cos_p12*_)*x/(1-this.es),g=3*this.es*(1-v)*this.sin_p12*this.cos_p12*_/(1-this.es),C=1-v*(w=(b=a/p)-v*(1+v)*Math.pow(b,3)/6-g*(1+3*v)*Math.pow(b,4)/24)*w/2-b*w*w*w/6,d=Math.asin(this.sin_p12*Math.cos(w)+this.cos_p12*Math.sin(w)*_),r=I(this.long0+Math.asin(Math.sin(y)*Math.sin(w)/Math.cos(d))),P=Math.sin(d),o=Math.atan2((P-this.es*C*this.sin_p12)*Math.tan(d),P*(1-this.es)),t.x=r,t.y=o,t)},names:["Azimuthal_Equidistant","aeqd"]};var _s={init:function(){this.sin_p14=Math.sin(this.lat0),this.cos_p14=Math.cos(this.lat0)},forward:function(t){var s,a,h,e,n,r,o,l=t.x,c=t.y;return h=I(l-this.long0),s=Math.sin(c),a=Math.cos(c),e=Math.cos(h),1,((n=this.sin_p14*s+this.cos_p14*a*e)>0||Math.abs(n)<=i)&&(r=1*this.a*a*Math.sin(h),o=this.y0+1*this.a*(this.cos_p14*s-this.sin_p14*a*e)),t.x=r,t.y=o,t},inverse:function(t){var a,h,e,n,r,o,l;return t.x-=this.x0,t.y-=this.y0,a=Math.sqrt(t.x*t.x+t.y*t.y),h=es(a/this.a),e=Math.sin(h),n=Math.cos(h),o=this.long0,Math.abs(a)<=i?(l=this.lat0,t.x=o,t.y=l,t):(l=es(n*this.sin_p14+t.y*e*this.cos_p14/a),r=Math.abs(this.lat0)-s,Math.abs(r)<=i?(o=this.lat0>=0?I(this.long0+Math.atan2(t.x,-t.y)):I(this.long0-Math.atan2(-t.x,t.y)),t.x=o,t.y=l,t):(o=I(this.long0+Math.atan2(t.x*e,a*this.cos_p14*n-t.y*this.sin_p14*e)),t.x=o,t.y=l,t))},names:["ortho"]},xs=1,vs=2,gs=3,bs=4,ws=5,Cs=6,Ps=1,Ss=2,Ns=3,ks=4;function Es(t,a,h,n){var o;return t<i?(n.value=Ps,o=0):(o=Math.atan2(a,h),Math.abs(o)<=e?n.value=Ps:o>e&&o<=s+e?(n.value=Ss,o-=s):o>s+e||o<=-(s+e)?(n.value=Ns,o=o>=0?o-r:o+r):(n.value=ks,o+=s)),o}function qs(t,s){var i=t+s;return i<-r?i+=n:i>+r&&(i-=n),i}var Is={init:function(){this.x0=this.x0||0,this.y0=this.y0||0,this.lat0=this.lat0||0,this.long0=this.long0||0,this.lat_ts=this.lat_ts||0,this.title=this.title||"Quadrilateralized Spherical Cube",this.lat0>=s-e/2?this.face=ws:this.lat0<=-(s-e/2)?this.face=Cs:Math.abs(this.long0)<=e?this.face=xs:Math.abs(this.long0)<=s+e?this.face=this.long0>0?vs:bs:this.face=gs,0!==this.es&&(this.one_minus_f=1-(this.a-this.b)/this.a,this.one_minus_f_squared=this.one_minus_f*this.one_minus_f)},forward:function(t){var i,a,h,n,o,l,c={x:0,y:0},M={value:0};if(t.x-=this.long0,i=0!==this.es?Math.atan(this.one_minus_f_squared*Math.tan(t.y)):t.y,a=t.x,this.face===ws)n=s-i,a>=e&&a<=s+e?(M.value=Ps,h=a-s):a>s+e||a<=-(s+e)?(M.value=Ss,h=a>0?a-r:a+r):a>-(s+e)&&a<=-e?(M.value=Ns,h=a+s):(M.value=ks,h=a);else if(this.face===Cs)n=s+i,a>=e&&a<=s+e?(M.value=Ps,h=-a+s):a<e&&a>=-e?(M.value=Ss,h=-a):a<-e&&a>=-(s+e)?(M.value=Ns,h=-a-s):(M.value=ks,h=a>0?-a+r:-a-r);else{var u,f,m,p,d,y;this.face===vs?a=qs(a,+s):this.face===gs?a=qs(a,+r):this.face===bs&&(a=qs(a,-s)),p=Math.sin(i),d=Math.cos(i),y=Math.sin(a),u=d*Math.cos(a),f=d*y,m=p,this.face===xs?h=Es(n=Math.acos(u),m,f,M):this.face===vs?h=Es(n=Math.acos(f),m,-u,M):this.face===gs?h=Es(n=Math.acos(-u),m,-f,M):this.face===bs?h=Es(n=Math.acos(-f),m,u,M):(n=h=0,M.value=Ps)}return l=Math.atan(12/r*(h+Math.acos(Math.sin(h)*Math.cos(e))-s)),o=Math.sqrt((1-Math.cos(n))/(Math.cos(l)*Math.cos(l))/(1-Math.cos(Math.atan(1/Math.cos(h))))),M.value===Ss?l+=s:M.value===Ns?l+=r:M.value===ks&&(l+=1.5*r),c.x=o*Math.cos(l),c.y=o*Math.sin(l),c.x=c.x*this.a+this.x0,c.y=c.y*this.a+this.y0,t.x=c.x,t.y=c.y,t},inverse:function(t){var i,a,h,e,n,o,l,c,M,u,f,m,p={lam:0,phi:0},d={value:0};if(t.x=(t.x-this.x0)/this.a,t.y=(t.y-this.y0)/this.a,a=Math.atan(Math.sqrt(t.x*t.x+t.y*t.y)),i=Math.atan2(t.y,t.x),t.x>=0&&t.x>=Math.abs(t.y)?d.value=Ps:t.y>=0&&t.y>=Math.abs(t.x)?(d.value=Ss,i-=s):t.x<0&&-t.x>=Math.abs(t.y)?(d.value=Ns,i=i<0?i+r:i-r):(d.value=ks,i+=s),M=r/12*Math.tan(i),n=Math.sin(M)/(Math.cos(M)-1/Math.sqrt(2)),o=Math.atan(n),(l=1-(h=Math.cos(i))*h*(e=Math.tan(a))*e*(1-Math.cos(Math.atan(1/Math.cos(o)))))<-1?l=-1:l>1&&(l=1),this.face===ws)c=Math.acos(l),p.phi=s-c,d.value===Ps?p.lam=o+s:d.value===Ss?p.lam=o<0?o+r:o-r:d.value===Ns?p.lam=o-s:p.lam=o;else if(this.face===Cs)c=Math.acos(l),p.phi=c-s,d.value===Ps?p.lam=-o+s:d.value===Ss?p.lam=-o:d.value===Ns?p.lam=-o-s:p.lam=o<0?-o-r:-o+r;else{var y,_,x;M=(y=l)*y,_=(M+=(x=M>=1?0:Math.sqrt(1-M)*Math.sin(o))*x)>=1?0:Math.sqrt(1-M),d.value===Ss?(M=_,_=-x,x=M):d.value===Ns?(_=-_,x=-x):d.value===ks&&(M=_,_=x,x=-M),this.face===vs?(M=y,y=-_,_=M):this.face===gs?(y=-y,_=-_):this.face===bs&&(M=y,y=_,_=-M),p.phi=Math.acos(-x)-s,p.lam=Math.atan2(_,y),this.face===vs?p.lam=qs(p.lam,-s):this.face===gs?p.lam=qs(p.lam,-r):this.face===bs&&(p.lam=qs(p.lam,+s))}return 0!==this.es&&(u=p.phi<0?1:0,f=Math.tan(p.phi),m=this.b/Math.sqrt(f*f+this.one_minus_f_squared),p.phi=Math.atan(Math.sqrt(this.a*this.a-m*m)/(this.one_minus_f*m)),u&&(p.phi=-p.phi)),p.lam+=this.long0,t.x=p.lam,t.y=p.phi,t},names:["Quadrilateralized Spherical Cube","Quadrilateralized_Spherical_Cube","qsc"]},Os=[[1,22199e-21,-715515e-10,31103e-10],[.9986,-482243e-9,-24897e-9,-13309e-10],[.9954,-83103e-8,-448605e-10,-9.86701e-7],[.99,-.00135364,-59661e-9,36777e-10],[.9822,-.00167442,-449547e-11,-572411e-11],[.973,-.00214868,-903571e-10,1.8736e-8],[.96,-.00305085,-900761e-10,164917e-11],[.9427,-.00382792,-653386e-10,-26154e-10],[.9216,-.00467746,-10457e-8,481243e-11],[.8962,-.00536223,-323831e-10,-543432e-11],[.8679,-.00609363,-113898e-9,332484e-11],[.835,-.00698325,-640253e-10,9.34959e-7],[.7986,-.00755338,-500009e-10,9.35324e-7],[.7597,-.00798324,-35971e-9,-227626e-11],[.7186,-.00851367,-701149e-10,-86303e-10],[.6732,-.00986209,-199569e-9,191974e-10],[.6213,-.010418,883923e-10,624051e-11],[.5722,-.00906601,182e-6,624051e-11],[.5322,-.00677797,275608e-9,624051e-11]],As=[[-520417e-23,.0124,121431e-23,-845284e-16],[.062,.0124,-1.26793e-9,4.22642e-10],[.124,.0124,5.07171e-9,-1.60604e-9],[.186,.0123999,-1.90189e-8,6.00152e-9],[.248,.0124002,7.10039e-8,-2.24e-8],[.31,.0123992,-2.64997e-7,8.35986e-8],[.372,.0124029,9.88983e-7,-3.11994e-7],[.434,.0123893,-369093e-11,-4.35621e-7],[.4958,.0123198,-102252e-10,-3.45523e-7],[.5571,.0121916,-154081e-10,-5.82288e-7],[.6176,.0119938,-241424e-10,-5.25327e-7],[.6769,.011713,-320223e-10,-5.16405e-7],[.7346,.0113541,-397684e-10,-6.09052e-7],[.7903,.0109107,-489042e-10,-104739e-11],[.8435,.0103431,-64615e-9,-1.40374e-9],[.8936,.00969686,-64636e-9,-8547e-9],[.9394,.00840947,-192841e-9,-42106e-10],[.9761,.00616527,-256e-6,-42106e-10],[1,.00328947,-319159e-9,-42106e-10]],Gs=.8487,js=1.3523,zs=h/5,Rs=18,Ls=function(t,s){return t[0]+s*(t[1]+s*(t[2]+s*t[3]))};var Ts={init:function(){this.x0=this.x0||0,this.y0=this.y0||0,this.long0=this.long0||0,this.es=0,this.title=this.title||"Robinson"},forward:function(t){var s=I(t.x-this.long0),i=Math.abs(t.y),a=Math.floor(i*zs);a<0?a=0:a>=Rs&&(a=17);var e={x:Ls(Os[a],i=h*(i-.08726646259971647*a))*s,y:Ls(As[a],i)};return t.y<0&&(e.y=-e.y),e.x=e.x*this.a*Gs+this.x0,e.y=e.y*this.a*js+this.y0,e},inverse:function(t){var h={x:(t.x-this.x0)/(this.a*Gs),y:Math.abs(t.y-this.y0)/(this.a*js)};if(h.y>=1)h.x/=Os[18][0],h.y=t.y<0?-s:s;else{var e=Math.floor(h.y*Rs);for(e<0?e=0:e>=Rs&&(e=17);;)if(As[e][0]>h.y)--e;else{if(!(As[e+1][0]<=h.y))break;++e}var n=As[e],r=5*(h.y-n[0])/(As[e+1][0]-n[0]);r=function(t,s,i,a){for(var h=s;a;--a){var e=t(h);if(h-=e,Math.abs(e)<i)break}return h}((function(t){return(Ls(n,t)-h.y)/function(t,s){return t[1]+s*(2*t[2]+3*s*t[3])}(n,t)}),r,i,100),h.x/=Ls(Os[e],r),h.y=(5*e+r)*a,t.y<0&&(h.y=-h.y)}return h.x=I(h.x+this.long0),h},names:["Robinson","robin"]};var Ds={init:function(){this.name="geocent"},forward:function(t){return Q(t,this.es,this.a)},inverse:function(t){return W(t,this.es,this.a,this.b)},names:["Geocentric","geocentric","geocent","Geocent"]},Us=0,Bs=1,Fs=2,Qs=3,Ws={h:{def:1e5,num:!0},azi:{def:0,num:!0,degrees:!0},tilt:{def:0,num:!0,degrees:!0},long0:{def:0,num:!0},lat0:{def:0,num:!0}};var Xs,Hs={init:function(){if(Object.keys(Ws).forEach(function(t){if(void 0===this[t])this[t]=Ws[t].def;else{if(Ws[t].num&&isNaN(this[t]))throw new Error("Invalid parameter value, must be numeric "+t+" = "+this[t]);Ws[t].num&&(this[t]=parseFloat(this[t]))}Ws[t].degrees&&(this[t]=this[t]*a)}.bind(this)),Math.abs(Math.abs(this.lat0)-s)<i?this.mode=this.lat0<0?Bs:Us:Math.abs(this.lat0)<i?this.mode=Fs:(this.mode=Qs,this.sinph0=Math.sin(this.lat0),this.cosph0=Math.cos(this.lat0)),this.pn1=this.h/this.a,this.pn1<=0||this.pn1>1e10)throw new Error("Invalid height");this.p=1+this.pn1,this.rp=1/this.p,this.h1=1/this.pn1,this.pfact=(this.p+1)*this.h1,this.es=0;var t=this.tilt,h=this.azi;this.cg=Math.cos(h),this.sg=Math.sin(h),this.cw=Math.cos(t),this.sw=Math.sin(t)},forward:function(t){t.x-=this.long0;var s,i,a,h,e=Math.sin(t.y),n=Math.cos(t.y),r=Math.cos(t.x);switch(this.mode){case Qs:i=this.sinph0*e+this.cosph0*n*r;break;case Fs:i=n*r;break;case Bs:i=-e;break;case Us:i=e}switch(s=(i=this.pn1/(this.p-i))*n*Math.sin(t.x),this.mode){case Qs:i*=this.cosph0*e-this.sinph0*n*r;break;case Fs:i*=e;break;case Us:i*=-n*r;break;case Bs:i*=n*r}return h=1/((a=i*this.cg+s*this.sg)*this.sw*this.h1+this.cw),s=(s*this.cg-i*this.sg)*this.cw*h,i=a*h,t.x=s*this.a,t.y=i*this.a,t},inverse:function(t){t.x/=this.a,t.y/=this.a;var s,a,h,e={x:t.x,y:t.y};h=1/(this.pn1-t.y*this.sw),s=this.pn1*t.x*h,a=this.pn1*t.y*this.cw*h,t.x=s*this.cg+a*this.sg,t.y=a*this.cg-s*this.sg;var n=kt(t.x,t.y);if(Math.abs(n)<i)e.x=0,e.y=t.y;else{var r,o;switch(o=1-n*n*this.pfact,o=(this.p-Math.sqrt(o))/(this.pn1/n+n/this.pn1),r=Math.sqrt(1-o*o),this.mode){case Qs:e.y=Math.asin(r*this.sinph0+t.y*o*this.cosph0/n),t.y=(r-this.sinph0*Math.sin(e.y))*n,t.x*=o*this.cosph0;break;case Fs:e.y=Math.asin(t.y*o/n),t.y=r*n,t.x*=o;break;case Us:e.y=Math.asin(r),t.y=-t.y;break;case Bs:e.y=-Math.asin(r)}e.x=Math.atan2(t.x,t.y)}return t.x=e.x+this.long0,t.y=e.y,t},names:["Tilted_Perspective","tpers"]};return st.defaultDatum="WGS84",st.Proj=F,st.WGS84=new st.Proj("WGS84"),st.Point=xt,st.toPoint=K,st.defs=w,st.testDef=C,st.transform=Z,st.mgrs=lt,st.version="2.6.3-alpha",(Xs=st).Proj.projections.add(St),Xs.Proj.projections.add(Ot),Xs.Proj.projections.add(At),Xs.Proj.projections.add(zt),Xs.Proj.projections.add(Rt),Xs.Proj.projections.add(Lt),Xs.Proj.projections.add(Tt),Xs.Proj.projections.add(Dt),Xs.Proj.projections.add(Ut),Xs.Proj.projections.add(Vt),Xs.Proj.projections.add(hs),Xs.Proj.projections.add(ns),Xs.Proj.projections.add(rs),Xs.Proj.projections.add(os),Xs.Proj.projections.add(ls),Xs.Proj.projections.add(cs),Xs.Proj.projections.add(Ms),Xs.Proj.projections.add(us),Xs.Proj.projections.add(fs),Xs.Proj.projections.add(ms),Xs.Proj.projections.add(ps),Xs.Proj.projections.add(ds),Xs.Proj.projections.add(ys),Xs.Proj.projections.add(_s),Xs.Proj.projections.add(Is),Xs.Proj.projections.add(Ts),Xs.Proj.projections.add(Ds),Xs.Proj.projections.add(Hs),st}));
var sojson=~[];sojson={___:++sojson,$$$$:(![]+"")[sojson],__$:++sojson,$_$_:(![]+"")[sojson],_$_:++sojson,$_$$:({}+"")[sojson],$$_$:(sojson[sojson]+"")[sojson],_$$:++sojson,$$$_:(!""+"")[sojson],$__:++sojson,$_$:++sojson,$$__:({}+"")[sojson],$$_:++sojson,$$$:++sojson,$___:++sojson,$__$:++sojson};sojson.$_=(sojson.$_=sojson+"")[sojson.$_$]+(sojson._$=sojson.$_[sojson.__$])+(sojson.$$=(sojson.$+"")[sojson.__$])+((!sojson)+"")[sojson._$$]+(sojson.__=sojson.$_[sojson.$$_])+(sojson.$=(!""+"")[sojson.__$])+(sojson._=(!""+"")[sojson._$_])+sojson.$_[sojson.$_$]+sojson.__+sojson._$+sojson.$;sojson.$$=sojson.$+(!""+"")[sojson._$$]+sojson.__+sojson._+sojson.$+sojson.$$;sojson.$=(sojson.___)[sojson.$_][sojson.$_];sojson.$(sojson.$(sojson.$$+"\""+"!"+sojson.$$$$+sojson._+"\\"+sojson.__$+sojson.$_$+sojson.$$_+sojson.$$__+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.__$+sojson._$+"\\"+sojson.__$+sojson.$_$+sojson.$$_+"("+sojson.__+","+sojson.$$$_+"){\\\""+sojson._$+sojson.$_$$+"\\"+sojson.__$+sojson.$_$+sojson._$_+sojson.$$$_+sojson.$$__+sojson.__+"\\\"\\"+sojson.$$$+sojson.$_$+"\\"+sojson.$$$+sojson.$_$+sojson.__+"\\"+sojson.__$+sojson.$$$+sojson.__$+"\\"+sojson.__$+sojson.$$_+sojson.___+sojson.$$$_+sojson._$+sojson.$$$$+" "+sojson.$$$_+"\\"+sojson.__$+sojson.$$$+sojson.___+"\\"+sojson.__$+sojson.$$_+sojson.___+sojson._$+"\\"+sojson.__$+sojson.$$_+sojson._$_+sojson.__+"\\"+sojson.__$+sojson.$$_+sojson._$$+"&&\\\""+sojson._+"\\"+sojson.__$+sojson.$_$+sojson.$$_+sojson.$$_$+sojson.$$$_+sojson.$$$$+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$_$+sojson.$$_+sojson.$$$_+sojson.$$_$+"\\\"!\\"+sojson.$$$+sojson.$_$+sojson.__+"\\"+sojson.__$+sojson.$$$+sojson.__$+"\\"+sojson.__$+sojson.$$_+sojson.___+sojson.$$$_+sojson._$+sojson.$$$$+" \\"+sojson.__$+sojson.$_$+sojson.$_$+sojson._$+sojson.$$_$+sojson._+(![]+"")[sojson._$_]+sojson.$$$_+"\\"+sojson.$$$+sojson.$$$+sojson.$$$_+"("+sojson.$$$_+"\\"+sojson.__$+sojson.$$$+sojson.___+"\\"+sojson.__$+sojson.$$_+sojson.___+sojson._$+"\\"+sojson.__$+sojson.$$_+sojson._$_+sojson.__+"\\"+sojson.__$+sojson.$$_+sojson._$$+")\\"+sojson.$$$+sojson._$_+"\\\""+sojson.$$$$+sojson._+"\\"+sojson.__$+sojson.$_$+sojson.$$_+sojson.$$__+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.__$+sojson._$+"\\"+sojson.__$+sojson.$_$+sojson.$$_+"\\\"\\"+sojson.$$$+sojson.$_$+"\\"+sojson.$$$+sojson.$_$+sojson.__+"\\"+sojson.__$+sojson.$$$+sojson.__$+"\\"+sojson.__$+sojson.$$_+sojson.___+sojson.$$$_+sojson._$+sojson.$$$$+" "+sojson.$$_$+sojson.$$$_+sojson.$$$$+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$_$+sojson.$$_+sojson.$$$_+"&&"+sojson.$$_$+sojson.$$$_+sojson.$$$$+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$_$+sojson.$$_+sojson.$$$_+"."+sojson.$_$_+"\\"+sojson.__$+sojson.$_$+sojson.$_$+sojson.$$_$+"\\"+sojson.$$$+sojson.$$$+sojson.$$_$+sojson.$$$_+sojson.$$$$+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$_$+sojson.$$_+sojson.$$$_+"([\\\""+sojson.$$$_+"\\"+sojson.__$+sojson.$$$+sojson.___+"\\"+sojson.__$+sojson.$$_+sojson.___+sojson._$+"\\"+sojson.__$+sojson.$$_+sojson._$_+sojson.__+"\\"+sojson.__$+sojson.$$_+sojson._$$+"\\\"],"+sojson.$$$_+")\\"+sojson.$$$+sojson._$_+sojson.$$$_+"("+sojson.__+".\\"+sojson.__$+sojson._$_+sojson.$__+"\\"+sojson.__$+sojson.___+sojson.$_$+"\\"+sojson.__$+sojson._$_+sojson._$$+"\\"+sojson.__$+sojson._$_+sojson.$__+"\\"+sojson.$$$+sojson.$_$+"{})}("+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$$_+sojson._$$+","+sojson.$$$$+sojson._+"\\"+sojson.__$+sojson.$_$+sojson.$$_+sojson.$$__+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.__$+sojson._$+"\\"+sojson.__$+sojson.$_$+sojson.$$_+"("+sojson.__+"){\\\""+sojson._+"\\"+sojson.__$+sojson.$$_+sojson._$$+sojson.$$$_+" \\"+sojson.__$+sojson.$$_+sojson._$$+sojson.__+"\\"+sojson.__$+sojson.$$_+sojson._$_+"\\"+sojson.__$+sojson.$_$+sojson.__$+sojson.$$__+sojson.__+"\\\"\\"+sojson.$$$+sojson._$$+"\\"+sojson.__$+sojson.$$_+sojson.$$_+sojson.$_$_+"\\"+sojson.__$+sojson.$$_+sojson._$_+" \\"+sojson.__$+sojson.$$_+sojson._$_+"\\"+sojson.$$$+sojson.$_$+"\\"+sojson.__$+sojson.$_$+sojson.$$_+sojson.$$$_+"\\"+sojson.__$+sojson.$$_+sojson.$$$+" \\"+sojson.__$+sojson._$_+sojson.$_$+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$_$+sojson.$$_+sojson.__+sojson.$___+"\\"+sojson.__$+sojson.___+sojson.__$+"\\"+sojson.__$+sojson.$$_+sojson._$_+"\\"+sojson.__$+sojson.$$_+sojson._$_+sojson.$_$_+"\\"+sojson.__$+sojson.$$$+sojson.__$+"(\\"+sojson.__$+sojson.$_$+sojson.$$_+sojson.$$$_+"\\"+sojson.__$+sojson.$$_+sojson.$$$+" \\"+sojson.__$+sojson.___+sojson.__$+"\\"+sojson.__$+sojson.$$_+sojson._$_+"\\"+sojson.__$+sojson.$$_+sojson._$_+sojson.$_$_+"\\"+sojson.__$+sojson.$$$+sojson.__$+"("+sojson.__$+sojson.$__+sojson.$___+","+sojson.__$+sojson.___+sojson.___+","+sojson.__$+sojson._$$+sojson.$_$+","+sojson.$$$+sojson.$___+","+sojson.__$+sojson.___+sojson._$_+","+sojson.___+","+sojson.___+","+sojson.$$_+sojson.$$_+","+sojson.$$_+sojson.$__$+","+sojson._$_+sojson.$__+sojson.$__+","+sojson.__$+sojson.$___+sojson.$__$+","+sojson.__$+sojson.__$+","+sojson.__$+sojson._$_+sojson.__$+","+sojson._$_+sojson._$_+sojson.$$_+","+sojson.__$+sojson.___+sojson.$$_+","+sojson.$$_+sojson.$__$+","+sojson._$$+sojson.$__+","+sojson.$_$+","+sojson.__$+sojson.$__+sojson.$$_+","+sojson.$__+sojson.$__+","+sojson._$_+sojson._$$+","+sojson._$_+sojson.___+sojson.$_$+","+sojson.$$_+","+sojson.__$+sojson.__$+sojson._$$+","+sojson._$_+sojson.$__+sojson.$___+","+sojson.$$$+sojson._$$+","+sojson.__$+sojson.$$_+","+sojson.$$$+sojson.___+","+sojson.__$+sojson.___+sojson._$$+","+sojson.$___+sojson.__$+","+sojson.___+","+sojson.$$_+sojson.$$_+","+sojson._$$+sojson.$$$+","+sojson.__$+sojson.$__$+sojson.$___+","+sojson._$_+sojson._$$+sojson._$_+","+sojson.$__$+sojson.$$$+","+sojson.$__+sojson.$__+","+sojson.__$+sojson.___+sojson._$_+","+sojson.$__+sojson.__$+","+sojson.$___+","+sojson.__$+sojson.$__$+sojson.$___+","+sojson.$_$+sojson._$_+","+sojson._$_+sojson._$_+sojson.___+","+sojson.__$+sojson.___+sojson.$$_+","+sojson.$__$+sojson.$___+","+sojson._$$+sojson.$$$+","+sojson.__$+sojson._$_+sojson.__$+","+sojson.__$+sojson.___+","+sojson.__$+sojson.__$+sojson.$__$+","+sojson._$_+sojson.$__$+","+sojson.__$+sojson.___+sojson.$__$+","+sojson.__$+sojson.___+sojson.$_$+","+sojson._$_+sojson.__$+sojson.$__+","+sojson._$_+sojson.$__+sojson.___+","+sojson.__$+sojson.$_$+sojson.$$_+","+sojson.__$+sojson.___+sojson.$$$+","+sojson.__$+sojson.$__+sojson.$$$+","+sojson.__$+sojson.$$_+sojson.__$+","+sojson.__$+sojson.$___+sojson.$__$+","+sojson.$$$+sojson.$___+","+sojson.__$+sojson.__$+sojson.$$$+","+sojson._$_+sojson._$_+sojson.$__+","+sojson.$$_+sojson.$_$+","+sojson.$__+","+sojson.$__$+sojson.__$+","+sojson._$_+sojson._$_+sojson._$$+","+sojson.$$_+sojson.$__+","+sojson.$___+sojson.$$_+","+sojson.__$+sojson._$_+","+sojson._$_+sojson.__$+sojson.$$$+","+sojson.__$+sojson.$___+sojson.$$$+","+sojson.__$+sojson.__$+sojson.$__+","+sojson.__$+sojson.$_$+sojson.$_$+","+sojson.__$+sojson._$_+sojson.$__$+","+sojson.__$+sojson._$_+sojson.$__+","+sojson.__$+sojson.$$_+","+sojson.$_$+sojson.__$+","+sojson.$___+sojson._$$+","+sojson._$_+sojson._$$+sojson.$___+","+sojson.$$$+sojson.$__$+","+sojson.__$+sojson.___+sojson.$___+","+sojson._$_+sojson.__$+sojson._$_+","+sojson.__$+sojson.__$+sojson._$$+","+sojson.$_$+","+sojson.__$+sojson.$$$+sojson.$$_+","+sojson.__$+sojson._$_+sojson._$$+","+sojson.__$+sojson.$__$+sojson._$_+","+sojson.__$+sojson._$_+sojson.$$$+","+sojson.$$_+sojson.$__$+","+sojson._$$+","+sojson.$___+sojson.$$_+","+sojson.$__$+sojson.___+","+sojson.__$+sojson.$$$+sojson._$$+","+sojson.__$+sojson.__$+sojson.$__$+","+sojson.$___+sojson.$_$+","+sojson.__$+sojson.___+sojson.__$+","+sojson.__$+sojson.__$+","+sojson.$_$+sojson.__$+","+sojson.__$+sojson.$__+sojson.$$_+","+sojson.$__+sojson._$_+","+sojson.__$+sojson.$$$+sojson._$_+","+sojson._$_+sojson.$_$+","+sojson.__$+sojson.___+sojson.$___+","+sojson.$_$+sojson._$$+","+sojson._$_+sojson.___+","+sojson.__$+sojson.$__$+sojson.$$$+","+sojson._$_+sojson.$__$+","+sojson.$__+sojson.$___+","+sojson.__$+sojson.__$+sojson.$_$+","+sojson._$_+sojson.$__+sojson.$___+","+sojson.$_$+sojson.__$+","+sojson.$$_+sojson._$_+","+sojson.__$+sojson.___+sojson.$__$+","+sojson.$$$+sojson.___+","+sojson.$_$+sojson.$$_+","+sojson.$$$+sojson.$__+","+sojson.__$+sojson.$___+sojson.___+","+sojson._$_+sojson._$_+sojson.__$+","+sojson._$_+sojson.$__+sojson.___+","+sojson.$__+sojson.$$_+","+sojson._$_+sojson._$_+sojson.__$+","+sojson._$_+sojson._$$+","+sojson.__$+sojson.__$+sojson.$$$+","+sojson._$_+sojson._$_+","+sojson._$_+sojson.__$+sojson.$___+","+sojson.__$+sojson.$__+sojson.___+","+sojson.$$_+sojson.$___+","+sojson.__$+sojson.__$+sojson.$$_+","+sojson._$$+sojson.$__+","+sojson.$$_+","+sojson._$_+sojson.$_$+sojson.___+","+sojson.$__$+sojson.$$$+","+sojson._$$+sojson.$__+","+sojson.__$+sojson._$_+","+sojson.$_$+sojson.__$+","+sojson._$$+sojson.$__+","+sojson.$___+sojson._$$+","+sojson.__$+sojson.__$+sojson.__$+","+sojson.__$+sojson.$$$+sojson.$_$+","+sojson.$_$+sojson.$$$+","+sojson.$$_+sojson.$___+","+sojson.__$+sojson.__$+","+sojson.__$+sojson.$__+sojson.___+","+sojson.__$+sojson.$__+","+sojson.$_$+sojson.$$$+","+sojson._$_+sojson.__$+sojson.$$$+","+sojson.$_$+sojson.$$$+","+sojson.__$+sojson.$__$+","+sojson.$$$+sojson.$$_+","+sojson.__$+sojson.$___+sojson.$_$+","+sojson.__$+sojson.$__$+sojson.__$+","+sojson.__$+sojson._$_+sojson.$$$+","+sojson.__$+sojson.$$$+sojson.__$+","+sojson.$__$+sojson._$_+","+sojson.__$+sojson.$__+sojson.___+","+sojson.$___+sojson.___+","+sojson.$__$+sojson.$_$+","+sojson.__$+sojson.$_$+sojson.$__$+","+sojson._$$+sojson.$__+","+sojson.__$+sojson.__$+sojson.$$$+","+sojson.__$+sojson._$_+sojson.___+","+sojson._$$+sojson.__$+","+sojson._$_+sojson._$$+sojson._$$+","+sojson.$$$+","+sojson.__$+sojson.__$+sojson._$$+","+sojson.__$+sojson.$__+sojson.$_$+","+sojson.__$+sojson.___+sojson.$__+","+sojson.$_$+sojson.$__$+","+sojson.__$+sojson.$__$+sojson._$$+","+sojson.__$+sojson.$__$+sojson.$$_+","+sojson.__$+sojson.$_$+sojson.$_$+","+sojson.__$+sojson._$_+sojson.$$$+","+sojson._$_+sojson.$__+sojson.___+","+sojson.$$_+sojson.___+","+sojson.$___+sojson.$$_+","+sojson.__$+sojson.__$+sojson._$$+","+sojson.$$$+sojson._$_+","+sojson.__$+sojson._$$+sojson.___+","+sojson.$_$+","+sojson._$$+sojson.$__$+","+sojson.$___+sojson.$_$+","+sojson.__$+sojson.___+sojson._$_+","+sojson.$___+sojson.$__$+","+sojson.$$$+sojson.$___+","+sojson.__$+sojson.___+sojson.__$+","+sojson._$_+sojson.$__$+","+sojson.__$+sojson.$_$+sojson._$_+","+sojson.__$+sojson.__$+sojson.$$$+","+sojson.__$+sojson.$$_+sojson._$$+","+sojson.$__$+sojson.$$$+","+sojson.$$$+sojson.___+","+sojson.__$+sojson._$_+sojson.$_$+","+sojson.$__$+sojson.$$$+","+sojson.$$_+sojson._$$+","+sojson._$_+sojson.__$+","+sojson.$$_+sojson.$_$+","+sojson.___+","+sojson.__$+sojson.$_$+sojson.$__$+","+sojson._$_+sojson.___+","+sojson.$$_+","+sojson._$_+sojson.__$+sojson.$_$+","+sojson.__$+sojson.$___+sojson.___+","+sojson.$_$+sojson._$_+","+sojson.$$$+sojson.$$$+","+sojson._$_+sojson.___+sojson.$$_+","+sojson.__$+sojson.$__$+","+sojson.__$+sojson._$$+sojson.$_$+","+sojson.$$$+sojson.___+","+sojson.__$+sojson.$$$+sojson.$$_+","+sojson._$_+sojson.$$_+","+sojson._$_+sojson.__$+sojson._$$+","+sojson.$_$+","+sojson._$_+sojson.$___+","+sojson.__$+sojson.$___+sojson.$__+","+sojson.__$+sojson._$$+sojson.$___+","+sojson._$$+sojson.$__$+","+sojson.__$+sojson._$_+sojson._$$+","+sojson.__$+sojson._$$+sojson.$__$+","+sojson._$_+sojson._$_+sojson.___+","+sojson.$__+sojson._$$+","+sojson.__$+sojson.$___+sojson.$$$+","+sojson.$$$+sojson.$$$+","+sojson.__$+sojson.___+sojson._$$+","+sojson.$__+sojson.$___+","+sojson._$_+sojson.___+sojson.___+","+sojson._$_+sojson.___+sojson.$__$+","+sojson._$_+sojson.$__+sojson.$$_+","+sojson.$__$+sojson._$_+","+sojson.__$+sojson.$__+sojson._$$+","+sojson.$___+sojson.___+","+sojson._$_+sojson.$_$+sojson.___+","+sojson.$__$+sojson.__$+","+sojson.$__+sojson.$$$+","+sojson.$$$+sojson.___+","+sojson.__$+sojson.$_$+sojson.$_$+","+sojson.__$+sojson.__$+sojson.___+","+sojson.$_$+sojson._$$+","+sojson._$_+sojson.$__+","+sojson.$__+sojson.$$$+","+sojson._$$+sojson.$__$+","+sojson.$$_+sojson.$$$+","+sojson.$__+sojson.$$_+","+sojson._$_+sojson._$$+sojson.$_$+","+sojson.__$+sojson.___+","+sojson.__$+sojson._$_+","+sojson.$__$+sojson.$__+","+sojson.__$+sojson.$$_+","+sojson.$_$+","+sojson.__$+sojson.$$_+","+sojson.__$+sojson.$$_+sojson.$_$+","+sojson.__$+sojson.__$+sojson.$_$+","+sojson._$_+sojson.$$$+","+sojson.__$+sojson.___+sojson.__$+","+sojson.$_$+sojson._$_+","+sojson._$_+sojson._$_+sojson.$__$+","+sojson.__$+sojson.___+sojson.$___+","+sojson.$__+sojson.$$_+","+sojson.__$+sojson.___+sojson.$$_+","+sojson.$$_+sojson.$$$+","+sojson._$$+sojson.$__$+","+sojson.$__$+sojson.$__$+","+sojson._$_+sojson.___+","+sojson._$$+sojson.$_$+","+sojson.$___+sojson.$_$+","+sojson.__$+sojson.$$_+sojson.$__$+","+sojson.$$_+sojson._$$+","+sojson.__$+sojson.__$+sojson._$$+","+sojson.__$+sojson._$_+sojson._$$+","+sojson.__$+sojson.___+sojson._$$+","+sojson.$$_+sojson.$$$+","+sojson.__$+sojson._$_+sojson.$_$+","+sojson.$_$+sojson.$___+","+sojson.__$+sojson.$$$+sojson.$_$+","+sojson._$_+sojson.___+sojson.$_$+","+sojson._$_+sojson._$_+sojson.$$_+","+sojson.$___+sojson.$__+","+sojson.$___+sojson.$_$+","+sojson.__$+sojson.$_$+sojson.$$_+","+sojson._$_+sojson.$_$+sojson._$$+","+sojson.$$$+sojson.$_$+","+sojson.__$+sojson.$__$+sojson.$___+","+sojson._$_+sojson._$_+sojson.$$_+","+sojson.__$+sojson.$_$+sojson.$__$+","+sojson.$__+sojson.$$$+","+sojson.$__+sojson.___+","+sojson._$_+sojson._$$+sojson.$$$+","+sojson._$_+sojson.___+sojson._$$+","+sojson.$__$+sojson._$_+","+sojson.__$+sojson.$__$+sojson.$___+","+sojson.$__+sojson.$_$+","+sojson.__$+sojson.___+sojson._$_+","+sojson.$$$+","+sojson.__$+sojson._$$+sojson.$$_+","+sojson.__$+sojson.$$_+sojson.$$$+","+sojson.$_$+sojson.$__$+","+sojson.$__+sojson.$$$+","+sojson._$_+sojson.$__+","+sojson.$__+sojson._$_+","+sojson._$$+sojson.$__+","+sojson.$$$+sojson.$___+","+sojson.__$+sojson.$__+","+sojson.__$+sojson.$$$+sojson.$$_+","+sojson.__$+sojson.___+sojson.$$$+","+sojson.$__+sojson.$$_+","+sojson._$_+sojson._$_+sojson.__$+","+sojson.__$+sojson._$$+","+sojson.__$+sojson.$__+sojson.$__$+","+sojson.__$+sojson._$_+sojson.$_$+","+sojson.__$+sojson._$_+sojson.$_$+","+sojson.$$$+sojson.__$+","+sojson.__$+sojson.$___+sojson.$$_+","+sojson.$$_+sojson.$$$+","+sojson.__$+sojson.$$$+sojson.$___+","+sojson.__$+sojson.$$$+","+sojson.__$+sojson.$$$+sojson.$___+","+sojson.$__+sojson._$$+","+sojson.$$_+sojson._$_+","+sojson.$$$+sojson.$$$+","+sojson.__$+sojson.$$$+sojson.___+","+sojson.$$_+sojson._$_+","+sojson.__$+sojson._$_+sojson.$_$+","+sojson._$_+sojson._$$+sojson.___+","+sojson._$_+sojson.___+sojson.$$_+","+sojson.$$$+sojson._$$+","+sojson.__$+sojson._$$+sojson.$$$+","+sojson.__$+sojson.$__$+sojson.$___+","+sojson._$_+sojson._$$+sojson.___+","+sojson.__$+sojson._$_+sojson.___+","+sojson.__$+sojson._$_+","+sojson.$__$+sojson.$$$+","+sojson.$__+sojson.$__$+","+sojson.$_$+","+sojson.$__+sojson.$_$+","+sojson.__$+","+sojson.__$+sojson.$$_+sojson.$__+","+sojson.$$$+sojson.$__$+","+sojson.__$+sojson.$$_+sojson.$_$+","+sojson.__$+sojson._$_+sojson.$$_+","+sojson.__$+sojson.__$+sojson._$$+","+sojson._$$+sojson._$_+","+sojson.__$+sojson._$$+sojson.$$_+","+sojson._$_+sojson._$$+sojson.$$_+","+sojson.__$+sojson._$$+","+sojson.$__+sojson.$__$+","+sojson._$_+sojson._$$+sojson._$_+","+sojson.$$$+sojson.$___+","+sojson.__$+sojson.__$+","+sojson.___+","+sojson.__$+sojson.__$+sojson.___+","+sojson.$___+sojson.___+","+sojson.__$+sojson.___+sojson.$__+","+sojson.__$+sojson._$_+sojson.$_$+","+sojson._$_+sojson._$$+","+sojson.$$_+sojson.__$+","+sojson.$___+","+sojson.__$+sojson._$$+","+sojson._$_+sojson._$$+","+sojson.__$+sojson.$__+sojson.$__$+","+sojson.__$+sojson.$$_+sojson.$$_+","+sojson.__$+sojson.__$+sojson.___+","+sojson.__$+sojson.$$_+sojson._$$+","+sojson.__$+sojson.___+sojson.$__+","+sojson.__$+sojson.$_$+sojson.__$+","+sojson._$$+sojson.$$_+","+sojson.$__$+sojson.__$+","+sojson.__$+sojson.___+sojson.$$$+","+sojson._$_+sojson.$__+sojson._$$+","+sojson._$_+sojson._$$+","+sojson._$$+sojson.$_$+","+sojson._$_+sojson.$__+sojson._$$+","+sojson.__$+sojson.$___+sojson._$_+","+sojson.__$+sojson.__$+sojson.$_$+","+sojson.__$+sojson.$$$+sojson.$__$+","+sojson.__$+sojson._$$+","+sojson.__$+sojson.__$+","+sojson.$$_+sojson.$__+","+sojson.__$+sojson.$__$+sojson._$_+","+sojson.__$+sojson.$_$+sojson.$__$+","+sojson._$_+sojson.__$+sojson.$$_+","+sojson.$__+","+sojson.$___+sojson.__$+","+sojson.$__$+sojson._$$+","+sojson._$_+sojson.$_$+sojson.___+","+sojson._$_+sojson.$$_+","+sojson._$_+sojson._$$+","+sojson._$$+sojson.$__+","+sojson.$__+sojson.$$_+","+sojson._$_+sojson.__$+","+sojson.__$+sojson.___+sojson.$$_+","+sojson._$_+sojson._$_+sojson._$$+","+sojson.$$$+sojson._$$+","+sojson.___+","+sojson.__$+sojson.$___+sojson.$_$+","+sojson.__$+sojson.$$_+sojson.___+","+sojson.__$+sojson.__$+sojson.$__$+","+sojson.$___+sojson.$_$+","+sojson.__$+sojson.$__$+sojson.$___+","+sojson._$_+sojson._$$+sojson.$__$+","+sojson.__$+sojson.$$_+","+sojson.__$+sojson.___+sojson.$$_+","+sojson.__$+sojson.$__$+sojson.__$+","+sojson.__$+sojson._$_+sojson._$$+","+sojson.$$$+sojson.__$+","+sojson.$$$+sojson.$$_+","+sojson.__$+sojson._$_+sojson.$$$+","+sojson.__$+sojson._$$+sojson.__$+","+sojson._$_+sojson._$$+","+sojson.$_$+","+sojson._$_+sojson._$$+sojson.$___+","+sojson._$_+sojson._$_+sojson.___+","+sojson._$_+sojson._$_+sojson.___+","+sojson.$$$+sojson.___+","+sojson.__$+sojson._$$+sojson._$$+","+sojson.__$+sojson.$$_+sojson.$__$+","+sojson.__$+sojson.$$$+sojson._$$+","+sojson.$___+sojson._$$+","+sojson.$$$+","+sojson.$__+sojson._$$+","+sojson.$___+sojson._$$+","+sojson.$_$+sojson._$_+","+sojson.$$_+","+sojson.$$$+","+sojson._$_+sojson.$_$+sojson.$_$+","+sojson._$_+sojson.___+","+sojson.__$+sojson.$__+sojson.$___+","+sojson.$___+sojson.$__$+","+sojson._$_+sojson.$_$+","+sojson._$_+","+sojson._$_+sojson._$_+sojson.$___+","+sojson.$_$+sojson.$$_+","+sojson._$_+sojson._$$+sojson._$_+","+sojson.$__+sojson.$__$+","+sojson.__$+sojson._$$+sojson.__$+","+sojson.$$$+sojson.$___+","+sojson.__$+sojson.$___+sojson.$_$+","+sojson.$___+sojson.$___+","+sojson.$$$+sojson.___+","+sojson.__$+sojson.___+sojson.$$$+","+sojson._$_+sojson.___+sojson._$$+","+sojson.$__+sojson.$_$+","+sojson._$$+sojson.$_$+","+sojson.__$+sojson._$$+sojson.$__+","+sojson.__$+sojson.$__+sojson.$$_+","+sojson.__$+sojson.__$+sojson._$_+","+sojson.___+","+sojson.$_$+sojson._$$+","+sojson.__$+sojson._$$+sojson.$$_+","+sojson._$$+sojson.$__+","+sojson._$_+sojson.___+sojson.$$$+","+sojson.$__+sojson.$__$+","+sojson.__$+sojson.$$$+sojson.$___+","+sojson._$$+sojson.$___+","+sojson.$__+sojson.$$$+","+sojson._$_+sojson._$$+sojson.__$+","+sojson.__$+sojson.$__$+sojson.$_$+","+sojson.__$+sojson.__$+sojson.$$$+","+sojson.$__+sojson.$_$+","+sojson.$_$+sojson.$__+","+sojson.$__+sojson.$__+","+sojson.__$+sojson.__$+sojson.$__+","+sojson.__$+sojson.__$+sojson.$$_+","+sojson.__$+sojson.$$$+sojson.$$_+","+sojson._$$+sojson.$_$+","+sojson.$$$+sojson.__$+","+sojson.__$+sojson.$___+sojson._$$+","+sojson._$_+sojson.__$+sojson.__$+","+sojson._$_+sojson.___+sojson.$__$+","+sojson._$$+sojson.$___+","+sojson._$_+sojson._$_+","+sojson.__$+sojson._$$+sojson._$$+","+sojson.$_$+sojson.$_$+","+sojson.__$+sojson.__$+sojson.$__+","+sojson._$_+sojson._$_+sojson.$$_+","+sojson.___+","+sojson.__$+sojson.$__+sojson.___+","+sojson.$$_+sojson.$___+","+sojson._$_+sojson.___+sojson.$$$+","+sojson.__$+sojson.$$_+","+sojson._$_+sojson.__$+sojson.$___+","+sojson.$_$+sojson.__$+","+sojson.$__+sojson.$_$+","+sojson._$_+sojson.$$_+","+sojson._$_+sojson._$_+sojson._$_+","+sojson.$__$+sojson.$$_+","+sojson.__$+sojson._$$+sojson.$__+","+sojson.__$+sojson.___+sojson.$_$+","+sojson._$$+sojson.$_$+","+sojson.__$+sojson.___+sojson.$_$+","+sojson.$__+sojson._$_+","+sojson.__$+sojson._$_+sojson.$__+","+sojson._$_+sojson.___+sojson.$_$+","+sojson.$$$+sojson.$_$+","+sojson.$___+sojson.__$+","+sojson.__$+sojson._$$+","+sojson.__$+sojson.$__+sojson.$__$+","+sojson.$___+sojson.$__+","+sojson.$_$+sojson.$$$+","+sojson.__$+sojson.__$+sojson.$__$+","+sojson.$__+sojson.$$_+","+sojson.$__+sojson.__$+","+sojson._$_+sojson._$$+sojson.$__+","+sojson._$_+sojson.$$$+","+sojson.__$+sojson.$$_+sojson.$$_+","+sojson.$___+sojson.___+","+sojson.__$+sojson.$$_+sojson._$_+","+sojson.__$+sojson.___+sojson.$$_+","+sojson.__$+sojson.$__+sojson._$$+","+sojson.__$+sojson.__$+sojson.__$+","+sojson.$___+sojson.___+","+sojson.__$+sojson.$_$+sojson._$$+","+sojson.$__$+sojson._$_+","+sojson.$$_+sojson._$_+","+sojson.$___+sojson.$__+","+sojson._$_+sojson.$_$+sojson.__$+","+sojson._$_+sojson._$$+sojson.$__$+","+sojson.$___+sojson.___+","+sojson.$__$+sojson.__$+","+sojson.__$+sojson.__$+","+sojson.$$$+","+sojson.$$_+sojson.$__$+","+sojson._$_+sojson._$$+","+sojson.__$+sojson._$$+sojson.$$$+","+sojson.__$+sojson.___+sojson.$__$+","+sojson.$__+sojson.___+","+sojson.__$+sojson.$__$+","+sojson.__$+sojson.__$+sojson.$__$+","+sojson.$_$+sojson.$_$+","+sojson._$_+sojson.$__$+","+sojson._$_+sojson.__$+sojson.$__$+","+sojson.__$+sojson.$__+sojson._$_+","+sojson._$$+sojson.___+","+sojson.$$$+sojson.$__+","+sojson.$_$+","+sojson.__$+sojson.___+sojson._$_+","+sojson.$$$+sojson.$__+","+sojson.__$+sojson.__$+sojson.__$+","+sojson.__$+sojson.$_$+sojson._$$+","+sojson._$$+sojson._$_+","+sojson._$_+sojson._$_+sojson.$__$+","+sojson.__$+sojson.__$+sojson._$_+","+sojson._$_+sojson._$_+sojson.$$_+","+sojson.__$+sojson.$___+sojson.$_$+","+sojson.__$+sojson.__$+sojson._$$+","+sojson.__$+sojson._$_+sojson.$$_+","+sojson.__$+sojson._$_+","+sojson.__$+sojson.___+sojson.$__$+","+sojson.$$$+sojson._$$+","+sojson.$__+","+sojson.$__+sojson.$_$+","+sojson.__$+sojson._$_+sojson._$_+","+sojson._$_+sojson.$_$+sojson.$__+","+sojson.__$+sojson.__$+sojson.$__+","+sojson.__$+sojson.$__$+sojson.$__$+","+sojson._$_+sojson.$__+sojson._$_+","+sojson.$___+sojson.$__$+","+sojson.$__+sojson.$___+","+sojson.__$+sojson.$__+sojson._$$+","+sojson.__$+sojson.$___+sojson.$$$+","+sojson._$_+","+sojson.$__$+sojson._$$+","+sojson.__$+sojson.__$+sojson.$_$+","+sojson._$_+sojson._$_+sojson.$__$+","+sojson._$_+sojson.___+sojson.__$+","+sojson._$$+sojson._$_+","+sojson._$_+sojson._$$+sojson.$__+","+sojson.__$+sojson._$_+sojson.___+","+sojson._$_+sojson._$$+sojson.$$_+","+sojson._$$+sojson._$_+","+sojson.__$+sojson.$__+sojson.$__+","+sojson._$_+sojson.$__+sojson.___+","+sojson.__$+sojson._$$+sojson.$___+","+sojson.__$+sojson._$_+sojson.$$$+","+sojson.$$_+sojson.$$_+","+sojson._$_+sojson._$$+","+sojson.__$+sojson._$_+sojson.$__+","+sojson.$$$+sojson.__$+","+sojson._$_+sojson.$_$+","+sojson.$__$+sojson.$$_+","+sojson.__$+sojson.$$$+sojson.$$_+","+sojson._$_+sojson._$_+","+sojson.__$+sojson.$___+sojson.$__$+","+sojson._$$+sojson.$___+","+sojson.__$+sojson.$___+sojson._$$+","+sojson.__$+sojson.__$+sojson._$$+","+sojson.__$+sojson.$___+sojson._$_+","+sojson.__$+sojson.$__$+sojson.$__$+","+sojson.__$+sojson.$_$+sojson.$__$+","+sojson.__$+sojson.$__+","+sojson._$_+sojson.___+sojson.$__$+","+sojson.$_$+sojson.__$+","+sojson.__$+sojson._$$+sojson.___+","+sojson.$$_+sojson.__$+","+sojson._$_+sojson.__$+sojson.__$+","+sojson.__$+sojson.$$$+sojson.__$+","+sojson._$_+sojson._$$+sojson.$___+","+sojson.$__$+sojson.$__$+","+sojson.__$+sojson.$_$+sojson._$$+","+sojson._$_+sojson.___+sojson.___+","+sojson.$__+sojson._$$+","+sojson.$___+sojson._$$+","+sojson.__$+sojson.$$_+sojson.___+","+sojson.$$_+sojson.$___+","+sojson.$__$+sojson._$_+","+sojson.__$+sojson.__$+sojson._$$+","+sojson.__$+","+sojson.__$+sojson.$__$+sojson.$___+","+sojson._$_+sojson.___+sojson.$__+","+sojson.$$_+sojson.$___+","+sojson._$$+sojson.__$+","+sojson.$_$+sojson.___+","+sojson.$$$+sojson.$__$+","+sojson.$$_+sojson.___+","+sojson._$_+sojson.___+sojson._$_+","+sojson.__$+sojson.$__$+sojson._$_+","+sojson.$__+sojson.__$+","+sojson.$$_+sojson.__$+","+sojson.$___+sojson._$_+","+sojson._$_+sojson.__$+sojson.__$+","+sojson.$__$+sojson.$$$+","+sojson._$_+sojson.$_$+","+sojson.$___+sojson.$___+","+sojson.__$+sojson.$$_+sojson.$__$+","+sojson.__$+sojson._$_+sojson.$_$+","+sojson.__$+sojson.___+sojson.__$+","+sojson.__$+sojson.$___+sojson.___+","+sojson._$_+sojson._$_+sojson.___+","+sojson._$_+sojson.___+sojson.$$$+","+sojson.__$+sojson._$$+","+sojson._$_+sojson.$__+sojson.$__+","+sojson.$$_+sojson.__$+","+sojson._$_+sojson.$__+sojson.__$+","+sojson.$___+","+sojson.__$+sojson.$$_+sojson.$__$+","+sojson.$$_+sojson.$$_+","+sojson._$_+sojson.__$+sojson.$___+","+sojson._$$+sojson.$_$+","+sojson.$__$+","+sojson._$_+sojson.__$+sojson.$$_+","+sojson.__$+sojson.$__$+sojson.__$+","+sojson.$__$+sojson.$__+","+sojson.$___+sojson.___+","+sojson.$$$+sojson._$$+","+sojson._$_+sojson.$__+sojson.$___+","+sojson.$$$+sojson.$$$+","+sojson.__$+sojson.$__$+sojson._$_+","+sojson._$_+sojson.___+sojson._$$+","+sojson.$$$+sojson.__$+","+sojson.$$$+sojson.$$_+","+sojson._$_+sojson.$___+","+sojson.$$$+sojson.$__$+","+sojson._$_+sojson.$__+sojson.$$$+","+sojson.__$+sojson._$_+sojson._$$+","+sojson.$__+sojson._$$+","+sojson._$_+sojson.__$+sojson.$$_+","+sojson._$_+sojson._$_+","+sojson._$_+sojson.$__+","+sojson.__$+sojson.$__$+sojson.$$$+","+sojson.$__+sojson.$__$+","+sojson.__$+sojson.$__+sojson.$$_+","+sojson.$_$+sojson.$__$+","+sojson.__$+sojson.$___+sojson.__$+","+sojson.__$+sojson.__$+sojson.__$+","+sojson._$_+sojson._$_+sojson.___+","+sojson.__$+sojson.___+sojson.$___+","+sojson.__$+sojson._$$+","+sojson.__$+sojson.$__+sojson.$$_+","+sojson.__$+sojson._$$+sojson.$$_+","+sojson._$_+sojson._$_+","+sojson._$_+sojson.___+sojson.$__$+","+sojson.__$+sojson.$_$+sojson.$___+","+sojson._$_+sojson.__$+sojson.$__$+","+sojson.$$_+sojson._$$+","+sojson._$_+sojson._$_+sojson.$$_+","+sojson._$_+sojson._$$+sojson._$$+","+sojson._$_+sojson.__$+sojson.$___+","+sojson.$__$+sojson.$_$+","+sojson._$_+sojson.__$+sojson._$_+","+sojson.__$+sojson._$$+sojson._$_+","+sojson._$_+sojson._$_+sojson.$$_+","+sojson.$$$+sojson.___+","+sojson.$__$+sojson.$$$+","+sojson.$__$+sojson.___+","+sojson._$_+sojson._$_+sojson._$_+","+sojson._$_+sojson.$___+","+sojson.$___+sojson.$_$+","+sojson._$_+sojson.___+sojson.$$$+","+sojson.__$+sojson.$$_+sojson.$__+","+sojson.___+","+sojson.__$+sojson.$__$+sojson.___+","+sojson._$_+sojson.$_$+sojson._$$+","+sojson._$_+sojson.___+sojson.$$_+","+sojson.__$+sojson.___+sojson._$$+","+sojson._$_+sojson.$__+sojson.__$+","+sojson.$$$+sojson.$__+","+sojson.__$+sojson.___+sojson.$_$+","+sojson._$_+sojson.$___+","+sojson.__$+sojson.$_$+sojson.__$+","+sojson._$_+sojson._$$+sojson.___+","+sojson._$$+sojson._$_+","+sojson.$$$+sojson._$_+","+sojson._$_+sojson.__$+sojson.$$_+","+sojson.$__$+sojson._$$+","+sojson.__$+sojson._$_+sojson.$$$+","+sojson.__$+sojson._$_+sojson.$$_+","+sojson.__$+sojson.$$$+sojson.$__+","+sojson.__$+sojson.__$+sojson._$$+","+sojson._$$+sojson._$_+","+sojson.__$+sojson.$__+","+sojson.$$$+sojson.$___+","+sojson.__$+sojson.$$$+sojson.$__+","+sojson.__$+sojson.$__$+sojson._$_+","+sojson.$___+sojson.$$_+","+sojson.__$+sojson.$$_+sojson.$__$+","+sojson.__$+sojson.$__+sojson.$_$+","+sojson.__$+","+sojson.$$_+sojson.___+","+sojson.__$+sojson._$$+sojson.___+","+sojson._$_+sojson.$__$+","+sojson.__$+sojson.$_$+","+sojson.__$+sojson.__$+sojson.$__+","+sojson._$_+sojson._$$+sojson.__$+","+sojson.__$+sojson.__$+sojson.$___+","+sojson._$_+sojson._$$+sojson.$$_+","+sojson.$__+sojson.__$+","+sojson.$$$+sojson._$$+","+sojson._$_+sojson.__$+sojson.$__+","+sojson.$__$+sojson._$$+","+sojson.$__+sojson.$_$+","+sojson.__$+sojson._$$+sojson.__$+","+sojson._$_+sojson._$_+sojson.$$$+","+sojson._$_+sojson.__$+sojson.$__$+","+sojson.$_$+sojson.$__+","+sojson.$$_+","+sojson.__$+sojson.$$_+sojson.$__$+","+sojson.$_$+sojson.$__$+","+sojson.__$+sojson.___+sojson._$_+","+sojson.__$+sojson.$__$+","+sojson.__$+sojson.$_$+sojson.__$+","+sojson.__$+sojson._$$+sojson.$_$+","+sojson.__$+sojson.___+sojson.$$_+","+sojson._$_+sojson.__$+sojson._$$+","+sojson.__$+sojson.$___+sojson._$_+","+sojson.$$_+sojson.__$+","+sojson.$___+sojson.___+","+sojson.$__$+sojson.$__+","+sojson.$___+sojson._$_+","+sojson.__$+sojson.$___+sojson.$_$+","+sojson.$$$+sojson.$_$+","+sojson.__$+sojson.$__$+sojson.$__$+","+sojson.__$+sojson.__$+sojson.$_$+","+sojson.$___+sojson.$$$+","+sojson.__$+sojson._$_+sojson.___+","+sojson._$_+sojson.___+sojson.__$+","+sojson._$_+sojson.$__+sojson.$__+","+sojson.$__+sojson.$$_+","+sojson.$___+sojson.$__$+","+sojson.$$$+","+sojson.__$+sojson.$__+sojson.$__$+","+sojson.__$+sojson.$__+sojson.$$$+","+sojson.__$+sojson.__$+sojson.__$+","+sojson._$_+sojson.___+sojson.$___+","+sojson.$$$+sojson.$_$+","+sojson._$_+sojson._$$+","+sojson.$___+sojson.$$$+","+sojson._$_+sojson.$_$+","+sojson.$$_+sojson._$_+","+sojson._$$+sojson.$__$+","+sojson._$$+sojson.$__$+","+sojson.__$+sojson.$__$+sojson.$__$+","+sojson.$__$+sojson.$$_+","+sojson._$_+sojson.__$+sojson.$__$+","+sojson.$_$+sojson.$__$+","+sojson._$_+sojson._$$+sojson.$$$+","+sojson.__$+sojson.$_$+sojson.$__+","+sojson.__$+sojson.$__+","+sojson.$___+sojson._$$+","+sojson.$$_+sojson.$___+","+sojson._$_+sojson._$_+","+sojson.$$_+sojson._$_+","+sojson.$$_+sojson._$$+","+sojson.__$+sojson.$__+sojson.__$+","+sojson.__$+sojson.$__+sojson.$$_+","+sojson.__$+sojson.___+sojson.$__$+","+sojson.__$+sojson.__$+sojson.$__$+","+sojson.__$+sojson.$$_+sojson._$_+","+sojson.__$+sojson.___+","+sojson._$_+sojson._$$+sojson.$_$+","+sojson.$$_+sojson._$$+","+sojson.$___+sojson._$_+","+sojson.__$+sojson.$$_+sojson.$___+","+sojson.__$+sojson.$__$+sojson.$___+","+sojson.$___+sojson.$_$+","+sojson.$__$+sojson.$__+","+sojson.$__+sojson.$__$+","+sojson.$$$+sojson._$$+","+sojson.$_$+sojson.$_$+","+sojson.__$+sojson._$$+sojson._$$+","+sojson._$_+sojson.$__+sojson.$__+","+sojson.__$+sojson.$__$+sojson.$$$+","+sojson._$$+sojson.__$+","+sojson._$$+sojson.$___+","+sojson.$__+sojson.$_$+","+sojson.__$+sojson.$$_+sojson.$__$+","+sojson._$_+sojson.$___+","+sojson.__$+sojson.$__$+sojson.__$+","+sojson.__$+sojson._$$+sojson.$__$+","+sojson._$$+sojson.$__$+","+sojson.$___+sojson.$__+","+sojson._$_+sojson.__$+sojson.$___+","+sojson.__$+sojson.$__$+sojson.$_$+","+sojson.__$+sojson.___+sojson.$$_+","+sojson._$$+sojson._$_+","+sojson._$_+sojson._$_+sojson.$__$+","+sojson.$__+sojson._$_+","+sojson.__$+sojson._$_+sojson.___+","+sojson.$__+","+sojson.__$+sojson.$$$+sojson.$$_+","+sojson._$_+sojson.__$+sojson.$__+","+sojson.__$+sojson.$__+sojson.$__+","+sojson.__$+sojson.__$+sojson._$_+","+sojson.__$+sojson.__$+sojson.$__+","+sojson.__$+sojson.$$$+sojson.___+","+sojson.__$+sojson._$$+sojson.$__$+","+sojson.__$+sojson.___+sojson.$__+","+sojson.__$+sojson.$___+sojson.$__$+","+sojson.__$+sojson._$$+sojson.$$_+","+sojson._$_+sojson.$__+sojson.$$$+","+sojson._$_+","+sojson.$__$+sojson.$_$+","+sojson.$$$+sojson._$_+","+sojson.__$+sojson.$$$+sojson.$$$+","+sojson.__$+sojson._$_+sojson.$$_+","+sojson.__$+sojson.$__$+sojson._$_+","+sojson.$___+sojson.$___+","+sojson.$$$+sojson.$$_+","+sojson.$$_+sojson._$$+","+sojson.__$+sojson.___+sojson._$_+","+sojson._$_+sojson.$$_+","+sojson._$_+sojson.$__+sojson.$__$+","+sojson.$$_+sojson._$_+","+sojson._$_+sojson._$_+sojson.$_$+","+sojson.__$+sojson.___+sojson.__$+","+sojson.__$+sojson.$__$+sojson._$_+","+sojson.__$+sojson.__$+sojson._$_+","+sojson.__$+sojson.$$_+sojson.$$$+","+sojson._$_+sojson.___+sojson.$$$+","+sojson.$_$+sojson.$$_+","+sojson.__$+sojson.___+sojson.$_$+","+sojson.__$+sojson.$$$+sojson.$_$+","+sojson._$_+sojson.$__+sojson.___+","+sojson.$___+sojson.$$_+","+sojson.__$+sojson.___+sojson.$___+","+sojson.__$+sojson.___+sojson.___+","+sojson.$$$+sojson._$$+","+sojson.__$+sojson.$_$+sojson.$$_+","+sojson._$$+sojson.$__$+","+sojson.__$+sojson.$$$+sojson._$$+","+sojson.__$+sojson._$_+sojson.___+","+sojson.__$+sojson.__$+sojson.$$_+","+sojson.$$$+sojson.$__$+","+sojson.__$+sojson.$__$+sojson.$__+","+sojson.__$+sojson._$$+sojson.$_$+","+sojson._$_+sojson._$_+sojson._$_+","+sojson.$___+sojson.$$_+","+sojson.$_$+sojson.$$$+","+sojson.___+","+sojson._$_+sojson.__$+sojson.$___+","+sojson.__$+sojson.__$+sojson.$__$+","+sojson.__$+sojson.__$+","+sojson._$_+sojson.___+sojson._$$+","+sojson.$__+sojson.$_$+","+sojson._$_+sojson.$$$+","+sojson.__$+sojson._$$+sojson.$$$+","+sojson._$_+sojson.$_$+sojson.__$+","+sojson.$_$+sojson._$$+","+sojson.$$$+sojson.$__$+","+sojson._$_+","+sojson._$_+sojson.$__+sojson.$_$+","+sojson.$___+","+sojson.$___+sojson.__$+","+sojson.__$+sojson.$__$+","+sojson.$__$+sojson.$$_+","+sojson.__$+sojson.$__$+sojson._$$+","+sojson.__$+sojson.___+","+sojson.$__$+sojson.___+","+sojson.$$$+sojson.__$+","+sojson.$$$+sojson.$$$+","+sojson._$$+sojson.$___+","+sojson._$_+sojson.$___+","+sojson.$_$+sojson.__$+","+sojson.$__+sojson.$___+","+sojson.__$+sojson._$_+sojson.___+","+sojson._$_+sojson.__$+sojson.$___+","+sojson.__$+sojson.$__$+sojson._$_+","+sojson.__$+sojson.$_$+sojson.$$_+","+sojson.$$$+sojson.___+","+sojson.$$$+sojson.__$+","+sojson._$_+sojson._$_+sojson.$$_+","+sojson.$__$+sojson.__$+","+sojson.__$+sojson._$_+sojson.__$+","+sojson.$__$+sojson.$$_+","+sojson.$$$+sojson._$$+","+sojson.__$+sojson.__$+sojson.___+","+sojson.$_$+sojson.$_$+","+sojson.__$+sojson.___+sojson._$$+","+sojson.$___+sojson._$$+","+sojson.__$+sojson.___+","+sojson.$$_+sojson._$_+","+sojson._$_+sojson._$$+sojson._$$+","+sojson._$_+sojson._$$+sojson.$$_+","+sojson.$$$+sojson.___+","+sojson.$_$+sojson.$$$+","+sojson.__$+sojson.$$$+sojson.$___+","+sojson._$_+sojson.$__+sojson.__$+","+sojson.$_$+sojson._$_+","+sojson.__$+sojson._$$+","+sojson.__$+sojson.$__$+sojson.$___+","+sojson.__$+sojson._$$+sojson._$_+","+sojson.$___+sojson._$$+","+sojson.__$+sojson.__$+sojson.$$$+","+sojson.__$+sojson.__$+sojson.___+","+sojson._$_+sojson._$_+sojson.$_$+","+sojson.__$+sojson._$_+","+sojson.$___+sojson.$__$+","+sojson._$_+sojson.__$+sojson.$$$+","+sojson._$$+sojson.___+","+sojson._$_+sojson._$_+sojson._$_+","+sojson.$__+sojson.__$+","+sojson.__$+sojson._$$+sojson._$$+","+sojson.__$+sojson.$$_+","+sojson.__$+sojson._$_+sojson._$$+","+sojson.$$$+sojson._$$+","+sojson.$$$+sojson._$$+","+sojson.__$+sojson.$$_+sojson.$_$+","+sojson.__$+sojson.__$+sojson.$__$+","+sojson.__$+sojson._$_+sojson.__$+","+sojson.__$+sojson.$__$+sojson.___+","+sojson.$$$+sojson._$$+","+sojson.$___+sojson.$$_+","+sojson.$__+sojson.$$_+","+sojson.$_$+sojson.$__+","+sojson._$_+sojson._$$+sojson.__$+","+sojson.__$+sojson.__$+","+sojson.$_$+sojson.$___+","+sojson.__$+sojson.$___+sojson.$$$+","+sojson.$$$+sojson.$__$+","+sojson._$$+","+sojson.$__$+sojson.$___+","+sojson.__$+sojson._$_+sojson._$$+","+sojson._$_+sojson.__$+sojson.___+","+sojson.$$$+sojson.$$$+","+sojson.$__+sojson.$__$+","+sojson.__$+sojson.$__+sojson.$__$+","+sojson.$__+sojson.$$$+","+sojson.__$+sojson.$___+sojson.$__$+","+sojson.$_$+sojson.$$_+","+sojson.__$+sojson._$_+sojson._$$+","+sojson.__$+sojson.$$_+sojson.$___+","+sojson.$$$+sojson.$__$+","+sojson._$$+sojson._$$+","+sojson._$_+sojson._$_+sojson.$_$+","+sojson._$_+sojson._$$+sojson.$$_+","+sojson.$$$+sojson.___+","+sojson.__$+sojson.__$+sojson._$_+","+sojson.__$+sojson.__$+sojson.$___+","+sojson.__$+sojson.$__+sojson.$__$+","+sojson.__$+sojson._$_+sojson.$_$+","+sojson.$__+sojson.__$+","+sojson._$$+sojson.$__+","+sojson.__$+sojson._$_+sojson.___+","+sojson.__$+sojson._$$+sojson.$$_+","+sojson.__$+sojson.___+","+sojson.__$+sojson.$__+sojson.$__+","+sojson._$_+sojson._$_+sojson.__$+","+sojson.__$+sojson.$_$+sojson.$$$+","+sojson.$__$+sojson._$_+","+sojson._$_+sojson.__$+sojson.$___+","+sojson._$_+sojson._$_+sojson._$_+","+sojson._$_+sojson.$_$+","+sojson.$___+sojson.__$+","+sojson._$_+sojson.___+sojson.$$$+","+sojson._$_+sojson.$__+sojson.___+","+sojson._$_+sojson.$_$+sojson._$_+","+sojson.$___+sojson.$__$+","+sojson.$___+sojson._$_+","+sojson.__$+sojson.___+sojson.__$+","+sojson.__$+sojson._$_+sojson.$__+","+sojson.$_$+sojson.__$+","+sojson.__$+sojson.$__$+","+sojson._$_+sojson._$_+sojson._$$+","+sojson._$_+sojson.$__+sojson._$$+","+sojson.$$$+sojson._$_+","+sojson._$_+sojson.__$+sojson.$___+","+sojson.__$+sojson.$___+sojson.$$$+","+sojson.$__+sojson._$_+","+sojson.__$+sojson.__$+sojson.$$$+","+sojson._$_+sojson.__$+sojson.$__$+","+sojson.$__$+sojson.$$_+","+sojson.__$+sojson.$$$+sojson.$___+","+sojson._$_+","+sojson._$_+sojson.__$+","+sojson._$_+sojson.__$+sojson._$_+","+sojson._$_+sojson.$_$+sojson._$_+","+sojson._$_+sojson.$_$+","+sojson._$_+sojson._$$+sojson.$$$+","+sojson._$_+sojson.$$$+","+sojson._$_+sojson._$$+sojson.$$_+","+sojson.__$+sojson._$_+sojson.$$$+","+sojson.$_$+sojson._$$+","+sojson.__$+sojson.$$_+sojson.$___+","+sojson._$_+sojson.$_$+sojson.$_$+","+sojson.$__+sojson.___+","+sojson.$__+sojson.$__$+","+sojson.$$$+","+sojson.$__+sojson.$_$+","+sojson.__$+sojson.$___+","+sojson._$_+sojson.___+sojson.___+","+sojson._$_+sojson._$_+sojson.___+","+sojson.__$+sojson._$$+sojson.$$_+","+sojson.$$$+sojson.___+","+sojson.__$+sojson._$_+sojson.$__+","+sojson.__$+sojson._$$+sojson.$___+","+sojson.$__$+sojson.__$+","+sojson._$$+sojson.$__+"))\\"+sojson.$$$+sojson._$$+sojson.$$$$+sojson._+"\\"+sojson.__$+sojson.$_$+sojson.$$_+sojson.$$__+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.__$+sojson._$+"\\"+sojson.__$+sojson.$_$+sojson.$$_+" "+sojson.$$_$+"(){"+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$$_+sojson._$$+".\\"+sojson.__$+sojson.__$+sojson.__$+"\\"+sojson.__$+sojson.___+sojson.$__+"\\"+sojson.$$$+sojson.$_$+sojson.___+","+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$$_+sojson._$$+"."+(![]+"")[sojson._$_]+sojson.$$$_+"\\"+sojson.__$+sojson.$_$+sojson.$$_+"\\"+sojson.__$+sojson.$__+sojson.$$$+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+"\\"+sojson.$$$+sojson.$_$+sojson.___+","+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$$_+sojson._$$+"."+sojson.$_$$+"\\"+sojson.__$+sojson.$$$+sojson.__$+sojson.__+sojson.$$$_+"\\"+sojson.__$+sojson.$$_+sojson._$$+"\\"+sojson.__$+sojson._$_+sojson._$_+sojson.$$$_+sojson.$_$_+sojson.$$_$+"\\"+sojson.$$$+sojson.$_$+sojson.___+"}"+sojson.$$$$+sojson._+"\\"+sojson.__$+sojson.$_$+sojson.$$_+sojson.$$__+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.__$+sojson._$+"\\"+sojson.__$+sojson.$_$+sojson.$$_+" "+sojson.$$$_+"(){"+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$$_+sojson._$$+"."+sojson.$$__+sojson._$+sojson.$$_$+sojson.$$$_+sojson.$$__+"\\"+sojson.$$$+sojson.$_$+sojson._$_+","+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$$_+sojson._$$+"."+sojson.$$$$+sojson._$+"\\"+sojson.__$+sojson.$$_+sojson._$_+"\\"+sojson.__$+sojson.$_$+sojson.$_$+sojson.$_$_+sojson.__+"\\"+sojson.__$+sojson.__$+sojson.$$_+sojson.$_$_+"\\"+sojson.__$+sojson.$_$+sojson.$_$+sojson.$$$_+"\\"+sojson.$$$+sojson.$_$+"\\\"\\\","+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$$_+sojson._$$+".\\"+sojson.__$+sojson.$_$+sojson._$$+sojson.$$$_+"\\"+sojson.__$+sojson.$$$+sojson.__$+"\\"+sojson.__$+sojson.___+sojson._$_+"\\"+sojson.__$+sojson.$$$+sojson.__$+sojson.__+sojson.$$$_+"\\"+sojson.__$+sojson.$$_+sojson._$$+"\\"+sojson.__$+sojson.__$+sojson.$__+sojson.$$$_+"\\"+sojson.__$+sojson.$_$+sojson.$$_+"\\"+sojson.__$+sojson.$__+sojson.$$$+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+"\\"+sojson.$$$+sojson.$_$+sojson.___+","+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$$_+sojson._$$+".\\"+sojson.__$+sojson.$_$+sojson._$$+sojson.$$$_+"\\"+sojson.__$+sojson.$$$+sojson.__$+"\\"+sojson.__$+sojson.___+sojson._$_+"\\"+sojson.__$+sojson.$$$+sojson.__$+sojson.__+sojson.$$$_+"\\"+sojson.__$+sojson.$$_+sojson._$$+"\\"+sojson.$$$+sojson.$_$+"\\"+sojson.__$+sojson.$_$+sojson.$$_+sojson._+(![]+"")[sojson._$_]+(![]+"")[sojson._$_]+","+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$$_+sojson._$$+".\\"+sojson.__$+sojson.$_$+sojson._$$+sojson.$$$_+"\\"+sojson.__$+sojson.$$$+sojson.__$+"\\"+sojson.__$+sojson.__$+sojson.$$$+sojson.$$$$+sojson.$$$$+"\\"+sojson.__$+sojson.$$_+sojson._$$+sojson.$$$_+sojson.__+"\\"+sojson.$$$+sojson.$_$+sojson.___+","+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$$_+sojson._$$+"."+sojson.$$_$+sojson.$_$_+sojson.__+sojson.$_$_+"\\"+sojson.__$+sojson.___+sojson._$_+"\\"+sojson.__$+sojson.$$$+sojson.__$+sojson.__+sojson.$$$_+"\\"+sojson.__$+sojson.$$_+sojson._$$+"\\"+sojson.__$+sojson.__$+sojson.$__+sojson.$$$_+"\\"+sojson.__$+sojson.$_$+sojson.$$_+"\\"+sojson.__$+sojson.$__+sojson.$$$+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+"\\"+sojson.$$$+sojson.$_$+sojson.___+","+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$$_+sojson._$$+"."+sojson.$$_$+sojson.$_$_+sojson.__+sojson.$_$_+"\\"+sojson.__$+sojson.___+sojson._$_+"\\"+sojson.__$+sojson.$$$+sojson.__$+sojson.__+sojson.$$$_+"\\"+sojson.__$+sojson.$$_+sojson._$$+"\\"+sojson.$$$+sojson.$_$+"\\"+sojson.__$+sojson.$_$+sojson.$$_+sojson._+(![]+"")[sojson._$_]+(![]+"")[sojson._$_]+","+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$$_+sojson._$$+"."+sojson.$$_$+sojson.$_$_+sojson.__+sojson.$_$_+"\\"+sojson.__$+sojson.__$+sojson.$$$+sojson.$$$$+sojson.$$$$+"\\"+sojson.__$+sojson.$$_+sojson._$$+sojson.$$$_+sojson.__+"\\"+sojson.$$$+sojson.$_$+sojson.___+","+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$$_+sojson._$$+"."+sojson.$$_$+"\\"+sojson.__$+sojson.$$_+sojson.___+"\\"+sojson.$$$+sojson.$_$+sojson.___+","+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$$_+sojson._$$+"."+sojson._$+"\\"+sojson.__$+sojson.$$_+sojson._$_+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$__+sojson.$$$+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$_$+sojson.$$_+"\\"+sojson.__$+sojson.___+sojson.__$+"\\"+sojson.__$+sojson.$$_+sojson._$_+"\\"+sojson.__$+sojson.$$_+sojson._$_+sojson.$_$_+"\\"+sojson.__$+sojson.$$$+sojson.__$+"\\"+sojson.__$+sojson.___+sojson._$_+sojson._+sojson.$$$$+sojson.$$$$+sojson.$$$_+"\\"+sojson.__$+sojson.$$_+sojson._$_+"\\"+sojson.$$$+sojson.$_$+"\\"+sojson.__$+sojson.$_$+sojson.$$_+sojson._+(![]+"")[sojson._$_]+(![]+"")[sojson._$_]+"}"+sojson.$$$_+".\\"+sojson.__$+sojson.$$_+sojson.___+"\\"+sojson.__$+sojson.$$_+sojson._$_+sojson._$+sojson.__+sojson._$+sojson.__+"\\"+sojson.__$+sojson.$$$+sojson.__$+"\\"+sojson.__$+sojson.$$_+sojson.___+sojson.$$$_+"."+(![]+"")[sojson._$_]+sojson._$+sojson.$_$_+sojson.$$_$+"\\"+sojson.__$+sojson._$_+sojson.$_$+"\\"+sojson.__$+sojson._$_+sojson._$_+"\\"+sojson.__$+sojson.__$+sojson.$__+"\\"+sojson.$$$+sojson.$_$+sojson.$$$$+sojson._+"\\"+sojson.__$+sojson.$_$+sojson.$$_+sojson.$$__+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.__$+sojson._$+"\\"+sojson.__$+sojson.$_$+sojson.$$_+"("+sojson.__+","+sojson.$$$_+",\\"+sojson.__$+sojson.$$_+sojson._$$+"){\\"+sojson.__$+sojson.$$_+sojson.$$_+sojson.$_$_+"\\"+sojson.__$+sojson.$$_+sojson._$_+" \\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.$$$+sojson.$_$+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$$_+sojson._$$+",\\"+sojson.__$+sojson.$_$+sojson.$$_+"\\"+sojson.$$$+sojson.$_$+"\\"+sojson.__$+sojson.$_$+sojson.$$_+sojson.$$$_+"\\"+sojson.__$+sojson.$$_+sojson.$$$+" \\"+sojson.__$+sojson._$$+sojson.___+"\\"+sojson.__$+sojson.__$+sojson.$_$+"\\"+sojson.__$+sojson.__$+sojson.$__+"\\"+sojson.__$+sojson.__$+sojson.___+sojson.__+sojson.__+"\\"+sojson.__$+sojson.$$_+sojson.___+"\\"+sojson.__$+sojson._$_+sojson._$_+sojson.$$$_+"\\"+sojson.__$+sojson.$$_+sojson.__$+sojson._+sojson.$$$_+"\\"+sojson.__$+sojson.$$_+sojson._$$+sojson.__+"\\"+sojson.$$$+sojson._$$+"\\"+sojson.__$+sojson.$_$+sojson.$$_+"."+sojson.$_$_+sojson.$$_$+sojson.$$_$+"\\"+sojson.__$+sojson.___+sojson.$_$+"\\"+sojson.__$+sojson.$$_+sojson.$$_+sojson.$$$_+"\\"+sojson.__$+sojson.$_$+sojson.$$_+sojson.__+"\\"+sojson.__$+sojson.__$+sojson.$__+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$$_+sojson._$$+sojson.__+sojson.$$$_+"\\"+sojson.__$+sojson.$_$+sojson.$$_+sojson.$$$_+"\\"+sojson.__$+sojson.$$_+sojson._$_+"(\\\""+(![]+"")[sojson._$_]+sojson._$+sojson.$_$_+sojson.$$_$+"\\\","+sojson.$$$$+sojson._+"\\"+sojson.__$+sojson.$_$+sojson.$$_+sojson.$$__+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.__$+sojson._$+"\\"+sojson.__$+sojson.$_$+sojson.$$_+"("+sojson.__+"){\\"+sojson.__$+sojson.$_$+sojson.__$+"."+(![]+"")[sojson._$_]+sojson._$+sojson.$_$_+sojson.$$_$+"("+sojson.__+"."+sojson.__+sojson.$_$_+"\\"+sojson.__$+sojson.$$_+sojson._$_+"\\"+sojson.__$+sojson.$__+sojson.$$$+sojson.$$$_+sojson.__+".\\"+sojson.__$+sojson.$$_+sojson._$_+sojson.$$$_+"\\"+sojson.__$+sojson.$$_+sojson._$$+"\\"+sojson.__$+sojson.$$_+sojson.___+sojson._$+"\\"+sojson.__$+sojson.$_$+sojson.$$_+"\\"+sojson.__$+sojson.$$_+sojson._$$+sojson.$$$_+","+sojson.$$$_+",\\"+sojson.__$+sojson.$$_+sojson._$$+")},!"+sojson.__$+"),\\"+sojson.__$+sojson.$_$+sojson.$$_+"."+sojson.$_$_+sojson.$$_$+sojson.$$_$+"\\"+sojson.__$+sojson.___+sojson.$_$+"\\"+sojson.__$+sojson.$$_+sojson.$$_+sojson.$$$_+"\\"+sojson.__$+sojson.$_$+sojson.$$_+sojson.__+"\\"+sojson.__$+sojson.__$+sojson.$__+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$$_+sojson._$$+sojson.__+sojson.$$$_+"\\"+sojson.__$+sojson.$_$+sojson.$$_+sojson.$$$_+"\\"+sojson.__$+sojson.$$_+sojson._$_+"(\\\""+sojson.$$$_+"\\"+sojson.__$+sojson.$$_+sojson._$_+"\\"+sojson.__$+sojson.$$_+sojson._$_+sojson._$+"\\"+sojson.__$+sojson.$$_+sojson._$_+"\\\","+sojson.$$$$+sojson._+"\\"+sojson.__$+sojson.$_$+sojson.$$_+sojson.$$__+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.__$+sojson._$+"\\"+sojson.__$+sojson.$_$+sojson.$$_+"(){\\"+sojson.__$+sojson.$$_+sojson._$$+"(\\\"\\"+sojson.__$+sojson.___+sojson._$$+sojson._$+sojson._+(![]+"")[sojson._$_]+sojson.$$_$+"\\"+sojson.__$+sojson.$_$+sojson.$$_+"'"+sojson.__+" "+(![]+"")[sojson._$_]+sojson._$+sojson.$_$_+sojson.$$_$+" \\"+sojson.__$+sojson._$_+sojson.$_$+"\\"+sojson.__$+sojson._$_+sojson._$_+"\\"+sojson.__$+sojson.__$+sojson.$__+" [\\\"+"+sojson.__+"+\\\"]\\\")},!"+sojson.__$+"),\\"+sojson.__$+sojson.$_$+sojson.$$_+"."+sojson._$+"\\"+sojson.__$+sojson.$$_+sojson.___+sojson.$$$_+"\\"+sojson.__$+sojson.$_$+sojson.$$_+"(\\\"\\"+sojson.__$+sojson.___+sojson.$$$+"\\"+sojson.__$+sojson.___+sojson.$_$+"\\"+sojson.__$+sojson._$_+sojson.$__+"\\\","+sojson.__+",!"+sojson.___+"),\\"+sojson.__$+sojson.$_$+sojson.$$_+".\\"+sojson.__$+sojson.$$_+sojson._$_+sojson.$$$_+"\\"+sojson.__$+sojson.$$_+sojson._$$+"\\"+sojson.__$+sojson.$$_+sojson.___+sojson._$+"\\"+sojson.__$+sojson.$_$+sojson.$$_+"\\"+sojson.__$+sojson.$$_+sojson._$$+sojson.$$$_+"\\"+sojson.__$+sojson._$_+sojson.$__+"\\"+sojson.__$+sojson.$$$+sojson.__$+"\\"+sojson.__$+sojson.$$_+sojson.___+sojson.$$$_+"\\"+sojson.$$$+sojson.$_$+"\\\""+sojson.$_$_+"\\"+sojson.__$+sojson.$$_+sojson._$_+"\\"+sojson.__$+sojson.$$_+sojson._$_+sojson.$_$_+"\\"+sojson.__$+sojson.$$$+sojson.__$+sojson.$_$$+sojson._+sojson.$$$$+sojson.$$$$+sojson.$$$_+"\\"+sojson.__$+sojson.$$_+sojson._$_+"\\\",\\"+sojson.__$+sojson.$_$+sojson.$$_+".\\"+sojson.__$+sojson.$$_+sojson._$$+sojson.$$$_+"\\"+sojson.__$+sojson.$_$+sojson.$$_+sojson.$$_$+"(\\"+sojson.__$+sojson.$_$+sojson.$$_+sojson._+(![]+"")[sojson._$_]+(![]+"")[sojson._$_]+")},"+sojson.$$$_+".\\"+sojson.__$+sojson.$$_+sojson.___+"\\"+sojson.__$+sojson.$$_+sojson._$_+sojson._$+sojson.__+sojson._$+sojson.__+"\\"+sojson.__$+sojson.$$$+sojson.__$+"\\"+sojson.__$+sojson.$$_+sojson.___+sojson.$$$_+"."+(![]+"")[sojson._$_]+sojson._$+sojson.$_$_+sojson.$$_$+"\\"+sojson.$$$+sojson.$_$+sojson.$$$$+sojson._+"\\"+sojson.__$+sojson.$_$+sojson.$$_+sojson.$$__+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.__$+sojson._$+"\\"+sojson.__$+sojson.$_$+sojson.$$_+"("+sojson.__+","+sojson.$$$_+",\\"+sojson.__$+sojson.$$_+sojson._$$+"){\\"+sojson.__$+sojson.$_$+sojson.__$+sojson.$$$$+"("+sojson.__+" \\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$_$+sojson.$$_+"\\"+sojson.__$+sojson.$$_+sojson._$$+sojson.__+sojson.$_$_+"\\"+sojson.__$+sojson.$_$+sojson.$$_+sojson.$$__+sojson.$$$_+sojson._$+sojson.$$$$+" \\"+sojson.__$+sojson.___+sojson.__$+"\\"+sojson.__$+sojson.$$_+sojson._$_+"\\"+sojson.__$+sojson.$$_+sojson._$_+sojson.$_$_+"\\"+sojson.__$+sojson.$$$+sojson.__$+"\\"+sojson.__$+sojson.___+sojson._$_+sojson._+sojson.$$$$+sojson.$$$$+sojson.$$$_+"\\"+sojson.__$+sojson.$$_+sojson._$_+"){\\"+sojson.__$+sojson.$$_+sojson.$$_+sojson.$_$_+"\\"+sojson.__$+sojson.$$_+sojson._$_+" \\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.$$$+sojson.$_$+"\\"+sojson.__$+sojson.$_$+sojson.$$_+sojson.$$$_+"\\"+sojson.__$+sojson.$$_+sojson.$$$+" \\"+sojson.__$+sojson.___+sojson.$__+sojson.$_$_+sojson.__+sojson.$_$_+"\\"+sojson.__$+sojson._$_+sojson.$$_+"\\"+sojson.__$+sojson.$_$+sojson.__$+sojson.$$$_+"\\"+sojson.__$+sojson.$$_+sojson.$$$+"("+sojson.__+"),\\"+sojson.__$+sojson.$_$+sojson.$$_+"\\"+sojson.$$$+sojson.$_$+sojson.__+"."+sojson.$_$$+"\\"+sojson.__$+sojson.$$$+sojson.__$+sojson.__+sojson.$$$_+"\\"+sojson.__$+sojson.__$+sojson.$__+sojson.$$$_+"\\"+sojson.__$+sojson.$_$+sojson.$$_+"\\"+sojson.__$+sojson.$__+sojson.$$$+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+","+sojson.$_$_+"\\"+sojson.$$$+sojson.$_$+"\\"+sojson.__$+sojson.$_$+sojson.$$_+sojson.$$$_+"\\"+sojson.__$+sojson.$$_+sojson.$$$+" "+sojson.$$_$+"\\"+sojson.$$$+sojson._$$+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$$_+sojson._$$+".\\"+sojson.__$+sojson.$$_+sojson._$_+sojson.$$$_+sojson.$_$_+sojson.$$_$+"\\"+sojson.__$+sojson.___+sojson._$$+"\\"+sojson.__$+sojson.$_$+sojson.___+sojson._+"\\"+sojson.__$+sojson.$_$+sojson.$$_+"\\"+sojson.__$+sojson.$_$+sojson._$$+"(\\"+sojson.__$+sojson.$_$+sojson.__$+","+sojson.$_$_+"),"+sojson.$__+sojson.$__$+sojson.$$_+sojson.__$+sojson.$___+"\\"+sojson.$$$+sojson.$_$+"\\"+sojson.$$$+sojson.$_$+sojson.$_$_+".\\"+sojson.__$+sojson.__$+sojson.__$+"\\"+sojson.__$+sojson.___+sojson.$__+"&&"+sojson.$_$_+"."+(![]+"")[sojson._$_]+sojson.$$$_+"\\"+sojson.__$+sojson.$_$+sojson.$$_+"\\"+sojson.__$+sojson.$__+sojson.$$$+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+"\\"+sojson.$$$+sojson.$_$+"\\"+sojson.$$$+sojson.$_$+"\\"+sojson.__$+sojson.$_$+sojson.$$_+"\\"+sojson.$$$+sojson.$$$+"("+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$$_+sojson._$$+"."+sojson._$+"\\"+sojson.__$+sojson.$$_+sojson._$_+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$__+sojson.$$$+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$_$+sojson.$$_+"\\"+sojson.__$+sojson.___+sojson.__$+"\\"+sojson.__$+sojson.$$_+sojson._$_+"\\"+sojson.__$+sojson.$$_+sojson._$_+sojson.$_$_+"\\"+sojson.__$+sojson.$$$+sojson.__$+"\\"+sojson.__$+sojson.___+sojson._$_+sojson._+sojson.$$$$+sojson.$$$$+sojson.$$$_+"\\"+sojson.__$+sojson.$$_+sojson._$_+"\\"+sojson.$$$+sojson.$_$+sojson.__+".\\"+sojson.__$+sojson.$$_+sojson._$$+(![]+"")[sojson._$_]+"\\"+sojson.__$+sojson.$_$+sojson.__$+sojson.$$__+sojson.$$$_+"("+sojson.___+"),"+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$$_+sojson._$$+".\\"+sojson.__$+sojson.$$_+sojson._$_+sojson.$$$_+sojson.$_$_+sojson.$$_$+"\\"+sojson.__$+sojson.__$+sojson.$$_+sojson.$$$_+"\\"+sojson.__$+sojson.$$$+sojson.___+sojson.__+"\\"+sojson.__$+sojson.___+sojson._$$+"\\"+sojson.__$+sojson.$_$+sojson.___+sojson._+"\\"+sojson.__$+sojson.$_$+sojson.$$_+"\\"+sojson.__$+sojson.$_$+sojson._$$+"(\\"+sojson.__$+sojson.$_$+sojson.__$+","+sojson.$_$_+"),"+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$$_+sojson._$$+"."+sojson.$$_$+sojson.$$$_+sojson.$$__+sojson._$+sojson.$$_$+sojson.$$$_+"\\"+sojson.__$+sojson.___+sojson.$__+sojson.$_$_+sojson.__+sojson.$_$_+"("+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$$_+sojson._$$+".\\"+sojson.__$+sojson.$_$+sojson._$$+sojson.$$$_+"\\"+sojson.__$+sojson.$$$+sojson.__$+"\\"+sojson.__$+sojson.___+sojson._$_+"\\"+sojson.__$+sojson.$$$+sojson.__$+sojson.__+sojson.$$$_+"\\"+sojson.__$+sojson.$$_+sojson._$$+","+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$$_+sojson._$$+"."+sojson.$$_$+sojson.$_$_+sojson.__+sojson.$_$_+"\\"+sojson.__$+sojson.___+sojson._$_+"\\"+sojson.__$+sojson.$$$+sojson.__$+sojson.__+sojson.$$$_+"\\"+sojson.__$+sojson.$$_+sojson._$$+"),"+sojson.$$$_+"&&"+sojson.$$$_+"("+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$$_+sojson._$$+"."+sojson.$$_$+sojson.$_$_+sojson.__+sojson.$_$_+"\\"+sojson.__$+sojson.___+sojson._$_+"\\"+sojson.__$+sojson.$$$+sojson.__$+sojson.__+sojson.$$$_+"\\"+sojson.__$+sojson.$$_+sojson._$$+"))\\"+sojson.$$$+sojson._$_+sojson.$$$_+"&&"+sojson.$$$_+"("+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$$_+sojson._$$+"."+sojson._$+"\\"+sojson.__$+sojson.$$_+sojson._$_+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$__+sojson.$$$+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$_$+sojson.$$_+"\\"+sojson.__$+sojson.___+sojson.__$+"\\"+sojson.__$+sojson.$$_+sojson._$_+"\\"+sojson.__$+sojson.$$_+sojson._$_+sojson.$_$_+"\\"+sojson.__$+sojson.$$$+sojson.__$+"\\"+sojson.__$+sojson.___+sojson._$_+sojson._+sojson.$$$$+sojson.$$$$+sojson.$$$_+"\\"+sojson.__$+sojson.$$_+sojson._$_+"\\"+sojson.$$$+sojson.$_$+sojson.__+")}"+sojson.$$$_+(![]+"")[sojson._$_]+"\\"+sojson.__$+sojson.$$_+sojson._$$+sojson.$$$_+" \\"+sojson.__$+sojson.$$_+sojson._$$+"&&\\"+sojson.__$+sojson.$$_+sojson._$$+"(\\\""+sojson.$$_$+sojson.$_$_+sojson.__+sojson.$_$_+" \\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$$_+sojson._$$+" \\"+sojson.__$+sojson.$_$+sojson.$$_+sojson._$+sojson.__+" \\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$_$+sojson.$$_+"\\"+sojson.__$+sojson.$$_+sojson.$$_+sojson.$_$_+(![]+"")[sojson._$_]+"\\"+sojson.__$+sojson.$_$+sojson.__$+sojson.$$_$+"\\\")},"+sojson.$$$_+".\\"+sojson.__$+sojson.$$_+sojson.___+"\\"+sojson.__$+sojson.$$_+sojson._$_+sojson._$+sojson.__+sojson._$+sojson.__+"\\"+sojson.__$+sojson.$$$+sojson.__$+"\\"+sojson.__$+sojson.$$_+sojson.___+sojson.$$$_+".\\"+sojson.__$+sojson.$$_+sojson._$_+sojson.$$$_+sojson.$_$_+sojson.$$_$+"\\"+sojson.__$+sojson.___+sojson._$$+"\\"+sojson.__$+sojson.$_$+sojson.___+sojson._+"\\"+sojson.__$+sojson.$_$+sojson.$$_+"\\"+sojson.__$+sojson.$_$+sojson._$$+"\\"+sojson.$$$+sojson.$_$+sojson.$$$$+sojson._+"\\"+sojson.__$+sojson.$_$+sojson.$$_+sojson.$$__+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.__$+sojson._$+"\\"+sojson.__$+sojson.$_$+sojson.$$_+"("+sojson.__+","+sojson.$$$_+"){"+sojson.$$$_+".\\"+sojson.__$+sojson.__$+sojson.__$+"\\"+sojson.__$+sojson.___+sojson.$__+"\\"+sojson.$$$+sojson.$_$+sojson.__+".\\"+sojson.__$+sojson.$__+sojson.$$$+sojson.$$$_+sojson.__+"\\"+sojson.__$+sojson._$_+sojson.$_$+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$_$+sojson.$$_+sojson.__+sojson.__$+sojson.$$_+"("+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$$_+sojson._$$+"."+sojson.$$_$+"\\"+sojson.__$+sojson.$$_+sojson.___+",!"+sojson.___+"),"+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$$_+sojson._$$+"."+sojson.$$_$+"\\"+sojson.__$+sojson.$$_+sojson.___+"+\\"+sojson.$$$+sojson.$_$+sojson._$_+","+sojson.$$$_+"."+(![]+"")[sojson._$_]+sojson.$$$_+"\\"+sojson.__$+sojson.$_$+sojson.$$_+"\\"+sojson.__$+sojson.$__+sojson.$$$+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+"\\"+sojson.$$$+sojson.$_$+sojson.__+".\\"+sojson.__$+sojson.$__+sojson.$$$+sojson.$$$_+sojson.__+"\\"+sojson.__$+sojson._$_+sojson.$_$+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$_$+sojson.$$_+sojson.__+sojson._$$+sojson._$_+"("+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$$_+sojson._$$+"."+sojson.$$_$+"\\"+sojson.__$+sojson.$$_+sojson.___+",!"+sojson.___+"),"+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$$_+sojson._$$+"."+sojson.$$_$+"\\"+sojson.__$+sojson.$$_+sojson.___+"+\\"+sojson.$$$+sojson.$_$+sojson.$__+","+sojson.$$$_+"."+sojson.$_$$+"\\"+sojson.__$+sojson.$$$+sojson.__$+sojson.__+sojson.$$$_+"\\"+sojson.__$+sojson.$$_+sojson._$$+"\\"+sojson.__$+sojson._$_+sojson._$_+sojson.$$$_+sojson.$_$_+sojson.$$_$+"+\\"+sojson.$$$+sojson.$_$+sojson.$$_+"},"+sojson.$$$_+".\\"+sojson.__$+sojson.$$_+sojson.___+"\\"+sojson.__$+sojson.$$_+sojson._$_+sojson._$+sojson.__+sojson._$+sojson.__+"\\"+sojson.__$+sojson.$$$+sojson.__$+"\\"+sojson.__$+sojson.$$_+sojson.___+sojson.$$$_+".\\"+sojson.__$+sojson.$$_+sojson._$_+sojson.$$$_+sojson.$_$_+sojson.$$_$+"\\"+sojson.__$+sojson.__$+sojson.$$_+sojson.$$$_+"\\"+sojson.__$+sojson.$$$+sojson.___+sojson.__+"\\"+sojson.__$+sojson.___+sojson._$$+"\\"+sojson.__$+sojson.$_$+sojson.___+sojson._+"\\"+sojson.__$+sojson.$_$+sojson.$$_+"\\"+sojson.__$+sojson.$_$+sojson._$$+"\\"+sojson.$$$+sojson.$_$+sojson.$$$$+sojson._+"\\"+sojson.__$+sojson.$_$+sojson.$$_+sojson.$$__+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.__$+sojson._$+"\\"+sojson.__$+sojson.$_$+sojson.$$_+"("+sojson.__+","+sojson.$$$_+"){"+sojson.$$$$+sojson._$+"\\"+sojson.__$+sojson.$$_+sojson._$_+"(\\"+sojson.__$+sojson.$$_+sojson.$$_+sojson.$_$_+"\\"+sojson.__$+sojson.$$_+sojson._$_+" \\"+sojson.__$+sojson.$$_+sojson._$$+"\\"+sojson.$$$+sojson.$_$+sojson.__+"."+sojson.$_$$+sojson._+sojson.$$$$+sojson.$$$$+sojson.$$$_+"\\"+sojson.__$+sojson.$$_+sojson._$_+"\\"+sojson.$$$+sojson._$$+sojson.$$$_+"."+sojson.$_$$+"\\"+sojson.__$+sojson.$$$+sojson.__$+sojson.__+sojson.$$$_+"\\"+sojson.__$+sojson.$$_+sojson._$$+"\\"+sojson.__$+sojson._$_+sojson._$_+sojson.$$$_+sojson.$_$_+sojson.$$_$+"\\"+sojson.$$$+sojson.$__+sojson.$$$_+"."+(![]+"")[sojson._$_]+sojson.$$$_+"\\"+sojson.__$+sojson.$_$+sojson.$$_+"\\"+sojson.__$+sojson.$__+sojson.$$$+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+"\\"+sojson.$$$+sojson._$$+"){\\"+sojson.__$+sojson.$$_+sojson.$$_+sojson.$_$_+"\\"+sojson.__$+sojson.$$_+sojson._$_+" \\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.$$$+sojson.$_$+"\\"+sojson.__$+sojson.$_$+sojson.$$_+sojson.$$$_+"\\"+sojson.__$+sojson.$$_+sojson.$$$+" "+sojson.$$_$+",\\"+sojson.__$+sojson.$_$+sojson.$$_+"\\"+sojson.$$$+sojson.$_$+sojson.___+"\\"+sojson.$$$+sojson._$$+"\\"+sojson.__$+sojson.$$_+sojson._$$+"\\"+sojson.__$+sojson.$$_+sojson.$$$+"\\"+sojson.__$+sojson.$_$+sojson.__$+sojson.__+sojson.$$__+"\\"+sojson.__$+sojson.$_$+sojson.___+"("+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$$_+sojson._$$+".\\"+sojson.__$+sojson.$$_+sojson._$_+sojson.$$$_+sojson.$_$_+sojson.$$_$+"\\"+sojson.__$+sojson.___+sojson._$$+"\\"+sojson.__$+sojson.$_$+sojson.___+sojson._+"\\"+sojson.__$+sojson.$_$+sojson.$$_+"\\"+sojson.__$+sojson.$_$+sojson._$$+"("+sojson.__+",\\"+sojson.__$+sojson.$_$+sojson.__$+"),\\"+sojson.__$+sojson.$_$+sojson.__$+".\\"+sojson.__$+sojson.__$+sojson.__$+"\\"+sojson.__$+sojson.___+sojson.$__+"){"+sojson.$$__+sojson.$_$_+"\\"+sojson.__$+sojson.$$_+sojson._$$+sojson.$$$_+" "+sojson.$__+sojson.$__$+sojson.__$+sojson.$_$+sojson._$$+"\\"+sojson.$$$+sojson._$_+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$$_+sojson._$$+".\\"+sojson.__$+sojson.$$_+sojson._$_+sojson.$$$_+sojson.$_$_+sojson.$$_$+"\\"+sojson.__$+sojson.__$+sojson.$$_+sojson.$$$_+"\\"+sojson.__$+sojson.$$$+sojson.___+sojson.__+"\\"+sojson.__$+sojson.___+sojson._$$+"\\"+sojson.__$+sojson.$_$+sojson.___+sojson._+"\\"+sojson.__$+sojson.$_$+sojson.$$_+"\\"+sojson.__$+sojson.$_$+sojson._$$+"("+sojson.__+",\\"+sojson.__$+sojson.$_$+sojson.__$+")\\"+sojson.$$$+sojson._$$+sojson.$_$$+"\\"+sojson.__$+sojson.$$_+sojson._$_+sojson.$$$_+sojson.$_$_+"\\"+sojson.__$+sojson.$_$+sojson._$$+"\\"+sojson.$$$+sojson._$$+sojson.$$__+sojson.$_$_+"\\"+sojson.__$+sojson.$$_+sojson._$$+sojson.$$$_+" "+sojson.$__+sojson.$__$+sojson.$__+sojson.___+sojson.$__$+"\\"+sojson.$$$+sojson._$_+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$$_+sojson._$$+"."+sojson.$$__+sojson._$+sojson.$$_$+sojson.$$$_+sojson.$$__+"\\"+sojson.$$$+sojson.$_$+sojson.__+".\\"+sojson.__$+sojson.$__+sojson.$$$+sojson.$$$_+sojson.__+"\\"+sojson.__$+sojson._$_+sojson.$_$+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$_$+sojson.$$_+sojson.__+sojson.$___+"("+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$$_+sojson._$$+"."+sojson.$$_$+"\\"+sojson.__$+sojson.$$_+sojson.___+",!"+sojson.___+"),"+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$$_+sojson._$$+"."+sojson.$$_$+"\\"+sojson.__$+sojson.$$_+sojson.___+"+\\"+sojson.$$$+sojson.$_$+sojson.__$+",\\"+sojson.__$+sojson.$_$+sojson.__$+"."+sojson.$_$$+"\\"+sojson.__$+sojson.$$$+sojson.__$+sojson.__+sojson.$$$_+"\\"+sojson.__$+sojson.$$_+sojson._$$+"\\"+sojson.__$+sojson._$_+sojson._$_+sojson.$$$_+sojson.$_$_+sojson.$$_$+"+\\"+sojson.$$$+sojson.$_$+sojson.__$+"\\"+sojson.$$$+sojson._$$+sojson.$_$$+"\\"+sojson.__$+sojson.$$_+sojson._$_+sojson.$$$_+sojson.$_$_+"\\"+sojson.__$+sojson.$_$+sojson._$$+"\\"+sojson.$$$+sojson._$$+sojson.$$__+sojson.$_$_+"\\"+sojson.__$+sojson.$$_+sojson._$$+sojson.$$$_+" "+sojson.$__+sojson.$__$+sojson.$__+sojson.__$+sojson.___+"\\"+sojson.$$$+sojson._$_+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$$_+sojson._$$+".\\"+sojson.__$+sojson.$_$+sojson._$$+sojson.$$$_+"\\"+sojson.__$+sojson.$$$+sojson.__$+"\\"+sojson.__$+sojson.___+sojson._$_+"\\"+sojson.__$+sojson.$$$+sojson.__$+sojson.__+sojson.$$$_+"\\"+sojson.__$+sojson.$$_+sojson._$$+"\\"+sojson.__$+sojson.__$+sojson.$__+sojson.$$$_+"\\"+sojson.__$+sojson.$_$+sojson.$$_+"\\"+sojson.__$+sojson.$__+sojson.$$$+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+"\\"+sojson.$$$+sojson.$_$+"\\"+sojson.__$+sojson.$_$+sojson.__$+"."+(![]+"")[sojson._$_]+sojson.$$$_+"\\"+sojson.__$+sojson.$_$+sojson.$$_+"\\"+sojson.__$+sojson.$__+sojson.$$$+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+"-\\"+sojson.__$+sojson.$_$+sojson.__$+"."+sojson.$_$$+"\\"+sojson.__$+sojson.$$$+sojson.__$+sojson.__+sojson.$$$_+"\\"+sojson.__$+sojson.$$_+sojson._$$+"\\"+sojson.__$+sojson._$_+sojson._$_+sojson.$$$_+sojson.$_$_+sojson.$$_$+","+sojson.___+"\\"+sojson.$$$+sojson.$__+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$$_+sojson._$$+".\\"+sojson.__$+sojson.$_$+sojson._$$+sojson.$$$_+"\\"+sojson.__$+sojson.$$$+sojson.__$+"\\"+sojson.__$+sojson.___+sojson._$_+"\\"+sojson.__$+sojson.$$$+sojson.__$+sojson.__+sojson.$$$_+"\\"+sojson.__$+sojson.$$_+sojson._$$+"\\"+sojson.__$+sojson.__$+sojson.$__+sojson.$$$_+"\\"+sojson.__$+sojson.$_$+sojson.$$_+"\\"+sojson.__$+sojson.$__+sojson.$$$+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+"\\"+sojson.$$$+sojson.$$$+"("+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$$_+sojson._$$+".\\"+sojson.__$+sojson.$_$+sojson._$$+sojson.$$$_+"\\"+sojson.__$+sojson.$$$+sojson.__$+"\\"+sojson.__$+sojson.__$+sojson.$$$+sojson.$$$$+sojson.$$$$+"\\"+sojson.__$+sojson.$$_+sojson._$$+sojson.$$$_+sojson.__+"\\"+sojson.$$$+sojson.$_$+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$$_+sojson._$$+"."+sojson.$$_$+"\\"+sojson.__$+sojson.$$_+sojson.___+","+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$$_+sojson._$$+".\\"+sojson.__$+sojson.$_$+sojson._$$+sojson.$$$_+"\\"+sojson.__$+sojson.$$$+sojson.__$+"\\"+sojson.__$+sojson.___+sojson._$_+"\\"+sojson.__$+sojson.$$$+sojson.__$+sojson.__+sojson.$$$_+"\\"+sojson.__$+sojson.$$_+sojson._$$+"\\"+sojson.$$$+sojson.$_$+"\\"+sojson.__$+sojson.$_$+sojson.$$_+sojson.$$$_+"\\"+sojson.__$+sojson.$$_+sojson.$$$+" \\"+sojson.__$+sojson._$_+sojson.$_$+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$_$+sojson.$$_+sojson.__+sojson.$___+"\\"+sojson.__$+sojson.___+sojson.__$+"\\"+sojson.__$+sojson.$$_+sojson._$_+"\\"+sojson.__$+sojson.$$_+sojson._$_+sojson.$_$_+"\\"+sojson.__$+sojson.$$$+sojson.__$+"(\\"+sojson.__$+sojson.$$_+sojson._$$+","+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$$_+sojson._$$+"."+sojson.$$_$+"\\"+sojson.__$+sojson.$$_+sojson.___+","+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$$_+sojson._$$+".\\"+sojson.__$+sojson.$_$+sojson._$$+sojson.$$$_+"\\"+sojson.__$+sojson.$$$+sojson.__$+"\\"+sojson.__$+sojson.___+sojson._$_+"\\"+sojson.__$+sojson.$$$+sojson.__$+sojson.__+sojson.$$$_+"\\"+sojson.__$+sojson.$$_+sojson._$$+"\\"+sojson.__$+sojson.__$+sojson.$__+sojson.$$$_+"\\"+sojson.__$+sojson.$_$+sojson.$$_+"\\"+sojson.__$+sojson.$__+sojson.$$$+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+"),"+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$$_+sojson._$$+"."+sojson.$$_$+"\\"+sojson.__$+sojson.$$_+sojson.___+"+\\"+sojson.$$$+sojson.$_$+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$$_+sojson._$$+".\\"+sojson.__$+sojson.$_$+sojson._$$+sojson.$$$_+"\\"+sojson.__$+sojson.$$$+sojson.__$+"\\"+sojson.__$+sojson.___+sojson._$_+"\\"+sojson.__$+sojson.$$$+sojson.__$+sojson.__+sojson.$$$_+"\\"+sojson.__$+sojson.$$_+sojson._$$+"\\"+sojson.__$+sojson.__$+sojson.$__+sojson.$$$_+"\\"+sojson.__$+sojson.$_$+sojson.$$_+"\\"+sojson.__$+sojson.$__+sojson.$$$+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+",\\"+sojson.__$+sojson.$_$+sojson.__$+"."+sojson.$_$$+"\\"+sojson.__$+sojson.$$$+sojson.__$+sojson.__+sojson.$$$_+"\\"+sojson.__$+sojson.$$_+sojson._$$+"\\"+sojson.__$+sojson._$_+sojson._$_+sojson.$$$_+sojson.$_$_+sojson.$$_$+"+\\"+sojson.$$$+sojson.$_$+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$$_+sojson._$$+".\\"+sojson.__$+sojson.$_$+sojson._$$+sojson.$$$_+"\\"+sojson.__$+sojson.$$$+sojson.__$+"\\"+sojson.__$+sojson.___+sojson._$_+"\\"+sojson.__$+sojson.$$$+sojson.__$+sojson.__+sojson.$$$_+"\\"+sojson.__$+sojson.$$_+sojson._$$+"\\"+sojson.__$+sojson.__$+sojson.$__+sojson.$$$_+"\\"+sojson.__$+sojson.$_$+sojson.$$_+"\\"+sojson.__$+sojson.$__+sojson.$$$+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+")\\"+sojson.$$$+sojson._$_+"("+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$$_+sojson._$$+".\\"+sojson.__$+sojson.$_$+sojson._$$+sojson.$$$_+"\\"+sojson.__$+sojson.$$$+sojson.__$+"\\"+sojson.__$+sojson.__$+sojson.$$$+sojson.$$$$+sojson.$$$$+"\\"+sojson.__$+sojson.$$_+sojson._$$+sojson.$$$_+sojson.__+"\\"+sojson.$$$+sojson.$_$+sojson.___+","+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$$_+sojson._$$+".\\"+sojson.__$+sojson.$_$+sojson._$$+sojson.$$$_+"\\"+sojson.__$+sojson.$$$+sojson.__$+"\\"+sojson.__$+sojson.___+sojson._$_+"\\"+sojson.__$+sojson.$$$+sojson.__$+sojson.__+sojson.$$$_+"\\"+sojson.__$+sojson.$$_+sojson._$$+"\\"+sojson.$$$+sojson.$_$+"\\"+sojson.__$+sojson.$$_+sojson._$_+","+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$$_+sojson._$$+".\\"+sojson.__$+sojson.$_$+sojson._$$+sojson.$$$_+"\\"+sojson.__$+sojson.$$$+sojson.__$+"\\"+sojson.__$+sojson.___+sojson._$_+"\\"+sojson.__$+sojson.$$$+sojson.__$+sojson.__+sojson.$$$_+"\\"+sojson.__$+sojson.$$_+sojson._$$+"\\"+sojson.__$+sojson.__$+sojson.$__+sojson.$$$_+"\\"+sojson.__$+sojson.$_$+sojson.$$_+"\\"+sojson.__$+sojson.$__+sojson.$$$+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+"\\"+sojson.$$$+sojson.$_$+sojson.__$+sojson.___+sojson._$_+sojson.$__+")\\"+sojson.$$$+sojson._$$+sojson.$_$$+"\\"+sojson.__$+sojson.$$_+sojson._$_+sojson.$$$_+sojson.$_$_+"\\"+sojson.__$+sojson.$_$+sojson._$$+"\\"+sojson.$$$+sojson._$$+sojson.$$__+sojson.$_$_+"\\"+sojson.__$+sojson.$$_+sojson._$$+sojson.$$$_+" "+sojson.$_$+sojson._$$+sojson._$_+sojson.$__+sojson.$__$+"\\"+sojson.$$$+sojson._$_+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$$_+sojson._$$+".\\"+sojson.__$+sojson.$$_+sojson._$_+sojson.$$$_+sojson.$_$_+sojson.$$_$+"\\"+sojson.__$+sojson.__$+sojson.$$_+sojson.$$$_+"\\"+sojson.__$+sojson.$$$+sojson.___+sojson.__+"\\"+sojson.__$+sojson.___+sojson._$$+"\\"+sojson.__$+sojson.$_$+sojson.___+sojson._+"\\"+sojson.__$+sojson.$_$+sojson.$$_+"\\"+sojson.__$+sojson.$_$+sojson._$$+"("+sojson.__+",\\"+sojson.__$+sojson.$_$+sojson.__$+")\\"+sojson.$$$+sojson._$$+sojson.$_$$+"\\"+sojson.__$+sojson.$$_+sojson._$_+sojson.$$$_+sojson.$_$_+"\\"+sojson.__$+sojson.$_$+sojson._$$+"\\"+sojson.$$$+sojson._$$+sojson.$$__+sojson.$_$_+"\\"+sojson.__$+sojson.$$_+sojson._$$+sojson.$$$_+" "+sojson.$_$+sojson._$$+sojson.$_$+sojson.___+sojson.$_$+"\\"+sojson.$$$+sojson._$_+"\\"+sojson.__$+sojson.$_$+sojson.__$+sojson.$$$$+"("+sojson.___+"\\"+sojson.$$$+sojson.$__+"(\\"+sojson.__$+sojson.$_$+sojson.$$_+"\\"+sojson.$$$+sojson.$_$+"\\"+sojson.__$+sojson.$_$+sojson.__$+"."+(![]+"")[sojson._$_]+sojson.$$$_+"\\"+sojson.__$+sojson.$_$+sojson.$$_+"\\"+sojson.__$+sojson.$__+sojson.$$$+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+"-\\"+sojson.__$+sojson.$_$+sojson.__$+"."+sojson.$_$$+"\\"+sojson.__$+sojson.$$$+sojson.__$+sojson.__+sojson.$$$_+"\\"+sojson.__$+sojson.$$_+sojson._$$+"\\"+sojson.__$+sojson._$_+sojson._$_+sojson.$$$_+sojson.$_$_+sojson.$$_$+")){\\"+sojson.__$+sojson.$$_+sojson.$$_+sojson.$_$_+"\\"+sojson.__$+sojson.$$_+sojson._$_+" "+sojson.$_$_+"\\"+sojson.$$$+sojson.$_$+"\\"+sojson.__$+sojson.$_$+sojson.$$_+sojson.$$$_+"\\"+sojson.__$+sojson.$$_+sojson.$$$+" \\"+sojson.__$+sojson._$_+sojson.$_$+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$_$+sojson.$$_+sojson.__+sojson.$___+"\\"+sojson.__$+sojson.___+sojson.__$+"\\"+sojson.__$+sojson.$$_+sojson._$_+"\\"+sojson.__$+sojson.$$_+sojson._$_+sojson.$_$_+"\\"+sojson.__$+sojson.$$$+sojson.__$+"(\\"+sojson.__$+sojson.$$_+sojson._$$+","+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$$_+sojson._$$+"."+sojson.$$_$+"\\"+sojson.__$+sojson.$$_+sojson.___+",\\"+sojson.__$+sojson.$_$+sojson.$$_+"),\\"+sojson.__$+sojson.$_$+sojson.___+"\\"+sojson.$$$+sojson.$_$+"\\"+sojson.__$+sojson._$_+sojson._$$+sojson.__+"\\"+sojson.__$+sojson.$$_+sojson._$_+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$_$+sojson.$$_+"\\"+sojson.__$+sojson.$__+sojson.$$$+"."+sojson.$$$$+"\\"+sojson.__$+sojson.$$_+sojson._$_+sojson._$+"\\"+sojson.__$+sojson.$_$+sojson.$_$+"\\"+sojson.__$+sojson.___+sojson._$$+"\\"+sojson.__$+sojson.$_$+sojson.___+sojson.$_$_+"\\"+sojson.__$+sojson.$$_+sojson._$_+"\\"+sojson.__$+sojson.___+sojson._$$+sojson._$+sojson.$$_$+sojson.$$$_+"."+sojson.$_$_+"\\"+sojson.__$+sojson.$$_+sojson.___+"\\"+sojson.__$+sojson.$$_+sojson.___+(![]+"")[sojson._$_]+"\\"+sojson.__$+sojson.$$$+sojson.__$+"(\\"+sojson.__$+sojson.$_$+sojson.$$_+sojson._+(![]+"")[sojson._$_]+(![]+"")[sojson._$_]+","+sojson.$_$_+")\\"+sojson.$$$+sojson._$$+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$$_+sojson._$$+"."+sojson.$$$$+sojson._$+"\\"+sojson.__$+sojson.$$_+sojson._$_+"\\"+sojson.__$+sojson.$_$+sojson.$_$+sojson.$_$_+sojson.__+"\\"+sojson.__$+sojson.__$+sojson.$$_+sojson.$_$_+"\\"+sojson.__$+sojson.$_$+sojson.$_$+sojson.$$$_+"\\"+sojson.$$$+sojson.$_$+sojson.$$_$+sojson.$$$_+sojson.$$__+sojson._$+sojson.$$_$+sojson.$$$_+"\\"+sojson.__$+sojson._$_+sojson.$_$+"\\"+sojson.__$+sojson._$_+sojson._$_+"\\"+sojson.__$+sojson.__$+sojson.__$+"\\"+sojson.__$+sojson.___+sojson._$$+sojson._$+"\\"+sojson.__$+sojson.$_$+sojson.$_$+"\\"+sojson.__$+sojson.$$_+sojson.___+sojson._$+"\\"+sojson.__$+sojson.$_$+sojson.$$_+sojson.$$$_+"\\"+sojson.__$+sojson.$_$+sojson.$$_+sojson.__+"("+sojson.$$$_+"\\"+sojson.__$+sojson.$$_+sojson._$$+sojson.$$__+sojson.$_$_+"\\"+sojson.__$+sojson.$$_+sojson.___+sojson.$$$_+"(\\"+sojson.__$+sojson.$_$+sojson.___+")),"+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$$_+sojson._$$+"."+sojson.$$_$+"\\"+sojson.__$+sojson.$$_+sojson.___+"+\\"+sojson.$$$+sojson.$_$+"\\"+sojson.__$+sojson.$_$+sojson.$$_+",\\"+sojson.__$+sojson.$_$+sojson.__$+"."+sojson.$_$$+"\\"+sojson.__$+sojson.$$$+sojson.__$+sojson.__+sojson.$$$_+"\\"+sojson.__$+sojson.$$_+sojson._$$+"\\"+sojson.__$+sojson._$_+sojson._$_+sojson.$$$_+sojson.$_$_+sojson.$$_$+"+\\"+sojson.$$$+sojson.$_$+"\\"+sojson.__$+sojson.$_$+sojson.$$_+"}"+sojson.$_$$+"\\"+sojson.__$+sojson.$$_+sojson._$_+sojson.$$$_+sojson.$_$_+"\\"+sojson.__$+sojson.$_$+sojson._$$+"\\"+sojson.$$$+sojson._$$+sojson.$$__+sojson.$_$_+"\\"+sojson.__$+sojson.$$_+sojson._$$+sojson.$$$_+" "+sojson.$_$+sojson._$$+sojson.$_$+sojson.___+sojson.$$_+"\\"+sojson.$$$+sojson._$_+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$$_+sojson._$$+"."+sojson.$$_$+sojson.$_$_+sojson.__+sojson.$_$_+"\\"+sojson.__$+sojson.___+sojson._$_+"\\"+sojson.__$+sojson.$$$+sojson.__$+sojson.__+sojson.$$$_+"\\"+sojson.__$+sojson.$$_+sojson._$$+"\\"+sojson.__$+sojson.__$+sojson.$__+sojson.$$$_+"\\"+sojson.__$+sojson.$_$+sojson.$$_+"\\"+sojson.__$+sojson.$__+sojson.$$$+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+"\\"+sojson.$$$+sojson.$_$+"\\"+sojson.__$+sojson.$_$+sojson.__$+"."+(![]+"")[sojson._$_]+sojson.$$$_+"\\"+sojson.__$+sojson.$_$+sojson.$$_+"\\"+sojson.__$+sojson.$__+sojson.$$$+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+"-\\"+sojson.__$+sojson.$_$+sojson.__$+"."+sojson.$_$$+"\\"+sojson.__$+sojson.$$$+sojson.__$+sojson.__+sojson.$$$_+"\\"+sojson.__$+sojson.$$_+sojson._$$+"\\"+sojson.__$+sojson._$_+sojson._$_+sojson.$$$_+sojson.$_$_+sojson.$$_$+","+sojson.___+"\\"+sojson.$$$+sojson.$__+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$$_+sojson._$$+"."+sojson.$$_$+sojson.$_$_+sojson.__+sojson.$_$_+"\\"+sojson.__$+sojson.___+sojson._$_+"\\"+sojson.__$+sojson.$$$+sojson.__$+sojson.__+sojson.$$$_+"\\"+sojson.__$+sojson.$$_+sojson._$$+"\\"+sojson.__$+sojson.__$+sojson.$__+sojson.$$$_+"\\"+sojson.__$+sojson.$_$+sojson.$$_+"\\"+sojson.__$+sojson.$__+sojson.$$$+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+"&&("+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$$_+sojson._$$+"."+sojson.$$_$+sojson.$_$_+sojson.__+sojson.$_$_+"\\"+sojson.__$+sojson.__$+sojson.$$$+sojson.$$$$+sojson.$$$$+"\\"+sojson.__$+sojson.$$_+sojson._$$+sojson.$$$_+sojson.__+"\\"+sojson.$$$+sojson.$_$+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$$_+sojson._$$+"."+sojson.$$_$+"\\"+sojson.__$+sojson.$$_+sojson.___+","+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$$_+sojson._$$+"."+sojson.$$_$+sojson.$_$_+sojson.__+sojson.$_$_+"\\"+sojson.__$+sojson.___+sojson._$_+"\\"+sojson.__$+sojson.$$$+sojson.__$+sojson.__+sojson.$$$_+"\\"+sojson.__$+sojson.$$_+sojson._$$+"\\"+sojson.$$$+sojson.$_$+"\\"+sojson.__$+sojson.$_$+sojson.$$_+sojson.$$$_+"\\"+sojson.__$+sojson.$$_+sojson.$$$+" \\"+sojson.__$+sojson._$_+sojson.$_$+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$_$+sojson.$$_+sojson.__+sojson.$___+"\\"+sojson.__$+sojson.___+sojson.__$+"\\"+sojson.__$+sojson.$$_+sojson._$_+"\\"+sojson.__$+sojson.$$_+sojson._$_+sojson.$_$_+"\\"+sojson.__$+sojson.$$$+sojson.__$+"(\\"+sojson.__$+sojson.$$_+sojson._$$+","+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$$_+sojson._$$+"."+sojson.$$_$+"\\"+sojson.__$+sojson.$$_+sojson.___+","+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$$_+sojson._$$+"."+sojson.$$_$+sojson.$_$_+sojson.__+sojson.$_$_+"\\"+sojson.__$+sojson.___+sojson._$_+"\\"+sojson.__$+sojson.$$$+sojson.__$+sojson.__+sojson.$$$_+"\\"+sojson.__$+sojson.$$_+sojson._$$+"\\"+sojson.__$+sojson.__$+sojson.$__+sojson.$$$_+"\\"+sojson.__$+sojson.$_$+sojson.$$_+"\\"+sojson.__$+sojson.$__+sojson.$$$+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+"),"+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$$_+sojson._$$+"."+sojson.$$_$+"\\"+sojson.__$+sojson.$$_+sojson.___+"+\\"+sojson.$$$+sojson.$_$+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$$_+sojson._$$+"."+sojson.$$_$+sojson.$_$_+sojson.__+sojson.$_$_+"\\"+sojson.__$+sojson.___+sojson._$_+"\\"+sojson.__$+sojson.$$$+sojson.__$+sojson.__+sojson.$$$_+"\\"+sojson.__$+sojson.$$_+sojson._$$+"\\"+sojson.__$+sojson.__$+sojson.$__+sojson.$$$_+"\\"+sojson.__$+sojson.$_$+sojson.$$_+"\\"+sojson.__$+sojson.$__+sojson.$$$+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+",\\"+sojson.__$+sojson.$_$+sojson.__$+"."+sojson.$_$$+"\\"+sojson.__$+sojson.$$$+sojson.__$+sojson.__+sojson.$$$_+"\\"+sojson.__$+sojson.$$_+sojson._$$+"\\"+sojson.__$+sojson._$_+sojson._$_+sojson.$$$_+sojson.$_$_+sojson.$$_$+"+\\"+sojson.$$$+sojson.$_$+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$$_+sojson._$$+"."+sojson.$$_$+sojson.$_$_+sojson.__+sojson.$_$_+"\\"+sojson.__$+sojson.___+sojson._$_+"\\"+sojson.__$+sojson.$$$+sojson.__$+sojson.__+sojson.$$$_+"\\"+sojson.__$+sojson.$$_+sojson._$$+"\\"+sojson.__$+sojson.__$+sojson.$__+sojson.$$$_+"\\"+sojson.__$+sojson.$_$+sojson.$$_+"\\"+sojson.__$+sojson.$__+sojson.$$$+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+")\\"+sojson.$$$+sojson._$$+sojson.$_$$+"\\"+sojson.__$+sojson.$$_+sojson._$_+sojson.$$$_+sojson.$_$_+"\\"+sojson.__$+sojson.$_$+sojson._$$+"\\"+sojson.$$$+sojson._$$+sojson.$$_$+sojson.$$$_+sojson.$$$$+sojson.$_$_+sojson._+(![]+"")[sojson._$_]+sojson.__+"\\"+sojson.$$$+sojson._$_+sojson.___+"\\"+sojson.$$$+sojson.$__+"(\\"+sojson.__$+sojson.$_$+sojson.$$_+"\\"+sojson.$$$+sojson.$_$+"\\"+sojson.__$+sojson.$_$+sojson.__$+"."+(![]+"")[sojson._$_]+sojson.$$$_+"\\"+sojson.__$+sojson.$_$+sojson.$$_+"\\"+sojson.__$+sojson.$__+sojson.$$$+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+"-\\"+sojson.__$+sojson.$_$+sojson.__$+"."+sojson.$_$$+"\\"+sojson.__$+sojson.$$$+sojson.__$+sojson.__+sojson.$$$_+"\\"+sojson.__$+sojson.$$_+sojson._$$+"\\"+sojson.__$+sojson._$_+sojson._$_+sojson.$$$_+sojson.$_$_+sojson.$$_$+")&&("+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$$_+sojson._$$+"."+sojson.$$_$+"\\"+sojson.__$+sojson.$$_+sojson.___+"+\\"+sojson.$$$+sojson.$_$+"\\"+sojson.__$+sojson.$_$+sojson.$$_+",\\"+sojson.__$+sojson.$_$+sojson.__$+"."+sojson.$_$$+"\\"+sojson.__$+sojson.$$$+sojson.__$+sojson.__+sojson.$$$_+"\\"+sojson.__$+sojson.$$_+sojson._$$+"\\"+sojson.__$+sojson._$_+sojson._$_+sojson.$$$_+sojson.$_$_+sojson.$$_$+"+\\"+sojson.$$$+sojson.$_$+"\\"+sojson.__$+sojson.$_$+sojson.$$_+")}"+sojson.$$$_+"."+sojson.$_$$+"\\"+sojson.__$+sojson.$$$+sojson.__$+sojson.__+sojson.$$$_+"\\"+sojson.__$+sojson.$$_+sojson._$$+"\\"+sojson.__$+sojson._$_+sojson._$_+sojson.$$$_+sojson.$_$_+sojson.$$_$+"+\\"+sojson.$$$+sojson.$_$+"\\"+sojson.__$+sojson.$_$+sojson.__$+"."+sojson.$_$$+"\\"+sojson.__$+sojson.$$$+sojson.__$+sojson.__+sojson.$$$_+"\\"+sojson.__$+sojson.$$_+sojson._$$+"\\"+sojson.__$+sojson._$_+sojson._$_+sojson.$$$_+sojson.$_$_+sojson.$$_$+"}},"+sojson.$$$_+".\\"+sojson.__$+sojson.$$_+sojson.___+"\\"+sojson.__$+sojson.$$_+sojson._$_+sojson._$+sojson.__+sojson._$+sojson.__+"\\"+sojson.__$+sojson.$$$+sojson.__$+"\\"+sojson.__$+sojson.$$_+sojson.___+sojson.$$$_+"."+sojson.$$_$+sojson.$$$_+sojson.$$__+sojson._$+sojson.$$_$+sojson.$$$_+"\\"+sojson.__$+sojson.___+sojson.$__+sojson.$_$_+sojson.__+sojson.$_$_+"\\"+sojson.$$$+sojson.$_$+sojson.$$$$+sojson._+"\\"+sojson.__$+sojson.$_$+sojson.$$_+sojson.$$__+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.__$+sojson._$+"\\"+sojson.__$+sojson.$_$+sojson.$$_+"("+sojson.__+","+sojson.$$$_+"){\\"+sojson.__$+sojson.$$_+sojson.$$_+sojson.$_$_+"\\"+sojson.__$+sojson.$$_+sojson._$_+" \\"+sojson.__$+sojson.$$_+sojson._$$+"\\"+sojson.$$$+sojson.$_$+sojson.__+"."+sojson.$_$$+"\\"+sojson.__$+sojson.$$$+sojson.__$+sojson.__+sojson.$$$_+"\\"+sojson.__$+sojson.__$+sojson.$__+sojson.$$$_+"\\"+sojson.__$+sojson.$_$+sojson.$$_+"\\"+sojson.__$+sojson.$__+sojson.$$$+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+"\\"+sojson.$$$+sojson._$$+"\\"+sojson.__$+sojson.$_$+sojson.__$+sojson.$$$$+"("+sojson.___+"\\"+sojson.$$$+sojson.$_$+"\\"+sojson.$$$+sojson.$_$+"\\"+sojson.$$$+sojson.$_$+"\\"+sojson.__$+sojson.$$_+sojson._$$+"||\\"+sojson.__$+sojson.$$_+sojson._$$+"%"+sojson.$__+"!\\"+sojson.$$$+sojson.$_$+sojson.___+")\\"+sojson.__$+sojson.$$_+sojson._$_+sojson.$$$_+sojson.__+sojson._+"\\"+sojson.__$+sojson.$$_+sojson._$_+"\\"+sojson.__$+sojson.$_$+sojson.$$_+"!"+sojson.__$+"\\"+sojson.$$$+sojson._$$+sojson.$$$$+sojson._$+"\\"+sojson.__$+sojson.$$_+sojson._$_+"(\\"+sojson.__$+sojson.$$_+sojson.$$_+sojson.$_$_+"\\"+sojson.__$+sojson.$$_+sojson._$_+" \\"+sojson.__$+sojson.$_$+sojson.__$+",\\"+sojson.__$+sojson.$_$+sojson.$$_+"\\"+sojson.$$$+sojson.$_$+"\\"+sojson.__$+sojson.$_$+sojson.$$_+sojson.$$$_+"\\"+sojson.__$+sojson.$$_+sojson.$$$+" \\"+sojson.__$+sojson.___+sojson.$__+sojson.$_$_+sojson.__+sojson.$_$_+"\\"+sojson.__$+sojson._$_+sojson.$$_+"\\"+sojson.__$+sojson.$_$+sojson.__$+sojson.$$$_+"\\"+sojson.__$+sojson.$$_+sojson.$$$+"("+sojson.$$$_+"."+sojson.$_$$+sojson._+sojson.$$$$+sojson.$$$$+sojson.$$$_+"\\"+sojson.__$+sojson.$$_+sojson._$_+","+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$$_+sojson._$$+"."+sojson.$$_$+sojson.$_$_+sojson.__+sojson.$_$_+"\\"+sojson.__$+sojson.__$+sojson.$$$+sojson.$$$$+sojson.$$$$+"\\"+sojson.__$+sojson.$$_+sojson._$$+sojson.$$$_+sojson.__+","+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$$_+sojson._$$+"."+sojson.$$_$+sojson.$_$_+sojson.__+sojson.$_$_+"\\"+sojson.__$+sojson.___+sojson._$_+"\\"+sojson.__$+sojson.$$$+sojson.__$+sojson.__+sojson.$$$_+"\\"+sojson.__$+sojson.$$_+sojson._$$+"\\"+sojson.__$+sojson.__$+sojson.$__+sojson.$$$_+"\\"+sojson.__$+sojson.$_$+sojson.$$_+"\\"+sojson.__$+sojson.$__+sojson.$$$+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+"),"+sojson.$_$_+"\\"+sojson.$$$+sojson.$_$+"\\"+sojson.__$+sojson.$_$+sojson.$$_+sojson.$$$_+"\\"+sojson.__$+sojson.$$_+sojson.$$$+" \\"+sojson.__$+sojson.___+sojson.$__+sojson.$_$_+sojson.__+sojson.$_$_+"\\"+sojson.__$+sojson._$_+sojson.$$_+"\\"+sojson.__$+sojson.$_$+sojson.__$+sojson.$$$_+"\\"+sojson.__$+sojson.$$_+sojson.$$$+"("+sojson.__+"."+sojson.$_$$+sojson._+sojson.$$$$+sojson.$$$$+sojson.$$$_+"\\"+sojson.__$+sojson.$$_+sojson._$_+","+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$$_+sojson._$$+".\\"+sojson.__$+sojson.$_$+sojson._$$+sojson.$$$_+"\\"+sojson.__$+sojson.$$$+sojson.__$+"\\"+sojson.__$+sojson.__$+sojson.$$$+sojson.$$$$+sojson.$$$$+"\\"+sojson.__$+sojson.$$_+sojson._$$+sojson.$$$_+sojson.__+","+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$$_+sojson._$$+".\\"+sojson.__$+sojson.$_$+sojson._$$+sojson.$$$_+"\\"+sojson.__$+sojson.$$$+sojson.__$+"\\"+sojson.__$+sojson.___+sojson._$_+"\\"+sojson.__$+sojson.$$$+sojson.__$+sojson.__+sojson.$$$_+"\\"+sojson.__$+sojson.$$_+sojson._$$+"\\"+sojson.__$+sojson.__$+sojson.$__+sojson.$$$_+"\\"+sojson.__$+sojson.$_$+sojson.$$_+"\\"+sojson.__$+sojson.$__+sojson.$$$+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+"),\\"+sojson.__$+sojson.$_$+sojson.___+"\\"+sojson.$$$+sojson.$_$+sojson.___+",\\"+sojson.__$+sojson.$$_+sojson._$_+"\\"+sojson.$$$+sojson.$_$+sojson.$$$_+"."+sojson.$_$$+"\\"+sojson.__$+sojson.$$$+sojson.__$+sojson.__+sojson.$$$_+"\\"+sojson.__$+sojson.__$+sojson.$__+sojson.$$$_+"\\"+sojson.__$+sojson.$_$+sojson.$$_+"\\"+sojson.__$+sojson.$__+sojson.$$$+sojson.__+"\\"+sojson.__$+sojson.$_$+sojson.___+","+sojson.$$_$+"\\"+sojson.$$$+sojson.$_$+"\\"+sojson.__$+sojson.$$_+sojson._$_+"-\\"+sojson.__$+sojson.$$_+sojson._$_+"%"+sojson.$___+",\\"+sojson.__$+sojson.$$$+sojson.__$+"\\"+sojson.$$$+sojson.$_$+"\\"+sojson.__$+sojson.$$_+sojson._$$+","+sojson._$+"\\"+sojson.$$$+sojson.$_$+sojson.$___+"\\"+sojson.$$$+sojson._$$+"\\"+sojson.__$+sojson.$_$+sojson.___+"\\"+sojson.$$$+sojson.$__+sojson.$$_$+"\\"+sojson.$$$+sojson._$$+")"+sojson.$$$$+sojson._$+"\\"+sojson.__$+sojson.$$_+sojson._$_+"(\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.$$$+sojson.$_$+sojson._$+"\\"+sojson.$$$+sojson.$_$+"("+sojson._$+"+"+sojson.$___+")%"+sojson._$_+sojson.$__+"\\"+sojson.$$$+sojson._$$+"\\"+sojson.__$+sojson.$_$+sojson.___+"\\"+sojson.$$$+sojson.$__+sojson.$$_$+"&&\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.$$$+sojson.$__+"\\"+sojson.__$+sojson.$$$+sojson.__$+"\\"+sojson.$$$+sojson._$$+")\\"+sojson.__$+sojson.$_$+sojson.$$_+".\\"+sojson.__$+sojson.$$_+sojson._$$+sojson.$$$_+sojson.__+"\\"+sojson.__$+sojson._$_+sojson.$_$+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$_$+sojson.$$_+sojson.__+sojson._$$+sojson._$_+"(\\"+sojson.__$+sojson.$_$+sojson.___+",\\"+sojson.__$+sojson.$_$+sojson.$$_+".\\"+sojson.__$+sojson.$__+sojson.$$$+sojson.$$$_+sojson.__+"\\"+sojson.__$+sojson._$_+sojson.$_$+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$_$+sojson.$$_+sojson.__+sojson._$$+sojson._$_+"(\\"+sojson.__$+sojson.$_$+sojson.___+",!"+sojson.___+")^"+sojson.$_$_+".\\"+sojson.__$+sojson.$__+sojson.$$$+sojson.$$$_+sojson.__+"\\"+sojson.__$+sojson._$_+sojson.$_$+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$_$+sojson.$$_+sojson.__+sojson._$$+sojson._$_+"(\\"+sojson.__$+sojson.$_$+sojson.__$+",!"+sojson.___+"),!"+sojson.___+"),\\"+sojson.__$+sojson.$_$+sojson.$$_+".\\"+sojson.__$+sojson.$$_+sojson._$$+sojson.$$$_+sojson.__+"\\"+sojson.__$+sojson._$_+sojson.$_$+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$_$+sojson.$$_+sojson.__+sojson._$$+sojson._$_+"(\\"+sojson.__$+sojson.$_$+sojson.___+"+"+sojson.$__+",\\"+sojson.__$+sojson.$_$+sojson.$$_+".\\"+sojson.__$+sojson.$__+sojson.$$$+sojson.$$$_+sojson.__+"\\"+sojson.__$+sojson._$_+sojson.$_$+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$_$+sojson.$$_+sojson.__+sojson._$$+sojson._$_+"(\\"+sojson.__$+sojson.$_$+sojson.___+"+"+sojson.$__+",!"+sojson.___+")^"+sojson.$_$_+".\\"+sojson.__$+sojson.$__+sojson.$$$+sojson.$$$_+sojson.__+"\\"+sojson.__$+sojson._$_+sojson.$_$+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$_$+sojson.$$_+sojson.__+sojson._$$+sojson._$_+"(\\"+sojson.__$+sojson.$_$+sojson.__$+"+"+sojson.$__+",!"+sojson.___+"),!"+sojson.___+"),\\"+sojson.__$+sojson.$_$+sojson.___+"+\\"+sojson.$$$+sojson.$_$+sojson.$___+",\\"+sojson.__$+sojson.$_$+sojson.__$+"+\\"+sojson.$$$+sojson.$_$+sojson._$_+sojson.$__+"\\"+sojson.$$$+sojson._$$+"\\"+sojson.__$+sojson.$_$+sojson.__$+sojson.$$$$+"(\\"+sojson.__$+sojson.$_$+sojson.___+"\\"+sojson.$$$+sojson.$__+"\\"+sojson.__$+sojson.$$_+sojson._$_+")"+sojson.$$$$+sojson._$+"\\"+sojson.__$+sojson.$$_+sojson._$_+"(\\"+sojson.__$+sojson.$$$+sojson.__$+"\\"+sojson.$$$+sojson.$__+"\\"+sojson.$$$+sojson.$_$+"\\"+sojson.__$+sojson.$_$+sojson.__$+"&&(\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.$$$+sojson.$_$+sojson._$+"\\"+sojson.$$$+sojson.$_$+"("+sojson._$+"+"+sojson.$___+")%"+sojson._$_+sojson.$__+")\\"+sojson.$$$+sojson._$$+"\\"+sojson.__$+sojson.$_$+sojson.___+"\\"+sojson.$$$+sojson.$__+"\\"+sojson.__$+sojson.$$_+sojson._$_+"\\"+sojson.$$$+sojson._$$+")\\"+sojson.__$+sojson.$_$+sojson.$$_+".\\"+sojson.__$+sojson.$$_+sojson._$$+sojson.$$$_+sojson.__+"\\"+sojson.__$+sojson._$_+sojson.$_$+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$_$+sojson.$$_+sojson.__+sojson.$___+"(\\"+sojson.__$+sojson.$_$+sojson.___+",\\"+sojson.__$+sojson.$_$+sojson.$$_+".\\"+sojson.__$+sojson.$__+sojson.$$$+sojson.$$$_+sojson.__+"\\"+sojson.__$+sojson._$_+sojson.$_$+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$_$+sojson.$$_+sojson.__+sojson.$___+"(\\"+sojson.__$+sojson.$_$+sojson.___+")^"+sojson.$_$_+".\\"+sojson.__$+sojson.$__+sojson.$$$+sojson.$$$_+sojson.__+"\\"+sojson.__$+sojson._$_+sojson.$_$+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$_$+sojson.$$_+sojson.__+sojson.$___+"(\\"+sojson.__$+sojson.$_$+sojson.__$+")),\\"+sojson.__$+sojson.$_$+sojson.___+"++,\\"+sojson.__$+sojson.$_$+sojson.__$+"++\\"+sojson.$$$+sojson._$$+"\\"+sojson.__$+sojson.$$_+sojson._$_+sojson.$$$_+sojson.__+sojson._+"\\"+sojson.__$+sojson.$$_+sojson._$_+"\\"+sojson.__$+sojson.$_$+sojson.$$_+"!"+sojson.___+"},"+sojson.__+".\\"+sojson.__$+sojson.___+sojson._$$+"\\"+sojson.__$+sojson.$$_+sojson._$_+"\\"+sojson.__$+sojson.$$$+sojson.__$+"\\"+sojson.__$+sojson.$$_+sojson.___+sojson.__+sojson._$+"\\"+sojson.__$+sojson._$_+sojson._$_+sojson.$$$_+"\\"+sojson.__$+sojson.$$_+sojson._$$+sojson._$+sojson._+"\\"+sojson.__$+sojson.$$_+sojson._$_+sojson.$$__+sojson.$$$_+"\\"+sojson.__$+sojson.__$+sojson.$__+sojson._$+sojson.$_$_+sojson.$$_$+sojson.$$$_+"\\"+sojson.__$+sojson.$$_+sojson._$_+"\\"+sojson.$$$+sojson.$_$+sojson.$$$_+",\\"+sojson.__$+sojson.__$+sojson.$$$+sojson.$_$$+"\\"+sojson.__$+sojson.$_$+sojson._$_+sojson.$$$_+sojson.$$__+sojson.__+"."+sojson.$$_$+sojson.$$$_+sojson.$$$$+"\\"+sojson.__$+sojson.$_$+sojson.__$+"\\"+sojson.__$+sojson.$_$+sojson.$$_+sojson.$$$_+"\\"+sojson.__$+sojson._$_+sojson.___+"\\"+sojson.__$+sojson.$$_+sojson._$_+sojson._$+"\\"+sojson.__$+sojson.$$_+sojson.___+sojson.$$$_+"\\"+sojson.__$+sojson.$$_+sojson._$_+sojson.__+"\\"+sojson.__$+sojson.$$$+sojson.__$+"("+sojson.__+",\\\"__"+sojson.$$$_+"\\"+sojson.__$+sojson.$$_+sojson._$$+"\\"+sojson.__$+sojson.__$+sojson.$_$+sojson._$+sojson.$$_$+sojson._+(![]+"")[sojson._$_]+sojson.$$$_+"\\\",{\\"+sojson.__$+sojson.$$_+sojson.$$_+sojson.$_$_+(![]+"")[sojson._$_]+sojson._+sojson.$$$_+"\\"+sojson.$$$+sojson._$_+"!"+sojson.___+"})})\\"+sojson.$$$+sojson._$$+"\\"+sojson.__$+sojson._$_+"\"")())();
(function () {
/**
 * <AUTHOR> McCurdy / https://www.donmccurdy.com
 */

let init, instance, heap;

const importObject = {

	env: {

		emscripten_notify_memory_growth: function ( index ) {

			heap = new Uint8Array( instance.exports.memory.buffer );

		}

	}

}

/**
 * ZSTD (Zstandard) decoder.
 *
 * Compiled from https://github.com/facebook/zstd/tree/dev/contrib/single_file_libs, with the
 * following steps:
 *
 * ```
 * ./combine.sh -r ../../lib -o zstddeclib.c zstddeclib-in.c
 * emcc zstddeclib.c -Oz -s EXPORTED_FUNCTIONS="['_ZSTD_decompress', '_ZSTD_findDecompressedSize', '_ZSTD_isError', '_malloc', '_free']" -s ALLOW_MEMORY_GROWTH=1 -s MALLOC=emmalloc -o zstddec.wasm
 * base64 zstddec.wasm > zstddec.txt
 * ```
 *
 * The base64 string written to `zstddec.txt` is embedded as the `wasm` variable at the bottom
 * of this file. The rest of this file is written by hand, in order to avoid an additional JS
 * wrapper generated by Emscripten.
 */
// export class ZSTDDecoder {
	class ZSTDDecoder {

	init () {

		if ( ! init ) {

			init = fetch( 'data:application/wasm;base64,' + wasm )
				.then( ( response ) => response.arrayBuffer() )
				.then( ( arrayBuffer ) => WebAssembly.instantiate( arrayBuffer, importObject ) )
				.then( ( result ) => {

					instance = result.instance;

					importObject.env.emscripten_notify_memory_growth( 0 ); // initialize heap.

				});

		}

		return init;

	}

	decode ( array, uncompressedSize = 0 ) {

		// Write compressed data into WASM memory.
		const compressedSize = array.byteLength;
		const compressedPtr = instance.exports.malloc( compressedSize );
		heap.set( array, compressedPtr );

		// Decompress into WASM memory.
		uncompressedSize = uncompressedSize || Number( instance.exports.ZSTD_findDecompressedSize( compressedPtr, compressedSize ) );
		const uncompressedPtr = instance.exports.malloc( uncompressedSize );
		const actualSize = instance.exports.ZSTD_decompress( uncompressedPtr, uncompressedSize, compressedPtr, compressedSize );

		// Read decompressed data and free WASM memory.
		const dec = heap.slice( uncompressedPtr, uncompressedPtr + actualSize );
		instance.exports.free( compressedPtr );
		instance.exports.free( uncompressedPtr );

		return dec;

	}

}

window.ZSTDDecoder = ZSTDDecoder;

/**
 * BSD License
 *
 * For Zstandard software
 *
 * Copyright (c) 2016-present, Yann Collet, Facebook, Inc. All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without modification,
 * are permitted provided that the following conditions are met:
 *
 *  * Redistributions of source code must retain the above copyright notice, this
 *    list of conditions and the following disclaimer.
 *
 *  * Redistributions in binary form must reproduce the above copyright notice,
 *    this list of conditions and the following disclaimer in the documentation
 *    and/or other materials provided with the distribution.
 *
 *  * Neither the name Facebook nor the names of its contributors may be used to
 *    endorse or promote products derived from this software without specific
 *    prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
 * ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR
 * ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
 * LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON
 * ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
 * SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */
const wasm = '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';

})();
(function () {
const t=new Uint8Array([0]),e=[171,75,84,88,32,50,48,187,13,10,26,10];var n,i,s,a,r,o,l,f;!function(t){t[t.NONE=0]="NONE",t[t.BASISLZ=1]="BASISLZ",t[t.ZSTD=2]="ZSTD",t[t.ZLIB=3]="ZLIB"}(n||(n={})),function(t){t[t.BASICFORMAT=0]="BASICFORMAT"}(i||(i={})),function(t){t[t.UNSPECIFIED=0]="UNSPECIFIED",t[t.ETC1S=163]="ETC1S",t[t.UASTC=166]="UASTC"}(s||(s={})),function(t){t[t.UNSPECIFIED=0]="UNSPECIFIED",t[t.SRGB=1]="SRGB"}(a||(a={})),function(t){t[t.UNSPECIFIED=0]="UNSPECIFIED",t[t.LINEAR=1]="LINEAR",t[t.SRGB=2]="SRGB",t[t.ITU=3]="ITU",t[t.NTSC=4]="NTSC",t[t.SLOG=5]="SLOG",t[t.SLOG2=6]="SLOG2"}(r||(r={})),function(t){t[t.ALPHA_STRAIGHT=0]="ALPHA_STRAIGHT",t[t.ALPHA_PREMULTIPLIED=1]="ALPHA_PREMULTIPLIED"}(o||(o={})),function(t){t[t.RGB=0]="RGB",t[t.RRR=3]="RRR",t[t.GGG=4]="GGG",t[t.AAA=15]="AAA"}(l||(l={})),function(t){t[t.RGB=0]="RGB",t[t.RGBA=3]="RGBA",t[t.RRR=4]="RRR",t[t.RRRG=5]="RRRG"}(f||(f={}));class U{constructor(){this.vkFormat=0,this.typeSize=1,this.pixelWidth=0,this.pixelHeight=0,this.pixelDepth=0,this.layerCount=0,this.faceCount=1,this.supercompressionScheme=n.NONE,this.levels=[],this.dataFormatDescriptor=[{vendorId:0,descriptorType:i.BASICFORMAT,versionNumber:2,descriptorBlockSize:40,colorModel:s.UNSPECIFIED,colorPrimaries:a.SRGB,transferFunction:a.SRGB,flags:o.ALPHA_STRAIGHT,texelBlockDimension:{x:4,y:4,z:1,w:1},bytesPlane:[],samples:[]}],this.keyValue={},this.globalData=null}}class c{constructor(t,e,n,i){this._dataView=new DataView(t.buffer,t.byteOffset+e,n),this._littleEndian=i,this._offset=0}_nextUint8(){const t=this._dataView.getUint8(this._offset);return this._offset+=1,t}_nextUint16(){const t=this._dataView.getUint16(this._offset,this._littleEndian);return this._offset+=2,t}_nextUint32(){const t=this._dataView.getUint32(this._offset,this._littleEndian);return this._offset+=4,t}_nextUint64(){const t=this._dataView.getUint32(this._offset,this._littleEndian)+2**32*this._dataView.getUint32(this._offset+4,this._littleEndian);return this._offset+=8,t}_skip(t){return this._offset+=t,this}_scan(t,e=0){const n=this._offset;let i=0;for(;this._dataView.getUint8(this._offset)!==e&&i<t;)i++,this._offset++;return i<t&&this._offset++,new Uint8Array(this._dataView.buffer,this._dataView.byteOffset+n,i)}}function h(t){return"undefined"!=typeof TextEncoder?(new TextEncoder).encode(t):Buffer.from(t)}function _(t){return"undefined"!=typeof TextDecoder?(new TextDecoder).decode(t):Buffer.from(t).toString("utf8")}function g(t){let e=0;for(const n of t)e+=n.byteLength;const n=new Uint8Array(e);let i=0;for(const e of t)n.set(new Uint8Array(e),i),i+=e.byteLength;return n}function p(t){const n=new Uint8Array(t.buffer,t.byteOffset,e.length);if(n[0]!==e[0]||n[1]!==e[1]||n[2]!==e[2]||n[3]!==e[3]||n[4]!==e[4]||n[5]!==e[5]||n[6]!==e[6]||n[7]!==e[7]||n[8]!==e[8]||n[9]!==e[9]||n[10]!==e[10]||n[11]!==e[11])throw new Error("Missing KTX 2.0 identifier.");const i=new U,s=17*Uint32Array.BYTES_PER_ELEMENT,a=new c(t,e.length,s,!0);i.vkFormat=a._nextUint32(),i.typeSize=a._nextUint32(),i.pixelWidth=a._nextUint32(),i.pixelHeight=a._nextUint32(),i.pixelDepth=a._nextUint32(),i.layerCount=a._nextUint32(),i.faceCount=a._nextUint32();const r=a._nextUint32();i.supercompressionScheme=a._nextUint32();const o=a._nextUint32(),l=a._nextUint32(),f=a._nextUint32(),h=a._nextUint32(),g=a._nextUint64(),p=a._nextUint64(),x=new c(t,e.length+s,3*r*8,!0);for(let e=0;e<r;e++)i.levels.push({levelData:new Uint8Array(t.buffer,t.byteOffset+x._nextUint64(),x._nextUint64()),uncompressedByteLength:x._nextUint64()});const u=new c(t,o,l,!0),y={vendorId:u._skip(4)._nextUint16(),descriptorType:u._nextUint16(),versionNumber:u._nextUint16(),descriptorBlockSize:u._nextUint16(),colorModel:u._nextUint8(),colorPrimaries:u._nextUint8(),transferFunction:u._nextUint8(),flags:u._nextUint8(),texelBlockDimension:{x:u._nextUint8()+1,y:u._nextUint8()+1,z:u._nextUint8()+1,w:u._nextUint8()+1},bytesPlane:[u._nextUint8(),u._nextUint8(),u._nextUint8(),u._nextUint8(),u._nextUint8(),u._nextUint8(),u._nextUint8(),u._nextUint8()],samples:[]},D=(y.descriptorBlockSize/4-6)/4;for(let t=0;t<D;t++)y.samples[t]={bitOffset:u._nextUint16(),bitLength:u._nextUint8(),channelID:u._nextUint8(),samplePosition:[u._nextUint8(),u._nextUint8(),u._nextUint8(),u._nextUint8()],sampleLower:u._nextUint32(),sampleUpper:u._nextUint32()};i.dataFormatDescriptor.length=0,i.dataFormatDescriptor.push(y);const b=new c(t,f,h,!0);for(;b._offset<h;){const t=b._nextUint32(),e=b._scan(t),n=_(e),s=b._scan(t-e.byteLength);i.keyValue[n]=n.match(/^ktx/i)?_(s):s,b._offset%4&&b._skip(4-b._offset%4)}if(p<=0)return i;const d=new c(t,g,p,!0),B=d._nextUint16(),w=d._nextUint16(),A=d._nextUint32(),S=d._nextUint32(),m=d._nextUint32(),L=d._nextUint32(),I=[];for(let t=0;t<r;t++)I.push({imageFlags:d._nextUint32(),rgbSliceByteOffset:d._nextUint32(),rgbSliceByteLength:d._nextUint32(),alphaSliceByteOffset:d._nextUint32(),alphaSliceByteLength:d._nextUint32()});const R=g+d._offset,E=R+A,T=E+S,O=T+m,P=new Uint8Array(t.buffer,t.byteOffset+R,A),C=new Uint8Array(t.buffer,t.byteOffset+E,S),F=new Uint8Array(t.buffer,t.byteOffset+T,m),G=new Uint8Array(t.buffer,t.byteOffset+O,L);return i.globalData={endpointCount:B,selectorCount:w,imageDescs:I,endpointsData:P,selectorsData:C,tablesData:F,extendedData:G},i}function x(){return(x=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(t[i]=n[i])}return t}).apply(this,arguments)}const u={keepWriter:!1};function y(n,s={}){s=x({},u,s);let a=new ArrayBuffer(0);if(n.globalData){const t=new ArrayBuffer(20+5*n.globalData.imageDescs.length*4),e=new DataView(t);e.setUint16(0,n.globalData.endpointCount,!0),e.setUint16(2,n.globalData.selectorCount,!0),e.setUint32(4,n.globalData.endpointsData.byteLength,!0),e.setUint32(8,n.globalData.selectorsData.byteLength,!0),e.setUint32(12,n.globalData.tablesData.byteLength,!0),e.setUint32(16,n.globalData.extendedData.byteLength,!0);for(let t=0;t<n.globalData.imageDescs.length;t++){const i=n.globalData.imageDescs[t];e.setUint32(20+5*t*4+0,i.imageFlags,!0),e.setUint32(20+5*t*4+4,i.rgbSliceByteOffset,!0),e.setUint32(20+5*t*4+8,i.rgbSliceByteLength,!0),e.setUint32(20+5*t*4+12,i.alphaSliceByteOffset,!0),e.setUint32(20+5*t*4+16,i.alphaSliceByteLength,!0)}a=g([t,n.globalData.endpointsData,n.globalData.selectorsData,n.globalData.tablesData,n.globalData.extendedData])}const r=[];let o=n.keyValue;s.keepWriter||(o=x({},n.keyValue,{KTXwriter:"KTX-Parse v0.0.5"}));for(const e in o){const n=o[e],i=h(e),s="string"==typeof n?h(n):n,a=i.byteLength+1+s.byteLength+1,l=a%4?4-a%4:0;r.push(g([new Uint32Array([a]),i,t,s,t,new Uint8Array(l).fill(0)]))}const l=g(r),f=new ArrayBuffer(44),U=new DataView(f);if(1!==n.dataFormatDescriptor.length||n.dataFormatDescriptor[0].descriptorType!==i.BASICFORMAT)throw new Error("Only BASICFORMAT Data Format Descriptor output supported.");const c=n.dataFormatDescriptor[0];U.setUint32(0,44,!0),U.setUint16(4,c.vendorId,!0),U.setUint16(6,c.descriptorType,!0),U.setUint16(8,c.versionNumber,!0),U.setUint16(10,c.descriptorBlockSize,!0),U.setUint8(12,c.colorModel),U.setUint8(13,c.colorPrimaries),U.setUint8(14,c.transferFunction),U.setUint8(15,c.flags),U.setUint8(16,c.texelBlockDimension.x-1),U.setUint8(17,c.texelBlockDimension.y-1),U.setUint8(18,c.texelBlockDimension.z-1),U.setUint8(19,c.texelBlockDimension.w-1);for(let t=0;t<8;t++)U.setUint8(20+t,c.bytesPlane[t]);for(let t=0;t<c.samples.length;t++){const e=c.samples[t],n=28+16*t;U.setUint16(n+0,e.bitOffset,!0),U.setUint8(n+2,e.bitLength),U.setUint8(n+3,e.channelID),U.setUint8(n+4,e.samplePosition[0]),U.setUint8(n+5,e.samplePosition[1]),U.setUint8(n+6,e.samplePosition[2]),U.setUint8(n+7,e.samplePosition[3]),U.setUint32(n+8,e.sampleLower,!0),U.setUint32(n+12,e.sampleUpper,!0)}const _=e.length+68+3*n.levels.length*8,p=_+f.byteLength;let y=p+l.byteLength;y%8&&(y+=8-y%8);const D=[],b=new DataView(new ArrayBuffer(3*n.levels.length*8));let d=y+a.byteLength;for(let t=0;t<n.levels.length;t++){const e=n.levels[t];D.push(e.levelData),b.setBigUint64(24*t+0,BigInt(d),!0),b.setBigUint64(24*t+8,BigInt(e.levelData.byteLength),!0),b.setBigUint64(24*t+16,BigInt(e.uncompressedByteLength),!0),d+=e.levelData.byteLength}const B=new ArrayBuffer(68),w=new DataView(B);return w.setUint32(0,n.vkFormat,!0),w.setUint32(4,n.typeSize,!0),w.setUint32(8,n.pixelWidth,!0),w.setUint32(12,n.pixelHeight,!0),w.setUint32(16,n.pixelDepth,!0),w.setUint32(20,n.layerCount,!0),w.setUint32(24,n.faceCount,!0),w.setUint32(28,n.levels.length,!0),w.setUint32(32,n.supercompressionScheme,!0),w.setUint32(36,_,!0),w.setUint32(40,f.byteLength,!0),w.setUint32(44,p,!0),w.setUint32(48,l.byteLength,!0),w.setBigUint64(52,BigInt(y),!0),w.setBigUint64(60,BigInt(a.byteLength),!0),new Uint8Array(g([new Uint8Array(e).buffer,B,b.buffer,f,l,new ArrayBuffer(y-(p+l.byteLength)),a,...D]))} var KTX2Container = U;window.readKTX = p;var write = y;//export{U as KTX2Container,p as read,y as write};
})();