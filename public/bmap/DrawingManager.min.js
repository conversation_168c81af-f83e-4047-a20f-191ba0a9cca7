var BMapGLLib=window.BMapGLLib=BMapGLLib||{};var BMAP_DRAWING_MARKER="marker",BMAP_DRAWING_POLYLINE="polyline",BMAP_DRAWING_CIRCLE="circle",BMAP_DRAWING_RECTANGLE="rectangle",BMAP_DRAWING_POLYGON="polygon";(function(){var d=d||{guid:"$BAIDU$"};(function(){window[d.guid]={};d.extend=function(n,l){for(var m in l){if(l.hasOwnProperty(m)){n[m]=l[m]}}return n};d.lang=d.lang||{};d.lang.guid=function(){return"TANGRAM__"+(window[d.guid]._counter++).toString(36)};window[d.guid]._counter=window[d.guid]._counter||1;window[d.guid]._instances=window[d.guid]._instances||{};d.lang.Class=function(l){this.guid=l||d.lang.guid();window[d.guid]._instances[this.guid]=this};window[d.guid]._instances=window[d.guid]._instances||{};d.lang.isString=function(l){return"[object String]"==Object.prototype.toString.call(l)};d.lang.isFunction=function(l){return"[object Function]"==Object.prototype.toString.call(l)};d.lang.Class.prototype.toString=function(){return"[object "+(this._className||"Object")+"]"};d.lang.Class.prototype.dispose=function(){delete window[d.guid]._instances[this.guid];for(var l in this){if(!d.lang.isFunction(this[l])){delete this[l]}}this.disposed=true};d.lang.Event=function(l,m){this.type=l;this.returnValue=true;this.target=m||null;this.currentTarget=null};d.lang.Class.prototype.addEventListener=function(o,n,m){if(!d.lang.isFunction(n)){return}!this.__listeners&&(this.__listeners={});var l=this.__listeners,p;if(typeof m=="string"&&m){if(/[^\w\-]/.test(m)){throw ("nonstandard key:"+m)}else{n.hashCode=m;p=m}}o.indexOf("on")!=0&&(o="on"+o);typeof l[o]!="object"&&(l[o]={});p=p||d.lang.guid();n.hashCode=p;l[o][p]=n};d.lang.Class.prototype.removeEventListener=function(n,m){if(d.lang.isFunction(m)){m=m.hashCode}else{if(!d.lang.isString(m)){return}}!this.__listeners&&(this.__listeners={});n.indexOf("on")!=0&&(n="on"+n);var l=this.__listeners;if(!l[n]){return}l[n][m]&&delete l[n][m]};d.lang.Class.prototype.dispatchEvent=function(o,l){if(d.lang.isString(o)){o=new d.lang.Event(o)}!this.__listeners&&(this.__listeners={});l=l||{};for(var n in l){o[n]=l[n]}var n,m=this.__listeners,q=o.type;o.target=o.target||this;o.currentTarget=this;q.indexOf("on")!=0&&(q="on"+q);d.lang.isFunction(this[q])&&this[q].apply(this,arguments);if(typeof m[q]=="object"){for(n in m[q]){m[q][n].apply(this,arguments)}}return o.returnValue};d.lang.inherits=function(r,p,o){var n,q,l=r.prototype,m=new Function();m.prototype=p.prototype;q=r.prototype=new m();for(n in l){q[n]=l[n]}r.prototype.constructor=r;r.superClass=p.prototype;if("string"==typeof o){q._className=o}};d.dom=d.dom||{};d._g=d.dom._g=function(l){if(d.lang.isString(l)){return document.getElementById(l)}return l};d.g=d.dom.g=function(l){if("string"==typeof l||l instanceof String){return document.getElementById(l)}else{if(l&&l.nodeName&&(l.nodeType==1||l.nodeType==9)){return l}}return null};d.insertHTML=d.dom.insertHTML=function(o,l,n){o=d.dom.g(o);var m,p;if(o.insertAdjacentHTML){o.insertAdjacentHTML(l,n)}else{m=o.ownerDocument.createRange();l=l.toUpperCase();if(l=="AFTERBEGIN"||l=="BEFOREEND"){m.selectNodeContents(o);m.collapse(l=="AFTERBEGIN")}else{p=l=="BEFOREBEGIN";m[p?"setStartBefore":"setEndAfter"](o);m.collapse(p)}m.insertNode(m.createContextualFragment(n))}return o};d.ac=d.dom.addClass=function(r,s){r=d.dom.g(r);var n=s.split(/\s+/),m=r.className,q=" "+m+" ",p=0,o=n.length;for(;p<o;p++){if(q.indexOf(" "+n[p]+" ")<0){m+=(m?" ":"")+n[p]}}r.className=m;return r};d.event=d.event||{};d.event._listeners=d.event._listeners||[];d.on=d.event.on=function(m,p,r){p=p.replace(/^on/i,"");m=d._g(m);var q=function(t){r.call(m,t)},l=d.event._listeners,o=d.event._eventFilter,s,n=p;p=p.toLowerCase();if(o&&o[p]){s=o[p](m,p,q);n=s.type;q=s.listener}if(m.addEventListener){m.addEventListener(n,q,false)}else{if(m.attachEvent){m.attachEvent("on"+n,q)}}l[l.length]=[m,p,r,q,n];return m};d.un=d.event.un=function(n,q,m){n=d._g(n);q=q.replace(/^on/i,"").toLowerCase();var t=d.event._listeners,o=t.length,p=!m,s,r,l;while(o--){s=t[o];if(s[1]===q&&s[0]===n&&(p||s[2]===m)){r=s[4];l=s[3];if(n.removeEventListener){n.removeEventListener(r,l,false)}else{if(n.detachEvent){n.detachEvent("on"+r,l)}}t.splice(o,1)}}return n};d.getEvent=d.event.getEvent=function(l){return window.event||l};d.getTarget=d.event.getTarget=function(l){var l=d.getEvent(l);return l.target||l.srcElement};d.preventDefault=d.event.preventDefault=function(l){var l=d.getEvent(l);if(l.preventDefault){l.preventDefault()}else{l.returnValue=false}};d.stopBubble=d.event.stopBubble=function(l){l=d.getEvent(l);l.stopPropagation?l.stopPropagation():l.cancelBubble=true};d.browser=d.browser||{};if(/msie (\d+\.\d)/i.test(navigator.userAgent)){d.browser.ie=d.ie=document.documentMode||+RegExp["\x241"]}})();var i=BMapGLLib.DrawingManager=function(m,l){if(!m){return}a.push(this);l=l||{};this.overlays=[];this._initialize(m,l)};d.lang.inherits(i,d.lang.Class,"DrawingManager");i.prototype.open=function(){if(this._isOpen==true){return true}e(this);this._open()};i.prototype.close=function(){if(this._isOpen==false){return true
}var l=this;this._close();l._map.removeOverlay(b);setTimeout(function(){l._map.enableDoubleClickZoom()},2000)};i.prototype.setDrawingMode=function(l){if(this._drawingType!=l){e(this);this._setDrawingMode(l)}};i.prototype.getDrawingMode=function(){return this._drawingType};i.prototype.enableCalculate=function(){this._enableCalculate=true;this._addGeoUtilsLibrary()};i.prototype.disableCalculate=function(){this._enableCalculate=false};i.prototype.enableSorption=function(){this._enableSorption=true};i.prototype.disableSorption=function(){this._enableSorption=false};i.prototype.enableGpc=function(){this._enableGpc=true;this._addGPCLibrary()};i.prototype.disableGpc=function(){this._enableGpc=false};i.prototype.getOverlays=function(){return this.overlays};i.prototype.addOverlayData=function(l){return this.overlays.push(l)};i.prototype.setOverlaysData=function(l){return this.overlays=l};i.prototype.clearOverlayData=function(l){var n=this._map;for(var m=0;m<this.overlays.length;m++){if(this.overlays[m]===l){this.overlays.splice(m,1);return l}}};i.prototype.clearOverlay=function(l){var m=this._map;var l=this.clearOverlayData(l);if(l){m.removeOverlay(l)}};i.prototype.clearOverlays=function(){var l=this._map;this.overlays.forEach(function(m){l.removeOverlay(m)});this.overlays.length=0};i.prototype._initialize=function(n,m){this._map=n;this._opts=m;this._drawingType=m.drawingMode||BMAP_DRAWING_MARKER;if(m.enableDrawingTool){this.enableDrawingTool()}if(m.sorptionDistance!==undefined){this.setSorptionDistance(m.sorptionDistance)}if(m.enableCalculate===true){this.enableCalculate()}else{this.disableCalculate()}if(m.enableLimit===true){var l=m.limitOptions;this.limit=l}if(m.enableSorption===true){this.enableSorption()}else{this.disableSorption()}if(m.enableGpc===true){this.enableGpc()}else{this.disableGpc()}this._isOpen=!!(m.isOpen===true);if(this._isOpen){this._open()}this.setPolygonOptions(m.polygonOptions);this.setMarkerOptions(m.markerOptions);this.setCircleOptions(m.circleOptions);this.setPolylineOptions(m.polylineOptions);this.setRectangleOptions(m.rectangleOptions);this.setLabelOptions(m.labelOptions);this.controlButton=m.controlButton=="right"?"right":"left"};i.prototype.enableDrawingTool=function(){var l=this._opts;if(!this._drawingTool){var m=new f(this,l.drawingToolOptions);this._drawingTool=m}this._map.addControl(this._drawingTool)};i.prototype.disableDrawingTool=function(){if(this._drawingTool){this._map.removeControl(this._drawingTool)}};i.prototype.setSorptionDistance=function(l){this._sorptionDistance=l||0};i.prototype.setPolygonOptions=function(l){this.polygonOptions=l||{}};i.prototype.setMarkerOptions=function(l){this.markerOptions=l||{}};i.prototype.setCircleOptions=function(l){this.circleOptions=l||{}};i.prototype.setPolylineOptions=function(l){this.polylineOptions=l||{}};i.prototype.setRectangleOptions=function(l){this.rectangleOptions=l||{}};i.prototype.setLabelOptions=function(l){this.labelOptions=l||{}};i.prototype._open=function(){this._isOpen=true;if(!this._mask){this._mask=new g()}this._map.addOverlay(this._mask);this._setDrawingMode(this._drawingType)};i.prototype._setDrawingMode=function(l){this._drawingType=l;if(this._isOpen){this._mask.__listeners={};switch(l){case BMAP_DRAWING_MARKER:this._bindMarker();break;case BMAP_DRAWING_CIRCLE:this._bindCircle();break;case BMAP_DRAWING_POLYLINE:case BMAP_DRAWING_POLYGON:this._bindPolylineOrPolygon();break;case BMAP_DRAWING_RECTANGLE:this._bindRectangle();break}}if(this._drawingTool&&this._isOpen){this._drawingTool.setStyleByDrawingMode(l)}};i.prototype._close=function(){this._isOpen=false;if(this._mask){this._map.removeOverlay(this._mask)}if(this._drawingTool){this._drawingTool.setStyleByDrawingMode("hander")}};i.prototype._bindMarker=function(){var n=this,o=this._map,m=this._mask;var l=function(q){var p=new BMapGL.Marker(q.point,n.markerOptions);o.addOverlay(p);n._dispatchOverlayComplete(p)};m.addEventListener("click",l)};var b=null;i.prototype._bindCircle=function(){var E=this,D=this._map,A=this._mask,p=null,G=[],x=null;var o=1;var n=null;var u=null;var l=null;var r=null;var v={strokeColor:"#4E6DF1",strokeWeight:2};var q=new BMapGL.Icon("//mapopen.bj.bcebos.com/cms/images/DrawingManager/circenter.png",new BMapGL.Size(20,20));var y=new BMapGL.Icon("///mapopen.bj.bcebos.com/cms/images/DrawingManager/nbsearch2.png",new BMapGL.Size(40,20),{imageOffset:new BMapGL.Size(0,10)});var B=function(I){if(E.controlButton=="right"&&(I.button==1||I.button==0)){return}x=I.point;var H=new BMapGL.Marker(x);q.setImageSize(new BMapGL.Size(20,20));H.setIcon(q);H.enableDragging();H.addEventListener("dragstart",w);H.addEventListener("dragging",s);H.addEventListener("dragend",F);D.addOverlay(H);G.push(H);p=new BMapGL.Circle(x,o,E.circleOptions);D.addOverlay(p);A.enableEdgeMove();A.addEventListener("mousemove",t);d.on(document,"mouseup",z)};var t=function(H){o=E._map.getDistance(x,H.point).toFixed(0);p.setRadius(o);D.removeOverlay(b);b=new BMapGL.Label("半径："+o+"米<br>松开完成绘制",{position:H.point,offset:new BMapGL.Size(10,10)});
b.setStyle(E.labelOptions);D.addOverlay(b)};var z=function(K){var L=D.getViewport(p.getBounds());L.zoom-=1;D.setViewport(L);D.removeOverlay(b);var J=new BMapGL.Point(p.getBounds().getNorthEast().lng,x.lat);A.hide();n=new BMapGL.Marker(J);n.setIcon(y);n.enableDragging();u=new BMapGL.Polyline([x,J],v);var M=new BMapGL.Point((p.getBounds().getNorthEast().lng+x.lng)/2,x.lat);l=new j("circle",M,o,p,E);G=G.concat([n,u,l]);var H=null;if(E.limit){H=E.limit.area}var I={limit:H,type:"circle",point:J,overlay:p,overlays:G};r=new c(I,E);D.addOverlay(n);D.addOverlay(u);D.addOverlay(l);D.addOverlay(r);l.addEventListener("radiuschange",function(S){var N=S.radius;p.setRadius(N);var R=h(x,N,"east");var O=new BMapGL.Point(R.lng,x.lat);var Q=R.lng>x.lng?(p.getBounds().getNorthEast().lng+x.lng)/2:(p.getBounds().getSouthWest().lng+x.lng)/2;var P=new BMapGL.Point(Q,x.lat);n.setPosition(O);l.setInfo(P,N);r.setPosition(O,true);r.updateWindow();u.setPath([x,O])});n.addEventListener("dragging",function(R){var O=new BMapGL.Point(R.latLng.lng,x.lat);var Q=R.latLng.lng>x.lng?(p.getBounds().getNorthEast().lng+x.lng)/2:(p.getBounds().getSouthWest().lng+x.lng)/2;var N=R.latLng.lng>x.lng?true:false;var P=new BMapGL.Point(Q,x.lat);R.target.setPosition(O);l.setInfo(P,E._map.getDistance(x,R.latLng).toFixed(0));r.setPosition(O,N);u.setPath([x,O]);o=E._map.getDistance(x,R.latLng).toFixed(0);p.setRadius(E._map.getDistance(x,R.latLng))});n.addEventListener("dragend",function(N){r.updateWindow()});A.disableEdgeMove();A.removeEventListener("mousemove",t);A.removeEventListener("mousemove",C);d.un(document,"mouseup",z);E.close()};var C=function(H){d.preventDefault(H);d.stopBubble(H);if(E.controlButton=="right"&&H.button==1){return}if(x==null){B(H)}};var m=function(H){d.preventDefault(H);d.stopBubble(H);D.removeOverlay(b);b=new BMapGL.Label("按下确认中心点，拖拽确认半径",{position:H.point,offset:new BMapGL.Size(10,10)});b.setStyle(E.labelOptions);D.addOverlay(b)};var w=function(H){D.removeOverlay(n);D.removeOverlay(u);D.removeOverlay(l);D.removeOverlay(r)};var s=function(H){x=H.latLng;p.setCenter(H.latLng)};var F=function(H){x=H.latLng;z(H)};A.addEventListener("mousedown",C);A.addEventListener("mousemove",m)};i.prototype._bindPolylineOrPolygon=function(){var s=this,l=this._map,x=this._mask,u=[],r=null,o=null,p=null,m=false;function t(){var A=arguments[0];var B=0;var z=0;for(var y=0;y<A.length;y++){if(B<A[y].lng){B=A[y].lng;z=y}}return A[z]}var n=function(z){if(s.controlButton=="right"&&(z.button==1||z.button==0)){return}var y=z.point;if(p){y=p}u.push(y);r=u.concat(u[u.length-1]);if(u.length==1){if(s._drawingType==BMAP_DRAWING_POLYLINE){o=new BMapGL.Polyline(r,s.polylineOptions)}else{if(s._drawingType==BMAP_DRAWING_POLYGON){o=new BMapGL.Polygon(r,s.polygonOptions)}}l.addOverlay(o)}else{o.setPath(r)}if(!m){m=true;x.enableEdgeMove();x.removeEventListener("mousemove",v);x.addEventListener("mousemove",w);x.addEventListener("dblclick",q)}};var w=function(z){var y=z.point;if(s._enableSorption){var A=s.getSorptionMatch(y,s.overlays,s._sorptionDistance);if(A&&A.length>0){p=A[0].point;o.setPositionAt(r.length-1,A[0].point);return}}p=null;o.setPositionAt(r.length-1,z.point);l.removeOverlay(b);b=new BMapGL.Label("单击绘制下一个点，双击完成绘制",{position:z.point,offset:new BMapGL.Size(10,10)});b.setStyle(s.labelOptions);l.addOverlay(b)};var q=function(E){d.stopBubble(E);m=false;l.removeOverlay(b);x.disableEdgeMove();x.removeEventListener("mousedown",n);x.removeEventListener("mousemove",w);x.removeEventListener("mousemove",v);x.removeEventListener("dblclick",q);if(s.controlButton=="right"){u.push(E.point)}else{if(d.ie<=8){}else{u.pop()}}try{if(s._enableGpc&&window.gpcas&&"polygon"===s._drawingType){var G=new gpcas.geometry.PolyDefault();for(var B=0;B<u.length;B++){G.addPoint(new gpcas.Point(u[B].lng,u[B].lat))}for(var A=0;A<s.overlays.length;A++){var J=s.overlays[A].getPath();var F=new gpcas.geometry.PolyDefault();for(var B=0;B<J.length;B++){F.addPoint(new gpcas.Point(J[B].lng,J[B].lat))}var H=G.difference(F);var K=H.getPoints();var I=[];for(var B=0;B<K.length;B++){I.push(new BMapGL.Point(K[B].x,K[B].y))}G=new gpcas.geometry.PolyDefault();for(var B=0;B<K.length;B++){G.addPoint(new gpcas.Point(K[B].x,K[B].y))}u=I}}}catch(E){}o.setPath(u);var y=l.getViewport(u);y.zoom-=1;l.setViewport(y);o.enableEditing();var z=null;if(s.limit){z="polygon"===s._drawingType?s.limit.area:s.limit.distance}var D={limit:z,type:s._drawingType,point:t(u),overlay:o,overlays:[]};var C=new c(D,s);l.addOverlay(C);o.addEventListener("lineupdate",function(M){var L=t(M.currentTarget.getPath());C.setPosition(L,true);C.updateWindow()});u.length=0;r.length=0;s.close()};var v=function(y){d.preventDefault(y);d.stopBubble(y);l.removeOverlay(b);b=new BMapGL.Label("单击确认起点",{position:y.point,offset:new BMapGL.Size(10,10)});b.setStyle(s.labelOptions);l.addOverlay(b)};x.addEventListener("mousemove",v);x.addEventListener("mousedown",n);x.addEventListener("dblclick",function(y){d.stopBubble(y)})};i.prototype._bindRectangle=function(){var r=this,m=this._map,v=this._mask,s=null,n=null;
function q(w,E){var x=new BMapGL.Point(w.lng,w.lat);var D=new BMapGL.Point(E.lng,w.lat);var A=new BMapGL.Point(E.lng,E.lat);var C=new BMapGL.Point(w.lng,E.lat);var F=new BMapGL.Point((w.lng+E.lng)/2,w.lat);var z=new BMapGL.Point(E.lng,(w.lat+E.lat)/2);var y=new BMapGL.Point((w.lng+E.lng)/2,E.lat);var B=new BMapGL.Point(w.lng,(w.lat+E.lat)/2);return[x,F,D,z,A,y,C,B]}var l=new BMapGL.Icon("//mapopen.bj.bcebos.com/cms/images/DrawingManager/bullet2.png",new BMapGL.Size(10,10));l.setImageSize(new BMapGL.Size(10,10));var o=function(x){d.stopBubble(x);d.preventDefault(x);if(r.controlButton=="right"&&(x.button==1||x.button==0)){return}n=x.point;var w=n;s=new BMapGL.Polygon(r._getRectanglePoint(n,w),r.rectangleOptions);m.addOverlay(s);v.enableEdgeMove();v.addEventListener("mousemove",u);d.on(document,"mouseup",p)};var u=function(z){m.removeOverlay(b);s.setPath(r._getRectanglePoint(n,z.point));var y=q(n,z.point);var x=r._map.getDistance(n,y[2]).toFixed(0);var w=r._map.getDistance(n,y[6]).toFixed(0);b=new BMapGL.Label("尺寸："+x+"米 x "+w+"米<br>松开结束绘制",{position:z.point,offset:new BMapGL.Size(10,10)});b.setStyle(r.labelOptions);m.addOverlay(b)};var p=function(H){v.hide();var I=null;var C=[];var J=q(n,H.point);var G=[];var z=m.getViewport(J);z.zoom-=1;m.setViewport(z);m.removeOverlay(b);var y=r._map.getDistance(n,J[2]).toFixed(0);var K=r._map.getDistance(n,J[6]).toFixed(0);var x=new j("rectangle",J[0],{width:y,height:K},s,r);for(var E=0;E<J.length;E++){var D=new BMapGL.Marker(J[E]);D.setIcon(l);D.enableDragging();C.push(D);m.addOverlay(D);G[E]=r.mc2ll(D.point);D.addEventListener("mousedown",function(L){I=r.mc2ll(L.target.point)});D.addEventListener("dragging",function(N){var L=N.latLng;for(var M=0;M<G.length;M++){if(I.lng==G[M].lng){J[M].lng=L.lng}if(I.lat==G[M].lat){J[M].lat=L.lat}}J=q(J[0],J[4]);for(var M=0;M<C.length;M++){C[M].setPosition(J[M])}y=r._map.getDistance(J[0],J[2]).toFixed(0);K=r._map.getDistance(J[0],J[6]).toFixed(0);x.setInfo(J[0],{width:y,height:K});F.setPosition(J[3],true);s.setPath(J)});D.addEventListener("dragend",function(N){for(var M=0;M<C.length;M++){var L=C[M];G[M]=r.mc2ll(L.point)}F.updateWindow()})}x.addEventListener("rectwhchange",function(R){var Q=R.width;var M=R.height;var L=h(J[0],Q,"east");var S=h(J[0],M,"south");J[4].lng=L.lng;J[4].lat=S.lat;J=q(J[0],J[4]);for(var O=0;O<C.length;O++){C[O].setPosition(J[O])}x.setInfo(J[0],{width:Q,height:M});F.setPosition(J[3],true);s.setPath(J);for(var P=0;P<C.length;P++){var N=C[P];G[P]=r.mc2ll(N.point)}F.updateWindow()});var w=[C,x];var A=null;if(r.limit){A=r.limit.area}var B={limit:A,type:"rectangle",point:J[3],overlay:s,overlays:w};var F=new c(B,r);m.addOverlay(F);m.addOverlay(x);v.disableEdgeMove();v.removeEventListener("mousemove",u);v.removeEventListener("mousemove",t);d.un(document,"mouseup",p);r.close()};var t=function(w){d.preventDefault(w);d.stopBubble(w);m.removeOverlay(b);b=new BMapGL.Label("按住确认起点，拖拽进行绘制",{position:w.point,offset:new BMapGL.Size(10,10)});b.setStyle(r.labelOptions);m.addOverlay(b)};v.addEventListener("mousedown",o);v.addEventListener("mousemove",t)};i.prototype._calculate=function(o,n){var m={data:0,label:null};if(this._enableCalculate&&BMapGLLib.GeoUtils){var p=o.toString();switch(p){case"Polyline":m.data=BMapGLLib.GeoUtils.getPolylineDistance(o);break;case"Polygon":m.data=BMapGLLib.GeoUtils.getPolygonArea(o);break;case"Circle":var l=o.getRadius();m.data=Math.PI*l*l;break}if(!m.data||m.data<0){m.data=0;console.error("计算函数异常处理")}else{m.data=m.data.toFixed(2)}}return m};i.prototype._addGeoUtilsLibrary=function(){if(!BMapGLLib.GeoUtils){var l=document.createElement("script");l.setAttribute("type","text/javascript");l.setAttribute("src","//mapopen.cdn.bcebos.com/github/BMapGLLib/GeoUtils/src/GeoUtils.min.js");document.body.appendChild(l)}};i.prototype._addGPCLibrary=function(){if(!window.gpcas){var l=document.createElement("script");l.setAttribute("type","text/javascript");l.setAttribute("src","//mapopen.cdn.bcebos.com/github/BMapGLLib/DrawingManager/src/gpc.js");document.body.appendChild(l)}};i.prototype._addLabel=function(l,n){var m=new BMapGL.Label(n,{position:l});this._map.addOverlay(m);return m};i.prototype._getRectanglePoint=function(m,l){return[new BMapGL.Point(m.lng,m.lat),new BMapGL.Point(l.lng,m.lat),new BMapGL.Point(l.lng,l.lat),new BMapGL.Point(m.lng,l.lat)]};i.prototype._dispatchOverlayComplete=function(m,n){var l={overlay:m,drawingMode:this._drawingType};if(n){l.calculate=n.data||null;l.label=n.label||null}this.dispatchEvent(this._drawingType+"complete",m);this.dispatchEvent("overlaycomplete",l)};i.prototype._dispatchOverlayCancel=function(m){var l={overlay:m,drawingMode:this._drawingType};this.dispatchEvent(this._drawingType+"cancel",m);this.dispatchEvent("overlaycancel",l)};i.prototype.getSorptionMatch=function(G,L,p){p=p||20;var M=this._map;var t=M.pointToPixel(G);var x=[];for(var I=0;I<L.length;I++){var C=L[I].getPath();var w=C[0];var y=C[C.length-1];if(!w.equals(y)){C.push(C[0])}for(var K=1;K<C.length;K++){var E=M.pointToPixel(C[K-1]);
var D=M.pointToPixel(C[K]);var H=[t.x-E.x,t.y-E.y];var R=[D.x-E.x,D.y-E.y];var m=[D.x-t.x,D.y-t.y];var J=H[0]*R[0]+H[1]*R[1];var s=Math.sqrt(Math.pow(H[0],2)+Math.pow(H[1],2))*Math.sqrt(Math.pow(R[0],2)+Math.pow(R[1],2));var N=Math.acos(J/s);var l=R[0]*m[0]+R[1]*m[1];var F=Math.sqrt(Math.pow(R[0],2)+Math.pow(R[1],2))*Math.sqrt(Math.pow(m[0],2)+Math.pow(m[1],2));var v=Math.acos(l/F);if(N<Math.PI/2&&v<Math.PI/2){var n=Math.sqrt(Math.pow(H[0],2)+Math.pow(H[1],2));var z=Math.sqrt(Math.pow(R[0],2)+Math.pow(R[1],2));var o=Math.cos(N)*n;var q=o/z;var r=Math.sin(N)*n;var u=[E.x+R[0]*q,E.y+R[1]*q];if(r<p){x.push({point:M.pixelToPoint({x:u[0],y:u[1]}),length:r})}}}}x.sort(function(B,A){return B.length-A.length});var Q=x.length>0?x:null;return Q};i.prototype.mc2ll=function(l){var n=this._map;var m=n.mercatorToLnglat(l.lng,l.lat);return new BMapGL.Point(m[0],m[1])};i.prototype.ll2mc=function(l){var n=this._map;var m=n.lnglatToMercator(l.lng,l.lat);return new BMapGL.Point(m[0],m[1])};function c(l,m){this.limit=l.limit;this.type=l.type;this.point=l.point;this.overlay=l.overlay;this.overlays=l.overlays;this.DrawingManager=m}c.prototype=new BMapGL.Overlay();c.prototype.dispatchEvent=d.lang.Class.prototype.dispatchEvent;c.prototype.addEventListener=d.lang.Class.prototype.addEventListener;c.prototype.removeEventListener=d.lang.Class.prototype.removeEventListener;c.prototype.initialize=function(p){var o=this;this._map=p;var l=(this.type==="polyline"?"长度":"面积");var n=(this.type==="polyline"?"万米":"万平方米");var q=this.div=document.createElement("div");q.className="operateWindow";var m='<div><span id="confirmOperate"></span><span id="cancelOperate"></span><span id="warnOperate">'+l+"不超过"+this.limit/10000+n+"！</span></div>";q.innerHTML=m;this._map.getPanes().markerPane.appendChild(q);this.updateWindow();this._bind();return q};c.prototype._bind=function(){var n=this;var o=this._map;var l=this.overlay;var m=this.overlays;document.getElementById("confirmOperate").addEventListener("click",function(s){o.removeOverlay(n);if(n.type=="rectangle"){var r=n.DrawingManager._calculate(l,l.getPath())}else{if(n.type=="circle"){var r=n.DrawingManager._calculate(l,n.point)}else{if(n.type=="polygon"){var r=n.DrawingManager._calculate(l,(l.getPath()));n.DrawingManager.overlays.push(l);l.disableEditing()}else{if(n.type=="polyline"){var r=n.DrawingManager._calculate(l,(l.getPath()));n.DrawingManager.overlays.push(l);l.disableEditing()}}}}n.DrawingManager._dispatchOverlayComplete(l,r);for(var q=0;q<m.length;q++){if(Array.isArray(m[q])){for(var p in m[q]){o.removeOverlay(m[q][p])}}else{o.removeOverlay(m[q])}}n.DrawingManager.close()});document.getElementById("cancelOperate").addEventListener("click",function(r){o.removeOverlay(n);for(var q=0;q<m.length;q++){if(Array.isArray(m[q])){for(var p in m[q]){o.removeOverlay(m[q][p])}}else{o.removeOverlay(m[q])}}o.removeOverlay(l);n.DrawingManager._dispatchOverlayCancel(l);n.DrawingManager._mask.show();n.DrawingManager._setDrawingMode(n.type)})};c.prototype.updateWindow=function(){if(this.domElement===null){return}var m=this.overlay;var l=this.limit;var n;if(this.type=="rectangle"){n=this.DrawingManager._calculate(m,m.getPath())}else{if(this.type=="circle"){n=this.DrawingManager._calculate(m,this.point)}else{if(this.type=="polygon"){n=this.DrawingManager._calculate(m,m.getPath())}else{if(this.type=="polyline"){n=this.DrawingManager._calculate(m,m.getPath())}}}}if(Object.prototype.toString.call(l)==="[object Number]"&&n.data>l){document.getElementById("confirmOperate").style.display="none";document.getElementById("warnOperate").style.display="block"}else{document.getElementById("confirmOperate").style.display="block";document.getElementById("warnOperate").style.display="none"}};c.prototype.setPosition=function(m,l){this.point=m;var o=this._map,n=o.pointToOverlayPixel(this.point);if(l){this.div.classList.remove("operateLeft");this.div.style.left=n.x+15+"px"}else{this.div.classList.add("operateLeft");this.div.style.left=n.x-105+"px"}this.div.style.top=n.y-16+"px"};c.prototype.draw=function(){var m=this._map,l=m.pointToOverlayPixel(this.point);this.div.style.left=l.x+15+"px";this.div.style.top=l.y-16+"px"};function j(n,l,o,m,p){this.type=n;this.point=l;this.number=o;this.overlay=m;this.DrawingManager=p}j.prototype=new BMapGL.Overlay();j.prototype.dispatchEvent=d.lang.Class.prototype.dispatchEvent;j.prototype.addEventListener=d.lang.Class.prototype.addEventListener;j.prototype.removeEventListener=d.lang.Class.prototype.removeEventListener;j.prototype.initialize=function(n){var m=this;this._map=n;var o=this.div=document.createElement("div");o.className="screenshot";if(this.type=="circle"){var l='<div class="circlShot"><span id="screenshotNum">'+this.number+'</span><input id="circleInput" type="text" /><span class="unit">米</span></div>'}else{if(this.type=="rectangle"){var l='<div class="rectWH"><div class="wh"><span id="rectWidth">'+this.number.width+'</span><input id="rectWidthInput" type="text" /></div><span class="multiple">x</span><div class="wh"><span id="rectHeight">'+this.number.height+'</span><input id="rectHeightInput" type="text" /></div><span class="unit">米</span></div>'
}}o.innerHTML=l;this._map.getPanes().markerPane.appendChild(o);this._bind();return o};j.prototype._bind=function(){this.setNumber(this.number);if(this.type=="circle"){this.bindCircleEvent()}else{this.bindRectEvent()}};j.prototype.bindCircleEvent=function(){var m=this;var l=document.getElementById("screenshotNum");var n=document.getElementById("circleInput");l.addEventListener("click",function(o){var p=l.innerText;l.style.display="none";n.value=p;n.style.display="inline-block";n.focus()});n.addEventListener("click",function(o){n.focus()});n.addEventListener("keydown",function(p){if(p.keyCode===13){var q=n.value;n.style.display="none";l.style.display="inline-block";l.innerText=q;var o={radius:q,overlay:m.overlay};m._dispatchRadiusChange(o)}});n.addEventListener("blur",function(p){var q=n.value;n.style.display="none";l.style.display="inline-block";l.innerText=q;var o={radius:q,overlay:m.overlay};m._dispatchRadiusChange(o)})};j.prototype.bindRectEvent=function(){var n=this;var m=document.getElementById("rectWidth");var p=document.getElementById("rectWidthInput");var l=document.getElementById("rectHeight");var o=document.getElementById("rectHeightInput");p.value=m.innerText;o.value=l.innerText;m.addEventListener("click",function(q){var r=m.innerText;m.style.display="none";p.value=r;p.style.display="inline-block";p.focus()});l.addEventListener("click",function(q){var r=l.innerText;l.style.display="none";o.value=r;o.style.display="inline-block";o.focus()});p.addEventListener("click",function(q){p.focus()});o.addEventListener("click",function(q){o.focus()});p.addEventListener("keydown",function(t){if(t.keyCode===13){var r=p.value;var s=o.value;p.style.display="none";o.style.display="none";m.style.display="inline-block";l.style.display="inline-block";m.innerText=r;l.innerText=s;var q={width:r,height:s,overlay:n.overlay};n._dispatchRectWHChange(q)}});o.addEventListener("keydown",function(t){if(t.keyCode===13){var r=p.value;var s=o.value;p.style.display="none";o.style.display="none";m.style.display="inline-block";l.style.display="inline-block";m.innerText=r;l.innerText=s;var q={width:r,height:s,overlay:n.overlay};n._dispatchRectWHChange(q)}})};j.prototype.setInfo=function(l,m){this.setNumber(m);this.setPosition(l)};j.prototype.setNumber=function(l){if(this.type=="circle"){document.getElementById("screenshotNum").textContent=l}else{document.getElementById("rectWidth").textContent=l.width;document.getElementById("rectHeight").textContent=l.height}};j.prototype.setPosition=function(l){this.point=l;var o=this._map,n=this.type,m=o.pointToOverlayPixel(this.point);if(n=="circle"){this.div.style.left=m.x-30+"px";this.div.style.top=m.y-40+"px"}else{if(n=="rectangle"){this.div.style.left=m.x+"px";this.div.style.top=m.y-45+"px"}}};j.prototype.draw=function(){var n=this._map,m=this.type,l=n.pointToOverlayPixel(this.point);if(m=="circle"){this.div.style.left=l.x-30+"px";this.div.style.top=l.y-40+"px"}else{if(m=="rectangle"){this.div.style.left=l.x+"px";this.div.style.top=l.y-45+"px"}}};j.prototype._dispatchRadiusChange=function(l){this.dispatchEvent("radiuschange",l)};j.prototype._dispatchRectWHChange=function(l){this.dispatchEvent("rectwhchange",l)};function g(){this._enableEdgeMove=false}g.prototype=new BMapGL.Overlay();g.prototype.dispatchEvent=d.lang.Class.prototype.dispatchEvent;g.prototype.addEventListener=d.lang.Class.prototype.addEventListener;g.prototype.removeEventListener=d.lang.Class.prototype.removeEventListener;g.prototype.initialize=function(n){var m=this;this._map=n;var o=this.container=document.createElement("div");var l=this._map.getSize();o.style.cssText="position:absolute;background:transparent;cursor:crosshair;width:"+l.width+"px;height:"+l.height+"px";this._map.addEventListener("resize",function(p){m._adjustSize(p.size)});this._map.getPanes().floatPane.appendChild(o);this._bind();return o};g.prototype.draw=function(){var n=this._map,l=n.pixelToPoint(new BMapGL.Pixel(0,0)),m=n.pointToOverlayPixel(l);this.container.style.left=m.x+"px";this.container.style.top=m.y+"px"};g.prototype.enableEdgeMove=function(){this._enableEdgeMove=true};g.prototype.disableEdgeMove=function(){clearInterval(this._edgeMoveTimer);this._enableEdgeMove=false};g.prototype._bind=function(){var q=this,l=this._map,m=this.container,r=null,s=null;var p=function(u){return{x:u.clientX,y:u.clientY}};var o=function(w){var v=w.type;w=d.getEvent(w);point=q.getDrawPoint(w);var x=function(y){w.point=point;q.dispatchEvent(w)};if(v=="mousedown"){r=p(w)}var u=p(w);if(v=="click"){if(Math.abs(u.x-r.x)<5&&Math.abs(u.y-r.y)<5){if(!s||!(Math.abs(u.x-s.x)<5&&Math.abs(u.y-s.y)<5)){x("click");s=p(w)}else{s=null}}}else{x(v)}};var t=["click","mousedown","mousemove","mouseup","dblclick"],n=t.length;while(n--){d.on(m,t[n],o)}d.on(m,"mousemove",function(u){if(q._enableEdgeMove){q.mousemoveAction(u)}})};g.prototype.mousemoveAction=function(s){function l(x){var w=x.clientX,v=x.clientY;if(x.changedTouches){w=x.changedTouches[0].clientX;v=x.changedTouches[0].clientY}return new BMapGL.Pixel(w,v)
}var m=this._map,t=this,n=m.pointToPixel(this.getDrawPoint(s)),p=l(s),q=p.x-n.x,o=p.y-n.y;n=new BMapGL.Pixel((p.x-q),(p.y-o));this._draggingMovePixel=n;var u=m.pixelToPoint(n),r={pixel:n,point:u};this._panByX=this._panByY=0;if(n.x<=20||n.x>=m.width-20||n.y<=50||n.y>=m.height-10){if(n.x<=20){this._panByX=8}else{if(n.x>=m.width-20){this._panByX=-8}}if(n.y<=50){this._panByY=8}else{if(n.y>=m.height-10){this._panByY=-8}}if(!this._edgeMoveTimer){this._edgeMoveTimer=setInterval(function(){m.panBy(t._panByX,t._panByY,{noAnimation:true})},30)}}else{if(this._edgeMoveTimer){clearInterval(this._edgeMoveTimer);this._edgeMoveTimer=null}}};g.prototype._adjustSize=function(l){this.container.style.width=l.width+"px";this.container.style.height=l.height+"px"};g.prototype.getDrawPoint=function(q){var p=this._map,o=d.getTarget(q),m=q.offsetX||q.layerX||0,r=q.offsetY||q.layerY||0;if(o.nodeType!=1){o=o.parentNode}while(o&&o!=p.getContainer()){if(!(o.clientWidth==0&&o.clientHeight==0&&o.offsetParent&&o.offsetParent.nodeName=="TD")){m+=o.offsetLeft||0;r+=o.offsetTop||0}o=o.offsetParent}var n=new BMapGL.Pixel(m,r);var l=p.pixelToPoint(n);return l};function f(m,l){this.drawingManager=m;l=this.drawingToolOptions=l||{};this._opts={};this.defaultAnchor=BMAP_ANCHOR_TOP_LEFT;this.defaultOffset=new BMapGL.Size(10,10);this.defaultDrawingModes=[BMAP_DRAWING_MARKER,BMAP_DRAWING_CIRCLE,BMAP_DRAWING_POLYLINE,BMAP_DRAWING_POLYGON,BMAP_DRAWING_RECTANGLE];if(l.drawingModes){this.drawingModes=l.drawingModes}else{this.drawingModes=this.defaultDrawingModes}if(l.hasCustomStyle){if(l.anchor){this.setAnchor(l.anchor)}if(l.offset){this.setOffset(l.offset)}}}f.prototype=new BMapGL.Control();f.prototype.initialize=function(p){var m=this.container=document.createElement("div");m.className="BMapGLLib_Drawing";var l=this.panel=document.createElement("div");l.className="BMapGLLib_Drawing_panel";if(this.drawingToolOptions&&this.drawingToolOptions.hasCustomStyle&&this.drawingToolOptions.scale){this._setScale(this.drawingToolOptions.scale)}m.appendChild(l);var n=this._generalHtml();l.appendChild(n);var o=this.tip=document.createElement("div");o.className="BMapGLLib_tip";o.innerHTML='<p class="BMapGLLib_tip_title"></p><p class="BMapGLLib_tip_text"></p>';if(this.drawingToolOptions.enableTips===true){l.appendChild(o)}this._bind(l);if(this.drawingToolOptions.customContainer){d.g(this.drawingToolOptions.customContainer).appendChild(m)}else{p.getContainer().appendChild(m)}return m};f.prototype._generalHtml=function(r){var q=this;var m={};m.hander="拖动地图";m[BMAP_DRAWING_MARKER]="画点";m[BMAP_DRAWING_CIRCLE]="圆形工具";m[BMAP_DRAWING_POLYLINE]="画折线";m[BMAP_DRAWING_POLYGON]="多边形工具";m[BMAP_DRAWING_RECTANGLE]="矩形工具";var s=function(u,t){var v=document.createElement("a");v.className=u;v.href="javascript:void(0)";v.setAttribute("drawingType",t);v.setAttribute("onfocus","this.blur()");v.addEventListener("mouseenter",function(x){var w=x.target.getAttribute("drawingType");var y=m[w];if(w==="hander"){q.tip.children[0].innerText=y;q.tip.children[1].innerText="使用鼠标拖动地图"}else{q.tip.className+=" "+w;q.tip.children[0].innerText=y;q.tip.children[1].innerText="使用"+y+"选出目标区域"}q.tip.style.display="block"});v.addEventListener("mouseleave",function(y){var w=y.target.getAttribute("drawingType");var x=" "+q.tip.className.replace(/[\t\r\n]/g,"")+" ";while(x.indexOf(" "+w+" ")>=0){x=x.replace(" "+w+" "," ")}q.tip.className=x.replace(/^\s+|\s+$/g,"");q.tip.style.display="none"});return v};var n=document.createDocumentFragment();for(var o=0,l=this.drawingModes.length;o<l;o++){var p="BMapGLLib_box BMapGLLib_"+this.drawingModes[o];if(o==l-1){p+=" BMapGLLib_last"}n.appendChild(s(p,this.drawingModes[o]))}return n};f.prototype._setScale=function(o){var n=390,l=50,p=-parseInt((n-n*o)/2,10),m=-parseInt((l-l*o)/2,10);this.container.style.cssText=["-moz-transform: scale("+o+");","-o-transform: scale("+o+");","-webkit-transform: scale("+o+");","transform: scale("+o+");","margin-left:"+p+"px;","margin-top:"+m+"px;","*margin-left:0px;","*margin-top:0px;","margin-left:0px\\0;","margin-top:0px\\0;","filter: progid:DXImageTransform.Microsoft.Matrix(","M11="+o+",","M12=0,","M21=0,","M22="+o+",","SizingMethod='auto expand');"].join("")};f.prototype._bind=function(l){var m=this;d.on(this.panel,"click",function(p){var o=d.getTarget(p);var n=o.getAttribute("drawingType");m.setStyleByDrawingMode(n);m._bindEventByDraingMode(n)})};f.prototype.setStyleByDrawingMode=function(m){if(!m){return}var n=this.panel.getElementsByTagName("a");for(var o=0,l=n.length;o<l;o++){var q=n[o];if(q.getAttribute("drawingType")==m){var p="BMapGLLib_box BMapGLLib_"+m+"_hover";if(o==l-1){p+=" BMapGLLib_last"}q.className=p}else{q.className=q.className.replace(/_hover/,"")}}};f.prototype._bindEventByDraingMode=function(l){var n=this;var m=this.drawingManager;if(m._isOpen&&m.getDrawingMode()===l){m.close();m._map.enableDoubleClickZoom()}else{m.setDrawingMode(l);m.open();m._map.disableDoubleClickZoom()}};var a=[];function e(l){var m=a.length;while(m--){if(a[m]!=l){a[m].close()
}}}function k(l,s){var B=[];var p=l["lng"],n=l["lat"];var u=s/6378800,r=(Math.PI/180)*n,z=(Math.PI/180)*p;for(var q=0;q<271;q+=9){var o=(Math.PI/180)*q,v=Math.asin(Math.sin(r)*Math.cos(u)+Math.cos(r)*Math.sin(u)*Math.cos(o)),t=Math.atan2(Math.sin(o)*Math.sin(u)*Math.cos(r),Math.cos(u)-Math.sin(r)*Math.sin(v)),w=((z-t+Math.PI)%(2*Math.PI))-Math.PI,A=new BMapGL.Point(w*(180/Math.PI),v*(180/Math.PI));B.push(A)}var m=B[0];B.push(new BMapGL.Point(m["lng"],m["lat"]));return B}function h(m,l,A){var p=m["lng"],n=m["lat"];var v=l/6378800,r=(Math.PI/180)*n,B=(Math.PI/180)*p;var q,t,s;switch(A){case"North":case"north":case"N":case"n":q=0;t=m.lng;break;case"West":case"west":case"W":case"w":q=90;s=m.lat;break;case"South":case"south":case"S":case"s":q=180;t=m.lng;break;case"East":case"east":case"E":case"e":q=270;s=m.lat;break;default:q=~~A;break}var o=(Math.PI/180)*q,w=Math.asin(Math.sin(r)*Math.cos(v)+Math.cos(r)*Math.sin(v)*Math.cos(o)),u=Math.atan2(Math.sin(o)*Math.sin(v)*Math.cos(r),Math.cos(v)-Math.sin(r)*Math.sin(w)),z=((B-u+Math.PI)%(2*Math.PI))-Math.PI,C=new BMapGL.Point(t||z*(180/Math.PI),s||w*(180/Math.PI));C.lng=parseFloat(C.lng.toFixed(6));C.lat=parseFloat(C.lat.toFixed(6));return C}})();