<template>
    <div class="home">
        <div class="home_wrapper">
            <LayoutHeader />
            <Transition name="fade" mode="out-in" >
                <component :is="activeMenus.component || 'home'"></component>
            </Transition>
            <GMenu />
            <Floor v-if="activeMenus.showFloor"></Floor>

            <div class="view" v-if="mode == 4">
                <div class="diagram">
                    <iframe v-if="iframeUrl" frameborder="no" border="0" style="width: 100%; height: 100%"
                        :src="iframeUrl"></iframe>
                    <!-- <diagramNoData v-else></diagramNoData> -->
                </div>
            </div>

        </div>
        <!-- bim模型 -->
        <bim ref="bim"></bim>

        <vr v-if="activeMenus.name=='首页'||activeMenus.name=='综合态势'||activeMenus.name=='驾驶舱'"></vr>
        <!-- 百度地图 -->
        <!-- <bmap v-if="mode == 2"></bmap> -->
        <!-- 二维组态图 -->

        <el-dialog align-center append-to-body   class="pop" v-model="dialogVisible" :title="dialog.name" width="1500px" draggable>
            <component :is="dialog.component"></component>
        </el-dialog>

    </div>
</template>

<script>
import {
    defineComponent,
    reactive,
    toRefs,
    watch,
    inject,
    computed,
    getCurrentInstance,
    onMounted,
} from "vue";

import {
    getCookie,
    setCookie
} from "@/utils/cookie";

import {
    useStore
} from "vuex";

import "../lib/peer-stream";
export default defineComponent({
    components: {},
    setup() {
        const state = reactive({
            name: 'ba',
            dialogVisible: false,
            iframeUrl: '',
            mode: 4,

        });
        const store = useStore();
        const {
            proxy
        } = getCurrentInstance();
        const emitter = inject("mitt");

        emitter.off("changeMode");
        emitter.on("changeMode", (data) => {
            state.mode = data.mode;
            setCookie("mode", data.mode);
            if (data.mode == 1 || data.mode == 4) {
            }
        });

        const activeMenus = computed(() => {
            let menu = getCookie("funMenus");
            return store.state.menu.funMenus ?
                store.state.menu.funMenus : menu ? JSON.parse(menu) : {
                    name: '首页',
                    component: 'home',
                    showFloor: false,
                };
        });

        const dialog = computed(() => {
            return store.state.menu.dialog
        });

        const areaId = computed(() => {
            // let area = getCookie("area");
            return store.state.area.area;
        });

     
        watch(areaId, (val) => {
            if (state.mode == 4) {
                // getDeviceList()
            }
        });

        watch(activeMenus, (val) => {

            if (state.mode == 4) {
                // getdeviceList();
            }
        });

        watch(dialog, (val) => {
            if (val.component) {
                state.dialogVisible = true;
            } else {
                state.dialogVisible = false;
            }
        });

        onMounted(() => {
            let mode = getCookie('mode');
            if (mode) {
                state.mode = mode;
            }
        });

        return {
            ...toRefs(state),

            activeMenus,

            areaId,

            dialog

        };
    },
});
</script>

<style lang="scss" scoped>
.home {
    position: relative;
    background: linear-gradient(270deg, rgba(0, 8, 18, 1) 0%, rgba(0, 14, 30, 0.9) 72.19%, rgba(0, 15, 32, 0.68) 83.65%, rgba(0, 12, 26, 0) 100%);
    width: 100%;
    height: 100%;

    .content-item {
        display: flex;
        justify-content: space-around;
        align-items: center;
        width: 100%;
        height: 100%;
        padding: 10px 0;
        border-bottom: 1px solid rgb(103, 146, 160);
        color: white;
        cursor: pointer;
    }

    .home_wrapper {
        position: absolute;
        width: 100%;
        height: 100%;
        // z-index: 100;
        display: flex;
        justify-content: space-between;
        background: url("../assets/images/2D.jpg") no-repeat center center/100%;
    }

    .pop {
        position: absolute;
        width: 100%;
        height: calc(100% - 64px);
        top: 64px;
        background: url("../assets/images/home.jpg") no-repeat 100%/100% fixed;
        z-index: 101;
    }
}

.tuli {
    position: absolute;
    right: 100px;
    bottom: 100px;
    color: white;

    .list {
        display: flex;
        justify-content: center;
        align-items: center;
    }

    svg {
        margin-right: 10px;
    }
}

.view {
    height: 100%;
    width: 100%;
    background: url("../assets/images/2D.jpg");
    // background: rgba(128, 128, 128, 0.9);
    position: absolute;
    z-index: 8;
    left: 0;
    top: 0;
   
}

.diagram {
    width: 1160px;
    margin: 1rem auto 0.7rem auto;
    height: calc(100% - 1.7rem);
    display: flex;
    justify-content: center;
    align-items: center;
}

</style>

