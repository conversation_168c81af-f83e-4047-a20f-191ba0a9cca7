<template>
<div class="box">
    <div class="left-box">
        <div class="title cursor">个人中心</div>
        <div class="list">
            <div v-for="(item, index) in tabs" :key="index" class="item cursor" @click="clickMenu(index, item.type)">
                {{ item.name }}
                <div :class="{ active: isActive == item.id }"></div>
            </div>
        </div>
    </div>
    <div class="right-box">
        <component :is="name"></component>
    </div>
</div>
</template>

<script>
import msg from './msg.vue'
import setting from './setting.vue'
import work from './work.vue'
import {
    defineComponent,
    reactive,
    toRefs
} from 'vue'

export default defineComponent({
    name:"personal",
    components: {
        msg,
        setting,
        work
    },
    setup() {
        const state = reactive({
            isActive: 0,
            name: 'msg',
            tabs: [{
                id: 0,
                name: '消息',
                type: 'msg'
            }, {
                id: 1,
                name: '设置',
                type: 'setting'
            },
            //  {
            //     id: 2,
            //     name: '工作台',
            //     type: 'work'
            // }
        ]
        });
        const clickMenu = (index, type) => {
            state.isActive = index;
            state.name = type;
        };
        return {
            ...toRefs(state),
            clickMenu
        }
    }
})
</script>

<style lang="scss" scoped>
.box {
    width: 100%;
    position: absolute;
    left: 0;
    top: 70px;
    height: calc(100% - 250px);
    z-index: 100;
    overflow-y: auto;
    display: flex;
    background: rgba(#103457, 0.85);
    .left-box {
        width: 204px;
        // background: #000000;
        border: 1px solid RGBA(54, 60, 63, 0.6);
        padding: 15px 0;
        height: calc(100% - 200px);

        .title {
            width: 68px;
            font-size: 16px;
            font-family: "PingFangSC-Medium", "PingFang SC";
            font-weight: 500;
            color: #ffffff;
            letter-spacing: 1px;
            margin-left: 22px;
            margin-bottom: 20px;
        }

        .list {
            color: #fff;
            font-size: 14px;
            margin-left: 55px;
            width: 75px;

            .item {
                position: relative;
                height: 40px;
                line-height: 40px;
                text-align: center;
                font-size: 14px;
                font-family: "Alibaba-PuHuiTi";
                font-weight: 400;
                color: rgba(255, 255, 255, 0.5);

                .active {
                    position: absolute;
                    width: 100%;
                    height: 18px;
                    bottom: 0px;
                    background: url("../../assets/images/btn_bak.png") no-repeat;
                }
            }
        }
    }

    .right-box {
        flex: 1;
        margin-left: 24px;
        height: 100%;
    }
}
</style>
