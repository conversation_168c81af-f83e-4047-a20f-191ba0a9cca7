<template>
<div class="content_tab">
    <div class="left_tab">
        <span class="btn" v-for="(item, i) in dosageTab" :key="i" :class="{ active: tab == item.id }" @click="changeTab(item.id)">{{ item.name }}</span>
    </div>
</div>

<div v-if="tab == 'word'" class="info addDiagram">
    <el-form ref="userRef" class="form" :model="user" :rules="userRules" label-width="100px" label-position="right">
        <div class="form-item">
            <el-form-item class="item" prop="name" label="用户名">
                <el-input placeholder="" v-model="user.name"></el-input>
            </el-form-item>
            <el-form-item class="item" prop="phone" label="手机号">
                <el-input placeholder="" v-model="user.phone"></el-input>
            </el-form-item>
        </div>
        <div class="form-item">
            <el-form-item class="item" label="微信">
                <el-input placeholder="" v-model="user.weChat"></el-input>
            </el-form-item>
            <el-form-item class="item" label="邮箱" prop="email">
                <el-input placeholder="" v-model="user.email"></el-input>
            </el-form-item>
        </div>
        <div class="form-item">
            <el-form-item class="item" label="性别">
                <el-radio-group v-model="user.sex">
                    <!-- <div class="sex"> -->
                    <el-radio :label="0">男</el-radio>
                    <el-radio :label="1">女</el-radio>
                    <!-- </div> -->
                </el-radio-group>
            </el-form-item>

            <el-form-item class="item" label="公司">
                <el-input placeholder="" v-model="user.company"></el-input>
            </el-form-item>
        </div>
        <div class="form-item">
            <el-form-item class="item" label="职务">
                <el-input placeholder="" v-model="user.position"></el-input>
            </el-form-item>
            <el-form-item class="item" label="地址">
                <el-input placeholder="" v-model="user.address"></el-input>
            </el-form-item>
        </div>

        <div class="form-footer search_box">
            <div class="searchBtn" size="small" @click="resetInfo" type="primary">确认修改</div>
        </div>
    </el-form>
</div>
<div v-if="tab == 'pass'" class="info addDiagram">
    <el-form ref="passRef" class="form-pass" :rules="rules" :model="pass" label-width="100px" label-position="right">
        <div class="form-item">
            <el-form-item class="item" prop="oldPassword" label="原始密码">
                <el-input type="password" placeholder="" v-model="pass.oldPassword"></el-input>
            </el-form-item>
        </div>
        <div class="form-item">
            <el-form-item class="item" prop="newPassword" label="新密码">
                <el-input type="password" placeholder="" v-model="pass.newPassword"></el-input>
            </el-form-item>
        </div>
        <div class="form-item">
            <el-form-item class="item" prop="confirmPass" label="确认密码">
                <el-input type="password" placeholder="" v-model="pass.confirmPass"></el-input>
            </el-form-item>
        </div>

        <div class="form-footer search_box">
            <div class="searchBtn" size="small" @click="resetPass" type="primary">确认修改</div>
        </div>
    </el-form>
</div>
</template>

<script>
import {
    defineComponent,
    reactive,
    ref,
    toRefs,
    onMounted,
    getCurrentInstance
} from "vue"
import {
    ElMessage
} from "element-plus";
import {
    getCookie
} from "@/utils/cookie";

export default defineComponent({
    setup() {

        const passRef = ref();
        const userRef = ref();
        const {
            proxy
        } = getCurrentInstance()
        const state = reactive({
            dosageTab: [{
                    name: '个人资料',
                    id: 'word',
                },
                {
                    name: '密码重置',
                    id: 'pass',
                }
            ],
            tab: 'word',
            pass: {
                oldPassword: "",
                newPassword: "",
                confirmPass: ""
            },
            rules: {
                oldPassword: [{
                    required: true,
                    message: "请输入原始密码",
                    trigger: "blur"
                }],
                newPassword: [{
                    required: true,
                    message: "请输入新密码",
                    trigger: "blur"
                }],
                confirmPass: [{
                    required: true,
                    validator: (rule, value, callback) => {
                        if (value === "") {
                            callback(new Error("请再次输入密码"));
                        } else if (value !== state.pass.newPassword) {
                            callback(new Error("两次输入密码不一致!"));
                        } else {
                            callback();
                        }
                    },
                    trigger: "blur"
                }]
            },
            user: {},
            userRules: {
                name: [{
                    required: true,
                    message: "请输入用户名",
                    trigger: "blur"
                }],
                phone: [{
                        required: true,
                        message: "请输入手机号",
                        trigger: "blur"
                    },
                    {
                        pattern: /^((0\d{2,3}-\d{7,8})|(1[3584]\d{9}))$/,
                        message: '请输入正确的手机号码或者座机号',
                    },

                ],
                email: [{
                        required: false,
                        message: '请输入邮箱',
                        trigger: 'blur'
                    },
                    {
                        pattern: /^[A-Za-z0-9\u4e00-\u9fa5]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/,
                        message: '请输入正确的邮箱',
                    },
                ],
            }
        });

        onMounted(() => {
            getUser();
        });

        const changeTab = (key) => {
            state.tab = key;
        };
        const resetPass = () => {
            passRef.value.validate(valid => {
                if (valid) {
                    proxy.$api.resetPass({
                        newPassword: state.pass.newPassword,
                        oldPassword: state.pass.oldPassword
                    }).then(res => {
                        ElMessage.success({
                            message: '修改成功'
                        })
                        passRef.value.resetFields();
                    });
                } else {
                    return false;
                }
            });
        };
        const getUser = () => {
            proxy.$api.getUserInfo({
                id: getCookie("gh_id")
            }).then(res => {
                state.user = res.data;
            });
        };
        const resetInfo = () => {
            userRef.value.validate(valid => {
                if (valid) {
                    state.user.userName = null;
                    proxy.$api.updateUser(state.user).then(res => {
                        ElMessage.success({
                            message: "修改成功",
                            type: "success"
                        });
                        // getUser();
                    });
                }
            });
        };

        return {
            ...toRefs(state),
            changeTab,
            resetPass,
            passRef,
            userRef,
            resetInfo,
            getUser

        }
    },
})
</script>

<style lang="scss" scoped>
.info {
    display: flex;
    justify-content: center;
    padding-top: 30px;

    .form-footer {
        margin-top: 100px;
        display: flex;
        justify-content: center;
    }

    .btn {
        width: 73px;
        height: 28px;
        background: rgba(61, 233, 250, 0.5);
        border-radius: 2px;
        border: 1px solid #3de9fa;
    }

    .form {
        width: 760px;

        .sex {
            display: flex;
        }

        .form-item {
            display: flex;

            .item {
                flex: 1;
            }

            .item:first-of-type {
                margin-right: 48px;
            }
        }
    }

    .form-pass {
        width: 480px;
    }
}
</style>
