<template>
<div class="work">
    <el-scrollbar>
        <div class="top">
            <panel-title>
                快捷入口
                <div class="flex-center operator">
                    <div @click="showMenu" class="cursor" style="margin-right: 10px">
                        <i class="Plus"></i>添加
                    </div>
                    <div @click="delMenu" class="cursor">
                        <i class="el-icon-close"></i>删除
                    </div>
                </div>
            </panel-title>
            <div class="menu-list">
                <div class="menu cursor" v-for="item in userMenus" :key="item.id">
                    <div class="icon">
                        <span @click="changePath(item.path)" :style="{ color: item.color || '#3088e1' }" class="icon" :class="item.icon || 'iconfont iconlujingdaohang-'"></span>
                    </div>
                    <div class="info">
                        {{ item.name }}
                    </div>
                    <div class="check">
                        <el-checkbox v-model="item.checked"></el-checkbox>
                    </div>
                </div>

                <i v-for="(item, i) in 10" :key="i" style="width: 126px; margin-right: 16px"></i>
            </div>
        </div>
        <div class="bottom">
            <panel-title>我的待办</panel-title>
            <sub-title2 title="巡检任务"></sub-title2>
            <div class="order">
                <div class="order-list flex-center">
                    <div class="order-icon flex-center">
                        <span class="iconfont iconyoujian icon"></span>
                        <span class="send icon-text">待派发</span>
                    </div>
                    <div class="info flex-center">
                        {{ patrol.send }}
                    </div>
                </div>
                <div class="order-list flex-center">
                    <div class="order-icon flex-center">
                        <span class="iconfont iconyoujian icon"></span>
                        <span class="receive icon-text">待接收</span>
                    </div>
                    <div class="info flex-center">
                        {{ patrol.receive }}
                    </div>
                </div>
                <div class="order-list flex-center">
                    <div class="order-icon flex-center">
                        <span class="iconfont iconyoujian icon"></span>
                        <span class="complete icon-text">未完成</span>
                    </div>
                    <div class="info flex-center">
                        {{ patrol.complete }}
                    </div>
                </div>
                <div class="order-list flex-center">
                    <div class="order-icon flex-center">
                        <span class="iconfont iconyoujian icon"></span>
                        <span class="review icon-text">待审核</span>
                    </div>
                    <div class="info flex-center">
                        {{ patrol.review }}
                    </div>
                </div>
            </div>
            <sub-title2 title="工单任务"></sub-title2>
            <div class="order">
                <div class="order-list flex-center">
                    <div class="order-icon flex-center">
                        <span class="iconfont iconyoujian icon"></span>
                        <span class="send icon-text">待派发</span>
                    </div>
                    <div class="info flex-center">
                        {{ order.send }}
                    </div>
                </div>
                <div class="order-list flex-center">
                    <div class="order-icon flex-center">
                        <span class="iconfont iconyoujian icon"></span>
                        <span class="receive icon-text">待接收</span>
                    </div>
                    <div class="info flex-center">
                        {{ order.receive }}
                    </div>
                </div>
                <div class="order-list flex-center">
                    <div class="order-icon flex-center">
                        <span class="iconfont iconyoujian icon"></span>
                        <span class="complete icon-text">未完成</span>
                    </div>
                    <div class="info flex-center">
                        {{ order.complete }}
                    </div>
                </div>
                <div class="order-list flex-center">
                    <div class="order-icon flex-center">
                        <span class="iconfont iconyoujian icon"></span>
                        <span class="review icon-text">待审核</span>
                    </div>
                    <div class="info flex-center">
                        {{ order.review }}
                    </div>
                </div>
            </div>
        </div>
    </el-scrollbar>
    <el-dialog align-center append-to-body   v-model="Visible" title="菜单选择" custom-class="custom_dialog" width="700px">
        <el-scrollbar class="tree-list tree">
            <el-tree ref="treeRef" :data="menus" show-checkbox node-key="id" :props="defaultProps">
            </el-tree>
        </el-scrollbar>
        <template #footer>
            <div class="dialog-footer">
                <el-button type="primary" class="saveBtn" size="mini" @click="saveMenuConfig">确定</el-button>
            </div>
        </template>
    </el-dialog>
</div>
</template>

<script>
import {
    defineComponent,
    reactive,
    toRefs,
    onMounted,
    ref,
    computed,
    getCurrentInstance,
    watch
} from "vue"
import {
    useStore
} from 'vuex'
import PanelTitle from '@/components/PanelTitle.vue'
import {
    getCookie,
    setCookie
} from "@/utils/cookie";
import {
    useRouter
} from 'vue-router'

export default defineComponent({
    components: {
        PanelTitle
    },
    setup() {
        const {
            proxy
        } = getCurrentInstance()
        const router = useRouter()
        const treeRef = ref();
        const store = useStore();
        const state = reactive({
            order: {
                send: 0,
                receive: 0,
                review: 0,
                complete: 0
            },
            patrol: {
                send: 0,
                receive: 0,
                review: 0,
                complete: 0
            },
            userMenus: [],
            Visible: false,
            defaultProps: {
                children: 'children',
                label: 'name'
            }
        });
        onMounted(() => {
            getOrder();
            getPatrol();
            getMenu();
        });
        const menus = computed(() => store.state.menu.menus)
        const projectId = computed(() => {
            return store.state.user.projectId || getCookie('gh_projectId')
        })
        watch(projectId, (val) => {
            if (val) {
                getOrder();
                getPatrol();
                getMenu();
            }
        })
        const getOrder = () => {
            proxy.$api.orderStatistic({
                projectId: getCookie('gh_projectId')
            }).then(res => {
                state.order.send = res.data.Pending;
                state.order.receive = res.data.TakeOrder;
                state.order.complete = res.data.UnComplete;
                state.order.review = res.data.Aduit
            });
        };
        const getPatrol = () => {
            proxy.$api.inspectionStatustic({
                projectId: getCookie('gh_projectId')
            }).then(res => {
                state.patrol.send = res.data.Pending;
                state.patrol.receive = res.data.TakeOrder;
                state.patrol.complete = res.data.UnComplete;
                state.patrol.review = res.data.Aduit
            });
        };
        const saveMenuConfig = () => {
            let nodes = treeRef.value.getCheckedNodes();
            let data = [];
            nodes.forEach(node => {
                if (node.children.length == 0) {
                    data.push({
                        userId: getCookie('gh_id'),
                        projectId: getCookie('gh_projectId'),
                        path: node.fullPath,
                        menuId: node.id
                    });
                }
            });
            if (data.length > 0) {
                proxy.$api.addUserMenu(data).then(res => {
                    state.Visible = false;
                    getMenu();
                });
            }

        };
        const getMenu = () => {
            proxy.$api.getUserMenu({
                userId: getCookie('gh_id'),
                projectId: getCookie('gh_projectId'),
            }).then(res => {
                state.userMenus = res.data;
            });
        };
        const delMenu = () => {
            let data = [];
            state.userMenus.forEach(menu => {
                if (menu.checked) {
                    data.push({
                        userId: getCookie('gh_id'),
                        projectId: getCookie('gh_projectId'),
                        menuId: menu.id
                    });
                }
            });
            if (data.length > 0) {
                proxy.$api.delUserMenu(data).then(res => {
                    getMenu();
                });
            }

        };
        const showMenu = () => {
            state.Visible = true;
        };
        const changePath = (path) => {
            if (path && path.includes("|")) {
                let paths = path.split('|');
                menus.value.forEach((m, i) => {
                    if (m.id == paths[0]) {
                        store.commit('menu/SET_FUN_MENU', m);
                        store.commit('menu/SET_MENU_INDEX', i);
                        setCookie('funMenus', i);
                        if (m.children && m.children.length > 0) {
                            getActiveMenu(m.children, paths[paths.length - 1]);
                        }
                    }
                })
            }
        };
        const getActiveMenu = (menus, id) => {
            for (let i = 0; i < menus.length; i++) {
                if (menus[i].id == id) {
                    if (menus[i].path) {
                        router.push({
                            path: menus[i].path,
                        })
                    }
                    setCookie('activeMenus', JSON.stringify(menus[i]))
                    return;
                }
                if (menus[i].children && menus[i].children.length > 0) {
                    getActiveMenu(menus[i].children, id);
                }
            }
        };
        return {
            ...toRefs(state),
            getOrder,
            getPatrol,
            saveMenuConfig,
            getMenu,
            treeRef,
            menus,
            delMenu,
            showMenu,
            changePath,
            projectId
        }
    },

})
</script>

<style lang="scss" scoped>
.work {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;

    .top {
        .operator {
            position: absolute;
            right: 5px;
            font-size: 12px;
        }

        .menu-list {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            align-content: flex-start;
        }

        .menu {
            position: relative;
            flex: 0 0 100/8;
            margin: 16px 16px 16px 0;
            color: rgba(119, 136, 151, 1);
            width: 124px;
            height: 100px;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            border-radius: 15px;
            border: 1px solid RGBA(54, 60, 63, 1);

            .icon {
                font-size: 48px;
            }

            .check {
                right: 5px;
                top: 5px;
                position: absolute;
            }
        }
    }

    .bottom {
        height: 63%;

        .order {
            display: flex;
            justify-content: space-between;
            margin-top: 20px;

            .order-list {
                width: calc(25% - 20px);
                height: 158px;
                border: 1px solid RGBA(54, 60, 63, 1);
                flex-direction: column;

                .order-icon {
                    font-size: 18px;

                    .icon {
                        color: rgba(199, 223, 255, 1);
                        margin-right: 8px;
                    }

                    .icon-text {
                        font-size: 18px;
                        font-family: "PingFangSC-Medium", "PingFang SC";
                        font-weight: 500;
                    }

                    .send {
                        background: linear-gradient(180deg, #ffffff 0%, #ed7327 100%);
                        -webkit-background-clip: text;
                        -webkit-text-fill-color: transparent;
                    }

                    .receive {
                        background: linear-gradient(180deg, #ffffff 0%, #e5950d 100%);
                        -webkit-background-clip: text;
                        -webkit-text-fill-color: transparent;
                    }

                    .complete {
                        background: linear-gradient(180deg, #ffffff 0%, #27a6ed 100%);
                        -webkit-background-clip: text;
                        -webkit-text-fill-color: transparent;
                    }

                    .review {
                        background: linear-gradient(180deg, #ffffff 0%, #27edbb 100%);
                        -webkit-background-clip: text;
                        -webkit-text-fill-color: transparent;
                    }
                }

                .info {
                    width: 332px;
                    height: 50px;
                    background: #101215;
                    font-size: 25px;
                    color: white;
                    margin-top: 20px;
                    opacity: 0.5;
                }
            }
        }
    }

    .tree-list {
        height: 400px;
    }
}
</style>
