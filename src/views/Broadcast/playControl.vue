<template>
<div class="h100 play_wrapper">
    <div class="btn-group search_box">
        <el-button type="primary" icon="Plus" size="mini" class="searchBtn" @click="addMusic">新建任务</el-button>
    </div>
    <div class="playControl">
        <div class="task h100">
            <el-table ref="singleTable" class="table" height="calc(100% - 60px)" :data="tableData" style="width: 100%">
                <template #empty>
                    <no-data />
                </template>
                <el-table-column type="selection" width="59">
                    <template #default="scope">
                        <el-radio-group v-model="radioId">
                            <el-radio :label='scope.row.taskID.replace("{", "").replace("}", "")' @change="handleCurrentChange(scope.row)">&nbsp;
                            </el-radio>
                        </el-radio-group>
                    </template>
                </el-table-column>
                <el-table-column label="任务名称" prop="name">
                    <template #default="scope">
                        {{scope.row.taskName}}
                    </template>
                </el-table-column>
                <!-- taskShowInfo -->
                <el-table-column label="当前播放曲面" prop="taskShowInfo" align="center" />
                <el-table-column label="状态" prop="status" align="center">
                    <template #default="scope">
                        <span>{{state[scope.row.taskStatus]}}</span>
                    </template>
                </el-table-column>
                <el-table-column label="音量" prop="taskVolume" align="center" />
            </el-table>

            <div class="music">
                <!--pause 暂停播放
                    resume 继续播放
                    previous_play 上一曲
                    next_play 下一曲
                    play_index 指定序号歌曲播放
                    progress 指定进度 当前歌曲进度的百分比(0-100)
                    play_mode 播放模式 [参考播放模式表]

                    task_process_build 任务建立中
                    task_process_work 任务正在运行
                    task_process_stop 任务停止
                    task_process_failed 任务失败
                    -->
                <span class="iconfont iconshangyishou" @click="taskCmd('previous_play')"></span>
                <!-- 恢复播放 -->
                <span class="iconfont icontingzhi2-copy" v-if="keys.get(taskID)=='pause'" @click="taskCmd('resume')"></span>
                <!-- 暂停播放 -->
                <span class="iconfont iconbofang2" v-else @click="taskCmd('pause')"></span>
         
                <span class="iconfont iconxiayishou" @click="taskCmd('next_play')"></span>
            </div>
        </div>
    </div>
    <text-dialog v-if="dialogData.visible" :dialog-data="dialogData" @getTaskPage="getTaskPage" />
</div>
</template>

<script>
import {
    reactive,
    toRefs,
    getCurrentInstance,
    onMounted,
    onBeforeUnmount
} from 'vue'
import textDialog from './components/textDialog.vue'
import {
    ElMessage
} from 'element-plus'

export default {
    name: 'BplayControl',
    components: {
        textDialog
    },
    setup() {
        const {
            proxy
        } = getCurrentInstance()
        const state = reactive({
            dialogData: {
                visible: false,
                title: '',
                milepostActive: 1,
                milepost: [],
            },
            tableData: [],
            radioId: '',
            music: [],
            musicList: [],
            terminal: [],
            terminalList: [],
            musicUrl: '',
            taskID: '',
            taskStatus: 'task_process_stop',
            interval: null,
            state: {
                task_process_build: '任务建立中',
                task_process_work: '任务工作中',
                task_process_stop: '任务停止',
                task_process_failed: '任务创建失败',
            },
            keys: new Map()
        })
        onMounted(() => {
            getTaskPage()
            getTerminalPage()
            getMusicPage()

            state.interval = setInterval(getTaskPage, 2000)
        })

        const addMusic = () => {
            state.dialogData.visible = true
            state.dialogData.title = '新建音乐任务'
            state.dialogData.milepostActive = 1
            state.dialogData.milepost = [{
                title: '选择终端'
            }, {
                title: '选择音乐'
            }, {
                title: '输入参数'
            }]
        }
        // 任务列表
        const getTaskPage = () => {
            proxy.$api.getITCTask().then(res => {
                if (res.success) {
                    const data = res.data
                    state.tableData = data
                }
            })
        }
        // 终端列表
        const getTerminalPage = () => {
            proxy.$api.getTerminal().then(res => {
                state.terminalList = res.data
                state.terminal = res.data
            })
        }
        // 音乐列表
        const getMusicPage = () => {
            proxy.$api.getITCMusic().then(res => {
                state.musicList = res.data
                state.music = res.data
            })
        }
        const handleCurrentChange = (row) => {
            debugger
            let id = row.taskID.replace("{", "").replace("}", "");
            state.taskID = id;
            state.taskStatus = row.taskStatus;
            state.radioId = id;
            // state.music = []
            // state.terminal = []
            // getTerminalById(row.sid);

            // state.music.push({
            //     fid: 1,
            //     name: row.name
            // });

        }
        // 停止任务
        const pause = (data) => {
            proxy.$api.getTaskStop({
                taskId: data.taskID.replaceAll(/(\{|\})/g, '')
            }).then(res => {
                if (res.success)
                    ElMessage({
                        type: 'success',
                        message: '操作成功'
                    })
                getTaskPage()
            })
        }
        const taskCmd = (code) => {
            if (state.taskID) {
                if (code == "pause" || code == "resume") {
                    state.keys.set(state.taskID,code)
                }
                proxy.$api.postTaskCmd({
                    code: code,
                    taskId: state.taskID
                }).then(res => {
                    if (res.success) {
                        getTaskPage()
                        ElMessage({
                            type: 'success',
                            message: '操作成功'
                        })
                    }
                })
            } else {
                ElMessage({
                    type: 'warning',
                    message: '请选择任务'
                })
            }

        }

        onBeforeUnmount(() => {
            if (state.interval) {
                clearInterval(state.interval)
            }
        })
        return {
            ...toRefs(state),
            addMusic,
            handleCurrentChange,
            pause,
            taskCmd
        }
    }
}
</script>

<style lang="scss" scoped>
.play_wrapper {
    .playControl {
        display: flex;
        height: calc(100% - 37px);

        .task {
            flex: 1;
            margin-left: 20px;

            .music {
                display: flex;
                padding: 15px 0;
                align-items: center;
                justify-content: center;
                background: rgba(16, 52, 87, 0.6);
                border: 1px solid #2f363c;

                span {
                    color: #fff;
                    font-size: 30px;
                    margin: 0 10px;
                }
            }

            .jl {
                color: #ffeb6d;
            }
        }

        .item {
            width: 418px;
            margin-left: 20px;

            .content {
                height: calc(100% - 80px);
                background-color: rgba(0, 0, 0, 0.2);
                border: 1px solid rgba(255, 255, 255, 0.3);
                padding: 10px 15px;

                .list {
                    display: flex;
                    font-size: 16px;
                    font-family: "PingFangSC-Medium", "PingFang SC";
                    font-weight: 500;
                    color: #c7dfff;
                    padding: 14px 0;
                    border-bottom: 1px dashed rgba(96, 105, 109, 0.2);

                    .icon {
                        width: 40px;
                        height: 40px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        margin-right: 9px;
                        background: rgba(255, 255, 255, 0.1);
                    }
                }
            }
        }
    }
}
</style><style>
.btn-group {
    justify-content: flex-end !important;
}
</style>
