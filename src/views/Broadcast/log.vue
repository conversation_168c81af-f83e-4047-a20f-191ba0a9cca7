<template>
<div class="h100">
    <el-form :inline="true" class="search_box " size="small">
        <!-- <el-form-item label="关键字">
            <el-input placeholder="请输入关键字" v-model="keyword"></el-input>
        </el-form-item> -->
        <el-form-item label="时间选择">
            <el-date-picker v-model="date" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
        </el-form-item>
        <el-form-item>
            <div class="searchBtn" type="text" @click="getTerminalRecordPage">
                查询
            </div>
        </el-form-item>
    </el-form>

    <el-table class="table" :data="list" height="calc(100% - 90px)">
        <template #empty>
            <no-data />
        </template>
        <el-table-column prop="time" label="时间" align="center" />
        <el-table-column prop="tasks_name" label="任务名称" align="center" />

        <el-table-column label="用户" prop="status" align="center">
            <template #default="scope">
                <span>{{scope.row.user.name}}</span>
            </template>
        </el-table-column>
        <el-table-column label="状态" prop="terminal.ip" align="center">
            <template #default="scope">
                <span>{{scope.row.status==0?'发起任务':'结束任务'}}</span>
            </template>
        </el-table-column>
        <!-- <el-table-column label="执行终端" prop="terminal.ip" align="center" show-overflow-tooltip>
            <template #default="scope">
                <span>{{scope.row.terminal.ip}}</span>
            </template>
        </el-table-column> -->
        <el-table-column label="持续时间" prop="life_time" align="center">

        </el-table-column>
    </el-table>

    <div class="page center">
        <el-pagination prev-icon="CaretLeft" next-icon="CaretRight" :page-size="size" :current-page="page" layout="total,prev, pager, next" @current-change="handleCurrentChange" :total="total">
        </el-pagination>
    </div>
</div>
</template>

<script>
import {
    reactive,
    toRefs
} from "vue";
import {
    getCurrentInstance,
    onMounted
} from "vue";
export default {
    name: 'Blog',
    setup() {
        const {
            proxy
        } = getCurrentInstance();
        const state = reactive({
            list: [],
            page: 1,
            size: 10,
            total: 0,
            date: [],
            keyword: ''
        });
        onMounted(() => {
            getTerminalRecordPage()
        })
        const getTerminalRecordPage = () => {
            proxy.$api.getTaskRecord({
                page: state.page,
                size: state.size,
                // keyword: state.keyword,
                bt: state.date.length > 0 ? dayjs(state.date[0]).format("YYYY-MM-DD 00:00:00") : '',
                et: state.date.length > 0 ? dayjs(state.date[1]).format("YYYY-MM-DD 23:59:59") : '',
            }).then((res) => {
                const data = res.data.data
                state.list = data.data
                state.total = data.total
            });
        };
        const handleCurrentChange = (page) => {
            state.page = page
            getTerminalRecordPage()
        }
        return {
            ...toRefs(state),
            handleCurrentChange,
            getTerminalRecordPage
        };
    },
};
</script>
