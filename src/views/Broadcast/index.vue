<template>
    <div class="z100">
        <div class="left">
            <div class="header flex-start">
                <img src="../../assets/images/common/head.png">
                <div> 终端列表</div>
            </div>
            <div class="input">
                <el-input v-model="keyword" @change="search" :prefix-icon="Search" placeholder="按终端名称搜索"></el-input>
            </div>
            <div class="device">

                <el-scrollbar>
                    <div class="list space-between" v-for="item in list" :key="item.id">
                        <div class="center cursor">
                            <div>
                                <span class="iconfont iconV"></span>
                            </div>
                            <div class="name">{{ item.endpointName }}</div>
                        </div>
                        <div class="center state">
                            <span style="color:#F64444" v-if="item.status == 0">离线</span>
                            <span v-else>在线</span>
                        </div>
                        <div class="position cursor">
                            <img src="../../assets/images/common/position.png" />
                        </div>
                    </div>
                </el-scrollbar>

            </div>

            <div class="page center">
                <el-pagination prev-icon="CaretLeft" next-icon="CaretRight" :page-size="size" :current-page="page"
                    layout="total,prev, pager, next" @current-change="handleCurrentChange" :total="total">
                </el-pagination>
            </div>

        </div>

        <!-- 中间查询记录 -->
        <pop :show="activeMenus.popName ? true : false" :title="activeMenus.name || ''">
            <Transition name="fade" mode="out-in">
                <component :is="activeMenus.popName"></component>
            </Transition>
        </pop>

        <div class="right">
            <div class="item" style="flex:1">
                <sub-title1 title='运行统计' />
                <div class="item-body order">
                    <div>
                        <div class="total center">终端总数</div>
                        <div class="order-left">
                            <div class="center">
                                <div>{{ on + off }}</div>
                            </div>
                        </div>
                    </div>
                    <div class="order-right">
                        <div class="order-text">
                            <div class="dot" style="background:#1AAC1A"></div><span class="text">在线数量:</span><span
                                class="num">{{ on }}</span>
                        </div>
                        <div class="order-text">
                            <div class="dot" style="background:#C47F13"></div><span class="text">离线数量:</span><span
                                class="num">{{ off }}</span>
                        </div>
                        <!-- <div class="order-text">
                        <div class="dot" style="background:#C33838"></div><span class="text">离线中:</span><span class="num">100</span>
                    </div> -->
                    </div>
                </div>
            </div>
            <div class="item" style="flex:2">
                <sub-title1 title='任务控制' />
                <div class="item-body kong">
                    <el-scrollbar v-if="tasks.length > 0">
                        <div class="list space-between" v-for="item in tasks" :key="item.taskID">
                            <div class="name">
                                <img src="../../assets/images/common/music.png" />
                                <div>{{ item.taskName }}</div>
                            </div>
                            <div class="dot"></div>
                            <div class="bra-btn space-between">
                                <div class="center cursor" @click="taskCmd('resume', item.taskID)">
                                    <div class="center">播放</div>
                                </div>

                                <div class="center cursor" @click="taskCmd('pause', item.taskID)">
                                    <div class="center">停止</div>
                                </div>
                            </div>
                        </div>
                    </el-scrollbar>
                    <noData v-else></noData>
                </div>
            </div>
            <div class="item" style="flex:2">
                <sub-title1 title='终端日志' />
                <div class="item-body event">
                    <el-scrollbar>
                        <div class="list space-between" v-for="item in records" :key="item.id">
                            <div class="name">
                                <span v-if="item.status == 1" style="color:#0CCA0F" class="iconfont iconzaixian"></span>
                                <span style="color:#F84141" v-if="item.status == 0" class="iconfont iconlixian"></span> 出
                                <div>{{ item.terminal.name }}</div>
                            </div>

                            <div>
                                {{ item.status == 0 ? '掉线' : '上线' }}
                            </div>

                            <div class="time">
                                {{ item.time }}
                            </div>

                            <div class="bar"></div>
                        </div>
                    </el-scrollbar>
                </div>
            </div>
        </div>

    </div>
</template>

<script>
import {
    defineComponent,
    getCurrentInstance,
    reactive,
    toRefs,
    computed,
    onMounted,
    watch,
    onBeforeUnmount
} from 'vue';

import {
    getCookie
} from "@/utils/cookie";
import {
    useStore
} from 'vuex';
import diagram from '@/components/diagram'
import pop from '@/components/pop'
import {
    ElMessage
} from 'element-plus'
export default defineComponent({
    name: "music",
    components: {
        diagram,
        pop,

    },
    setup() {
        const {
            proxy
        } = getCurrentInstance()
        const store = useStore();
        const state = reactive({
            keyword: "",
            size: 10,
            page: 1,
            total: 10,
            list: [], //设备列表
            mode: 0, //0---AR  3 vr  4---2D   1---BIM   2---GIS,
            popName: '',
            on: 0,
            off: 0,
            records: [], //终端日志
            tasks: [],
            interval: null,
        })
        onMounted(() => {
            let mode = getCookie("mode");
            if (mode) {
                state.mode = mode;
            }
            // 终端统计
            getTerminalPage();
            // 终端日志
            getTerminalRecordPage();

            getTaskPage();

            state.interval = setInterval(getTaskPage, 2000)

        });
        //当前激活的一级菜单
        const activeMenus = computed(() => {
            let menu = getCookie("funMenus");
            return store.state.menu.funMenus ?
                store.state.menu.funMenus :
                menu ?
                    JSON.parse(menu) :
                    "";
        });

        watch(activeMenus, (val) => {
            if (state.mode == 4 || state.mode == 1) {
                if (!val.popName) { //中间记录不需要加载设备
                    //getDeviceList();
                }
            }
        });

        const getTerminalPage = () => {
            state.on = 0;
            state.off = 0;
            proxy.$api.getTerminal().then(res => {
                if (state.keyword) {
                    state.list = res.data.filter(d => d.endpointName.includes(state.keyword))
                } else {
                    state.list = res.data
                }
                res.data.forEach(d => {
                    if (d.status == 0) {
                        state.off++;
                    } else if (d.status == 1) {
                        state.on++;
                    }
                })
            })
        }

        const handleCurrentChange = (page) => {
            state.page = page;

        }

        const getTerminalRecordPage = () => {
            proxy.$api.getTerminalRecord({
                page: state.page,
                size: state.size,
            }).then((res) => {
                const data = res.data.data;
                state.records = data.data
            });
        };

        // 任务列表
        const getTaskPage = () => {
            proxy.$api.getITCTask().then(res => {
                if (res.success) {
                    state.tasks = res.data
                }
            })
        }

        const search = () => {
            state.page = 1;
            getTerminalPage();
        }
        const taskCmd = (code, id) => {

            if (id) {
                id = id.replace("{", "").replace("}", "")
                proxy.$api.postTaskCmd({
                    code: code,
                    taskId: id
                }).then(res => {
                    if (res.success) {
                        // getTaskPage()
                        ElMessage({
                            type: 'success',
                            message: '操作成功'
                        })
                    }
                })
            } else {
                ElMessage({
                    type: 'warning',
                    message: '请选择任务'
                })
            }

        }

        onBeforeUnmount(() => {
            if (state.interval) {
                clearInterval(state.interval)
            }
        })

        return {
            ...toRefs(state),
            activeMenus,
            handleCurrentChange,
            search,
            taskCmd

        }
    }
});
</script>

<style lang="scss" scoped>
.bra-btn {
    background-size: 100%;
    margin-left: 7px;

    &>div {
        flex: 1
    }

    &>div:first-child {
        margin-right: 10px;
    }

    &>div>div {

        width: 54px;
        height: 28px;
        background: #1B6DC2;
        border-radius: 4px;

        box-shadow: 0px 0px 3px 3px #2CBDEB inset;
    }
}</style>
