<template>
<div class="layout_wrapper manage">
    <div class="left-manage">
        <sub-title2 title="终端列表" />
        <div class="btn">
            <div class="btn-group">
                <el-button type="primary" icon="Plus" size="mini" class="addBtn" @click="addSubarea">新增</el-button>
                <el-button type="primary" icon="Minus" size="mini" class="deleteBtn" @click="delPlan">删除</el-button>
                <el-button type="primary" icon="Edit" size="mini" class="amendBtn" @click="delPlan">修改</el-button>
            </div>
            <div class="btn-group">
                <el-button type="text" size="mini" class="settingBtn" @click="editPlan">导入
                </el-button>
            </div>
        </div>
        <el-scrollbar class="tree-list tree">
            <el-tree ref="tree" :data="data" node-key="id" check-strictly :props="props" @check="handleCheckChange" />
        </el-scrollbar>
    </div>
    <div class="right-manager">
        <div class="btn-group">
            <el-button type="primary" icon="Plus" size="mini" class="addBtn" @click="add">新增</el-button>
            <el-button type="primary" icon="Minus" size="mini" class="deleteBtn" @click="delMain">删除</el-button>
        </div>
        <div class="table">
            <el-table :data="list" :height="tableHeight" fit @select="select" @select-all="select">
                <template #empty>
                    <no-data />
                </template>
                <el-table-column prop="name" label="序号" align="center">
                </el-table-column>
                <el-table-column prop="content" label="终端ID" align="center">
                </el-table-column>
                <el-table-column prop="content" label="终端名称" align="center">
                </el-table-column>
                <el-table-column prop="content" label="终端IP" align="center">
                </el-table-column>
                <el-table-column prop="content" label="终端型号" align="center">
                </el-table-column>

            </el-table>
        </div>
        <div class="center page">
            <el-pagination prev-icon="CaretLeft" next-icon="CaretRight" :page-size="size" :current-page="page" layout="total,prev, pager, next" @current-change="handleCurrentChange" :total="total">
        </el-pagination>
        </div>
    </div>
    <el-dialog align-center append-to-body   v-model="dialogVisible" title="分区名称" custom-class="custom_dialog" width="482px">
        <el-form ref="form" :model="subareaForm">
            <el-form-item label="名称">
                <el-input placeholder="请输入名称" />
            </el-form-item>
        </el-form>
        <template #footer>
            <div class="dialog-footer">
                <el-button type="primary" class="saveBtn" size="mini" @click="saveContent('form')">确 定</el-button>
            </div>
        </template>
    </el-dialog>
</div>
</template>

<script lang="ts">
import {
    reactive,
    toRefs
} from "vue";
export default {
    name: 'Bmanage',
    setup() {
        const state = reactive({
            tableHeight: window.innerHeight * 0.6,
            page: 1,
            size: 10,
            total: 0,
            list: [],
            data: [{
                    label: "一级 1",
                    children: [{
                        label: "二级 1-1",
                        children: [{
                            label: "三级 1-1-1",
                        }, ],
                    }, ],
                },
                {
                    label: "一级 2",
                    children: [{
                            label: "二级 2-1",
                            children: [{
                                label: "三级 2-1-1",
                            }, ],
                        },
                        {
                            label: "二级 2-2",
                            children: [{
                                label: "三级 2-2-1",
                            }, ],
                        },
                    ],
                },
                {
                    label: "一级 3",
                    children: [{
                            label: "二级 3-1",
                            children: [{
                                label: "三级 3-1-1",
                            }, ],
                        },
                        {
                            label: "二级 3-2",
                            children: [{
                                label: "三级 3-2-1",
                            }, ],
                        },
                    ],
                },
            ],
            props: {
                children: "children",
                label: "label",
            },
            dialogVisible: false,
            subareaForm: {},
        });
        const addSubarea = () => {
            state.dialogVisible = true;
        };
        return {
            ...toRefs(state),
            addSubarea,
        };
    },
};
</script>

<style lang="scss" scoped>
.manage {
    display: flex;

    .left-manage {
        width: 369px;
        margin-right: 15px;

        .tree-list {
            padding: 8px;
            border: 1px solid #2b2e32;
            height: calc(100% - 150px);
        }

        .btn {
            display: flex;
            justify-content: space-between;
        }
    }

    .right-manager {
        flex: 1;
    }
}
</style>
