<template>
<div class="z100">
    <div class="left">
        <div class="header flex-start">
            <img src="../../assets/images/common/head.png">
            <div> 工单列表</div>
        </div>
        <div class="input">
            <el-input v-model="keyword" @change="search" :prefix-icon="Search" placeholder="工单名称搜索"></el-input>
        </div>
        <div class="device">

            <el-scrollbar>
                <div class="list space-between" v-for="item in list" :key="item.id">
                    <div class="center cursor">
                        <div class="icon center">
                            <span class="iconfont icongongdan"></span>
                        </div>
                        <div class="name">{{item.name}}</div>
                    </div>
                    <div class="center state">
                        <div style="color:green">{{getStatus(item.status)}}</div>
                    </div>
                    <div class="position cursor">
                        <img src="../../assets/images/common/position.png" />
                    </div>
                </div>
            </el-scrollbar>

        </div>

        <div class="page center">
            <el-pagination prev-icon="CaretLeft" next-icon="CaretRight" :page-size="size" :current-page="page" layout="total,prev, pager, next" @current-change="handleCurrentChange" :total="total">
            </el-pagination>
        </div>

    </div>

    <!-- 中间查询记录 -->
    <pop :show="activeMenus.popName?true:false" :title="activeMenus.name||''">
        <Transition name="fade" mode="out-in">
        <component :is="activeMenus.popName"></component>
        </Transition>
    </pop>

    <div class="right">
        <div class="item" style="flex:1">
            <sub-title1 title='工单统计' />
            <div class="item-body order">
                <div>
                    <div class="total center">工单总数</div>
                    <div class="order-left">
                        <div class="center">
                            <div>{{total}}</div>
                        </div>
                    </div>
                </div>
                <div class="order-right">
                    <div class="order-text">
                        <div class="dot" style="background:#1AAC1A"></div><span class="text">待保设备:</span><span class="num">20</span>
                    </div>
                    <div class="order-text">
                        <div class="dot" style="background:#C47F13"></div><span class="text">维保计划:</span><span class="num">10</span>
                    </div>

                </div>
            </div>
        </div>
        <div class="item" style="flex:2">
            <sub-title1 title='本月报修' />
            <div class="item-body kong">
                <div class="month_repair">
                    <div v-for="(item, i) in orderList" :key="i" class="list" :style="{ color: item.color }">
                        <div class="icon" :style="{ background: item.color }"></div>
                        {{ item.label }}
                        <span class="value">{{ item.value }}</span>
                    </div>
                </div>
                <div class="sub_echart">
                    <subEchart :echartData="echartSub" :colors="colors"></subEchart>
                </div>
            </div>
        </div>
        <div class="item" style="flex:2">
            <sub-title1 title='本年度工单趋势' />
            <div class="item-body event">
                <Alarm :xAxisData="xAxisData" :echartData="echartData" />
            </div>
        </div>
    </div>

</div>
</template>

<script>
import {
    defineComponent,
    getCurrentInstance,
    reactive,
    toRefs,
    computed,
    onMounted,
    watch,
} from 'vue';

import {
    getCookie
} from "@/utils/cookie";
import {
    useStore
} from 'vuex';
import Alarm from '@/components/echarts/weekEventEchart.vue'
import pop from '@/components/pop'
import subEchart from "./subEchart.vue";
export default defineComponent({
    name: "order",
    components: {
        Alarm,
        pop,
        subEchart
    },

    setup() {
        const {
            proxy
        } = getCurrentInstance()
        const store = useStore();
        const state = reactive({
            keyword: "",
            size: 10,
            page: 1,
            total: 100,
            list: [],
            echartData: [10, 11, 12, 0, 8, 6, 2],
            xAxisData: ['星期一', '星期二', '星期三', '星期四', '星期五', '星期六', '星期天'],
            orderList: [{
                    label: "已处理",
                    value: 0,
                    color: "#27EDBB",
                },
                {
                    label: "未处理",
                    value: 0,
                    color: "#E3731B",
                },
            ],
            colors: ["#27EDBB", "#E3731B"],
            echartSub: [{
                    name: "已处理",
                    value: 0,
                },
                {
                    name: "未处理",
                    value: 0,
                },
            ],
            state: [{
                    value: -1,
                    name: '待派单',
                },
                {
                    value: 1,
                    name: '待接单',
                },
                {
                    value: 2,
                    name: '挂起',
                },
                {
                    value: 3,
                    name: '正常关闭',
                },
                {
                    value: 4,
                    name: '异常关闭',
                },
                {
                    value: 5,
                    name: '退回',
                },
                {
                    value: 6,
                    name: '完成',
                },
                {
                    value: 8,
                    name: '恢复工单',
                },
                {
                    value: 9,
                    name: '处理中',
                },
            ],
        })
        //当前激活的一级菜单
        const activeMenus = computed(() => {
            let menu = getCookie("funMenus");
            return store.state.menu.funMenus ?
                store.state.menu.funMenus :
                menu ?
                JSON.parse(menu) :
                "";
        });
        onMounted(() => {
            getOrderPage();
            getRepairStatus();
            getOrderTrend()
        });
        const getStatus = (val) => {
            let name = ''
            state.state.forEach((t) => {
                if (t.value == val) {
                    name = t.name
                }
            })
            return name
        }
        const getOrderPage = () => {
            proxy.$api.getOrder({
                page: state.page,
                size: state.size,
                projectId: getCookie("gh_projectId"),
            }).then((res) => {
                state.list = res.data
                state.total = res.total
            })
        }

        const handleCurrentChange = (page) => {
            state.page = page;
            getOrderPage();
        }

        const search = () => {
            state.page = 1;
            getOrderPage();
        }
        const getRepairStatus = async () => {
            let {
                data
            } = await proxy.$api.getRepairStatus({
                projectId: getCookie("gh_projectId"),
            });
            state.orderList[0].value = data.complete;
            state.orderList[1].value = data.unComplete;
            state.echartSub[0].value = data.complete;
            state.echartSub[1].value = data.unComplete;
        };
        const getOrderTrend = async () => {
            let {
                data
            } = await proxy.$api.getOrderTrend({
                projectId: getCookie("gh_projectId"),
            });

            state.xAxisData = [];
            state.echartData = [];
            if (data) {
                Object.keys(data).sort().forEach(key => {
                    state.xAxisData.push(key+'月');
                    state.echartData.push(data[key]);
                })
            }
        };
        return {
            ...toRefs(state),
            handleCurrentChange,
            search,
            activeMenus,
            getStatus,
            getRepairStatus
        }
    }
});
</script>

<style lang="scss" scoped>

</style>
