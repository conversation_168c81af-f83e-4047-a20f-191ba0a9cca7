<template>
<el-container class="layout-content">
    <el-main>
        <!-- 页面内容 -->
        <div class="content">
            <slot></slot>
        </div>
    </el-main>
</el-container>
</template>

<style lang="scss" scoped>
.layout-content {
    position: absolute;
    width: 100%;
    height: 100%;

    .el-main {
        padding: 0;
        overflow: hidden;
        .content {
            height: 100%;
            box-sizing: border-box;
        }
    }
}
</style>
