<template>
    <el-dialog align-center append-to-body   @closed="closeDialog" custom-class="custom_dialog" v-model="dialogData.visible" width="1200px" :title="dialogData.title" top="64px" >
        <div class="wrapper">
            <div class="leftWrapper">
                <el-scrollbar>
                    <div class="card">
                        <sub-title2 title="报警设备"></sub-title2>
                        <div class="card-body">
                            <div class="card-data">
                                <div class="card-data-title">报警设备:</div>
                                <div class="card-data-val">
                                    {{ alarm.deviceName }}
                                </div>
                            </div>
                            <div class="card-data">
                                <div class="card-data-title">报警指标:</div>
                                <div class="card-data-val">
                                    {{ alarm.standardName }}
                                </div>
                            </div>
                            <div class="card-data">
                                <div class="card-data-title">当前值:</div>
                                <div class="val">{{ alarm.value }}</div>
                            </div>
                        </div>
                    </div>
                    <div class="card">
                        <sub-title2 title="联动指令">
                            <el-radio-group v-model="alarm.auto" size="mini">
                                <el-radio-button disabled :label="false">手动</el-radio-button>
                                <el-radio-button disabled :label="true">自动</el-radio-button>
                            </el-radio-group>
                        </sub-title2>
                        <div class="card-body">
                            <el-timeline>
                                <el-timeline-item color="#3e82d1">
                                    <div class="link-data">
                                        <div>报警录像</div>
                                        <div @click="openRecord">
                                            <i class="pointer iconfont icontingzhi2-copy"></i>
                                        </div>
                                    </div>
                                </el-timeline-item>
                                <el-timeline-item color="#3e82d1">
                                    <div class="link-data">
                                        <div>报警抓图</div>
                                        <div @click="openSnap">
                                            <i class="pointer iconfont icontingzhi2-copy"></i>
                                        </div>
                                    </div>
                                </el-timeline-item>
                                <el-timeline-item color="#3e82d1">
                                    <div class="link-data">
                                        <div>联动场景</div>
                                        <div @click="openScene">
                                            <i class="pointer iconfont icontingzhi2-copy"></i>
                                        </div>
                                    </div>
                                </el-timeline-item>
                            </el-timeline>
                        </div>
                    </div>
                    <div class="card">
                        <sub-title2 title="报警处理"></sub-title2>
                        <div class="card-body">
                            <el-timeline>
                                <el-timeline-item type="primary" color="#3e82d1">
                                    <div class="progress-data">
                                        <div>现场复核</div>
                                        <div style="flex: 1; margin-left: 10px">
                                            <el-input v-model="content" type="textarea" placeholder="请输入具体描述文字"></el-input>
                                        </div>
                                    </div>
                                </el-timeline-item>
                                <el-timeline-item type="primary" color="#3e82d1">
                                    <div class="progress-data">
                                        <div>生成工单</div>
                                        <el-tag class="cursor" size="mini" type="primary" @click="createOrder" effect="plain">
                                            生成工单
                                        </el-tag>
                                    </div>
                                </el-timeline-item>
                                <el-timeline-item type="primary" color="#3e82d1">
                                    <div class="progress-data">
                                        <div>结案,移至历史记录</div>
                                        <el-tag @click="process" class="cursor" size="mini" type="primary" effect="plain">
                                            结案
                                        </el-tag>
                                    </div>
                                </el-timeline-item>
                            </el-timeline>
                        </div>
                    </div>
                </el-scrollbar>
            </div>
            <div class="rightWrapper">
                <div class="iframe">
                    <iframe :src="iframe"></iframe>
                </div>
                <div class="video_list">
                    <div class="col">
                        <playVideo :token="token1" :ip="ip1"></playVideo>
                    </div>
                    <div class="col">
                        <playVideo :token="token2" :ip="ip2"></playVideo>
                    </div>
                </div>
            </div>
        </div>
    
        <el-dialog align-center append-to-body   class="form " v-model="show" title="报警录像" center width="670px"  custom-class="custom_dialog cctv-dialog">
            <video :src="src" controls="controls" style="width:100%;height:500px">
                您的浏览器不支持 video 标签。
            </video>
        </el-dialog>
    
        <el-dialog align-center append-to-body   class="form " v-model="showImg" title="报警抓图" center width="670px"  custom-class="custom_dialog cctv-dialog">
            <img :src="src1" height="100%" width="100%" style="width:100%;height:500px" />
        </el-dialog>
    
        <el-dialog align-center append-to-body   class="cctv-dialog" v-model="showScene" title="联动场景" center width="670px"  custom-class="custom_dialog cctv-dialog">
            <el-table  fit :data="scenes" style="width: 100%">
                <template #empty>
                    <no-data />
                </template>
                <el-table-column align="center" property="name" label="设备名称" width="120">
                </el-table-column>
                <el-table-column align="center" property="standardName" label="指标" width="120">
                </el-table-column>
                <el-table-column align="center" property="variable" label="联动变量">
                </el-table-column>
                <el-table-column align="center" property="varValue" label="联动指令">
                </el-table-column>
            </el-table>
            <template #footer>
                <div class="dialog-footer">
                    <el-button v-if="alarm.auto" type="primary" @click="send">发送指令</el-button>
                </div>
            </template>
        </el-dialog>
    </el-dialog>
    </template>
    
    <script>
    import {
        defineComponent,
        getCurrentInstance,
        inject,
        reactive,
        toRefs,
        watch,
    } from "vue";
    import playVideo from "@/components/cctv/Video.vue";
    import socket from "@/utils/socket";
    import {
        getCookie
    } from "@/utils/cookie";
    import { ElMessage } from "element-plus";
    import dayjs from "dayjs";
    export default defineComponent({
        props: {
            dialogData: {
                title: "",
                visible: false,
                loading: false,
            },
        },
        components: {
            playVideo,
        },
        setup(props) {
            const emitter = inject("mitt");
            const {
                proxy
            } = getCurrentInstance();
            const state = reactive({
                alarm: {
                    standardName: "",
                    deviceName: "",
                    value: "",
                    auto: false,
                    alarmVideo: "",
                    alarmGuid: "",
                    sceneId: null,
                },
               
                url:   window.PROD_9008_API+ "/runview.html?diagramId=",
                iframe: "",
                conf: null,
                conf1: null,
                ip1: "",
                token1: "",
                ip2: "",
                token2: "",
                src: "",
                src1: "",
                showScene: false,
                sockets: null,
                showImg: false,
                show: false,
                guid: "",
                content: "",
            });
            state.sockets = inject("socket");
    
            watch(
                () => props.dialogData,
                (val) => {
                    getAlarmById(val.alarmGuid);
                    if (state.guid != val.alarmGuid) {
                        if (val.alarmVariable) {
                            let variable = val.alarmVariable.split(":");
                            state.alarm = Object.assign(state.alarm, val);
                            getVariable(variable[0], variable[1], variable[2], variable[3]);
                        }
                        if (val.alarmForm) {
                            state.iframe = state.url + val.alarmForm;
                        }
                        if (val.alarmVideo) {
                            let data = val.alarmVideo.split("#");
                            let [, , server, port] = data[0].split("_");
                            let [token, title] = data[1].split("_");
                            state.ip1 = server + ":" + port;
                            state.token1 = token+'_'+dayjs().unix();
                        }
                        // 109_36.152.149.51_36.152.149.51_2600#servertoken1-44_会议室44
                        if (val.alarmVideo2) {
                            let data = val.alarmVideo2.split("#");
                            let [, , server, port] = data[0].split("_");
                            let [token, title] = data[1].split("_");
                            state.ip2 = server + ":" + port;
                            state.token2 = token+'_'+dayjs().unix();
                        }
                    }
                    state.guid = val.alarmGuid;
                }, {
                    deep: true,
                }
            );
    
            const getAlarmById = (uuid) => {
                proxy.$api
                    .getAlarmById({
                        uuid,
                    })
                    .then((res) => {
                        if (res.data) {
                            state.alarm = Object.assign(state.alarm, res.data);
                        }
                    });
            };
    
            const getVariable = (server, chal, controller, variable) => {
                proxy.$api
                    .getVariableData(server, chal, controller, variable)
                    .then((res) => {
                        if (res.data && res.data.Value) {
                            state.alarm.value = res.data.Value;
                        }
                    });
            };
    
            const openRecord = () => {
                if (state.alarm.auto) {
                    //手动录像
                    if (state.alarm.alarmVariable) {
                        state.sockets.emit("SnapVideo", state.alarm.alarmGuid, (res) => {
                            if (res) {
                                ElMessage.success({
                                    type: "success",
                                    message: "录像成功",
                                });
                            }
                        });
                    }
                } else {
                    state.show = true;
                    if (state.alarm.alarmVideo) {
                        {
                            if (state.alarm.alarmVideoUrl) {
                                state.src = state.alarm.alarmVideoUrl;
                            }
                        }
                    }
                }
            };
            const openSnap = () => {
                if (state.alarm.auto) {
                    //手动抓拍
                    if (state.alarm.alarmVariable) {
                        state.sockets.emit("SnapPhoto", state.alarm.alarmGuid, (res) => {
                            if (res) {
                                ElMessage.success({
                                    type: "success",
                                    message: "抓拍成功",
                                });
                            }
                        });
                    }
                } else {
                    state.showImg = true;
                    if (state.alarm.alarmVideo) {
                        {
                            if (state.alarm.alarmSnapUrl) {
                                state.src1 = state.alarm.alarmSnapUrl;
                            }
                        }
                    }
                }
            };
            const openScene = () => {
                state.showScene = true;
                if (state.alarm.sceneId) {
                    getScene(state.alarm.sceneId);
                }
            };
            const createOrder = () => {
                if (state.alarm && state.alarm.alarmGuid) {
                    proxy.$api.createOrderByAlarm({
                        uuid: state.alarm.alarmGuid,
                        projectId: getCookie("gh_projectId"),
                    });
    
                    process();
    
                }
            };
            const closeDialog = () => {
                emitter.emit("stopVideo");
            };
            const process = () => {
                if (state.alarm.alarmVariable) {
                    socket.ProcessAlarm(
                        state.sockets,
                        state.alarm.alarmVariable,
                        state.alarm.alarmGuid,
                        state.content,
                        getCookie("gh_id"),
                        getCookie("gh_projectId")
                    );
                    state.alarm = Object.assign(state.alarm, {
                        standardName: "",
                        deviceName: "",
                        value: "",
                    });
    
                    state.content = "";
                    props.dialogData.visible = false;
                    emitter.emit("stopAudio");
                }
            };
            return {
                ...toRefs(state),
                getVariable,
                getAlarmById,
                openRecord,
                openSnap,
                openScene,
                createOrder,
                closeDialog,
                process,
            };
        },
    });
    </script>
    
    <style lang="scss" scoped>
    .wrapper {
        display: flex;
        width: 100%;
        height: 100%;
    
        .leftWrapper {
            width: 357px;
            margin-right: 10px;
            font-family: "Alibaba-PuHuiTi";
            font-weight: 400;
            font-size: 16px;
    
            .card-body {
                .card-data {
                    display: flex;
                    margin-bottom: 15px;
                    padding-left: 15px;
    
                    &:first-child {
                        margin-top: 20px;
                    }
    
                    .card-data-title {
                        color: #889cc3;
                        margin-right: 10px;
                    }
    
                    .card-data-val {
                        color: #fff;
                    }
    
                    .val {
                        font-size: 16px;
                        font-family: "Alibaba-PuHuiTi";
                        font-weight: 400;
                        color: #1bc0ed;
                    }
                }
    
                .link-data {
                    display: flex;
                    justify-content: space-between;
                    font-family: "Alibaba-PuHuiTi";
                    font-weight: 400;
                    padding: 0 8px 0 0;
                    color: #ffffff;
                    font-size: 16px;
    
                    .iconfont {
                        color: #02e287;
                    }
                }
    
                .progress-data {
                    display: flex;
                    font-size: 16px;
                    font-family: "Alibaba-PuHuiTi";
                    font-weight: 400;
                    color: #fff;
    
                    .el-tag {
                        height: 18px;
                        line-height: 18px;
                        background: rgba(27, 192, 237, 0.3);
                        border: 1px solid #1bc0ed;
                        margin-left: 10px;
                    }
                }
            }
        }
    
        .rightWrapper {
            flex: 1;
            border: 1px solid #2e3a42;
            padding: 10px;
    
            .iframe {
                width: 100%;
                height: calc(100% - 223px);
                margin-bottom: 10px;
            }
    
            .video_list {
                display: flex;
                height: 208px;
    
                .col {
                    flex: 1;
                    background: rgba(34, 52, 62, 0.38);
                    border: 2px solid rgba(255, 255, 255, 0.5);
                    margin: 5px;
                }
            }
        }
    }
    </style>
    