<template>
<div class="road">

    <div class="scene">
        <div class=" center">
            <span>第一人称</span>
        </div>
        <div class=" center">
            <span>第三人称</span>
        </div>
        <div class=" center">
            <span>飞行视角</span>
        </div>
    </div>

    <div>
        <el-switch active-text="手动控制" inactive-text="自动控制" v-model="mode"></el-switch>
    </div>

    <div class="speed">
        <div class="name">移动速度</div>
        <el-slider v-model="time" :show-tooltip="false" />
    </div>

    <div class="lines">
        <div class="name">漫游路线</div>
        <div class="line center">1</div>
        <div class="line center">2</div>
        <div class="line center">3</div>
    </div>

    <div class="close" @click="clickRoad">
        <i class="iconfont iconchahao"></i>
    </div>

</div>
</template>

<script>
import {
    defineComponent,
    reactive,
    toRefs
} from 'vue'

export default defineComponent({
    setup(props,{emit}) {
        const state = reactive({
            time: 50,
            mode: true
        });
        const clickRoad = () => {

            emit('clickRoad')
        }
        return {
            ...toRefs(state),
            clickRoad
        }
    },
})
</script>

<style lang="scss" scoped>
.road {
    width: 418px;
    height: 327px;
    background: #020912;
    opacity: 0.85;
    border-top: 1px solid #6C9ED1;

    font-size: 16px;
    font-family: "Alibaba-PuHuiTi";
    font-weight: bold;
    font-style: italic;
    color: #FFFFFF;
    display: flex;
    flex-direction: column;

    &>div {
        margin-bottom: 20px;
        padding-left: 30px;
    }

    .close {
        color: white;
        position: absolute;
        right: 15px;
        top: 15px;
        cursor: pointer;
    }

    .scene {
        margin-top: 60px;
        display: flex;
        align-items: center;

        &>div:nth-of-type(2) {
            margin: 0 13px;
        }

        &>div {
            height: 40px;
            width: 104px;
            background-image: url("../img/视角按钮.png");
            background-repeat: no-repeat;

            span {
                font-size: 16px;
                font-family: "Alibaba-PuHuiTi";
                font-weight: bold;
                font-style: italic;
                color: #FFFFFF;
                background: linear-gradient(180deg, #FFFFFF 0%, #8ED6FF 100%);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
            }
        }
    }

    .lines {
        display: flex;
        align-items: center;

        .name {
            margin-right: 13px;
            white-space: nowrap;
        }

        .line {
            width: 32px;
            height: 32px;
            background: url("../img/漫游.png") no-repeat;
        }

        .line:nth-of-type(3) {
            margin: 0 13px;
        }

        .line:nth-of-type(1) {
            margin-left: 14px;
        }

    }

    .time {
        width: 60%;
        margin: 0 auto;

    }

    .speed {
        display: flex;
        align-items: center;

        .name {
            margin-right: 13px;
            white-space: nowrap;
        }
    }

}

::v-deep .el-slider__bar {
    background: linear-gradient(-90deg, #1E79F2, #1CD3DE);

}

::v-deep .el-slider__runway {
    background: #2D435E !important;
}

:deep .el-switch__label.is-active,
:deep .el-switch__label {
    font-size: 16px !important;
    font-family: "Alibaba-PuHuiTi";
    font-weight: bold;
    font-style: italic;
    color: #FFFFFF;
}

:deep .el-switch__label * {
    font-size: 16px !important;
}
</style>
