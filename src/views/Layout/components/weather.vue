<template>
<div class="weather center">
    <div class="row">
        <el-switch active-text="手动模拟" inactive-text="联网实时" v-model="mode"></el-switch>
    </div>
    <div class="row">
        <div class="h_center">
            <img src="../img/晴.png" />
            <div>晴</div>
        </div>
        <div class="h_center">
            <img src="../img/多云.png" />
            <div>多云</div>
        </div>
        <div class="h_center">
            <img src="../img/阵雨.png" />
            <div>雷阵雨</div>
        </div>
        <div class="h_center">
            <img src="../img/阴.png" />
            <div>阴</div>
        </div>
    </div>
    <div class="row">
        <div class="h_center">
            <img src="../img/小雨.png" />
            <div>小雨</div>
        </div>
        <div class="h_center">
            <img src="../img/大雨.png" />
            <div>大雨</div>
        </div>
        <div class="h_center">
            <img src="../img/雾霾.png" />
            <div>雾霾</div>
        </div>
        <div class="h_center">
            <img src="../img/小雪.png" />
            <div>小雪</div>
        </div>
    </div>
    <div class="row">
        <div class="h_center">
            <img src="../img/大雪.png" />
            <div>大雪</div>
        </div>
        <div class="h_center">
            <img src="../img/风暴.png" />
            <div>龙卷风</div>
        </div>
        <div class="h_center">
            <img src="../img/沙尘暴.png" />
            <div>沙尘暴</div>
        </div>
        <div class="h_center">
            <img src="../img/强沙尘暴.png" />
            <div>强沙尘暴</div>
        </div>
    </div>

    <div class="close" @click="clickWeather">
        <i class="iconfont iconchahao"></i>
    </div>
    <div class="time">
        <el-slider v-model="time" :show-tooltip="false" />
    </div>
</div>
</template>

<script>
import {
    defineComponent,
    reactive,
    toRefs,
} from 'vue'

export default defineComponent({
    setup(props,{emit}) {
        const state = reactive({
            time: 50,
            mode: true,
            emit:null,
        });
        const clickWeather = () => {
            
            emit('clickWeather')
        }
        return {
            ...toRefs(state),
            clickWeather
        }
    },
})
</script>

<style lang="scss" scoped>
.weather {
    width: 418px;
    height: 400px;
    background: #020912;
    opacity: 0.85;
    border-top: 1px solid #6C9ED1;

    font-size: 14px;
    font-family: "Alibaba-PuHuiTi";
    font-weight: 300;
    color: #92ACCC;
    flex-direction: column;

    position: relative;

    img {
        margin-bottom: 5px;
    }

    .h_center {
        width: 72px;
        height: 80px;
        background: #020D1C;
        border: 1px solid #2D435E;
    }

    .row {
        width: 339px;
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
    }

    .row:last-of-type {
        margin-bottom: unset;
    }

    .close {
        color: white;
        position: absolute;
        right: 15px;
        top: 15px;
        cursor: pointer;
    }

    .time {
        width: 338px;
        margin: 0 auto;
    }

}

::v-deep .el-slider__bar {
    background: linear-gradient(-90deg, #1E79F2, #1CD3DE);

}

::v-deep .el-slider__runway {
    background: #2D435E !important;

}

:deep .el-switch__label.is-active,
:deep .el-switch__label {
    font-size: 16px;
    font-family: "Alibaba-PuHuiTi";
    font-weight: bold;
    font-style: italic;
    color: #FFFFFF;
}
</style>
