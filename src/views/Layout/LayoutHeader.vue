<template>
    <div class="layout-header">
        <div class="header_left">
            <div class="cursor">
                <img width="100" src="../../assets/images/logo1.png" />
            </div>
            <div class="temperature">
                <!-- <div><span class="num">{{weather}} {{temperature}}</span>℃</div> -->
                <div>
                    {{ date }}
                </div>
            </div>

            <div class=" mode">
                <!-- <div class="cursor vr" :style="{color:mode==0?'#e7a91f':''}" @click="changeMode(0)">
                    <img :src="mode==0?require('../../assets/images/head/ARc.png'):require('../../assets/images/head/AR.png')" />
                </div> -->
                <!-- <div class="cursor vr" :style="{color:mode==3?'#e7a91f':''}" @click="changeMode(3)">
                    <img :src="mode==3?require('../../assets/images/head/VRc.png'):require('../../assets/images/head/VR.png')" />
                </div> -->
                <!-- <div class="cursor vr" :style="{color:mode==4?'#e7a91f':''}" @click="changeMode(4)">
                    <img :src="mode==4?require('../../assets/images/head/2Dc.png'):require('../../assets/images/head/2D.png')" />
                </div> -->
                <!-- <div class="cursor vr" :style="{color:mode==1?'#e7a91f':''}" @click="changeMode(1)">
                    <img :src="mode==1?require('../../assets/images/head/BIMc.png'):require('../../assets/images/head/BIM.png')" />
                </div> -->
                <!-- <div class="cursor" :style="{color:mode==2?'#e7a91f':''}" @click="changeMode(2)">
                    <img :src="mode==2?require('../../assets/images/head/GISc.png'):require('../../assets/images/head/GIS.png')" />
                </div> -->
            </div>
        </div>
        <div class="logo">
            <div class="center_container" @click="goHome">
                <div class="title">光明城科学城智慧运营管理中心</div>
            </div>
        </div>
        <div class="header_right">
            <div @click="showScene" class="scene center cursor">
                <span style="font-size: 26px;" class="iconfont iconyanjing_xianshi"></span>
            </div>


            <div class="icon" @click="showAlert" @mouseup.stop>
                <el-badge :value="alarmTotal" class="alarm-icon" type="danger" :class="{ ld: alarmTotal != 0 }">
                    <img class="icon" src="../../assets/images/head/icon_02c.png" />
                </el-badge>
            </div>
            <div class="icon" @click="showMessage">
                <el-badge :value="mesCount" class="alarm-icon" type="danger">
                    <img class="icon" src="../../assets/images/head/icon_03c.png" />
                </el-badge>
            </div>

            <!-- <div class="icon cursor" @click="clickRoad">
                <img class="icon" src="../../assets/images/head/icon_05c.png" />
            </div> -->

            <el-dropdown trigger="click" @command="userCommand">
                <span class="el-dropdown-link">
                    {{ userName }}
                    <i class="el-icon-arrow-down el-icon--right"></i>
                </span>
                <template #dropdown>
                    <el-dropdown-menu>
                        <el-dropdown-item command="personal">个人中心</el-dropdown-item>
                        <el-dropdown-item command="system">系统配置</el-dropdown-item>
                        <el-dropdown-item command="logout">退出登录</el-dropdown-item>
                    </el-dropdown-menu>
                </template>
            </el-dropdown>
        </div>
        <teleport to="body">
            <alarm-list id="treeWrap" v-show="dialogVisible" :total="alarmTotal" @current-change="handleCurrentChange" />
        </teleport>
        <alarm-dialog :dialogData="dialogData"></alarm-dialog>
        <audio hidden="true" loop>
            <source src="../../assets/images/woop.mp3" type="audio/mpeg" />
        </audio>
        <!-- 天气场景 -->
        <Transition name="fade" mode="out-in" appear>
            <div class="weather" v-if="showWeather">
                <weather @clickWeather="clickWeather"></weather>
            </div>
        </Transition>

        <!-- 漫游场景 -->
        <Transition name="fade" mode="out-in" appear>
            <div class="road" v-if="showRoad">
                <road @clickRoad="clickRoad"></road>
            </div>
        </Transition>

    </div>
</template>
    
<script>
import dayjs from 'dayjs'
import {
    useStore
} from "vuex";
import {
    toRefs,
    reactive,
    onMounted,
    inject,
    computed,
    watch,
    nextTick,
    getCurrentInstance,
    onBeforeUnmount
} from "vue";
import {
    onBeforeRouteUpdate,
    useRoute,
    useRouter
} from "vue-router";
import {
    getCookie,
    removeToken,
    setCookie
} from "@/utils/cookie";
import alarmList from "./components/alarmList.vue";
import alarmDialog from "./components/alarmDialog.vue";
import weatherData from "@/data/weather.js";
import weather from './components/weather.vue'
import road from './components/road.vue'
export default {
    components: {
        alarmList,
        alarmDialog,
        weather,
        road
    },
    sockets: {
        // 通过vue实例对象sockets实现组件中的事件监听
        connect: function () {
            // socket的connect事件
            this.$socket.emit("GetAdviceMes", getCookie("gh_id"));
            if (getCookie("gh_projectId")) {
                this.$socket.emit("SetProject", getCookie("gh_projectId"));
            }
        },
        h_alarm(res) {
            if (res) {
                this.$store.commit("message/SET_ALARM_H", res.records);
                this.alarmTotal = res.total;
                if (res.records.length > 0) {
                    nextTick(() => {
                        if (!this.isClose) {
                            document.getElementsByTagName("audio")[0].play();
                        }
                    });
                } else if (res.records.length == 0) {
                    //关闭列表
                    this.dialogData.visible = false;
                }
            } else {
                this.alarmTotal = 0;
            }
        },
        //重新连接
        reconnect() {
            if (getCookie("gh_id")) {
                this.$socket.emit("SetUserId", getCookie("gh_id"));
            }
            if (getCookie("gh_projectId")) {
                this.$socket.emit("SetProject", getCookie("gh_projectId"));
            }
        },
        alarm(res) {
            this.$socket.emit("SendHisAlarm", getCookie("gh_projectId"));
        },
        AdviceMes(res) {
            if (res) {
                let data = JSON.parse(JSON.stringify(this.messageData));
                let exsit = this.messageData.find(
                    (v) => JSON.stringify(v) == JSON.stringify(res)
                );
                if (!exsit) {
                    data.push(res);
                }
                this.$store.commit("message/SET_MESSAGE_DATA", data);
            }
        },
    },
    setup() {
        const {
            proxy
        } = getCurrentInstance()
        const router = useRouter();
        const route = useRoute();
        const store = useStore();

        const emitter = inject('mitt');

        const state = reactive({
            userName: "",
            timer: null,
            projectName: "",
            socket: null,
            mesCount: 0,
            dialogVisible: false,
            sockets: null,
            dialogData: {
                title: "报警联动",
                visible: false,
                guid: "",
                form1: "",
                video1: "",
                video2: "",
                alarm: {
                    deviceName: "",
                    standardName: "",
                    value: "",
                    auto: false,
                },
            },
            project: [],
            weather: '多云',
            temperature: 20,
            date: "",
            mode: 0,
            weatherPng: '',
            showTip: true,//shi否首页两侧数据
            showWeather: false,
            showRoad: false,
            alarmTotal: 0
        });
        const emiter = inject("mitt");
        state.socket = inject("socket");

        const isClose = computed(() => {
            return store.state.message.isClose
        })

        const messageData = computed(() => {
            return store.state.message.mesData;
        });

        const alarms = computed(() => {
            return [...store.state.message.r_alarm, ...store.state.message.h_alarm];
        });

        watch(messageData, (val) => {
            state.mesCount = val.length;
        });

        onBeforeUnmount(() => {
            if (state.timer) {
                clearInterval(state.timer) // 清除定时器
            }
        })

        // 定时器
        state.timer = setInterval(() => {
            state.date = dayjs().format('YYYY-MM-DD HH:mm:ss')
        }, 1000)

        state.sockets = inject("socket");
        onMounted(() => {
            let mode = getCookie('mode');
            if (!mode) {
                setCookie('mode', state.mode);
            } else {
                state.mode = mode;
            }

            state.userName = getCookie("gh_name");
            state.socket.emit("GetAdviceMes", getCookie("gh_id"));
            if (getCookie("gh_projectId") != "0")
                state.socket.emit("SendHisAlarm", getCookie("gh_projectId"));
            if (getCookie("gh_projectId")) {
                state.socket.emit("SetProject", getCookie("gh_projectId"));
            }
            getProjectPage()
            // 项目名称
            if (getCookie("gh_projectId") != 0) {
                getProject(getCookie("gh_projectId"));
            }
            if (getCookie("gh_projectId") != "0")
                state.sockets.emit("SendHisAlarm", getCookie("gh_projectId"), getCookie("gh_id"));

            emiter.on("stopAudio", (tag) => {
                if (tag) {
                    document.getElementsByTagName("audio")[0].pause();
                }
                if (isClose.value) {
                    document.getElementsByTagName("audio")[0].pause();

                } else if (!isClose.value && state.alarmTotal > 0) {

                    document.getElementsByTagName("audio")[0].play();
                }
            });
            emiter.on("stopAudio1", () => {
                if (document.getElementsByTagName("audio").length > 0)
                    document.getElementsByTagName("audio")[0].pause();
            });
            emiter.on("showLink", (data) => {
                if (data) {
                    if (state.dialogData) {
                        state.dialogData.visible = true;
                        state.dialogData = Object.assign(state.dialogData, data);
                        state.dialogData.title = "报警联动";
                    }
                }
            })
            // 点击弹窗以外的其他区域关闭弹窗
            document.addEventListener('mouseup', (e) => {
                const tree = document.querySelector('#treeWrap')
                if (tree) {
                    if (!tree.contains(e.target)) {
                        state.dialogVisible = false
                    }
                }
            })

        });
        // 查询项目
        const getProjectPage = () => {
            proxy.$api.getProject({
                removed: false
            }).then(res => {
                const data = res.data
                state.project = data
            })
        }
        const goHome = () => {
            store.commit('menu/SET_MENU_INDEX', 0)
            router.push({
                path: "/",
            });
        }

        const userCommand = (command) => {
            switch (command) {
                case "personal":
                    let menu = getCookie('funMenus');
                    if (menu) {
                        let obj = JSON.parse(menu);
                        obj.component = 'personal';
                        setCookie('funMenus', JSON.stringify(obj))
                        store.commit('menu/SET_FUN_MENU', obj)
                    }

                    break;
                case "system":
                    console.log(process.env.NODE_ENV)
                    window.open(process.env.NODE_ENV === 'production' ? window.PROD_DATA_WEB : window.DEV_DATA_WEB)
                    break;
                case "logout": // 退出登录
                    loginOut();
                    break;
            }
        }
        // 退出登录
        const loginOut = () => {
            proxy.$api.logout().then((res) => {
                removeToken("gh_token");
                removeToken("gh_projectId");
                removeToken("_refreshToken");
                removeToken("gh_id");
                removeToken("gh_name");
                store.commit("menu/USER_SIGNOUT");
                store.commit("message/DELETE_STATE");
                store.commit('menu/SET_FUN_MENU', '')
                router.push({
                    path: "/login",
                });
            });
        }

        const getProject = (id) => {
            proxy.$api.getProjectById(id).then((res) => {
                if (res.data) {
                    state.projectName = res.data.name;
                    store.commit("area/SET_Coordinate", res.data.coordinate);
                    if (res.data.area) {
                        proxy.$axios
                            .get(`https://restapi.amap.com/v3/weather/weatherInfo?city=${res.data.area}&key=504581ecfd67cb6d006010facdb7a3c8`)
                            .then((result) => {
                                state.temperature = result.data.lives[0].temperature;
                                state.weather = result.data.lives[0].weather;
                                let path = weatherData[result.data.lives[0].weather];
                                state.weatherPng = require('' + path)
                            })
                            .catch((error) => {
                                console.log(error)
                            })
                    }
                }
            });
        }

        const showAlert = () => {
            state.dialogVisible = !state.dialogVisible;
        }

        const showMessage = () => {
            let item = {
                id: null,
                name: "",
                component: "personal",
                secondComponent: null,
                code: "", //菜单编码
                hideCondition: null,
                showCondition: null,
                diagramId: null,
                popName: null,
                model: null,
            }
            setCookie('funMenus', JSON.stringify(item))
            store.commit('menu/SET_FUN_MENU', item)
        };

        const changeMode = (mode) => {
            state.mode = mode;
            emitter.emit('changeMode', {
                mode,
                tag: true //是否是头部自己切换模式
            });
        }

        //天气场景
        const clickWeather = () => {
            state.showWeather = !state.showWeather;
        }
        //漫游场景点击
        const clickRoad = () => {
            state.showRoad = !state.showRoad;
        }


        const showScene = () => {
            state.showTip = !state.showTip;
            emitter.emit('show', state.showTip);
        }

        return {
            ...toRefs(state),
            goHome,
            userCommand,
            changeMode,
            loginOut,
            getProject,
            showAlert,
            showMessage,
            clickRoad,
            clickWeather,
            alarms,
            messageData,
            isClose,
            getProjectPage,
            showScene,
        };
    },
};
</script>
    
<style lang="scss" scoped>
.layout-header {
    position: absolute;
    z-index: 199;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 70px;
    background-image: url("../../assets/images/head3.png");
    background-repeat: no-repeat;
    background-position: center;

    .header_left,
    .header_right {
        width: 377px;

        .scene {
            width: 80px;
            height: 34px;
            background: #0A1B29;
            border: 1px solid #28425B;
            border-radius: 17px;
            margin-right: 18px;

            font-size: 14px;
            font-family: "Alibaba-PuHuiTi";
            font-weight: normal;
            color: #B9DDFD;

        }

        .icon {
            height: 30px;
            width: 30px;
            margin-right: 18px;
        }

        .alarm-icon {
            display: flex;
            cursor: pointer;

            .iconfont {
                font-size: 22px;
            }
        }

        @keyframes fade {
            from {
                opacity: 1;
            }

            50% {
                opacity: 0.4;
            }

            to {
                opacity: 1;
            }
        }

        .ld {
            animation: fade 1s infinite;
        }
    }

    .header_left {
        display: flex;
        padding-left: 15px;
        align-items: center;

        .temperature {
            margin: 0 16px;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;

            div:first-of-type,
            div:last-of-type {
                font-family: "Alibaba-PuHuiTi";
                font-size: 14px;
                font-weight: 400;
                color: #ECECEC;
                white-space: nowrap;
            }

        }

        .mode {
            display: flex;
            align-items: center;

            img {
                width: 30px;
                height: 30px;
            }
        }

    }

    .header_right {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        padding: 0 15px;

    }

    .logo {
        .center_container {
            // background: url("../../assets/images/head/head.jpg") no-repeat center/100%;
            display: flex;
            justify-content: center;
            cursor: pointer;
            height: 70px;
            width: 1014px;
            display: flex;
            justify-content: center;

            .title {
                font-size: 26px;
                font-family: "DOUYU";
                font-weight: normal;
                letter-spacing: 5px;
                color: #fff;
                background: linear-gradient(0deg, #c2e8fd 0%, #a5e5fa 50%, #fff 100%);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                padding-top: 15px;
            }


        }
    }

    .vr {
        margin-right: 13px;
    }

    .weather {
        position: absolute;
        left: 10px;
        top: 30px;

    }

    .road {
        position: absolute;
        right: 10px;
        top: 30px;

    }

}
</style>
    