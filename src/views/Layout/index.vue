<template>
<div class="layout">
    <Content>
        <router-view></router-view>
    </Content>
</div>
</template>

<script>


import {
    computed,
    defineComponent,
    onMounted,
    reactive,
    toRefs,
    watch,
} from 'vue';
import {
    useStore
} from "vuex";
import {
    useRoute
} from 'vue-router';

import Content from './Content.vue';
export default defineComponent({
    components: {
        Content
    },
    setup() {
        const route = useRoute()
        const store = useStore()
        const state = reactive({
            isShow: true
        })
 

        return {
            ...toRefs(state),
        }
    }
})
</script>

<style lang="scss" scoped>
.layout {
    position: relative;
    width: 100%;
    height: 100%;
    background: url("../../assets/images/home.jpg") no-repeat center fixed;
    background-size: cover;
    overflow: hidden;
}

</style>
