<template>
<div class="h100">
    <el-form :inline="true" class="search_box " size="small">
        <el-form-item label="时间选择">
            <el-date-picker v-model="date" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
            </el-date-picker>
        </el-form-item>
        <el-form-item label="设备选择:">
            <el-cascader @change="getProcessLogPage" popper-class="cascader" v-model="deviceId" :options="devices" clearable :props="props1" placeholder="请选择"></el-cascader>
        </el-form-item>
        <el-form-item>
            <div @click="getProcessLogPage" size="small" class="searchBtn" type="text">查询</div>
        </el-form-item>
    </el-form>

    <el-table :data="list" class="table" height="calc(100% - 90px)" fit>
        <template #empty>
            <noData />
        </template>
        <el-table-column prop="deviceName" label="维保设备" align="center">
        </el-table-column>
        <el-table-column prop="createTime" label="维保时间" align="center">
        </el-table-column>
        <el-table-column prop="creatorName" label="维保人员" align="center">
        </el-table-column>
        <el-table-column prop="description" label="维保内容" align="center">
        </el-table-column>
        <el-table-column type="expand" label="维保照片" width="100px">
            <template #default="props">
                <el-image class="image" :preview-src-list="JSON.parse(props.row.paths)" style="width: 100px; height: 100px" :src="item" v-for="(item, i) in JSON.parse(props.row.paths)" :fit="fit" :key="i"></el-image>
            </template>
        </el-table-column>
    </el-table>

    <div class="page center">
        <el-pagination prev-icon="CaretLeft" next-icon="CaretRight" :page-size="size" :current-page="page" layout="total,prev, pager, next" @current-change="handleCurrentChange" :total="total">
        </el-pagination>
    </div>
</div>
</template>

<script>
import dayjs from 'dayjs'
import {
    getCookie
} from '@/utils/cookie'
import {
    computed,
    getCurrentInstance,
    onMounted,
    reactive,
    toRefs,
    watch
} from 'vue'
import {
    useRouter
} from 'vue-router'
import {
    useStore
} from 'vuex'

export default {
    name: 'mainlog',
    setup() {
        const router = useRouter()
        const store = useStore()
        const {
            proxy
        } = getCurrentInstance()
        const state = reactive({
            date: [],
            page: 1,
            size: 10,
            total: 0,
            list: [],
            deviceId: [],
            devices: [],
            props1: {
                label: 'name',
                value: 'id',
                checkStrictly: true,
            },
            tableHeight: window.innerHeight * 0.60
        })
        onMounted(() => {
            state.date.push(dayjs().format('YYYY-MM-DD 00:00:00'))
            state.date.push(dayjs().format('YYYY-MM-DD 23:59:59'))
            getDevicesOfTypePage()
            getProcessLogPage()
        })
        const projectId = computed(() => {
            return store.state.user.projectId || getCookie('gh_projectId')
        })
        watch(projectId, (val) => {
            if (val) {
                getDevicesOfTypePage()
                getProcessLogPage()
            }
        })
        const getProcessLogPage = () => {
            proxy.$api.getProcessLog({
                projectId: getCookie("gh_projectId"),
                page: state.page,
                size: state.size,
                deviceId: state.deviceId.length > 0 ?
                    state.deviceId[state.deviceId.length - 1] : null,
                type: 3,
                bt: typeof state.date[0] == 'string' ?
                    state.date[0] : dayjs(state.date[0]).format('YYYY-MM-DD 00:00:00'),
                et: typeof state.date[1] == 'string' ?
                    state.date[1] : dayjs(state.date[1]).format('YYYY-MM-DD 23:59:59'),
            }).then((res) => {
                state.list = res.data
                state.total = res.total
            })
        }
        const getDevicesOfTypePage = () => {
            proxy.$api.getDevicesOfType({
                projectId: getCookie("gh_projectId"),
            }).then((res) => {
                state.devices = res.data
            })
        }
        const handleCurrentChange = (page) => {
            state.page = page
            getProcessLogPage()
        }
        const edit = (index, row) => {
            router.push({
                path: `/orderDetail/${row.instanceId}`,
            })
        }

        return {
            ...toRefs(state),
            getProcessLogPage,
            getDevicesOfTypePage,
            handleCurrentChange,
            edit,
            projectId
        }
    },
}
</script>

<style lang="scss" scoped>
.h100 {
    .image {
        margin: 0 5px;
    }
}
</style>
