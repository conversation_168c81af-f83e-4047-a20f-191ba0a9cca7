<template>
    <div class="z100">
        <div class="left">
            <div class="header flex-start">
                <img src="../../assets/images/common/head.png">
                <div> 待报设备</div>
            </div>
            <div class="input">
                <el-input v-model="keyword" @change="search" :prefix-icon="Search" placeholder="设备名称搜索"></el-input>
            </div>
            <div class="device">

                <el-scrollbar>
                    <div class="list space-between" v-for="item in list" :key="item.id">
                        <div class="center cursor">
                            <div>
                                <span class="iconfont iconshebeiguzhang"></span>
                            </div>
                            <div class="name">{{ item.deviceName }}</div>
                        </div>
                        <div class="center state">
                            {{ item.nextTime }}
                        </div>
                        <div class="position cursor">
                            <img src="../../assets/images/common/position.png" />
                        </div>
                    </div>
                </el-scrollbar>

            </div>

            <div class="page center">
                <el-pagination prev-icon="CaretLeft" next-icon="CaretRight" :page-size="size" :current-page="page"
                    layout="total,prev, pager, next" @current-change="handleCurrentChange" :total="total">
                </el-pagination>
            </div>

        </div>

        <div class="right">
            <div class="item" style="flex:1">
                <sub-title1 title='运行统计' />
                <div class="item-body order">
                    <div>
                        <div class="total center">设备总数</div>
                        <div class="order-left">
                            <div class="center">
                                <div>{{ count }}</div>
                            </div>
                        </div>
                    </div>
                    <div class="order-right">
                        <div class="order-text">
                            <div class="dot" style="background:#1AAC1A"></div><span class="text">待保设备:</span><span
                                class="num">{{ total }}</span>
                        </div>
                        <div class="order-text">
                            <div class="dot" style="background:#C47F13"></div><span class="text">维保计划:</span><span
                                class="num">{{ plan.length }}</span>
                        </div>

                    </div>
                </div>
            </div>
            <div class="item" style="flex:2">
                <sub-title1 title='维保统计' />
                <div class="item-body kong">
                    <Alarm :xAxisData="xAxisData" :echartData="echartData" />
                </div>
            </div>
            <div class="item" style="flex:2">
                <sub-title1 title='维保超时预警' />
                <div class="item-body event">
                    <el-scrollbar>
                        <div class="list " v-for="(item, i) in tableData" :key="i">
                            <div class="name">
                                <div>
                                    <span style="color:#F84141" class="iconfont iconguzhang"></span>
                                </div>
                                <div>{{ item.deviceName }}</div>
                            </div>
                            <!-- <div>
                            <div>张三</div>
                        </div> -->
                            <div class="time">
                                {{ item.nextTime }}
                            </div>

                            <div class="bar"></div>
                        </div>
                    </el-scrollbar>
                </div>
            </div>
        </div>

        <!-- 中间查询记录 -->
        <pop :show="activeMenus.popName ? true : false" :title="activeMenus.name || ''">
            <Transition name="fade" mode="out-in">
                <component :is="activeMenus.popName"></component>
            </Transition>
        </pop>

    </div>
</template>

<script>
import {
    defineComponent,
    getCurrentInstance,
    reactive,
    toRefs,
    computed,
    onMounted,
    watch,
} from 'vue';

import {
    getCookie
} from "@/utils/cookie";
import {
    useStore
} from 'vuex';
import Alarm from '@/components/echarts/weekEventEchart.vue'
import pop from '@/components/pop'
export default defineComponent({
    name: "weibo",
    components: {
        Alarm,
        pop
    },

    setup() {
        const {
            proxy
        } = getCurrentInstance()
        const store = useStore();
        const state = reactive({
            keyword: "",
            size: 10,
            page: 1,
            total: 100,
            list: [],
            echartData: [10, 11, 12, 0, 8, 6, 2],
            xAxisData: ['星期一', '星期二', '星期三', '星期四', '星期五', '星期六', '星期天'],
            count: 0, //维保设备总数
            plan: [], //计划数量
            tableData: [], //超期设备
        })
        //当前激活的一级菜单
        const activeMenus = computed(() => {
            let menu = getCookie("funMenus");
            return store.state.menu.funMenus ?
                store.state.menu.funMenus :
                menu ?
                    JSON.parse(menu) :
                    "";
        });
        onMounted(() => {
            getUnCompleted();
            getPlanList();
            getMrecord();
            // getMainWeekCount()
        });

        const getUnCompleted = async () => {
            let {
                data,
                total
            } = await proxy.$api.getUnCompleted({
                keyword: state.keyword,
                size: state.size,
                page: state.page,
                projectId: getCookie("gh_projectId")
            })
            state.total = total;
            state.list = data;
        }

        const getPlanList = () => {
            proxy.$api.getMainPlan({
                keyword: state.keyword,
                projectId: getCookie("gh_projectId"),
            }).then((res) => {
                state.plan = res.data;
                res.data.forEach(d => {
                    state.count = state.count + d.count;
                });
            })
        }

        const handleCurrentChange = (page) => {
            state.page = page;
            getUnCompleted();
        }

        const getMrecord = async () => {
            let {
                data
            } = await proxy.$api.getMrecord({
                projectId: getCookie("gh_projectId"),
            });
            state.tableData = data;
        };
        const getMainWeekCount = async () => {
            let {
                data
            } = await proxy.$api.getMainWeekCount({
                projectId: getCookie("gh_projectId"),
            });
            state.echartData = [];
            data.forEach(d => {
                state.echartData.push(d.count);
            })
        };

        const search = () => {
            state.page = 1;
            getUnCompleted();
        }

        return {
            ...toRefs(state),
            handleCurrentChange,
            search,
            activeMenus
        }
    }
});
</script>

<style lang="scss" scoped></style>
