<template>
<div class="z100">
    <div class="left">
        <div class="header flex-start">
            <img src="../../assets/images/common/head.png">
            <div> 设备列表</div>
        </div>
        <div class="input">
            <el-input v-model="keyword" @change="searchDevice" prefix-icon="Search" placeholder="按设备名称搜索">
            </el-input>
        </div>
        <div class="device">

            <el-scrollbar>
                <div class="list space-between" v-for="item in list" :key="item.id">
                    <div class="center cursor">
                        <span class="iconfont iconjiemufabu-jiemuguanli"></span>
                        <div class="name">{{item.deviceName}}</div>
                    </div>
                    <div class="center state">
                        <span style="color:green" v-if="item.isOnline=='online'">在线</span>
                        <span v-else style="color:red">离线</span>
                    </div>
                    <div class="position cursor" @click="zoomToPosition">
                        <img src="../../assets/images/common/position.png" />
                    </div>
                </div>
            </el-scrollbar>
        </div>

        <div class="page center">
            <el-pagination prev-icon="CaretLeft" next-icon="CaretRight" :page-size="size" :current-page="page" layout="total,prev, pager, next" @current-change="handleCurrentChange" :total="total">
            </el-pagination>
        </div>

    </div>

    <!-- 中间查询记录 -->
    <!-- <pop :show="activeMenus.popName?true:false" :title="activeMenus.name||''">
        <component :is="activeMenus.popName"></component>
    </pop> -->
    <div></div>

    <!-- 右侧列表 -->
    <div class="right">
        <div class="item" style="flex:1">
            <sub-title1 title='设备统计' />
            <div class="item-body order">
                <div>
                    <div class="total center">设备总数</div>
                    <div class="order-left">
                        <div class="center">
                            <div>{{on+off}}</div>
                        </div>
                    </div>
                </div>
                <div class="order-right">
                    <div class="order-text">
                        <div class="dot" style="background:#1AAC1A"></div><span class="text">在线数量:</span><span class="num">{{on}}</span>
                    </div>
                    <div class="order-text">
                        <div class="dot" style="background:#C47F13"></div><span class="text">离线数量:</span><span class="num">{{off}}</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="item" style="flex:2">
            <sub-title1 title='终端控制' />
            <div class="item-body kong">
                <el-scrollbar>
                    <div class="list space-between" v-for="item in list" :key="item.orgIndexCode">
                        <div class="name">
                            <img src="../../assets/images/common/d3.png" />
                            <div>{{item.terminalName}}</div>
                        </div>
                        <div class="dot"></div>
                        <div class="bra-btn space-between">
                            <div class="center cursor" @click="taskCmd('powerOn',item.terminalId)">
                                <div class="center">开机</div>
                            </div>

                            <div class="center cursor" @click="taskCmd('powerOff',item.terminalId)">
                                <div class="center">关机</div>
                            </div>
                        </div>
                    </div>
                </el-scrollbar>
            </div>
        </div>
        <div class="item" style="flex:2">
            <sub-title1 title='节目列表' />
            <div class="item-body event">
                <el-scrollbar>
                    <div class="list space-between" v-for="item in program" :key="item.programId">
                        <div class="name">
                            <span class="iconfont iconjiemufabu-jiemuguanli"></span>
                            <div>{{item.programName}}</div>
                        </div>

                        <div class="time">
                            {{dayjs(item.updateTime).format("YYYY-MM-DD HH:mm:ss")}}
                        </div>

                        <div class="bar"></div>
                    </div>
                </el-scrollbar>
            </div>
        </div>
    </div>

</div>
</template>

<script>
import {
    defineComponent,
    getCurrentInstance,
    reactive,
    toRefs,
    onMounted,

} from 'vue';

import {
    getCookie
} from "@/utils/cookie";

import pop from '@/components/pop'
import {

    ElMessage
} from "element-plus";
import dayjs from 'dayjs'
export default defineComponent({
    name: "message",
    components: {
        pop,
    },

    setup() {
        const {
            proxy
        } = getCurrentInstance()
        const state = reactive({
            keyword: "",
            size: 10,
            page: 1,
            total: 100,
            list: [], //设备列表
            popName: '',
            program: [],
            on:0,
            off:0,
            dayjs,
        })
        onMounted(() => {
            getDeviceList();
            getTerminal();
            getHkProgram();
        });

        const getDeviceList = async () => {
            let {
                data
            } = await proxy.$api.getHkTerminal({
                keyword: state.keyword,
                size: state.size,
                page: state.page,
            })
            state.total = data.total;;
            state.list=data.list
        };

        const handleCurrentChange = (page) => {
            state.page = page;
            getDeviceList();
        }

        const searchDevice = () => {
            state.page = 1;
            getDeviceList();
        }

        const zoomToPosition = async (item) => {
            let {
                data
            } = await proxy.$api.getObjectId({
                projectId: getCookie("gh_projectId"),
                deviceId: item.id,
                menuId: activeMenus.value.id,
                // fileId: state.id,
            });
            if (data) {
                emitter.emit("zoomToComponents", {
                    objectId: data.objectId
                })
            } else {
                ElMessage.warning("该设备未关联模型构件");
                return;
            }

        };

        const getTerminal = () => {
            state.on=0;
            state.off=0;
            proxy.$api.getHkTerminal({
                page: 1,
                size: 200,
            }).then((res) => {
                state.list = res.data.list;
                state.list.forEach(d => {
                    if (d.isOnline == 'online') {
                        state.on++;
                    } else {
                        state.off++;
                    }
                })

            })
        }
        // getHkProgram
        const getHkProgram = () => {
            proxy.$api.getHkProgram({
                page: 1,
                size: 200,
            }).then((res) => {
                state.program = res.data.list;
            })
        }
        const taskCmd = (cmd, id) => {
            proxy.$api.hkTerminalCmd({
                id,
                cmd
            }).then((res) => {
                ElMessage.success("操作成功")

            })
        }

        return {
            ...toRefs(state),
            handleCurrentChange,
            searchDevice,

            zoomToPosition,
            taskCmd
        }
    }
});
</script>

<style lang="scss" scoped>
.bra-btn {
    background-size: 100%;
    margin-left: 7px;

    &>div {
        flex: 1
    }

    &>div:first-child {
        margin-right: 10px;
    }

    &>div>div {

        width: 54px;
        height: 28px;
        background: #1B6DC2;
        border-radius: 4px;

        box-shadow: 0px 0px 3px 3px #2CBDEB inset;
    }
}
</style>
