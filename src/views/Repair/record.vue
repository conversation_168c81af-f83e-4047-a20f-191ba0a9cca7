<template>
<div class="electric-box layout_wrapper">
    <el-form :inline="true" class="search_box form_inline" size="small">
        <el-form-item label="时间">
            <el-date-picker @change="search" v-model="date" type="datetimerange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
            </el-date-picker>
        </el-form-item>
        <el-form-item label="维修人员">
            <el-select placeholder="请选择" clearable v-model="userId" @change="search" >
                <el-option v-for="item in users" :key="item.id" :label="item.name" :value="item.id"></el-option>
            </el-select>
        </el-form-item>
        <el-form-item>
            <el-button @click="search" class="searchBtn" size="small" type="text">查询</el-button>
        </el-form-item>
    </el-form>
    <div class="table">
        <el-table :data="list" :height="tableHeight" fit>
            <template #empty>
                <no-data />
            </template>
            <el-table-column prop="createTime" label="维修时间" align="center">
            </el-table-column>
            <el-table-column prop="creatorName" label="维修人员" align="center">
            </el-table-column>
            <el-table-column prop="createTime" label="维修时间" align="center">
            </el-table-column>
            <el-table-column prop="description" label="维修内容" align="center">
            </el-table-column>
            <el-table-column type="expand" label="维修照片" width="100px">
                <template #default="props">
                    <el-image class="image" :preview-src-list="JSON.parse(props.row.paths)" style="width: 100px; height: 100px" :src="item" v-for="(item, i) in JSON.parse(props.row.paths)" :fit="fit" :key="i"></el-image>
                </template>
            </el-table-column>
        </el-table>
    </div>
    <div class="page center">
        <el-pagination prev-icon="CaretLeft" next-icon="CaretRight" :page-size="size" :current-page="page" layout="total,prev, pager, next" @current-change="handleCurrentChange" :total="total">
        </el-pagination>
    </div>
</div>
</template>

<script>
import dayjs from 'dayjs'
import {
    getCookie
} from '@/utils/cookie'
import {
    reactive,
    toRefs
} from 'vue'
import {
    computed,
    getCurrentInstance,
    onMounted,
    watch
} from 'vue'
import {
    useRouter
} from 'vue-router'
import {
    useStore
} from 'vuex'
export default {
    name: 'repairrecord',
    setup() {
        const router = useRouter()
        const store = useStore()
        const {
            proxy
        } = getCurrentInstance()
        const state = reactive({
            tableHeight: window.innerHeight * 0.60,
            date: [],
            page: 1,
            size: 10,
            total: 0,
            status: '',
            list: [],
            users: [],
            userId: '',
        })
        onMounted(() => {
            state.date.push(dayjs().format('YYYY-MM-DD 00:00:00'))
            state.date.push(dayjs().format('YYYY-MM-DD 23:59:59'))
            getUserPage()
            getProcessLogPage()
        })
        const projectId = computed(() => {
            return store.state.user.projectId || getCookie('gh_projectId')
        })
        watch(projectId, (val) => {
            if (val) {
                getUserPage()
                getProcessLogPage()
            }
        })
        const search = () => {
            state.page = 1
            getProcessLogPage()
        }
        const getProcessLogPage = () => {
            proxy.$api.getProcessLog({
                projectId: getCookie('gh_projectId'),
                page: state.page,
                size: state.size,
                userId: state.userId,
                type: 1,
                bt: typeof state.date[0] == 'string' ?
                    state.date[0] : dayjs(state.date[0]).format('YYYY-MM-DD HH:mm:ss'),
                et: typeof state.date[1] == 'string' ?
                    state.date[1] : dayjs(state.date[1]).format('YYYY-MM-DD HH:mm:ss'),
            }).then((res) => {
                state.list = res.data
                state.total = res.total
            })
        }
        const handleCurrentChange = (page) => {
            state.page = page
            getProcessLogPage()
        }
  
        const getUserPage = () => {
            proxy.$api.getUser({
                projectId: [getCookie('gh_projectId')],
                status: 1,
            }).then((res) => {
                state.users = res.data
            })
        }

        return {
            ...toRefs(state),
            search,
            getProcessLogPage,
            handleCurrentChange,
            getUserPage,
            projectId
        }
    },
}
</script>

<style lang="scss" scoped>
.electric-box {
    .image {
        margin-right: 10px;
    }
}
</style>
