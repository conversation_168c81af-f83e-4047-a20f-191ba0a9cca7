<template>
<el-dialog align-center append-to-body   custom-class="addDiagram border0" v-model="dialogData.visible" width="940px" :title="dialogData.title">
    <el-form class="form" ref="form" :model="dialogData.order" :rules="dialogData.rule">
        <el-row type="flex" :gutter="30">
            <el-col :span="12">
                <el-form-item label="所属专业：" prop="leader">
                    <el-select v-model="dialogData.order.leader" >
                        <el-option v-for="item in jobs" :label="item.tagName" :value="item.tagValue" :key="item.tagValue"></el-option>
                    </el-select>
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="选择区域：">
                    <el-cascader popper-class="cascader" v-model="order.areaId" style="width: 100%" :options="areas" clearable :props="props" placeholder="请选择" @change="getDevicePage"></el-cascader>
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="工单主题：" prop="name">
                    <el-input placeholder="请输入名称" v-model="dialogData.order.name"></el-input>
                </el-form-item>
            </el-col>
        </el-row>
        <el-form-item label="故障描述：" prop="description">
            <el-input type="textarea" placeholder="请输入内容" v-model="dialogData.order.description"></el-input>
        </el-form-item>
    </el-form>
    <template #footer>
        <div class="dialog-footer search_box">
            <div type="primary" :disabled="loading" size="mini" class="searchBtn" @click="submitOrder('form')">确 定</div>
        </div>
    </template>
</el-dialog>
</template>

<script>
import {
    ElMessage
} from 'element-plus'
import {
    reactive,
    toRefs
} from 'vue'
import {
    computed,
    getCurrentInstance,
    onMounted,
    watch
} from 'vue'
import {
    getCookie
} from '@/utils/cookie'
import {
    useStore
} from 'vuex'
export default {
    props: {
        dialogData: {
            order: {}
        }
    },
    setup(props) {
        const {
            proxy
        } = getCurrentInstance()
        const store = useStore()
        const state = reactive({
            types: [],
            areas: [],
            props1: {
                label: 'name',
                value: 'id',
                checkStrictly: true,
            },
            props: {
                label: 'name',
                value: 'id',
                checkStrictly: true
            },
            order: {
                areaId: []
            },
            headers: {
                Authorization: 'bearer ' + getCookie('gh_token'),
            },
            imgs: [],
            paths: [],
            param: {
                fileType: 0,
                projectId: getCookie('gh_projectId'),
            },
            action: (process.env.NODE_ENV == "development" ? window.DEV_BASE_API : window.PROD_BASE_API) + "/filecenter-service/upload",
            loading: false,
            jobs: null,
            leader: 0
        })
        onMounted(() => {
            getProjectArea()
            getProjectDeviceType()
            getJobType()
        })

        const projectId = computed(() => {
            return store.state.user.projectId || getCookie('gh_projectId')
        })
        watch(projectId, (val) => {
            if (val) {
                getProjectArea()
                getProjectDeviceType()
            }
        })
        const getProjectArea = () => {
            proxy.$api.getProjectArea({
                projectId: getCookie('gh_projectId')
            }).then(res => {
                state.areas = res.data
            });
        }
        const getProjectDeviceType = () => {
            proxy.$api.getProjectDeviceType({
                projectId: getCookie('gh_projectId'),
            }).then((res) => {
                state.types = res.data
            })
        }
        const handleRemove = (file) => {
            if (file) {
                state.imgs.forEach((img, i) => {
                    if (img.uid == file.uid) {
                        state.imgs.splice(i, 1)
                    }
                })
            }
        }
        const upload = (res, file, filelist) => {
            state.paths = []
            state.imgs.forEach(ele => {
                state.paths.push(ele.url)
            })
            state.paths.push(res.data[0]);

            state.imgs.push({
                name: file.name,
                url: res.data[0],
                uid: file.uid,
            })
        }
        const getDevicePage = () => {
            proxy.$api.getDevices({
                projectId: getCookie('gh_projectId'),
                areaId: state.order.areaId[state.order.areaId.length - 1],
                deviceType: state.order.deviceType
            }).then((res) => {
                state.devices = res.data
            })
        }
        const submitOrder = (formName) => {
            proxy.$refs[formName].validate((valid) => {
                if (valid) {
                    state.loading = true;
                    proxy.$api.addOrder({
                        projectId: getCookie('gh_projectId'),
                        name: props.dialogData.order.name,
                        description: props.dialogData.order.description,
                        type: 4,
                        repairId: props.dialogData.order.id,
                        paths: props.dialogData.order.paths,
                        leader: props.dialogData.order.leader
                    }).then(res => {
                        if (res.success) {
                            props.dialogData.visible = false
                            proxy.$emit('getFaultPage')
                            ElMessage({
                                type: 'success',
                                message: res.msg
                            });
                            state.loading = false;
                        }
                    })
                } else {
                    return false
                }
            })
        }
        const getJobType = () => {
            proxy.$api.getDicUtil({
                dicCode: "profession_type",
                projectId: getCookie("gh_projectId")
            }).then(res => {
                state.jobs = res.data;
            });
        }
        const changeStaff = (item) => {
            if (item) {
                proxy.$api.getLeader({
                    staffType: item.tagValue,
                    projectId: getCookie("gh_projectId")
                }).then(res => {
                    state.leader = res.data;
                })
            } else {
                state.leader = 0;
            }

        }
        return {
            ...toRefs(state),
            upload,
            submitOrder,
            getDevicePage,
            getJobType,
            handleRemove,
            getProjectDeviceType,
            getProjectArea,
            projectId
        }
    }
}
</script>
