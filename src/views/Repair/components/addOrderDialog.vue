<template>
<el-dialog align-center append-to-body   draggable custom-class="addDiagram border0" v-model="dialogData.visible" width="940px" :title="dialogData.title">
    <el-form class="form" ref="form" :model="dialogData.order" :rules="dialogData.rule" label-width="100px">
        <el-row type="flex" :gutter="30">
            <el-col :span="12">
                <el-form-item label="设备类型：">
                    <el-select v-model="order.deviceType"  @change="getDevicePage">
                        <el-option v-for="item in types" :label="item.name" :value="item.id" :key="item.id"></el-option>
                    </el-select>
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="选择区域：">
                    <el-cascader popper-class="cascader" v-model="order.areaId" style="width: 100%" :options="areas" clearable :props="props" placeholder="请选择" @change="getDevicePage"></el-cascader>
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="设备选择：">
                    <el-select  v-model="dialogData.order.deviceId" >
                        <el-option v-for="item in devices" :label="item.name" :value="item.id" :key="item.id"></el-option>
                    </el-select>
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="工单主题：" prop="name">
                    <el-input placeholder="请输入名称" v-model="dialogData.order.name"></el-input>
                </el-form-item>
            </el-col>
        </el-row>
        <el-form-item label="故障描述：" prop="description">
            <el-input type="textarea" placeholder="请输入内容" v-model="dialogData.order.description"></el-input>
        </el-form-item>
        <el-form-item label="图片上传" prop="content" class="upload">
            <el-upload class="upload-demo" :headers="headers" :action="action" name="files" :data="param" list-type="picture-card" :file-list="imgs" :on-remove="handleRemove" :on-success="upload">
              <el-icon><Plus /></el-icon>
            </el-upload>
        </el-form-item>
    </el-form>
    <template #footer>
        <div class="dialog-footer search_box">
            <div type="primary" size="mini" class="searchBtn" :disabled="loading" @click="saveOrder('form')">确 定</div>
        </div>
    </template>
</el-dialog>
</template>

<script>
import {
    reactive,
    toRefs,
    ref
} from 'vue'
import {
    computed,
    getCurrentInstance,
    onMounted,
    watch
} from 'vue'
import {
    getCookie
} from '@/utils/cookie'
import {
    ElMessage
} from 'element-plus'
import {
    useStore
} from 'vuex'

export default {
    props: {
        dialogData: {
            visible: false,
            order: {
                paths: []
            }
        }
    },
    setup(props) {
        const {
            proxy
        } = getCurrentInstance()
        const store = useStore()
        const imgs = ref([])
        const state = reactive({
            types: [],
            areas: [],
            devices: [],
            order: {
                areaId: []
            },
            headers: {
                Authorization: 'bearer ' + getCookie('gh_token'),
            },
            imgs: [],
            paths: [],
            paths1: [],
            param: {
                fileType: 0,
                projectId: getCookie('gh_projectId'),
            },
            action: (process.env.NODE_ENV == "development" ? window.DEV_BASE_API : window.PROD_BASE_API) + "/filecenter-service/upload",
            props1: {
                label: 'name',
                value: 'id',
                checkStrictly: true,
            },
            props: {
                label: 'name',
                value: 'id',
                checkStrictly: true
            },
            loading:false,
        })
        onMounted(() => {
            getProjectDeviceType()
            getProjectArea()
            getDevicePage()
        })
        const projectId = computed(() => {
            return store.state.user.projectId || getCookie('gh_projectId')
        })
        watch(projectId, (val) => {
            if (val) {
                getProjectDeviceType()
                getProjectArea()
                getDevicePage()
            }
        })
        const getProjectDeviceType = () => {
            proxy.$api.getProjectDeviceType({
                projectId: getCookie('gh_projectId'),
            }).then((res) => {
                state.types = res.data
            })
        }
        const getProjectArea = () => {
            proxy.$api.getProjectArea({
                projectId: getCookie('gh_projectId')
            }).then(res => {
                state.areas = res.data
            });
        }
        const getDevicePage = () => {
            state.devices = []
            proxy.$api.getDevices({
                projectId: getCookie('gh_projectId'),
                areaId: state.order.areaId[state.order.areaId.length - 1],
                deviceType: state.order.deviceType
            }).then((res) => {
                state.devices = res.data
            })
        }
        const handleRemove = (file) => {
            if (file) {
                state.imgs.forEach((img, i) => {
                    if (img.uid == file.uid) {
                        state.imgs.splice(i, 1)
                    }
                })
            }
        }
        const upload = (res, file, filelist) => {
            state.paths = []
            state.imgs.forEach(ele => {
                state.paths.push(ele.url)
            })
            state.paths.push(res.data[0]);

            state.imgs.push({
                name: file.name,
                url:  res.data[0],
                uid: file.uid,
            })
        }
        const saveOrder = (formName) => {
            proxy.$refs[formName].validate((valid) => {
                if (valid) {
                    state.loading=true;
                    proxy.$api.addOrder({
                        projectId: getCookie('gh_projectId'),
                        deviceId: props.dialogData.order.deviceId,
                        name: props.dialogData.order.name,
                        description: props.dialogData.order.description,
                        paths: JSON.stringify(state.paths),
                        type: props.dialogData.order.deviceId ? 1 : 4 //1--保修工单 4--非设备手动工单

                    }).then(res => {
                        if (res.success) {
                            props.dialogData.visible = false
                            proxy.$emit('getOrderPage')
                            ElMessage({
                                type: 'success',
                                message: res.msg
                            });
                            state.loading=false;
                        }
                    })
                } else {
                    return false;
                    
                }
            })
        }
        return {
            ...toRefs(state),
            imgs,
            saveOrder,
            handleRemove,
            upload,
            getDevicePage,
            getProjectDeviceType,
            getProjectArea,
            projectId
        }
    }
}
</script>
