<template>
    <div class="">
        <div class="left">
            <div class="header flex-start">
                <img src="../../assets/images/common/head.png">
                <div> 监控列表</div>
            </div>
            <div class="input">
                <el-input v-model="keyword" @change="search" prefix-icon="Search" placeholder="摄像机名称搜索"></el-input>
            </div>
            <div class="device">
                <el-scrollbar>
                    <div class="list space-between" v-for="item in videos" :key="item.id">
                        <div class="center cursor">
                            <div>
                                <span class="iconfont iconshexiangji_02"></span>
                            </div>
                            <div class="name" @click="playVideo(item)">{{ item.strName }}</div>
                        </div>
                        <div class="center state">
                            <div style="color:green" v-if="dataStatus['t_' + item.camToken]">在线</div>
                            <div v-else style="color:red">离线</div>
                        </div>
                        <div class="position cursor" @click="play(item)">
                            <span class="iconfont iconshexiangji"></span>
                        </div>

                        <div class="position cursor" @click="zoomToPosition(item)">
                            <img src="../../assets/images/common/position.png" />
                        </div>
                    </div>
                </el-scrollbar>

            </div>

            <div class="page center">
                <el-pagination prev-icon="CaretLeft" next-icon="CaretRight" :page-size="size" :current-page="page"
                    layout="total,prev, pager, next" @current-change="handleCurrentChange" :total="total">
                </el-pagination>
            </div>

        </div>

        <pop :show="activeMenus.popName ? true : false" :title="activeMenus.name || ''">
            <Transition name="fade" mode="out-in" appear>
                <component :is="activeMenus.popName" :cam="cam"></component>
            </Transition>
        </pop>

        <div class="right">
            <div class="item" style="flex:1">
                <sub-title1 title='运行统计' />
                <div class="item-body order">
                    <div>
                        <div class="total center">设备总数</div>
                        <div class="order-left">
                            <div class="center">
                                <div>{{ on + off }}</div>
                            </div>
                        </div>
                    </div>
                    <div class="order-right">
                        <div class="order-text">
                            <div class="dot" style="background:#1AAC1A"></div><span class="text">在线数量:</span>
                            <div class="num">{{ on }}</div>
                        </div>
                        <div class="order-text">
                            <div class="dot" style="background:#C47F13"></div><span class="text">离线数量:</span>
                            <div class="num">{{ off }}</div>
                        </div>

                    </div>
                </div>
            </div>
            <div class="item" style="flex:2">
                <sub-title1 title='运行统计' />
                <div class="item-body kong">
                    <liquid :nCPUUsage="nCPUUsage" :nMemoryUsage="nMemoryUsage" />
                </div>
            </div>
            <div class="item" style="flex:2">
                <sub-title1 title='离线监控' />
                <div class="item-body event">
                    <el-scrollbar v-if="list.length > 0">
                        <div class="list " v-for="i in list" :key="i.ip">
                            <div class="name">
                                <div>{{ i.name }}</div>
                            </div>

                            <div class="time">
                                {{ i.ip }}
                            </div>

                            <div class="bar"></div>
                        </div>
                    </el-scrollbar>
                    <noData v-else></noData>
                </div>
            </div>
        </div>
        <el-cctv ref="camRef"></el-cctv>
    </div>
</template>

<script>
import {
    defineComponent,
    getCurrentInstance,
    reactive,
    toRefs,
    computed,
    onMounted,
    ref,
    inject,
    watch
} from 'vue';

import {
    getCookie
} from "@/utils/cookie";
import {
    useStore
} from 'vuex';
import Alarm from '@/components/echarts/weekEventEchart.vue'
import pop from '@/components/pop'
import liquid from './liquid.vue'
import cctv from '@/components/cctv/src/main.vue'
export default defineComponent({
    name: "cctv",
    components: {
        Alarm,
        pop,
        liquid,
        'el-cctv': cctv,
    },

    setup() {
        const {
            proxy
        } = getCurrentInstance()
        const store = useStore();
        const state = reactive({
            keyword: "",
            size: 20,
            page: 1,
            total: 0,
            list: [],
            videos: [],
            echartData: [10, 11, 12, 0, 8, 6, 2],
            xAxisData: ['星期一', '星期二', '星期三', '星期四', '星期五', '星期六', '星期天'],
            off: 0,
            on: 0,
            cam: null,
            camRef: null,
            nCPUUsage: 0,
            nMemoryUsage: 0

        })
        const emitter = inject("mitt");
        const dataStatus = ref({
            name: ""
        });

        onMounted(() => {
            getVideoList();
        });

        //当前激活的一级菜单
        const activeMenus = computed(() => {
            let menu = getCookie("funMenus");
            return store.state.menu.funMenus ?
                store.state.menu.funMenus :
                menu ?
                    JSON.parse(menu) :
                    "";
        });

        //当前激活的楼层
        const areaId = computed(() => {
            return store.state.area.area;
        });

        watch(areaId, (val) => {

            getVideoList();

        });

        const GetSrcInfo = (ip, port) => {
            if (state.timeTick1) {
                clearTimeout(this.timeTick1);
                state.timeTick1 = null;
            }

            let root = `http://${ip}:${port}/`;
            var url = root + "/api/v1/GetSrc?session=3b347c65-c7a5-47b2-a2ca-10be8adda6a1";
            proxy.$axios
                .get(url)
                .then((result) => {
                    if (result.status == 200) {
                        var data = result.data;
                        state.on = 0;
                        state.off = 0;
                        state.list=[];
                        for (var i = 0; i < data.src.length; i++) {
                            var item = data.src[i];
                            dataStatus.value = {
                                ...dataStatus.value,
                                ["t_" + item["strToken"]]: item["bOnline"]
                            };
                            if (item["bOnline"]) {
                                state.on++;

                            } else {
                                state.off++;
                                state.list.push({
                                    ip: item["strSrcIpAddress"],
                                    name: item["strName"],
                                })
                            }
                        }
                    }
                })
                .catch((error) => {

                });
        };
        const getInfo = (ip, port) => {
            if (state.timeTick1) {
                clearTimeout(this.timeTick1);
                state.timeTick1 = null;
            }

            let root = `http://${ip}:${port}/`;
            var url = root + "api/v1/getruninfo?session=3b347c65-c7a5-47b2-a2ca-10be8adda6a1";
            proxy.$axios
                .get(url)
                .then((result) => {
                    if (result.status == 200) {
                        var data = result.data;
                        state.nCPUUsage = parseInt(data.nCPUUsage) / 100;
                        state.nMemoryUsage = parseInt(data.nMemoryUsage) / 100;

                    }
                })
                .catch((error) => {

                });
        };

        const getServerPage = () => {
            proxy.$api.getVideoServer({
                projectId: getCookie("gh_projectId"),
            }).then((res) => {
                state.servers = res.data;
                if (state.servers && state.servers.length > 0) {
                    state.servers.forEach((s) => {
                        GetSrcInfo(s.ip, s.port);
                        getInfo(s.ip, s.port);
                    });
                }
            });
        };

        const getVideoList = () => {
            proxy.$api.getVideo({
                projectId: getCookie("gh_projectId"),
                keyword: state.keyword,
                size: state.size,
                page: state.page,
                areaId: areaId.value ? (areaId.value.id == -1 ? null : areaId.value.id) : null
            }).then((res) => {
                state.videos = res.data;
                state.total = res.total;
                getServerPage();
            });
        };

        const handleCurrentChange = (page) => {
            state.page = page;
            getVideoList();
        }

        const search = () => {
            state.page = 1;
            getVideoList();
        }
        const playVideo = (item) => {
            if (activeMenus.value.popName == 'live') {
                state.cam = item;
            }
        }
        const play = (item) => {
            state.camRef.opened({
                server: item.ip, //流媒体ip --h5参数
                port: item.port, //流媒体port ---h5参数
                token: item.camToken, //zlm和h5平台通用
                name: item.strName, //摄像机名称 --通用
                serverType: 1, //平台类型 1 h5 2--zlm平台
                ip: item.strSrcIpAddress, //摄像机ip   zlm onvif
                username: item.strUser, //摄像机用户名 zlm onvif
                password: item.strPasswd, //摄像机密码 zlm onvif
                profileToken: null, //摄像机 onvif profileToken zlm onvif
                ptzEnable: item.ptzEnable
            });
        }
        const zoomToPosition = async (item) => {
            let {
                data
            } = await proxy.$api.getCamObjectId({
                projectId: getCookie("gh_projectId"),
                token: item.camToken
            });
            if (data) {
                emitter.emit("zoomToComponents", {
                    objectId: data.objectId
                })
            } else {
                ElMessage.warning("该设备未关联模型构件");
                return;
            }

        };

        return {
            ...toRefs(state),
            handleCurrentChange,
            search,
            activeMenus,
            dataStatus,
            areaId,
            playVideo,
            zoomToPosition,
            play
        }
    }
});
</script>

<style lang="scss" scoped></style>
