<template>
<div class="live_container h100 ">
    <div class="container_right">
        <div class="btngroup">

            <div type="primary" size="medium" @click="changePanel('1|1')" :class="isActive == 1 ? 'activeBtn' : ''">
                <span class="iconfont iconyifenping"></span>
            </div>
            <div type="primary" size="medium" @click="changePanel('2|2')" :class="isActive == 2 ? 'activeBtn' : ''">
                <span class="iconfont iconsifenping"></span>
            </div>
            <div type="primary" size="medium" @click="changePanel('3|3')" :class="isActive == 3 ? 'activeBtn' : ''">
                <span class="iconfont iconjiufenping"></span>
            </div>
            <div type="primary" size="medium" @click="changePanel('4|4')" :class="isActive == 4 ? 'activeBtn' : ''">
                <span class="iconfont iconshiliufenping"></span>
            </div>

        </div>
        <div class="wrapper_t">
            <div name="flex" v-for="r in rows" :key="r" class="rowlist" :style="heightStyle">
                <div class="col" :class="[selectCol == c && selectRow == r ? 'active' : '']" name="flex" v-for="c in cols" @contextmenu.prevent="stopVideo('h' + r + c, $event)" @click="videoClick(r, c, $event)" :key="c">
                    <video v-if="type != 2" @dblclick="full($event)" class="h5video" v-bind:id="'h' + r + c" autoplay></video>
                    <div v-if="type == 2" @dblclick="full($event)" class="h5video" v-bind:id="'h' + r + c" autoplay></div>
                    <div class="ptz" :style="{display:ptzDomMap.get('h'+r+c)?'flex':'none'}">
                        <div class="ptzbtn">
                            <div class="up">
                                <div>
                                    <el-button @mousedown="PtzAction('up', 'h' + r + c)" @mouseup="PtzAction('stop', 'h' + r + c)" type="primary" icon="ArrowUp" size="mini" circle></el-button>
                                </div>
                            </div>
                            <div class="mid">
                                <div>
                                    <el-button @mousedown="PtzAction('left', 'h' + r + c)" @mouseup="PtzAction('stop', 'h' + r + c)" type="primary" icon="ArrowLeft" size="mini" circle></el-button>
                                </div>
                                <div>
                                    <el-button @mousedown="PtzAction('right', 'h' + r + c)" @mouseup="PtzAction('stop', 'h' + r + c)" type="primary" icon="ArrowRight" size="mini" circle></el-button>
                                </div>
                            </div>
                            <div class="down">
                                <div>
                                    <el-button @mousedown="PtzAction('down', 'h' + r + c)" @mouseup="PtzAction('stop', 'h' + r + c)" type="primary" icon="ArrowDown" size="mini" circle></el-button>
                                </div>
                            </div>
                        </div>
                        <div class="ptzzoom">
                            <div>
                                <el-button @mousedown="PtzAction('zoomin', 'h' + r + c)" @mouseup="PtzAction('stop', 'h' + r + c)" type="primary" icon="Plus" size="mini" circle></el-button>
                            </div>
                            <div>
                                <el-button @mousedown="PtzAction('zoomout', 'h' + r + c)" @mouseup="PtzAction('stop', 'h' + r + c)" type="primary" icon="Minus" size="mini" circle></el-button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>
</div>
</template>

<script>
import "@/lib/h5splayer";
import {
    H5sPlayerCreate
} from "@/lib/h5splayerhelper";
import VAPI from "@/lib/videoapi";
import {
    getCookie
} from "@/utils/cookie";
import {
    getAreaVideo,
    movePtz,
    stopMovePtz
} from "@/api/video";

export default {
    name: "live",
    props: ['cam'],
    data() {
        return {
            rows: 1,
            cols: 1,
            selectCol: 1,
            selectRow: 1,
            map: new Map(),
            defaultProps: {
                children: "children",
                label: "label",
            },
            data: null,
            panelid: 1,
            cams: [],
            heightStyle: "",
            isActive: 1,
            type: 1,

            jessibuca: null,
            useWCS: false,
            useMSE: true,
            useOffscreen: true,
            mapZLM: new Map(),
            onvif: new Map(),
            ptzDomMap: new Map(),
            session: null,
        };
    },
    watch: {
 
        projectId(val) {
            if (val) this.loadCam();
        },
        cam(val) {
            this.handleCheckChange(val)
        }
    },
    computed: {
        projectId() {
            return this.$store.state.user.projectId || getCookie("gh_projectId");
        },
    },
    mounted() {
        //this.loadCam();
    },
    unmounted() {
        this.ptzDomMap.clear();
    },
    methods: {
        filterNode(value, data) {
            if (!value) return true;
            return data.label.indexOf(value) !== -1;
        },

        changePanel(val) {
            if (this.map.size > 0) {
                //清空视频，防止内存
                this.map.forEach((v, i) => {
                    v.disconnect();
                });
                this.map.forEach((k, i) => {
                    document.getElementById(i).setAttribute("src", "");
                    document.getElementById(i).setAttribute("poster", "");
                });
                this.map.clear();
            }
            if (this.mapZLM.size > 0) {
                this.mapZLM.forEach((v, i) => {
                    v.pause();
                    v.destroy();
                });
                this.mapZLM.clear();
            }
            this.onvif.clear();

            let cols = val.split("|")[1];
            let rows = val.split("|")[0];
            this.isActive = cols;

            Object.assign(this.$data, {
                rows: parseInt(rows),
                cols: parseInt(cols),
            });
            this.heightStyle = `height:calc((100% - ${(this.rows - 1) * 5 + "px"})/${
        this.rows
      })`;
        },
        videoClick(r, c, $event) {
            this.selectCol = c;
            this.selectRow = r;
        },
        loadCam() {
            getAreaVideo({
                projectId: this.projectId,
            }).then((res) => {
                this.data = res.data;
                this.resolve(res.data);
            });
        },
        resolve(data) {
            let _this = this;
            if (data) {
                data.forEach((d) => {
                    if (d.ip && _this.cams.length <= 1) {
                        _this.cams.push(d);
                    }
                    if (d.children && d.children.length > 0) {
                        _this.resolve(d.children);
                    }
                });
            }
        },
        handleCheckChange(data, node) {
            this.session = data.session;
            this.ptzDomMap.set("h" + this.selectRow + this.selectCol, data.ptzEnable)
            if (data.camToken) {
                this.$nextTick(() => {
                    let vid = "h" + this.selectRow + this.selectCol;
                    let conf = {
                        videoid: vid,
                        protocol: window.location.protocol, //http: or https:
                        host: (data.ip=='*************'?'**************':data.ip) + ":" + data.port, //localhost:8080
                        rootpath: "/", // '/'
                        token: data.camToken,
                        hlsver: "v1", //v1 is for ts, v2 is for fmp4
                        session: data.session, //session got from login
                    };
                    if (this.map.has(vid)) {
                        let config = this.map.get(vid);
                        config.disconnect();
                        // H5sPlayerDelete(config)
                        this.map.delete(vid);
                    }
                    this.map.set(vid, H5sPlayerCreate(conf));
                    this.map.get(vid).connect();
                });
            }

        },
        stopVideo(id, event) {

            if (this.map.has(id)) {
                let config = this.map.get(id);
                config.disconnect();
                this.map.delete(config);
                event.target.src = "";
                event.target.poster = "";
            }

        },
        PtzAction(action, domid) {

            if (this.map.has(domid)) {
                let config = this.map.get(domid);
                VAPI.PtzAction(
                    this,
                    config._token,
                    action,
                    "http://" + config._conf.host,
                    this.session
                );
            }

            return;
        },
        full(element) {
            if (element.currentTarget.requestFullscreen) {
                element.currentTarget.requestFullscreen();
            } else if (element.currentTarget.mozRequestFullScreen) {
                element.currentTarget.mozRequestFullScreen();
            } else if (element.currentTarget.webkitRequestFullScreen) {
                element.currentTarget.webkitRequestFullScreen();
            }
        },
    },
    beforeRouteLeave(to, from, next) {
        if (this.map.size > 0) {
            this.map.forEach((v, i) => {
                v.disconnect();
            });
            this.map.clear();
        }
        next();
    },
};
</script>

<style lang="scss" scoped>
.live_container {
    display: flex;
    // background: #103457;
    // opacity: 0.25;

    .container_right {
        box-sizing: border-box;
        height: calc(100% - 10px);
        width:100%;
        margin: 0 auto;

        .wrapper_t {
            width: 100%;
            height: calc(100% - 30px);
            background: #0A1B29;

            .rowlist {
                height: 100%;
                width: 100%;
                display: flex;

                .active {
                    border: 1px solid #3de9fa !important;
                }

                &:not(:last-child) {
                    margin-bottom: 5px;
                }

                div {
                    border: 1px solid #4a5966;
                }

                .col {
                    height: 100%;
                    margin-right: 5px;
                    flex: 1 1 0%;
                    box-sizing: border-box;

                    .ptz {
                        position: absolute;
                        bottom: 5px;
                        left: 5px;
                        z-index: 6000;
                        border: 0 !important;
                        display: flex;

                        div {
                            border: 0 !important;
                        }

                        align-items: center;

                        .ptzbtn {
                            display: flex;
                            flex-wrap: wrap;
                            flex-direction: column;
                            align-content: space-between;
                            align-items: center;
                            margin-right: 20px;

                            .mid {
                                display: flex;
                                justify-content: space-between;

                                div:nth-of-type(1) {
                                    margin-right: 20px;
                                }
                            }
                        }

                        .ptzzoom {
                            display: flex;
                            flex-direction: column;

                            div:nth-of-type(1) {
                                margin-bottom: 5px;
                            }
                        }
                    }
                }
            }
        }
    }

    .wrapper {
        display: flex;
        padding: 0 10px;
        height: 100%;
        border: 1px solid red;
    }

    .header {
        border-bottom: 1px solid rgba(33, 61, 112, 1);
    }

    div[name="flex"] {
        display: flex;
        position: relative;
        cursor: pointer;
    }

    .h5video {
        object-fit: fill;
        height: 100%;
        width: 100%;
    }

    .btngroup {
        width: 100%;
        display: flex;
        align-items: center;
        color: #B7D2E8;
        display: flex;
        justify-content: flex-end;

        &>div {
            width: 24px;
            height: 24px;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        button {
            background: rgba(97, 109, 148, 0.5);
            border-radius: 2px;
            border: 1px solid #616d94;
            margin: 0 17px 0 0;
            min-height: 24px;
            height: 24px;
            line-height: 3px;
            padding: 0 10px;
        }

        .activeBtn {
            background: #0A1B29;
        }
    }
}
</style>
