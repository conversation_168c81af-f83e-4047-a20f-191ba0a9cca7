<template>
    <div class="z100">
        <div class="left">
            <div class="header flex-start">
                <img src="../../assets/images/common/head.png">
                <div> 巡逻线路</div>
            </div>
            <div class="input">
                <el-input v-model="keyword" @change="search" prefix-icon="Search" placeholder="线路名称搜索"></el-input>
            </div>
            <div class="device">
                <el-scrollbar v-if="list.length > 0">
                    <div class="list space-between" v-for="item in list" :key="item.id">
                        <div class="center cursor">
                            <div>
                                <span class="iconfont iconliucheng1"></span>
                            </div>
                            <div class="name" @click="playLine(item)">{{ item.name }}</div>
                        </div>
                        <div class="center state">
                            <div style="color:green" v-if="item.enableAuto">自动</div>
                            <div v-else style="color:red">手动</div>
                        </div>
                        <div class="position cursor" @click="zoomToPosition(item)">
                            <span>{{ item.cams.length }}个</span>
                        </div>
                    </div>
                </el-scrollbar>
                <noData v-else />
            </div>

            <div class="page center">
                <el-pagination prev-icon="CaretLeft" next-icon="CaretRight" :page-size="size" :current-page="page"
                    layout="total,prev, pager, next" @current-change="handleCurrentChange" :total="total">
                </el-pagination>
            </div>

        </div>

        <pop show="true" :title="lineName">
            <div class="live_container h100 ">
                <div class="container_right">
                    <div class="btngroup">

                        <div v-if="stop" type="primary" size="medium" @click="continuLine">
                            <span class="iconfont iconbofang3"></span>
                        </div>


                        <div v-if="!stop" type="primary" size="medium" @click="stopLine">
                            <span class="iconfont iconzantingtingzhi"></span>
                        </div>

                        <div type="primary" size="medium" @click="showEvent">
                            <span class="iconfont icon06"></span>
                        </div>

                    </div>
                    <div class="wrapper_t">
                        <div name="flex" v-for="r in rows" :key="r" class="rowlist" :style="heightStyle">
                            <div class="col" :class="[selectCol == c && selectRow == r ? 'active' : '']" name="flex"
                                v-for="c in cols" @contextmenu.prevent="stopVideo('h' + r + c, $event)"
                                @click="videoClick(r, c, $event)" :key="c">
                                <video v-if="type != 2" @dblclick="full($event)" class="h5video" v-bind:id="'h' + r + c"
                                    autoplay></video>
                                <div v-if="type == 2" @dblclick="full($event)" class="h5video" v-bind:id="'h' + r + c"
                                    autoplay>
                                </div>
                                <div class="ptz" :style="{ display: ptzDomMap.get('h' + r + c) ? 'flex' : 'none' }">
                                    <div class="ptzbtn">
                                        <div class="up">
                                            <div>
                                                <el-button @mousedown="PtzAction('up', 'h' + r + c)"
                                                    @mouseup="PtzAction('stop', 'h' + r + c)" type="primary" icon="ArrowUp"
                                                    size="mini" circle></el-button>
                                            </div>
                                        </div>
                                        <div class="mid">
                                            <div>
                                                <el-button @mousedown="PtzAction('left', 'h' + r + c)"
                                                    @mouseup="PtzAction('stop', 'h' + r + c)" type="primary"
                                                    icon="ArrowLeft" size="mini" circle></el-button>
                                            </div>
                                            <div>
                                                <el-button @mousedown="PtzAction('right', 'h' + r + c)"
                                                    @mouseup="PtzAction('stop', 'h' + r + c)" type="primary"
                                                    icon="ArrowRight" size="mini" circle></el-button>
                                            </div>
                                        </div>
                                        <div class="down">
                                            <div>
                                                <el-button @mousedown="PtzAction('down', 'h' + r + c)"
                                                    @mouseup="PtzAction('stop', 'h' + r + c)" type="primary"
                                                    icon="ArrowDown" size="mini" circle></el-button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="ptzzoom">
                                        <div>
                                            <el-button @mousedown="PtzAction('zoomin', 'h' + r + c)"
                                                @mouseup="PtzAction('stop', 'h' + r + c)" type="primary" icon="Plus"
                                                size="mini" circle></el-button>
                                        </div>
                                        <div>
                                            <el-button @mousedown="PtzAction('zoomout', 'h' + r + c)"
                                                @mouseup="PtzAction('stop', 'h' + r + c)" type="primary" icon="Minus"
                                                size="mini" circle></el-button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </pop>

        <div class="right">
            <div class="item" style="flex:2">
                <sub-title1 title='视频列表' />
                <div class="item-body event">
                    <el-scrollbar v-if="line.cams.length > 0">
                        <div class="list " v-for="(item, i) in line.cams" :key="item.camToken">
                            <div class="name" style="width: 100%;">
                                <span class="iconfont iconshexiangji" style="color:green"></span>
                                <div class="cursor" @click="playVideo(item, i)">{{ item.strName }} {{ i == index ? '--正在播放'
                                    :
                                    '' }}</div>
                            </div>


                            <div class="bar"></div>
                        </div>
                    </el-scrollbar>
                    <noData v-else />
                </div>
            </div>

            <div class="item" style="flex:2">
                <sub-title1 title='事件列表' />
                <div class="item-body event">
                    <el-scrollbar v-if="event.length > 0">
                        <div class="list " v-for="i in event" :key="i.id">
                            <div class="name">
                                <div>{{ i.lineName }}</div>
                            </div>
                            <div class="name">
                                <div>{{ i.camName }}</div>
                            </div>
                            <div class="time" style="width:130px">
                                {{ i.createTime }}
                            </div>

                            <div class="bar"></div>
                        </div>
                    </el-scrollbar>
                    <noData v-else />
                </div>
            </div>
        </div>
        <el-dialog align-center append-to-body draggable custom-class="addDiagram border0" @open="open" v-model="dialogEvent"
            width="600px" title="事件记录">
            <el-form class="form" ref="form" :model="event" label-width="100px">

                <el-form-item label="事件主题:" prop="name">
                    <el-input placeholder="请输入名称" v-model="event.name"></el-input>
                </el-form-item>

                <el-form-item label="事件描述:" prop="description">
                    <el-input type="textarea" placeholder="请输入内容" v-model="event.description"></el-input>
                </el-form-item>
                <el-form-item label="巡检结果:" prop="description">
                    <el-radio-group v-model="event.result">
                        <el-radio label="0" size="large">正常</el-radio>
                        <el-radio label="1" size="large">异常</el-radio>
                    </el-radio-group>
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer search_box">
                    <div type="primary" size="mini" class="searchBtn" :disabled="loading" @click="saveEvent('form')">确 定
                    </div>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script>
import {
    defineComponent,
    getCurrentInstance,
    reactive,
    toRefs,
    computed,
    onMounted,
    ref,
    inject,
    watch
} from 'vue';
import "@/lib/h5splayer";
import {
    H5sPlayerCreate
} from "@/lib/h5splayerhelper";
import {
    getCookie
} from "@/utils/cookie";
import {
    useStore
} from 'vuex';
import Alarm from '@/components/echarts/weekEventEchart.vue'
import pop from '@/components/pop'
import liquid from './liquid.vue'
import {
    ElMessage
} from 'element-plus'
import { tr } from 'element-plus/es/locale';
export default defineComponent({
    name: "videoline",
    components: {
        Alarm,
        pop,
        liquid
    },

    setup() {
        const {
            proxy
        } = getCurrentInstance()
        const store = useStore();
        const state = reactive({
            rows: 1,
            cols: 1,
            ptzDomMap: new Map(),
            keyword: "",
            size: 20,
            page: 1,
            total: 0,
            list: [],
            videos: [],
            cam: null,
            lineName: '视频巡逻',
            line: {
                cams: []
            },
            dialogEvent: false,
            event: {
                name: '',
                description: '',
                result: "0",
                lineName: '',
                lineId: '',
                camName: '',
            },
            interval: null,
            index: 0,
            config: null,
            stop:false,

        })
        const emitter = inject("mitt");
        const dataStatus = ref({
            name: ""
        });

        onMounted(() => {
            getLine();
            getEvent();
        });

        //当前激活的一级菜单
        const activeMenus = computed(() => {
            let menu = getCookie("funMenus");
            return store.state.menu.funMenus ?
                store.state.menu.funMenus :
                menu ?
                    JSON.parse(menu) :
                    "";
        });

        const getLine = () => {
            proxy.$api.getVideoLine({
                projectId: getCookie("gh_projectId"),
                keyword: state.keyword,
                page: state.page,
                size: state.size,
            }).then((res) => {
                if (res.data) {
                    state.list = res.data;
                    state.total = res.total;
                }
            });
        }

        const handleCurrentChange = (page) => {
            state.page = page;
            getLine();
        }

        const search = () => {
            state.page = 1;
            getLine();
        }
        const playLine = (item) => {
            state.lineName = item.name;
            state.line = item;
            state.index = 0;
            state.cam = null;
            //自动播放
            if (item.enableAuto) {
                if (state.interval) {
                    clearInterval(state.interval);
                    state.interval = null;
                }

                if (item.cams.length > 0) {
                    //第一次执行
                    play(item.cams[0]);

                    if (state.index < item.cams.length - 1) {
                        state.interval = setInterval(() => {
                            state.index++;
                            if (state.index == item.cams.length - 1) {
                                play(state.line.cams[state.index]);
                                clearInterval(state.interval);
                                state.interval = null;
                                ElMessage.success(state.line.name + "巡检完成")
                            }
                            if (state.index < state.line.cams.length - 1) {
                                play(state.line.cams[state.index]);
                                // state.index++;
                            }

                        }, item.patrolTime ? item.patrolTime * 1000 : 10000);
                    } else {
                        ElMessage.success(state.line.name + "巡检完成")
                    }


                }

            }
        }
        const play = (item) => {
            if (state.line) {
                let vid = "h11";
                let conf = {
                    videoid: vid,
                    protocol: window.location.protocol, //http: or https:
                    host: item.ip + ":" + item.port, //localhost:8080
                    rootpath: "/", // '/'
                    token: item.camToken, //token got from login
                    hlsver: "v1", //v1 is for ts, v2 is for fmp4
                    session: "3b347c65-c7a5-47b2-a2ca-10be8adda6a1", //session got from login
                };
                if (state.config) {
                    state.config.disconnect();
                    state.config = null;
                }
                state.config = H5sPlayerCreate(conf);
                state.config.connect();
            }

        }

        const zoomToPosition = async (item) => {
            let {
                data
            } = await proxy.$api.getCamObjectId({
                projectId: getCookie("gh_projectId"),
                token: item.camToken
            });
            if (data) {
                emitter.emit("zoomToComponents", {
                    objectId: data.objectId
                })
            } else {
                ElMessage.warning("该设备未关联模型构件");
                return;
            }

        };
        const showEvent = () => {
            state.dialogEvent = true;
            state.event = {
                name: '',
                description: '',
                result: "0",
                lineName: '',
                lineId: '',
                camName: '',
            }
        }
        const saveEvent = () => {
            debugger
            if (!state.event.name) {
                ElMessage.warning("请输入事件主题");
                return;
            }
            if (!state.event.description) {
                ElMessage.warning("请输入事件描述");
                return;
            }
            state.event.lineName = state.lineName;
            state.event.lineId = state.line.id;
            state.event.camName = state.cam ? state.cam.strName : state.line.cams[state.index].strName;
            proxy.$api.saveLineEvent(state.event).then((res) => {
                ElMessage.success("保存成功");
                state.dialogEvent = false;
                getEvent();
            });
        }
        const getEvent = () => {
            proxy.$api.getLineEvent(state.event).then((res) => {
                state.event = res.data;
            });
        }
        const playVideo = (item, i) => {
            if (!state.line.enableAuto) {
                state.cam = item;
                state.index = i;
                play(item);
            } else {
                ElMessage.warning("自动模式下不支持手动播放视频");
            }
        }

        const continuLine = () => {
            if (state.line) {
                state.stop=false;
                let item=state.line; 
                ElMessage.success(state.line.name + "巡检继续")
                play(item.cams[state.index]);
                if (state.index < item.cams.length - 1) {
                    state.interval = setInterval(() => {
                        state.index++;
                        if (state.index == item.cams.length - 1) {
                            play(state.line.cams[state.index]);
                            clearInterval(state.interval);
                            state.interval = null;
                            ElMessage.success(state.line.name + "巡检完成")
                        }
                        if (state.index < state.line.cams.length - 1) {
                            play(state.line.cams[state.index]);
                            // state.index++;
                        }

                    }, item.patrolTime ? item.patrolTime * 1000 : 10000);
                } else {
                    ElMessage.success(state.line.name + "巡检完成")
                }
            }

        }

        const stopLine = (item, i) => {
            if (state.interval) {
                state.stop=true;
                clearInterval(state.interval);
                state.interval = null;
                ElMessage.success(state.line.name + "已暂停")
            }
        }

        return {
            ...toRefs(state),
            handleCurrentChange,
            search,
            playLine,
            zoomToPosition,
            saveEvent,
            showEvent,
            playVideo,
            continuLine,
            stopLine

        }
    }
});
</script>

<style lang="scss" scoped>
.live_container {
    display: flex;
    // background: #103457;
    // opacity: 0.25;

    .container_right {
        box-sizing: border-box;
        height: calc(100% - 10px);
        width: 100%;
        margin: 0 auto;

        .wrapper_t {
            width: 100%;
            height: calc(100% - 30px);
            background: #0A1B29;

            .rowlist {
                height: 100%;
                width: 100%;
                display: flex;

                .active {
                    border: 1px solid #3de9fa !important;
                }

                &:not(:last-child) {
                    margin-bottom: 5px;
                }

                div {
                    border: 1px solid #4a5966;
                }

                .col {
                    height: 100%;
                    margin-right: 5px;
                    flex: 1 1 0%;
                    box-sizing: border-box;

                    .ptz {
                        position: absolute;
                        bottom: 5px;
                        left: 5px;
                        z-index: 6000;
                        border: 0 !important;
                        display: flex;

                        div {
                            border: 0 !important;
                        }

                        align-items: center;

                        .ptzbtn {
                            display: flex;
                            flex-wrap: wrap;
                            flex-direction: column;
                            align-content: space-between;
                            align-items: center;
                            margin-right: 20px;

                            .mid {
                                display: flex;
                                justify-content: space-between;

                                div:nth-of-type(1) {
                                    margin-right: 20px;
                                }
                            }
                        }

                        .ptzzoom {
                            display: flex;
                            flex-direction: column;

                            div:nth-of-type(1) {
                                margin-bottom: 5px;
                            }
                        }
                    }
                }
            }
        }
    }

    .wrapper {
        display: flex;
        padding: 0 10px;
        height: 100%;
        border: 1px solid red;
    }

    .header {
        border-bottom: 1px solid rgba(33, 61, 112, 1);
    }

    div[name="flex"] {
        display: flex;
        position: relative;
        cursor: pointer;
    }

    .h5video {
        object-fit: fill;
        height: 100%;
        width: 100%;
    }

    .btngroup {
        width: 100%;
        display: flex;
        align-items: center;
        color: #B7D2E8;
        display: flex;
        justify-content: flex-end;

        &>div {
            width: 24px;
            height: 24px;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        button {
            background: rgba(97, 109, 148, 0.5);
            border-radius: 2px;
            border: 1px solid #616d94;
            margin: 0 17px 0 0;
            min-height: 24px;
            height: 24px;
            line-height: 3px;
            padding: 0 10px;
        }

        .activeBtn {
            background: #0A1B29;
        }
    }
}
</style>
