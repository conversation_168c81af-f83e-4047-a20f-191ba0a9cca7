<template>
    <div class="wrapper">
        <!-- <el-form :inline="true" class="search_box">
            <el-form-item label="名称:">
                <el-input placeholder="按名称查询" v-model="keyword"></el-input>
            </el-form-item>

            <el-form-item>
                <div class="searchBtn" @click="search" type="text">查询</div>
            </el-form-item>
        </el-form> -->

        <el-table :data="list" height="calc(100% - 60px)" fit>
            <template #empty>
                <no-data />
            </template>
            <el-table-column prop="doorName" label="门禁名称" align="center">
            </el-table-column>
            <el-table-column prop="doorID" label="门禁编号" align="center">
            </el-table-column>
            <el-table-column prop="devID" label="门禁设备ID" align="center">
            </el-table-column>
            <el-table-column prop="devMacAddr" label="设备MAC" align="center">
            </el-table-column>
            <el-table-column prop="serverIP" label="服务器ip" align="center">
            </el-table-column>
            <el-table-column prop="devIP" label="设备ip" align="center">
            </el-table-column>
            <el-table-column prop="devIsUsed" label="是否启用" align="center">
                <template #default="scope">
                    <span style="color:green">{{ scope.row.devIsUsed == 0 ? '未启用' : '启用' }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="isKqUse" label="是否考勤" align="center">
                <template #default="scope">
                    <span style="color:green">{{ scope.row.isKqUse == 0 ? '不考勤' : '考勤' }}</span>
                </template>
            </el-table-column>
        </el-table>

        <div class="page center">
            <el-pagination prev-icon="CaretLeft" next-icon="CaretRight" :page-size="size" :current-page="page"
                layout="total,prev, pager, next" @current-change="handleCurrentChange" :total="total">
            </el-pagination>
        </div>
    </div>
</template>
    
<script>
import {
    getCurrentInstance,
    onMounted,
    reactive,
    toRefs
} from 'vue'
import dayjs from 'dayjs'
export default {
    name: 'AcsDoor',
    setup() {
        const {
            proxy
        } = getCurrentInstance()
        const state = reactive({
            date: [],
            page: 1,
            size: 20,
            total: 0,
            list: [],
            status: ['初始状态', '开门状态', '关门状态', '离线状态'],
            keyword: null,
        })

        onMounted(() => {
            getDoorLog()
        })
        const getDoorLog = () => {
            proxy.$api.getHKDoor({
                pageIndex: state.page,
                pageSize: state.size,
                timestamp: dayjs().unix(),
            }).then((res) => {
                if (res.data) {
                    let data = JSON.parse(res.data).data;
                    state.list = data.datalist;
                    state.total = data.totalCount;
                }

            })
        }

        const handleCurrentChange = (page) => {
            state.page = page
            getDoorLog()

        }

        const search = () => {
            state.page = 1
            getDoorLog()

        }
        return {
            ...toRefs(state),
            getDoorLog,
            handleCurrentChange,
            search
        }
    },
}
</script>
    
<style lang="scss" scoped>
.wrapper {
    height: 100%;
}
</style>
    