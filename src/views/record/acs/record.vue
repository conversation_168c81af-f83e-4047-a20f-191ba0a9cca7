<template>
    <div class="wrapper">
        <el-form :inline="true" class="search_box">
            <el-form-item label="时间选择:">
                <el-date-picker v-model="date" type="datetimerange" range-separator="至" start-placeholder="开始日期"
                    end-placeholder="结束日期" @change="getEvent">
                </el-date-picker>
            </el-form-item>
            <el-form-item>
                <div class="searchBtn" @click="search" type="text">查询</div>
            </el-form-item>
        </el-form>

        <el-table :data="list" height="calc(100% - 80px)" fit>
            <template #empty>
                <no-data />
            </template>
            <el-table-column prop="empNo" label="卡号" align="center">
            </el-table-column>
            <el-table-column prop="empName" label="姓名" align="center">
            </el-table-column>
            <el-table-column prop="dptName" label="部门名称" align="center">
            </el-table-column>
            <el-table-column prop="doorName" label="门禁名称" align="center">
            </el-table-column>
            <el-table-column prop="cardDay" label="时间" align="center">
            </el-table-column>
            <el-table-column prop="recordType" label="记录类型" align="center">
            </el-table-column>
            <el-table-column prop="inoutFlag" label="方向" align="center">
                <template #default="scope">
                    <span v-if="scope.row.inoutFlag == '1'">进</span>
                    <span v-if="scope.row.inoutFlag == '2'">出</span>
                    <span v-if="scope.row.inoutFlag == '0'">未知</span>
                </template>
            </el-table-column>
        </el-table>

        <div class="page center">
            <el-pagination prev-icon="CaretLeft" next-icon="CaretRight" :page-size="size" :current-page="page"
                layout="total,prev, pager, next" @current-change="handleCurrentChange" :total="total">
            </el-pagination>
        </div>
    </div>
</template>
    
<script>
import dayjs from 'dayjs'
import {
    getCurrentInstance,
    onMounted,
    reactive,
    toRefs
} from 'vue'
export default {
    name: 'AcsRecord',
    setup() {
        const {
            proxy
        } = getCurrentInstance()
        const state = reactive({
            tableHeight: window.innerHeight * 0.60,
            date: [],
            page: 1,
            size: 10,
            total: 0,
            list: [],
        })
        state.date.push(dayjs().format('YYYY-MM-DD 00:00:00'));
        state.date.push(dayjs().format('YYYY-MM-DD 23:59:59'));

        onMounted(() => {
            getDoorLog()
        })
        const getDoorLog = () => {
            proxy.$api.getDaShiRecord({
                pageIndex: state.page,
                pageSize: state.size,
                startTime: dayjs(state.date[0]).format("YYYY-MM-DD HH:mm:ss"),
                endTime: dayjs(state.date[1]).format("YYYY-MM-DD HH:mm:ss"),
                timestamp: dayjs().unix()
            }).then((res) => {
                if (res.data) {
                    let data = JSON.parse(res.data).data;
                    state.list = data.datalist;
                    state.total = data.totalCount;
                }

            })
        }

        const handleCurrentChange = (page) => {
            state.page = page
            getDoorLog()
        }
        const search = (page) => {
            getDoorLog();
        }
        return {
            ...toRefs(state),
            getDoorLog,
            handleCurrentChange,
            search
        }
    },
}
</script>
    
<style lang="scss" scoped>
.wrapper {
    height: 100%;
}
</style>
    