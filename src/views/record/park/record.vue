<template>
<div class="wrapper">
    <el-form :inline="true" class="search_box">
        <el-form-item label="时间选择:">
            <el-date-picker v-model="date" type="datetimerange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" @change="getEvent">
            </el-date-picker>
        </el-form-item>
        <el-form-item label="车牌号:">
            <el-input placeholder="请输入车牌号" v-model="keyword"></el-input>
        </el-form-item>
        <el-form-item>
            <div class="searchBtn" @click="search" type="text">查询</div>
        </el-form-item>
    </el-form>

    <el-table :data="list" height="calc(100% - 80px)" fit>
        <template #empty>
            <no-data />
        </template>
        <el-table-column prop="parkName" label="停车场" align="center">
        </el-table-column>
        <el-table-column prop="entranceName" label="出入口" align="center">
        </el-table-column>
        <el-table-column prop="roadwayName" label="车道" align="center">
        </el-table-column>
        <el-table-column prop="plateNo" label="车牌号" align="center">
        </el-table-column>
        <el-table-column prop="cardNo" label="卡号" align="center">
        </el-table-column>
        <el-table-column prop="crossTime" label="时间" align="center">
            <template #default="scope">
                <span>{{ dayjs(scope.row.crossTime).format("YYYY-MM-DD HH:mm:ss") }}</span>
            </template>
        </el-table-column>
        <el-table-column prop="vehicleOut" label="方向" align="center">
            <template #default="scope">
                <span v-if="scope.row.vehicleOut==0">进</span>
                <span v-if="scope.row.vehicleOut==1">出</span>
            </template>
        </el-table-column>
    </el-table>

    <div class="page center">
        <el-pagination prev-icon="CaretLeft" next-icon="CaretRight" :page-size="size" :current-page="page" layout="total,prev, pager, next" @current-change="handleCurrentChange" :total="total">
        </el-pagination>
    </div>
</div>
</template>

<script>
import dayjs from 'dayjs'
import {
    getCurrentInstance,
    onMounted,
    reactive,
    toRefs
} from 'vue'

export default {
    name: 'ParkRecord',
    setup() {
        const {
            proxy
        } = getCurrentInstance()
        const state = reactive({
            date: [],
            page: 1,
            size: 10,
            total: 0,
            list: [],
            keyword: null,
            dayjs,
        })
        state.date.push(dayjs().format('YYYY-MM-DD 00:00:00'));
        state.date.push(dayjs().format('YYYY-MM-DD 23:59:59'));
        onMounted(() => {
            crossRecords();
        })
        const crossRecords = () => {
            proxy.$api.crossRecords({
                plateNo: state.keyword,
                page: state.page,
                size: state.size,
                bt: dayjs(state.date[0]).format("YYYY-MM-DDTHH:mm:ss")+"+08:00",
                et: dayjs(state.date[1]).format("YYYY-MM-DDTHH:mm:ss")+"+08:00",
            }).then((res) => {
                state.total = res.data.total
                state.list = res.data.list
            })
        }

        const handleCurrentChange = (page) => {
            state.page = page
            crossRecords();
        }

        const search = (page) => {
            state.page = 1
            crossRecords();
        }
        return {
            ...toRefs(state),
            handleCurrentChange,
            search,
        }
    },
}
</script>

<style lang="scss" scoped>
.wrapper {
    height: 100%;
}
</style>
