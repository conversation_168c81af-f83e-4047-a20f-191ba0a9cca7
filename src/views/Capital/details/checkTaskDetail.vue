<template>
<el-dialog align-center append-to-body   draggable custom-class="addDiagram" @open="open" @closed="close" width="840px" v-model="show" title="盘点详情">
    <el-row type="flex" :gutter="20" class="row">
        <el-col :span="8"> 任务时间：{{ detail.createTime }}</el-col>
        <el-col :span="8"> 任务名称：{{ detail.name }}</el-col>
        <el-col :span="8">
            状态：{{ detail.status == 0 ? "待处理" : "" }}</el-col>
        <el-col :span="8">操作人：{{ detail.createName }}</el-col>
    </el-row>
    <panel title="盘点资产列表" />
    <el-table :data="detail.itemList" height="300" fit>
        <template #empty>
            <noData />
        </template>
        <el-table-column prop="assetsCode" label="资产编号" align="center">
        </el-table-column>
        <el-table-column prop="assetsName" label="资产名称" align="center">
        </el-table-column>
        <el-table-column prop="status" label="状态" align="center">
            <template #default="scope">
                <el-select clearable v-model="scope.row.status" @change="check($event,scope.row)">
                    <el-option label="盘点" value="1">已盘点</el-option>
                    <el-option label="未盘点" value="0">未盘点</el-option>
                </el-select>
            </template>
        </el-table-column>
        <el-table-column prop="remark" label="盘点人/盘点时间" align="center">
            <template #default="scope">
                <span>{{scope.row.userName}}/{{scope.row.createTime}}</span>
            </template>
        </el-table-column>
    </el-table>

</el-dialog>
</template>

<script>
import {
    reactive,
    toRefs
} from "vue";
import {
    useRouter
} from "vue-router";
import {
    getCurrentInstance,
    computed
} from "vue";
import {getCookie} from '@/utils/cookie'
export default {
    props: ['showDialog', 'id'],
    setup(props,{emit}) {
        const {
            proxy
        } = getCurrentInstance();
        const router = useRouter();
        const state = reactive({
            detail: {
                oid: "4006828489407771636",
                createTime: "2021-05-28 15:16:05",
                createBy: "3992516245809307638",
                createId: "zhaolin",
                createName: "赵琳",
                updateTime: "2021-05-28 15:16:05",
                updateBy: "3992516245809307638",
                updateId: "zhaolin",
                updateName: "赵琳",
                version: 1,
                code: "PD000091",
                taskId: "4006828354003055604",
                taskName: "第三季度资产盘点",
                startTime: null,
                endTime: null,
                status: "0",
                organizeType: 0,
                organize: "72",
                organizeName: "橡树林",
                department: "3985884259092050934",
                departmentName: "市场管理与拓展部一组",
                remark: "",
                statusValue: "待处理",
                itemList: null,
            },
        });

        const open = () => {
            getTaskDetail();
        }

        const getTaskDetail = () => {
            proxy.$api.getCheckTaskDetail({
                "id": props.id
            }).then((res) => {
                state.detail = res.data;
                getCheckInfoList(res.data.deptId, res.data.id);
            });
        };
        const getCheckInfoList = (deptId, id) => {
            proxy.$api.getCheckInfoList({
                "deptId": deptId,
                taskId: id
            }).then((res) => {
                state.detail.itemList = res.data;
            });
        };
        const check = (event, row) => {
            let data = {
                status: event,
                checkTaskId: row.id,
                assetsCode: row.assetsCode,
                assetsName: row.assetsName,
                deptId:state.detail.deptId,
                projectId:getCookie("gh_projectId")
            }
            proxy.$api.addCheckRecord(data).then();
        };


        const close = () => {
            emit('update:showDialog', false)
            emit('getListByCondition')
        }
        const show=computed(()=>{return props.showDialog})
        return {
            ...toRefs(state),
            check,
            getTaskDetail,
            open,
            close,
            show,
        };
    },
};
</script>

<style lang="scss" scoped>
.purchase {
    color: #fff;
    padding: 15px;

    .row {
        line-height: 30px;
        margin-bottom: 10px;
    }
}
</style>
