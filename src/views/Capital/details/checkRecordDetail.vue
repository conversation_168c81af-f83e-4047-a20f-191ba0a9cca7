<template>
<div class="layout_wrapper">
    <div class="purchase">
        <el-page-header content="盘点记录详情" @back="goBack"></el-page-header>
        <el-row type="flex" :gutter="20" class="row">
            <el-col :span="8"> 盘点单号：{{detail.code}}</el-col>
            <el-col :span="8"> 盘点名称：{{detail.organizeName}}</el-col>
            <el-col :span="8"> 盘点状态：{{detail.createName}}</el-col>
            <el-col :span="8"> 预计完成时间：{{detail.createTime}}</el-col>
            <el-col :span="8">操作人：{{detail.status==1?'已通过':''}}</el-col>
            <el-col :span="8"> 创建时间：{{detail.auditDesc}}</el-col>
            <el-col :span="8">盘点说明：</el-col>
        </el-row>
        <panel title="盘点记录" />
        <div class="table">
            <el-table :data="detail.assetsCheckInstanceList" :height="tableHeight"  fit>
                <template #empty>
                    <noData />
                </template>
                <el-table-column prop="organizeName" label="单位" align="center">
                </el-table-column>
                <el-table-column prop="status" label="状态" align="center">
                    <template #default="scope">
                        <span>{{scope.row.status==0?'待处理':''}}</span>
                    </template>
                </el-table-column>
                <el-table-column label="盘点结果" align="center">
                    <template #default="scope">
                        <el-button type="text" class="editBtn" @click="checkDetail(scope.row)">查看</el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>
    </div>
</div>
</template>

<script>
import {
    reactive,
    toRefs
} from 'vue'
import {
    useRouter
} from 'vue-router'
import {
    getCurrentInstance,
    onMounted
} from 'vue'
export default {

    setup() {
        const {
            proxy
        } = getCurrentInstance();
        const router = useRouter()
        const state = reactive({
            detail: {
                "oid": "4001032788991066100",
                "createTime": "2021-03-25 15:55:54",
                "createBy": "30002",
                "createId": "ymuser2",
                "createName": "管理员",
                "updateTime": "2021-05-28 15:05:37",
                "updateBy": "3992516245809307638",
                "updateId": "zhaolin",
                "updateName": "赵琳",
                "version": 2,
                "code": "CG000041",
                "userId": "30002",
                "userName": "管理员",
                "dateTime": null,
                "reason": null,
                "purchaseCondition": null,
                "nowStatus": null,
                "totelMoney": null,
                "status": "1",
                "auditorDeptId": "3985884259092050934",
                "auditorDeptName": "市场管理与拓展部一组",
                "auditorId": "3992516245809307638",
                "auditorName": "赵琳",
                "auditDesc": "同意",
                "auditorDate": "2021-05-28 15:05:37",
                "remark": null,
                "organizeType": 0,
                "organize": "1",
                "organizeId": "0000",
                "organizeName": "石羊场店",
                "department": "3985884299912070134",
                "departmentName": "资产管理部",
                "assetsCheckInstanceList": [{
                    code: "PD000021",
                    createBy: "3992516245809307638",
                    createId: "zhaolin",
                    createName: "赵琳",
                    createTime: "2021-05-28 15:13:56",
                    department: "3985884259092050934",
                    departmentName: "市场管理与拓展部一组",
                    endTime: null,
                    itemList: null,
                    oid: "4006828354029270004",
                    organize: "1",
                    organizeName: "石羊场店",
                    organizeType: 0,
                    remark: "",
                    startTime: null,
                    status: "0",
                    statusValue: null,
                    taskId: "4006828354003055604",
                    taskName: "第三季度资产盘点",
                    updateBy: "3992516245809307638",
                    updateId: "zhaolin",
                    updateName: "赵琳",
                    updateTime: "2021-05-28 15:13:56",
                }]
            },
        })
        const goBack = () => {
            router.go(-1)
        }
        const checkDetail = (row) => {
            router.push({
                path: '/capital-checkRecordInfo',
                query: {
                    oid: row.oid,
                    detailOid: row.taskId
                },
            })
        }
        onMounted(() => {
            // 后台无接口
             getCheckTaskDetail();
        });
        const getCheckTaskDetail = () => {
            proxy.$api.getCheckTaskDetail({
                id: router.currentRoute.value.query.id
            }).then(res => {
                state.detail = res.data;
            });
        }
        return {
            ...toRefs(state),
            goBack,
            checkDetail
        }
    }
}
</script>

<style lang="scss" scoped>
.purchase {
    color: #fff;
    padding: 15px;

    .row {
        line-height: 30px;
        margin-bottom: 10px;
    }
}
</style>
