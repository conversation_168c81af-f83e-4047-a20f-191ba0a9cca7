<template>
<el-dialog align-center append-to-body   draggable custom-class="addDiagram" @open="open" @closed="close" width="840px" v-model="show" :title="panelTitle">
    <el-form label-width="100px" ref="formRef" :model="detail" :rules="rules">
        <el-row type="flex" :gutter="20">
            <el-col :span="12">
                <el-form-item label="资产名称" prop="assetsName">
                    <el-input v-model="detail.assetsName" placeholder="请输入规格型号" />
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="资产编号" prop="assetsCode">
                    <el-input v-model="detail.assetsCode" placeholder="请输入规格型号" />
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="二维码编码">
                    <el-input v-model="detail.qrCode" placeholder="请输入二维码编码" />
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="采购单号" >
                    <el-select v-model="detail.purchaseCode" placeholder="请选择" clearable class="w100">
                        <el-option v-for="item in purchases" :value="item.code" :label="item.name" :key="item.code"></el-option>
                    </el-select>
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="选择类型" prop="typeId">
                    <el-select v-model="detail.typeId" placeholder="请选择" clearable class="w100">
                        <el-option v-for="item in types" :value="item.id" :label="item.name" :key="item.id"></el-option>
                    </el-select>
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="选择用途">
                    <el-select v-model="detail.useType" placeholder="请选择" clearable class="w100">
                        <el-option v-for="item in uses" :value="item.id" :label="item.tagName" :key="item.id"></el-option>
                    </el-select>
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="SN号">
                    <el-input v-model="detail.sn" placeholder="请输入SN号" />
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="规格型号">
                    <el-input v-model="detail.model" placeholder="请输入规格型号" />
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="出厂日期">
                    <el-date-picker style="width:100%" popper-class="select_panel" v-model="detail.produceDate" type="date" placeholder="选择日期">
                    </el-date-picker>
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="使用期限">
                    <el-input placeholder="请输入内容" v-model="detail.useMonth">
                        <template #append>月</template>
                    </el-input>
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="购入日期">
                    <el-date-picker style="width:100%" v-model="detail.purchaseDate" popper-class="select_panel" type="date" placeholder="选择日期">
                    </el-date-picker>
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="存放位置" prop="addressValue">
                    <el-input v-model="detail.address" placeholder="请选择存放位置" />
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="金额">
                    <el-input placeholder="请输入金额" v-model="detail.price">
                        <template #append>元</template>
                    </el-input>
                </el-form-item>
            </el-col>

            <el-col :span="12">
                <el-form-item label="品牌" prop="brandValue">
                    <el-select v-model="detail.brandId" placeholder="请选择品牌" class="w100">
                        <el-option v-for="item in brands" :label="item.name" :key="item.id" :value="item.id"></el-option>
                    </el-select>
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="所属部门" prop="deptId">
                    <el-select v-model="detail.deptId" placeholder="请选择部门" class="w100">
                        <el-option v-for="item in depts" :label="item.name" :key="item.id" :value="item.id"></el-option>
                    </el-select>
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="设备关联">
                    <el-select v-model="detail.deviceId" placeholder="请选择部门" class="w100">
                        <el-option v-for="item in devices" :label="item.name" :key="item.id" :value="item.id"></el-option>
                    </el-select>
                </el-form-item>
            </el-col>

            <el-col :span="12">
                <el-form-item label="资产状态" prop="actionStatus">
                    <el-select v-model="detail.actionStatus" placeholder="请选择活动区域" class="w100">
                        <el-option label="闲置中" :value="0"></el-option>
                        <el-option label="使用中" :value="1"></el-option>
                        <el-option label="已报废" :value="2"></el-option>
                        <el-option label="待处置" :value="3"></el-option>
                    </el-select>
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="备注">
                    <el-input type="textarea" placeholder="请输入内容" v-model="detail.remark" maxlength="200" show-word-limit>
                    </el-input>
                </el-form-item>
            </el-col>

        </el-row>
        <el-row type="flex" :gutter="20">
            <el-col :span="12">
                <el-form-item label="供应商">
                    <el-input placeholder="请输入供应商名称" v-model="detail.supplier">
                    </el-input>
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="联系人">
                    <el-input placeholder="请输入联系人姓名" v-model="detail.contact">
                    </el-input>
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="联系方式">
                    <el-input placeholder="请输入联系方式" v-model="detail.phone">
                    </el-input>
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="维保到期">
                    <el-input placeholder="请输入维保到期期限" v-model="detail.guaranteeDate">
                        <template #append>天</template>
                    </el-input>
                </el-form-item>
            </el-col>
        </el-row>
        <div class="btn center search_box">
            <div type="primary" size="small" class="searchBtn" @click="save">保存</div>
        </div>
    </el-form>

</el-dialog>
</template>

<script>
import {
    computed,
    defineComponent,
    reactive,
    ref,
    toRefs
} from 'vue'
import {
    useRoute,
    useRouter
} from 'vue-router'
import {
    getCurrentInstance,
    onMounted
} from 'vue'
import {
    ElMessage
} from 'element-plus'
import {
    getCookie
} from '@/utils/cookie'
export default defineComponent({
    props: ['showAccount', 'id'],
    emits: ['getListByCondition'],
    setup(props, {
        emit
    }) {
        const {
            proxy
        } = getCurrentInstance();
        const route = useRoute()
        const state = reactive({
            panelTitle: '',
            formRef: null,
            detail: {
                financeCode: '',
                qrCode: "",
                typeValue: '',
                sn: '',
                model: "",
                produceDate: "",
                useMonth: "",
                purchaseDate: "",
                sourceType: "",
                addressValue: "",
                assetsName: "",
                price: "",
                unitId: "",
                brandValue: "",
                actionStatus: 0,
                remark: "",
                supplier: "",
                contact: "",
                phone: "",
                guaranteeDate: ""
            },
            rules: {
                assetsName: [{
                    required: true,
                    message: "请输入名称",
                    trigger: ["blur", "change"],
                }, ],
                assetsCode: [{
                    required: true,
                    message: "请输入编号",
                    trigger: ["blur", "change"],
                }, ],
                actionStatus: [{
                    required: true,
                    message: "请选择",
                    trigger: ["change"],
                }, ],
                brandId: [{
                    required: true,
                    message: "请选择",
                    trigger: ["change"],
                }, ],
                deptId: [{
                    required: true,
                    message: "请选择",
                    trigger: ["change"],
                }, ],
                typeId: [{
                    required: true,
                    message: "请选择",
                    trigger: ["change"],
                }, ],
                address: [{
                    required: true,
                    message: "请输入存放位置",
                    trigger: ["blur", "change"],
                }, ],
            },
            brands: [],
            types: [],
            depts: [],
            devices: [],
            uses: [],
            purchases: []
        })
        const router = useRouter()
        // onMounted(() => {
        //     if (props.id == 0) {
        //         state.panelTitle = "资产录入"
        //     } else {
        //         state.panelTitle = "资产编辑"
        //         getInfoDetail(props);
        //     }
        //     getBrand();
        //     getTypes();
        //     getDepts();
        //     getDevice();
        //     getAssetUse();
        // })
        const show = computed(() => {
            return props.showAccount;
        })

        const close = () => {
            emit('update:showAccount', false)

        }
        const open = () => {
            if (props.id == 0) {
                state.panelTitle = "资产录入"

                state.formRef.resetFields();
            } else {
                // proxy.$refs.form.reset();
                state.panelTitle = "资产编辑"
                getInfoDetail(props.id);
            }
            getBrand();
            getTypes();
            getDepts();
            getDevice();
            getAssetUse();
            getPurchase();
        }
        const getPurchase = () => {
            let data = {
                projectId: getCookie("gh_projectId"),
                status:1
            };
            proxy.$api.getPurchase(data).then((res) => {
                state.purchases = res.data;
            });
        };
        const getBrand = () => {
            proxy.$api.getBrandList({
                projectId: getCookie("gh_projectId")
            }).then(res => {
                state.brands = res.data;
            });
        }
        const getDevice = () => {
            proxy.$api.getDeviceInfo({
                projectId: getCookie("gh_projectId")
            }).then(res => {
                state.devices = res.data;
            });
        }
        const getTypes = () => {
            proxy.$api.getAssetsTypeList({
                projectId: getCookie("gh_projectId")
            }).then(res => {
                state.types = res.data;
            });
        }
        const getDepts = () => {
            proxy.$api.getDept({
                projectId: getCookie("gh_projectId")
            }).then(res => {
                state.depts = res.data;
            });
        }
        const getInfoDetail = (id) => {
            proxy.$api.getInfoDetail({
                id
            }).then(res => {
                state.detail = res.data;
            });
        }
        const save = () => {
            proxy.$refs.formRef.validate((validate) => {
                if (validate) {
                    state.detail.projectId = getCookie("gh_projectId");
                    if (state.panelTitle == "资产录入") {
                        proxy.$api.infoAdd(state.detail).then(res => {
                            ElMessage.success("录入成功");
                            emit("getListByCondition")
                            close();
                        });
                    } else if (state.panelTitle == "资产编辑") {
                        proxy.$api.infoEdit(state.detail).then(res => {
                            ElMessage.success("编辑成功")
                            emit("getListByCondition")
                            close();
                        });
                    }
                }
            });

        }

        const getAssetUse = () => {
            proxy.$api.getDicUtil({
                projectId: getCookie("gh_projectId"),
                dicCode: 'asset_useType',
            }).then((res) => {
                state.uses = res.data
            })
        }

        return {
            ...toRefs(state),
            save,
            close,
            show,
            open,
        }
    }
})
</script>

<style lang="scss" scoped>
.wrapper {
    display: flex;
    flex-direction: column;
    height: 100%;

    .form {
        width: 1000px;
        margin: 15px auto;
        height: calc(100% - 41px);
        overflow-x: hidden;
    }
}
</style>
