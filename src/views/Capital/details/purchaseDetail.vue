<template>
<el-dialog align-center append-to-body   draggable custom-class="addDiagram" @open="open" @closed="close" width="840px" v-model="show" title="采购详情">
    <div class="purchase">
        <el-page-header content="采购详情" @back="goBack"></el-page-header>
        <el-row type="flex" :gutter="20" class="row">
            <el-col :span="6"> 采购单编号：{{ detail.code }}</el-col>
            <el-col :span="6"> 需求部门：{{ detail.deptName }}</el-col>
            <el-col :span="6"> 操作人：{{ detail.createName }}</el-col>
            <el-col :span="6"> 创建日期：{{ detail.createTime }}</el-col>
            <el-col :span="6">状态：{{ status[detail.status] }}</el-col>
            <el-col :span="6"> 审核意见：{{ detail.auditDesc }}</el-col>
        </el-row>
        <panel title="资产需求" />
        <div class="table">
            <el-table :data="detail.items" :height="tableHeight" fit>
                <template #empty>
                    <noData />
                </template>
                <el-table-column type="index" width="50" label="序号" align="center">
                </el-table-column>
                <el-table-column prop="assetsName" label="资产名称" align="center">
                </el-table-column>
                <el-table-column prop="brandName" label="品牌" align="center">
                </el-table-column>
                <el-table-column prop="model" label="规格型号" align="center">
                </el-table-column>
                <el-table-column prop="qty" label="数量" align="center">
                </el-table-column>
                <el-table-column prop="remark" label="备注" align="center">
                </el-table-column>
                <!-- <el-table-column prop="organizeName" label="需求单位" align="center"></el-table-column> -->
            </el-table>
        </div>
    </div>
</el-dialog>
</template>

<script>
import {
    reactive,
    toRefs
} from "vue";
import {
    useRouter
} from "vue-router";
import {
    getCurrentInstance,
    computed
} from "vue";
export default {
    props: ['id', 'showDialog'],
    setup(props, {
        emit
    }) {
        const {
            proxy
        } = getCurrentInstance();
        const router = useRouter();
        const state = reactive({
            detail: {
                oid: "4001032788991066100",
                createTime: "2021-03-25 15:55:54",
                createBy: "30002",
                createId: "ymuser2",
                createName: "管理员",
                updateTime: "2021-05-28 15:05:37",
                updateBy: "3992516245809307638",
                updateId: "zhaolin",
                updateName: "赵琳",
                version: 2,
                code: "CG000041",
                userId: "30002",
                userName: "管理员",
                dateTime: null,
                reason: null,
                purchaseCondition: null,
                nowStatus: null,
                totelMoney: null,
                status: "1",
                auditorDeptId: "3985884259092050934",
                auditorDeptName: "市场管理与拓展部一组",
                auditorId: "3992516245809307638",
                auditorName: "赵琳",
                auditDesc: "同意",
                auditorDate: "2021-05-28 15:05:37",
                remark: null,
                organizeType: 0,
                organize: "1",
                organizeId: "0000",
                organizeName: "石羊场店",
                department: "3985884299912070134",
                departmentName: "资产管理部",
                itemList: [{
                    assetsName: "显示屏",
                    brandName: "定制",
                    qty: 1,
                    organizeName: "石羊场店",
                }, ],
            },
            status: ['待审核', '通过', '未通过']
        });
        const goBack = () => {
            router.push({
                path: "/capital-purchase",
            });
        };

        const getPurchaseDetail = () => {
            proxy.$api.getPurchaseDetail({
                id: props.id
            }).then(res => {
                state.detail = res.data;
            });
        };
        const show = computed(() => {
            return props.showDialog;
        })
        const close = () => {
            emit('update:showDialog',false);
        }
        const open = () => {
            getPurchaseDetail();
        }
        return {
            ...toRefs(state),
            goBack,
            getPurchaseDetail,
            show,
            open,
            close
        };
    },
};
</script>

<style lang="scss" scoped>
.purchase {
    color: #fff;
    padding: 15px;

    .row {
        line-height: 30px;
        margin-bottom: 10px;
    }
}
</style>
