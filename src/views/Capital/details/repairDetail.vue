<template>
  <div class="layout_wrapper">
    <div class="purchase">
      <el-page-header content="采购详情" @back="goBack"></el-page-header>
      <el-row type="flex" :gutter="20" class="row">
        <el-col :span="8"> 任务编号：{{detail.code}}</el-col>
        <el-col :span="8"> 资产编号：{{detail.itemList[0].assetsCode}}</el-col>
        <el-col :span="8"> 名称：{{detail.itemList[0].assetsName}}</el-col>
        <el-col :span="8"> 分类：{{detail.itemList[0].typeName}}</el-col>
        <el-col :span="8">品牌：{{detail.itemList[0].brandName}}</el-col>
        <el-col :span="8"> 规格：{{}}</el-col>
        <el-col :span="8"> 存放位置：{{detail.itemList[0].address}}</el-col>
        <el-col :span="8"> 状态：{{detail.auditDesc}}</el-col>
        <el-col :span="8"> 紧急程度：{{detail.level==0?'一般':''}}</el-col>
        <el-col :span="8"> 申请人：{{detail.auditorName}}</el-col>
        <el-col :span="8"> 报修说明：{{detail.repairDesc}}</el-col>
        <el-col :span="8"> 位置：{{}}</el-col>
      </el-row>
      <panel title="节点说明" />
    </div>
  </div>
</template>
<script>
import { reactive, toRefs } from 'vue'
import { useRouter } from 'vue-router'
import { getCurrentInstance, onMounted } from 'vue'
export default {
  setup () {
    const {proxy}=getCurrentInstance();
    const router = useRouter()
    const state = reactive({
      detail: {
        "oid": "4006081730811316212",
        "createTime": "2021-05-20 09:26:41",
        "createBy": "30001",
        "createId": "ymuser1",
        "createName": "管理员",
        "updateTime": "2021-05-20 09:48:59",
        "updateBy": "30001",
        "updateId": "ymuser1",
        "updateName": "管理员",
        "version": 3,
        "code": "WX000041",
        "level": "0",
        "dateTime": "2021-05-20 09:26:41",
        "constructUnit": "测试",
        "contact": "136144664646",
        "status": "3",
        "totalMoney": 100.00,
        "phone": "13316164646",
        "repairDate": "2021-05-20 09:28:00",
        "repairDesc": "测试",
        "outStatus": "0",
        "resultType": "0",
        "result": "测试",
        "userId": "30001",
        "userName": "管理员",
        "auditorDeptId": "3985884299912070134",
        "auditorDeptName": "资产管理部",
        "auditorId": "30001",
        "auditorName": "管理员",
        "auditDesc": "",
        "auditorDate": "2021-05-20 09:27:27",
        "department": "3985884299912070134",
        "departmentName": "资产管理部",
        "remark": null,
        "organizeType": 0,
        "organize": "1",
        "organizeId": "0000",
        "organizeName": "石羊场店",
        "itemList": [
          {
            "oid": "4006081730862696436",
            "createTime": "2021-05-20 09:26:41",
            "createBy": "30001",
            "createId": "ymuser1",
            "createName": "管理员",
            "updateTime": "2021-05-20 09:26:41",
            "updateBy": "30001",
            "updateId": "ymuser1",
            "updateName": "管理员",
            "version": 1,
            "repairId": "4006081730811316212",
            "code": "WX000041",
            "assets": "3995781525768537076",
            "assetsCode": "ZCJKSX017521",
            "assetsName": "摄像头",
            "organizeType": 0,
            "organize": "1",
            "organizeId": "0000",
            "organizeName": "石羊场店",
            "department": null,
            "departmentName": null,
            "remark": null,
            "financeCode": null,
            "sn": null,
            "qrCode": "ZCJKSX017521",
            "typeName": "摄像头",
            "brandName": null,
            "model": null,
            "address": "门店/汇彩园店",
            "location": null
          }
        ],
        "feeList": null,
        "files": null
      },
    })
    const goBack = () => {
      router.go(-1)
    }
    onMounted(()=>{
       getRepairDetail();
    });
    const getRepairDetail=()=>{
      proxy.$api.getRepairDetail({
         oid:router.currentRoute.value.query.oid
      }).then(res=>{
        state.detail=res.data;
      });
    }
    return {
      ...toRefs(state),
      goBack
    }
  }
}
</script>
<style lang="scss" scoped>
.purchase {
  color: #fff;
  padding: 15px;
  .row {
    line-height: 30px;
    margin-bottom: 10px;
  }
}
</style>