<template>
<el-dialog align-center append-to-body   draggable custom-class="addDiagram border0" width="560px" v-model="dialogData.visible" :title="dialogData.title">
    <el-form ref="form" class="form" :model="dialogData.form" :rules="dialogData.rules" label-width="120px">
        <el-form-item label="盘点名称:" prop="name">
            <el-input v-model="dialogData.form.name" placeholder="请输入盘点名称"></el-input>
        </el-form-item>
        <el-form-item label="盘点部门:">
            <el-select clearable v-model="dialogData.form.deptId" class="w100">
                <el-option v-for="item in depts" :key="item.id" :label="item.name" :value="item.id"></el-option>
            </el-select>
        </el-form-item>
        <el-form-item label="预计时间:" prop="finishDate">
            <el-date-picker style="width:100%" v-model="dialogData.form.finishDate" type="date" placeholder="选择日期" class="w100">
            </el-date-picker>
        </el-form-item>
        <el-form-item label="盘点说明:">
            <el-input v-model="dialogData.form.remark" type="textarea" placeholder="请输入备注" maxlength="200" show-word-limit>
            </el-input>
        </el-form-item>
    </el-form>
    <template #footer>
        <div class="dialog-footer search_box">
            <div type="primary" class="searchBtn" size="small" @click="save('form')">保存</div>
        </div>
    </template>
</el-dialog>
</template>

<script>
import {
    reactive,
    toRefs
} from 'vue'
import {
    getCurrentInstance,
    onMounted
} from 'vue'
import {
    getCookie
} from '@/utils/cookie'
import dayjs from 'dayjs'
export default {
    props: {
        dialogData: {
            visible: false,
            title: '',
            edit: false,
            form: {},
            rules: {

            }
        }
    },
    setup(props, context) {
        const {
            proxy
        } = getCurrentInstance()
        const state = reactive({
            depts: []
        })
        onMounted(() => {
            getDept()
        })
        const getDept = () => {
            proxy.$api.getDept(getCookie("gh_projectId")).then(res => {
                state.depts = res.data;
            })
        }
        const save = (formName) => {
            props.dialogData.form.finishDate=dayjs(props.dialogData.form.finishDate).format("YYYY-MM-DD HH:mm:ss")
            props.dialogData.form.projectId=getCookie("gh_projectId")
            proxy.$refs[formName].validate(valid => {
                if (valid) {
                    if (props.dialogData.edit) {
                        proxy.$api.updateCheckRecord(props.dialogData.form).then(res => {
                            context.emit('callback')
                        });
                    } else {
                        proxy.$api.addCheckTask(props.dialogData.form).then(res => {
                            context.emit('callback')
                        });
                    }

                } else {
                    return false
                }
            })
        }
        return {
            ...toRefs(state),
            save
        }
    }
}
</script>
