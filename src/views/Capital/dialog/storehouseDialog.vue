<template>
<el-dialog align-center append-to-body   custom-class="addDiagram" width="540px" v-model="dialogData.visible" :title="dialogData.title">
    <el-form ref="form" class="form" :model="dialogData.form" :rules="dialogData.rules" label-width="100px">
        <el-form-item label="库房名称:" prop="name">
            <el-input v-model="dialogData.form.name" placeholder="请输入库房名称"></el-input>
        </el-form-item>
        <el-form-item label="省市区:" prop="provinceId">
            <el-cascader ref="province" v-model="dialogData.form.provinceId" @change="changeProvince" popper-class="cascader" clearable placeholder="请选择省市区" :options="options" :props="props">
            </el-cascader>
        </el-form-item>
        <el-form-item label="地址:" prop="addr">
            <el-input v-model="dialogData.form.addr" placeholder="请输入详细地址"></el-input>
        </el-form-item>
        <el-form-item label="备注:">
            <el-input v-model="dialogData.form.remark" type="textarea" placeholder="请输入备注" maxlength="200" show-word-limit>
            </el-input>
        </el-form-item>
    </el-form>
    <template #footer>
        <div class="dialog-footer search_box">
            <div type="primary" class="searchBtn" size="small" @click="save('form')">保存</div>
        </div>
    </template>
</el-dialog>
</template>

<script>
import {
    reactive,
    toRefs
} from 'vue'
import {
    getCurrentInstance,
    onMounted
} from 'vue'
import {
    ref
} from 'vue'
export default {
    props: {
        dialogData: {
            visible: false,
            title: '',
            edit: false,
            form: {},
            rules: {

            }
        }
    },
    setup(props, context) {

        const {
            proxy
        } = getCurrentInstance()
        const state = reactive({
            options: [],
            action: '',
            props: {
                value: "oid",
                label: "name"
            },
            provinces: [],
            fileList: [{
                name: 'food.jpeg',
                url: 'https://fuss10.elemecdn.com/3/63/4e7f3a15429bfda99bce42a18cdd1jpeg.jpeg?imageMogr2/thumbnail/360x360/format/webp/quality/100'
            }]
        })
        const province = ref(null)
        onMounted(() => {
            getProvince();

        });
        const save = (formName) => {
            proxy.$refs[formName].validate(valid => {
                if (valid) {
                    props.dialogData.form.districtId = props.dialogData.form.provinceId[2];
                    props.dialogData.form.cityId = props.dialogData.form.provinceId[1];
                    props.dialogData.form.provinceId = props.dialogData.form.provinceId[0];
                    if (!props.dialogData.edit) {
                        proxy.$api.addHouse(props.dialogData.form).then(res => {
                            context.emit("callback");
                        });
                    } else {
                        proxy.$api.updateHouse(props.dialogData.form).then(res => {
                            context.emit("callback");
                        });
                    }
                } else {
                    return false
                }
            })
        }
        const getProvince = () => {
            proxy.$api.getProvince({}).then(res => {
                state.options = res.data;
            });
        }
        const changeProvince = (val) => {
            if (val) {
                let arr = province.value.getCheckedNodes()[0].pathLabels
                props.dialogData.form.provinceName = arr[0];
                props.dialogData.form.cityName = arr[1];
                props.dialogData.form.districtName = arr[2];
            }
        }
        return {
            ...toRefs(state),
            save,
            changeProvince,
            province
        }
    }
}
</script>
