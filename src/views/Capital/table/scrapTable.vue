<template>
<div class="h100">
    <el-form :inline="true" class="search_box form_inline" size="small" label-width="100px">
        <el-form-item label="资产名称">
            <el-input v-model="name" placeholder="请输入资产名称" />
        </el-form-item>

        <el-form-item>
            <div size="small" class="searchBtn" type="text" @click="getListByCondition">查询</div>
        </el-form-item>
    </el-form>

    <el-table class="table" :data="list" height="calc(100% - 90px)" fit @select="select" @select-all="select">
        <template #empty>
            <noData />
        </template>
        <el-table-column prop="assetsCode" label="资产编号" align="center">
        </el-table-column>
        <el-table-column prop="assetsName" label="资产名称" align="center">
        </el-table-column>
        <el-table-column prop="assetsName" label="报废说明" align="center"  show-overflow-tooltip>
        </el-table-column>
        <el-table-column prop="status" label="状态" align="center">
            <template #default="scope">
                <span>
                    <span style="color:green" v-if="scope.row.status=='0'">待审核</span>
                    <span style="color:green" v-if="scope.row.status=='1'">已通过</span>
                    <span style="color:green" v-if="scope.row.status=='2'">未通过</span>
                    <span v-if="scope.row.status==3"></span>
                </span>
            </template>
        </el-table-column>
        <el-table-column prop="organizeName" label="存放位置" align="center">
        </el-table-column>
        <el-table-column prop="createName" label="操作人" align="center">
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" align="center" width="180px">
        </el-table-column>
        <el-table-column label="操作" align="center">
            <template #default="scope">
                <el-button type="text" v-if="scope.row.status=='0'" class="editBtn" @click="dispose(scope.row)">审核</el-button>
            </template>
        </el-table-column>
    </el-table>

    <div class="center page">
        <el-pagination prev-icon="CaretLeft" next-icon="CaretRight"  :page-size="Pagination.size" :current-page="Pagination.page" layout="total,prev, pager, next" @current-change="handleCurrentChange" :total="Pagination.total">
        </el-pagination>
    </div>
    <el-dialog align-center append-to-body   custom-class="addDiagram border0" width="560px" v-model="dialogVisible" title="处置">
        <el-form ref="form1" class="form" label-width="100px">
            <el-form-item label="审核意见:">
                <el-input v-model="auditDesc" type="textarea" placeholder="请输入" maxlength="200" show-word-limit>
                </el-input>
            </el-form-item>
        </el-form>
        <template #footer>
            <div class="dialog-footer">
                <el-button type="primary" class="saveBtn" size="small" @click="updateScrap(1)">通过</el-button>
                <el-button type="primary" class="saveBtn" size="small" @click="updateScrap(2)">拒绝</el-button>
            </div>
        </template>
    </el-dialog>
</div>
</template>

<script>
import {
    reactive,
    toRefs
} from "vue";
import {
    useRouter
} from "vue-router";
import {
    getCurrentInstance,
    onMounted
} from "vue";
import {
    getCookie
} from '@/utils/cookie';
export default {
    props: ["tableData"],
    setup() {
        const {
            proxy
        } = getCurrentInstance();
        const router = useRouter();
        const state = reactive({
            moreShow: false,
            scrapTypeArr: ["售卖", "退租", "报废清理", "毁灭清理", "其他"],
            list: [],
            Pagination: {
                total: 0,
                page: 1,
                size: 10,
            },
            code: "",
            name: "",
            dialogVisible: false,
            auditDesc: "",
            form: {
                type: null,
                remark: "",
            },
            row: {},
            rules: {
                type: [{
                    required: true,
                    message: "请选择处置类型",
                    trigger: "change"
                }, ],
                remark: [{
                    required: true,
                    message: "请填写活动形式",
                    trigger: "blur"
                }, ],
            },
        });
        onMounted(() => {
            getListByCondition();
        });
        const getListByCondition = () => {
            let data = {
                page: state.Pagination.page,
                size: state.Pagination.size,
                keyword: state.name,
                projectId: getCookie("gh_projectId")
            };
            proxy.$api.getScraps(data).then((res) => {
                state.list = res.data;
                state.Pagination.total = res.total;
            });
        };
        const check = (row) => {
            router.push({
                path: `/capital-scrapDetail`,
                query: {
                    oid: row.oid,
                },
            });
        };
        const dispose = (row) => {
            state.row = row;
            state.dialogVisible = true;
        };
        const handleCurrentChange = (page) => {
            state.Pagination.page = page;
            getListByCondition();
        };
        const save = () => {
            proxy.$api
                .postScrap({
                    oid: state.row.oid,
                    reason: state.form.remark,
                    type: state.form.type,
                    status: "1",
                })
                .then((res) => {
                    getListByCondition();
                    state.dialogVisible = false;
                });
        };
        const updateScrap = (status) => {
            proxy.$api.auditAssetsScrap({
                id: state.row.id,
                desc: state.auditDesc,
                status: status,
                assetsCode: state.row.assetsCode
            }).then(res => {
                state.dialogVisible = false;
                getListByCondition();
            });

        }
        return {
            ...toRefs(state),
            check,
            dispose,
            save,
            getListByCondition,
            handleCurrentChange,
            updateScrap
        };
    },
};
</script>
