<template>
<div class="h100">
    <el-form :inline="true" class="search_box" size="small">

        <el-form-item label="日期范围">
            <el-date-picker v-model="value1" type="datetimerange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
            </el-date-picker>
        </el-form-item>
        <el-form-item>
            <div size="small" class="searchBtn" type="text" @click="getListByCondition">查询</div>
        </el-form-item>
    </el-form>

    <el-table class="table" :data="list" height="calc(100% - 90px)" fit @select="select" @select-all="select">
        <template #empty>
            <noData />
        </template>
        <el-table-column prop="code" label="采购单号" align="center">
        </el-table-column>
        <el-table-column prop="deptName" label="需求部门" align="center">
        </el-table-column>
        <el-table-column prop="createName" label="需求人" align="center">
        </el-table-column>
        <el-table-column prop="status" label="状态" align="center">
            <template #default="scope">
                <span style="color:green" v-if="scope.row.status=='0'">待审核</span>
                <span style="color:green" v-if="scope.row.status=='1'">已通过</span>
                <span style="color:green" v-if="scope.row.status=='2'">未通过</span>
                <span v-if="scope.row.status==3"></span>
            </template>
        </el-table-column>
        <el-table-column prop="createName" label="操作人" align="center">
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" align="center"></el-table-column>
        <el-table-column label="操作" align="center">
            <template #default="scope">
                <el-button type="text" v-if="scope.row.status=='0'" class="editBtn" @click="audit(scope.row)">审核</el-button>
                <el-button type="text" class="editBtn" @click="checkDetail(scope.row)">查看详情</el-button>
            </template>
        </el-table-column>
    </el-table>

    <div class="center page">
        <el-pagination prev-icon="CaretLeft" next-icon="CaretRight"  :page-size="pagination.size" :current-page="pagination.page"  layout="total,prev, pager, next" @current-change="handleCurrentChange" :total="pagination.total">
        </el-pagination>
    </div>
    <el-dialog align-center append-to-body   custom-class="addDiagram border0" width="540px" draggable v-model="auditDialog" title="采购审核">
        <el-form ref="form1" class="form" label-width="100px">
            <el-form-item label="审核意见:">
                <el-input v-model="auditDesc" type="textarea" placeholder="请输入" maxlength="200" show-word-limit>
                </el-input>
            </el-form-item>
        </el-form>
        <template #footer>
            <div class="dialog-footer search_box">
                <div type="primary" class="searchBtn margin10"  size="small" @click="updatePurchase(1)">通过</div>
                <div type="primary" class="searchBtn" size="small" @click="updatePurchase(2)">拒绝</div>
            </div>
        </template>
    </el-dialog>
   <detail :id="id" v-model:showDialog="showDialog"></detail>
</div>
</template>

<script>
import {
    reactive,
    toRefs
} from "vue";
import {
    useRouter
} from "vue-router";
import {
    getCurrentInstance,
    onMounted
} from "vue";
import dayjs from 'dayjs'
import {
    getCookie
} from '@/utils/cookie';
import detail from '../details/purchaseDetail.vue'
export default {
    props: ["tableData"],
    components:{detail},
    setup(proxys, context) {
        const {
            proxy
        } = getCurrentInstance();
        const router = useRouter();
        const state = reactive({
            auditDialog: false,
            pagination: {
                total: 0,
                page: 1,
                size: 10,
            },
            value1: [],
            list: [],
            dosageTab: [{
                    name: "未审核",
                    id: "1",
                },
                {
                    name: "已审核",
                    id: "2",
                },
            ],
            dosageType: "1",
            item: null,
            auditDesc: "",
            id:null,
            showDialog:false,
        });
        onMounted(() => {
            state.value1[0] = dayjs().startOf('day');
            state.value1[1] = dayjs().endOf('day');
            getListByCondition();
        });
        const getListByCondition = () => {

            let data = {
                page: state.pagination.page,
                size: state.pagination.size,
                bt: dayjs(state.value1[0]).format("YYYY-MM-DD HH:mm:ss"),
                et: dayjs(state.value1[1]).format("YYYY-MM-DD HH:mm:ss"),
                projectId: getCookie("gh_projectId")
            };
            proxy.$api.getPurchase(data).then((res) => {
                state.list = res.data;
                state.pagination.total = res.total;
            });
        };
        const changeTab = (id) => {
            state.dosageType = id;
            getListByCondition();
        };
        const checkDetail = (row) => {
            state.showDialog=true;
            state.id=row.id;
        };
        const handleCurrentChange = (page) => {
            state.pagination.page = page;
            getListByCondition();
        };
        const updatePurchase = (status) => {
            proxy.$api.auditPurchase({
                purchaseId: state.item.id,
                status: status,
                desc: state.auditDesc,
                projectId:getCookie("gh_projectId")
            }).then(res => {
                state.auditDialog = false;
                getListByCondition();
            });

        }
        const audit = (row) => {
            state.auditDialog = true;
            state.item = row;
        }

        return {
            ...toRefs(state),
            checkDetail,
            changeTab,
            handleCurrentChange,
            getListByCondition,
            updatePurchase,
            audit
        };
    },
};
</script>

<style scoped>
.margin10{
    margin-right: 10px;
}
</style>
