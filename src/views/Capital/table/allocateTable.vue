<template>
<div>
    <el-form :inline="true" class="search_box form_inline" size="small">
        <el-form-item label="状态">
            <el-select v-model="status" placeholder="请选择">
                <el-option label="待处理" value="0"></el-option>
                <el-option label="调拨中" value="1"></el-option>
                <el-option label="已处理" value="2"></el-option>
            </el-select>
        </el-form-item>
        <el-form-item>
            <el-button size="small" class="searchBtn" type="text" @click="getListByCondition">查询</el-button>
        </el-form-item>
    </el-form>

    <div class="table">
        <el-table :data="list" :height="tableHeight"  fit @select="select" @select-all="select">
            <template #empty>
                <noData />
            </template>
            <el-table-column prop="code" label="编号" align="center">
            </el-table-column>
            <el-table-column prop="nowDeptName" label="领用单位" align="center">
            </el-table-column>
            <el-table-column prop="updateName" label="领用人" align="center">
            </el-table-column>
            <el-table-column prop="updateTime" label="领用日期" align="center" width="180px">
            </el-table-column>
            <el-table-column prop="createTime" label="创建时间" align="center" width="180px">
            </el-table-column>
            <el-table-column prop="status" label="状态" align="center">
                <template #default="scope">
                    <span v-if="scope.row.status==0">待处理</span>
                    <span v-if="scope.row.status==1">调拨中</span>
                    <span v-if="scope.row.status==2">已处理</span>
                </template>
            </el-table-column>
            <el-table-column label="操作" align="center">
                <template #default="scope">
                    <el-button type="text" class="editBtn" @click="checkDetail(scope.row)">查看详情</el-button>
                </template>
            </el-table-column>
        </el-table>
    </div>
    <div class="center page">
        <el-pagination prev-icon="CaretLeft" next-icon="CaretRight"  :page-size="Pagination.size" :current-page="Pagination.page"  layout="total,prev, pager, next" @current-change="handleCurrentChange" :total="Pagination.total">
        </el-pagination>
    </div>
</div>
</template>

<script>
import {
    reactive,
    toRefs
} from 'vue'
import {
    useRouter
} from 'vue-router'
import {
    getCurrentInstance,
    onMounted
} from 'vue'
import {
    Base64
} from 'js-base64'
export default {
    props: ['tableData'],
    setup() {
        const {
            proxy
        } = getCurrentInstance();
        const router = useRouter()
        const state = reactive({
            moreShow: false,
            Pagination: {
                total: 0,
                page: 1,
                size: 10,
            },
            list: [],
            status: null,
            whse: null,
            originalDeptId: null,
        })
        onMounted(() => {
            getListByCondition();
            // findWhse();
        });
        const getListByCondition = () => {
            let param = "";
            if (state.status) {
                param = param + " status = '" + state.status + "'";
            }
            let data = {
                pageNum: state.Pagination.page,
                pageSize: state.Pagination.size,
                condition: Base64.encode(param)
            };
            proxy.$api.getAllocate(data).then((res) => {
                state.list = res.data.list;
                state.Pagination.total = Number(res.data.total);
            });
        };

        const checkDetail = (row) => {
            router.push({
                path: `/capital-allocateDetail/${row.oid}`,
            });
        };
        const operating = () => {
            router.push({
                path: `/capital-addTakeOver`,
            });
        };
        const handleCurrentChange = (page) => {
            state.Pagination.page = page;
            getListByCondition();
        };
        const findWhse = () => {
            proxy.$api.findWhse().then(res => {
                state.whse = res.data;
            });
        }
        return {
            ...toRefs(state),
            checkDetail,
            operating,
            handleCurrentChange,
            getListByCondition,
            findWhse
        }
    }
}
</script>
