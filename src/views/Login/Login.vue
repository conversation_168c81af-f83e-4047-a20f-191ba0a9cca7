<template>
    <div class="login">
        <login-heade>
            <el-form class="loginForm" :model="form" ref="ruleForm" :rules="rules" label-width="100px" v-if="!register">
                <div class="title">
                    <h3>用户登录</h3>
                </div>
                <el-form-item label="用户名：" prop="username">
                    <el-input type="text" v-model="form.username" placeholder="请输入用户名"></el-input>
                </el-form-item>
                <el-form-item label="密   码：" prop="password">
                    <el-input type="password" v-model="form.password" placeholder="请输入密码"></el-input>
                </el-form-item>
                <el-form-item label="邀请码：" prop="code" v-if="form.username.toLowerCase() === 'demo'">
                    <el-input type="text" v-model="form.code" placeholder="请输入邀请码"></el-input>
                </el-form-item>
                <el-form-item label-width="0">
                    <el-button :loading="isLogin" @click="handleSubmit('ruleForm')" type="primary" class="login_btn">
                        开始登录
                    </el-button>
                </el-form-item>

                <el-form-item label-width="0">

                    <el-button type="text" class="register" @click="clickRegister">
                        注册用户
                    </el-button>
                    <el-button type="text" class="forget">
                        忘记密码？
                    </el-button>
                </el-form-item>
            </el-form>
            <!-- 用户注册 -->
            <el-form class="loginForm" :model="userForm" ref="registerForm" :rules="regRules" label-width="100px"
                v-if="register">
                <i class="el-icon-close closeBtn" @click="back"></i>
                <div class="title">
                    <h3>用户注册</h3>
                </div>
                <!-- 用户名 -->
                <el-form-item label="用户名：" prop="name">
                    <el-input type="text" v-model="userForm.name" placeholder="请输入用户名"></el-input>
                </el-form-item>
                <!-- 登录名 -->
                <el-form-item label="登录名：" prop="userName">
                    <el-input type="text" v-model="userForm.userName" placeholder="请输入登录名"></el-input>
                </el-form-item>
                <el-form-item label="手机号：" prop="phone">
                    <el-input v-model.number="userForm.phone" placeholder="请输入手机号"></el-input>
                </el-form-item>
                <el-form-item label="邮  箱：" prop="email">
                    <el-input v-model="userForm.email" placeholder="请输入邮箱"></el-input>
                </el-form-item>
                <!-- 登录button -->
                <el-form-item label-width="0">
                    <el-button :loading="isLogin" @click="handleRegister('registerForm')" type="primary" class="login_btn">
                        注册
                    </el-button>
                </el-form-item>
            </el-form>
        </login-heade>
        <Verify @success="login" @close="close" :captchaType="'blockPuzzle'" :imgSize="{ width: '400px', height: '200px' }"
            ref="verify">
        </Verify>
    </div>
</template>

<script>
import {
    getCurrentInstance,
    reactive,
    toRefs,
    onMounted,
    inject,
    ref
} from 'vue';
import LoginHeade from './LoginHeade.vue';
import Verify from '@/components/verifition/Verify.vue';
import {
    encode
} from 'js-base64';
import {
    getCookie
} from '@/utils/cookie';
import {
    useStore
} from 'vuex';
import {
    useRouter
} from 'vue-router';
import {
    ElMessage
} from 'element-plus';
export default {
    components: {
        LoginHeade,
        Verify
    },
    setup() {
        const {
            proxy
        } = getCurrentInstance();
        const store = useStore();
        const router = useRouter();
        const state = reactive({
            register: false,
            form: {
                username: '',
                password: '',
                code: '',
                params: '',
                autoLogin: true
            },
            userForm: {
                userName: '',
                name: '',
                phone: '',
                email: ''
            },
            regRules: {
                userName: [{
                    required: true,
                    message: '请输入登录名',
                    trigger: 'blur'
                }],
                name: [{
                    required: true,
                    message: '请输入用户名',
                    trigger: 'blur'
                }],
                password: [{
                    required: true,
                    message: '请输入密码',
                    trigger: 'blur'
                }],
                email: [{
                    required: true,
                    message: '请输入邮箱',
                    trigger: 'blur'
                }, {
                    validator: (rule, value, callback) => {
                        const reg = /^[A-Za-z0-9\u4e00-\u9fa5]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/;
                        if (reg.test(value)) {
                            callback();
                        } else {
                            callback(new Error('请输入正确的邮箱'));
                        }
                    },
                    trigger: 'blur'
                }],
                phone: [{
                    required: true,
                    message: '请输入手机号',
                    trigger: 'blur'
                }, {
                    validator: (rule, value, callback) => {
                        const reg = /^1(3\d|4[5-9]|5[0-35-9]|6[2567]|7[0-8]|8\d|9[0-35-9])\d{8}$/;
                        if (reg.test(value)) {
                            callback();
                        } else {
                            callback(new Error("请输入正确的手机号码"));
                        }
                    },
                    trigger: 'blur'
                }],

            },
            isLogin: false,
            rules: {
                username: [{
                    required: true,
                    message: '请输入账号',
                    trigger: 'blur'
                }],
                password: [{
                    required: true,
                    message: '请输入密码',
                    trigger: 'blur'
                }],
                code: [{
                    required: true,
                    message: '请输入邀请码',
                    trigger: 'blur'
                }],

            },
            socket: {}
        });
        state.socket = inject('socket');

        const handleSubmit = (formName) => {
            proxy.$refs[formName].validate((valid) => {
                if (valid) {
                    state.isLogin = true;
                    proxy.$refs.verify.show();
                }
            });
        };
        const clickRegister = () => {
            state.register = true
        }
        // 注册
        const handleRegister = (formName) => {
            proxy.$refs[formName].validate((valid) => {
                if (valid) {
                    proxy.$api.register(state.userForm).then(res => {
                        if (res.success) {
                            state.register = false;
                            ElMessage({
                                message: '注册成功',
                                type: 'success'
                            });
                            router.push({
                                path: '/login'
                            })
                        }
                    })
                } else {
                    return false
                }
            })
        }
        // 登录
        const login = (params) => {
            state.form.params = params;
            store
                .dispatch('user/login', {
                    username: encode(state.form.username),
                    password: encode(state.form.password),
                    code: state.form.code,
                    params
                })
                .then(() => {
                    if (getCookie('gh_projectId') == 0) {

                    } else {
                        router.push({
                            path: '/'
                        })
                    }
                    state.socket.emit('SetProject', getCookie('gh_projectId'));
                    state.socket.emit('SetUserId', getCookie('gh_id'));
                })
                .catch(() => {
                    state.isLogin = false;
                    // ElMessage.error("用户名或者密码错误")
                });
        };
        const back = () => {
            state.register = false
        }
        const close = () => {
            state.isLogin = false;
        }
        return {
            ...toRefs(state),
            handleSubmit,
            login,
            clickRegister,
            handleRegister,
            back,
            close
        };
    }
};
</script>

<style lang="scss" scoped>
.login {
    position: relative;
    width: 100%;
    height: 100%;
    background: url("../../assets/images/login.jpg");
    // background: url("../../assets/images/login1.jpeg");
    background-repeat: no-repeat;
    background-size: cover;

    .loginForm {
        padding: 15px 43px 10px 43px;

        .title {
            margin: 0px auto 40px auto;
            text-align: center;
            font-size: 24px;
            font-family: "PingFangSC-Medium", "PingFang SC";
            font-weight: 500;
            color: #3de9fa;

            h3 {
                font-size: 24px;
                font-weight: 500;
            }
        }

        .auto {
            float: left;
        }

        .forget {
            float: right;
            font-size: 14px;
            font-weight: 400;
            color: #ffbd0f;
        }

        .register {
            color: #c7dfff;
            margin: 0 20px;
        }
    }

    .closeBtn {
        position: absolute;
        right: 20px;
    }
}
</style>
