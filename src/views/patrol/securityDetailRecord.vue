<template>
<div class="wrapper">
    <el-form :inline="true" class="search_box">
        <el-form-item label="时间选择:">
            <el-date-picker v-model="date" type="datetimerange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" @change="getEvent">
            </el-date-picker>
        </el-form-item>

        <el-form-item  label="巡更人员:">
            <el-input placeholder="关键字" v-model="keyword"></el-input>
        </el-form-item>

        <el-form-item>
            <div class="searchBtn cursor" @click="search" type="text">查询</div>
        </el-form-item>
    </el-form>

    <el-table :data="list" height="calc(100% - 80px)" fit>
        <template #empty>
            <no-data />
        </template>
        <el-table-column prop="startTime" label="计划开始时间" align="center" width="200">
        </el-table-column>
        <el-table-column prop="endTime" label="计划结束时间" align="center" width="200">
        </el-table-column>
        <el-table-column prop="times" label="计划次数" align="center">
        </el-table-column>
        <el-table-column prop="planName" label="计划名称" align="center" show-overflow-tooltip>
        </el-table-column>
        <el-table-column prop="logTime" label="巡检时间" align="center" width="200">
        </el-table-column>
        <el-table-column prop="events" label="巡检事件" align="center">
        </el-table-column>
        <el-table-column prop="username" label="巡检人员" align="center">
        </el-table-column>
        <el-table-column prop="pointName" label="巡检点" align="center">
        </el-table-column>
        <el-table-column prop="num" label="巡检次数" align="center">
        </el-table-column>
        <el-table-column prop="delay" label="是否准时" align="center">
            <template #scope>
                <span style="color:green" v-if="props.row.delay==true">准时</span>
                <span style="color:red" v-if="props.row.delay==false">延时</span>
                <span style="color:red" v-if="!props.row.logTime&&dayjs().isAfter(dayjs(props.row.endTime))">漏巡</span>
            </template>
        </el-table-column>
        <el-table-column prop="names" label="值班人员" align="center">
        </el-table-column>
        <el-table-column type="expand" label="巡检照片" width="150">
            <template #scope>
                <el-image class="image" :preview-src-list="JSON.parse(props.row.paths) " style="width: 100px; height: 100px" :src="item" v-for="(item,i) in JSON.parse(props.row.paths) " :fit="fit" :key="i"></el-image>
            </template>
        </el-table-column>
    </el-table>

    <div class="page center">
        <el-pagination prev-icon="CaretLeft" next-icon="CaretRight" :page-size="size" :current-page="page" layout="total,prev, pager, next" @current-change="handleCurrentChange" :total="total">
        </el-pagination>
    </div>
</div>
</template>

<script>
import dayjs from 'dayjs'
import {
    getCurrentInstance,
    onMounted,
    reactive,
    toRefs
} from 'vue'


export default {
    name: 'patroldetailrecord',
    setup() {
        const state = reactive({
            date: [],
            page: 1,
            size: 10,
            total: 0,
            list: [],
            keyword: null,
        })
        state.date.push(dayjs().format('YYYY-MM-DD 00:00:00'));
        state.date.push(dayjs().format('YYYY-MM-DD 23:59:59'));
        const {
            proxy
        } = getCurrentInstance();
        onMounted(() => {
            getPatrolRecord()
        })
        const getPatrolRecord = () => {
            proxy.$api.getSecurityDetailRecord({
                page: state.page,
                size: state.size,
                keyword: state.keyword,
                type: 5,
                bt: typeof state.date[0] == 'string' ? state.date[0] : dayjs(state.date[0]).format('YYYY-MM-DD HH:mm:ss'),
                et: typeof state.date[1] == 'string' ? state.date[1] : dayjs(state.date[1]).format('YYYY-MM-DD HH:mm:ss'),
            }).then(res => {
                state.list = res.data;
                state.total = res.total;
            });

        }
        const search = () => {
            sate.page = 1;
            getPatrolRecord();
        }

        const handleCurrentChange = (page) => {
            state.page = page
            getPatrolRecord();
        }
        return {
            ...toRefs(state),
            handleCurrentChange,
            search
        }
    },
}
</script>

<style lang="scss" scoped>
.wrapper {
    height: 100%;
}
</style>
