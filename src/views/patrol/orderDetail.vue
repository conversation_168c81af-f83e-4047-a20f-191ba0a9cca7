<template>
  <div class="electric-box layout_wrapper">
    <div class="wrapper">
      <panel title="工单详情"> <i class="el-icon-close closeBtn" @click="back"></i></panel>
      <div class="content card-body" style="height: calc(100% - 40px)">
        <el-timeline class="time">
          <el-timeline-item v-for="(item, index) in points" :key="index" :timestamp="item.start_TIME_" placement="top"
            v-show="item.act_NAME_">
            <div class="point-item">
              <h3>{{ item.act_NAME_ }}</h3>
              <h4>处理人:{{ item.name ? item.name : '系统' }}</h4>
            </div>
          </el-timeline-item>
        </el-timeline>
      </div>
      <div class="btn">
        <el-button class="cmdBtn" size="mini" v-for="(item, i) in cmd" :key="i" type="primary"
          @click="process(item.value)">{{ item.name }}</el-button>
      </div>

      <el-dialog align-center append-to-body   v-model="Visible" title="人员选择" custom-class="custom_dialog" width="700px">
        <el-form label-position="right" label-width="90px">
          <el-form-item label="接单人员:">
            <el-select clearable filterable v-model="userId" >
              <el-option v-for="item in users" :key="item.userId" :value="item.userId" :label="item.name"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button type="primary" class="saveBtn" size="small" @click="selectUser">派 单</el-button>
          </div>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import {
  ElMessage
} from 'element-plus'
import {
  getCookie
} from '@/utils/cookie'
import {
  reactive,
  toRefs
} from 'vue'
import {
  getCurrentInstance,
  onMounted
} from 'vue'
import {
  useRouter
} from 'vue-router'

export default {
  setup () {
    const router = useRouter()
    const { proxy } = getCurrentInstance()
    const state = reactive({
      order: {},
      activeNames: '',
      points: [],
      cmd: [],
      Visible: false,
      users: [],
      userId: '',
      order: null,
      send: {
        "cmd": -2,
        "taskId": '',
        "description": "",
        "instanceId": "",
        "operator": getCookie("gh_id"),
        "projectId": getCookie("gh_projectId"),
        "staffType": "",
        "status": "",
        "id": "",
        "userId": getCookie("gh_id")
      }
    })
    onMounted(() => {
      if (sessionStorage.getItem('order')) {
        state.order = JSON.parse(sessionStorage.getItem('order'));
        if (state.order.instanceId) {
          getOrderProcess(state.order.instanceId);
        }
        if (state.order.cmd) {
          state.order.cmd = state.order.cmd.replace(".", ",")
        }
        if (state.order.cmd && state.order.cmd.includes(",")) {
          let split = state.order.cmd.split(',');
          split.forEach(val => {
            state.cmd.push({
              name: val.split('=')[0],
              value: val.split('=')[1],
            });
          })
        } else if (state.order.cmd && state.order.cmd.includes(".")) {
          let split = state.order.cmd.split('.');
          split.forEach(val => {
            state.cmd.push({
              name: val.split('=')[0],
              value: val.split('=')[1],
            });
          })
        } else if (state.order.cmd && state.order.cmd.includes("=")) {
          let split = state.order.cmd.split('=');
          state.cmd.push({
            name: split[0],
            value: split[1],
          });

        }
      }
    })
    const back = () => {
      router.push({
        path: '/patrol-order'
      })
    }
    const getOrderProcess = (id) => {
      proxy.$api.getPatrolProcess({
        instanceId: id,
      }).then(res => {
        state.points = res.data;
      });
    }
    const process = (cmd) => {
      if (cmd == 1) //派单
      {
        getProjectStaffList({
          staffType: state.order.staffType,
          projectId: getCookie("gh_projectId")
        });
        state.Visible = true;

      } else {
        let data = {
          "cmd": cmd,
          "taskId": state.order.taskId,
          "description": "",
          "instanceId": state.order.instanceId,
          "operator": getCookie("gh_id"),
          "projectId": getCookie("gh_projectId"),
          "staffType": state.order.staffType,
          "status": state.order.status,
          "id": state.order.id,
          "userId": getCookie("gh_id")
        }
        processTask(data);
      }
    }
    const getProjectStaffList = (type) => {
      proxy.$api.getProjectStaff({
        staffType: type
      }).then(res => {
        state.users = res.data;
      });
    }
    const processTask = (data) => {
      proxy.$api.processTask(data).then(res => {
        ElMessage({
          type: 'success',
          message: '处理成功'
        });
        back();
      });
    }
    const selectUser = () => {
      if (!state.userId) {
        ElMessage({
          type: 'error',
          message: '请选择接单人员'
        });
        return;
      }
      state.send.userId = state.userId;
      state.send.cmd = 1;
      state.send.taskId = state.order.taskId;
      state.send.instanceId = state.order.instanceId;
      state.send.staffType = state.order.staffType;
      state.send.status = state.order.status;
      state.send.id = state.order.id;
      state.Visible = false;
      processTask(state.send);

    }
    return {
      ...toRefs(state),
      back,
      getOrderProcess,
      process,
      getProjectStaffList,
      processTask,
      selectUser
    }
  }
}
</script>

<style lang="scss" scoped>
.electric-box {
  .wrapper {
    display: flex;
    flex-direction: column;
    height: calc(100% - 40px);

    .closeBtn {
      position: absolute;
      right: 20px;
      top: 10px;
    }

    .content {
      display: flex;
      height: 100%;
      justify-content: center;
      overflow-y: auto;
      margin-top: 20px;

      .point-item {
        color: rgb(171, 186, 204);
      }
    }
  }

  .cmdBtn {
    background: rgba(61, 233, 250, 0.5);
    border: 1px solid #3de9fa;
  }
}

.btn {
  text-align: center;
}
</style>
