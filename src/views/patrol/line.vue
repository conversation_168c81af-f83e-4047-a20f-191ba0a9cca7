<template>
<div class="wrapper">
    <el-form :inline="true" class="search_box form_inline" size="small">
        <el-form-item label="名称">
            <el-input size="small" v-model="keyword" placeholder="请输入名称查询"></el-input>
        </el-form-item>
        <el-form-item label="类型">
            <el-select v-model="type" clearable placeholder="请选择">
                <el-option v-for="item in types" :label="item.tagName" :value="parseInt(item.tagValue)" :key="item.id">
                </el-option>
            </el-select>
        </el-form-item>
        <el-form-item>
            <div size="small" class="searchBtn" type="text" @click="search">查询</div>
        </el-form-item>

        <div class="btn-group">
            <div type="primary" icon="Plus" size="mini" class="searchBtn" @click="addLine(null, 'add')">新增
            </div>
            <div type="danger" icon="Minus" class="delBtn" size="mini" @click="del">删除</div>
        </div>
    </el-form>

    <el-table class="table" :data="list" height="530" fit @select="select" @select-all="select">
        <template #empty>
            <no-data />
        </template>
        <el-table-column type="selection" width="55" align="center">
        </el-table-column>

        <el-table-column prop="name" label="名称" align="center">
        </el-table-column>

        <el-table-column prop="type" label="线路类型" align="center">
            <template #default="scope">
                <span>{{ getTypeName(scope.row.type) }}</span>
            </template>
        </el-table-column>

        <el-table-column prop="createTime" label="创建时间" align="center">
        </el-table-column>
        <el-table-column label="操作" align="center">
            <template #default="scope">
                <el-button type="text" class="editBtn" @click="edit(scope.row, 'edit')">编辑</el-button>
            </template>
        </el-table-column>
    </el-table>

    <div class="center page">
        <el-pagination prev-icon="CaretLeft" next-icon="CaretRight" :page-size="size" :current-page="page" layout="total,prev, pager, next" @current-change="handleCurrentChange" :total="total">
        </el-pagination>
    </div>
    <line-dialog v-if="dialogData.visible" :dialog-data="dialogData" @getLine="getLine"></line-dialog>
</div>
</template>

<script>
import {
    ElMessage,
    ElMessageBox
} from 'element-plus'
import {
    getCookie
} from '@/utils/cookie'
import {
    computed,
    getCurrentInstance,
    onMounted,
    reactive,
    toRefs,
    watch
} from 'vue'
import lineDialog from './components/lineDialog.vue'
import {
    useStore
} from 'vuex'
export default {
    name: 'patrolline',
    components: {
        lineDialog
    },
    setup() {
        const {
            proxy
        } = getCurrentInstance()
        const store = useStore()
        const state = reactive({
            tableHeight: window.innerHeight * 0.60,
            page: 1,
            size: 10,
            total: 0,
            list: [],
            data: null,
            types: [],
            items: [], //表格select
            types: [], // 巡检项数据类型  状态 数值
            date: [],
            type: '',
            keyword: '',
            dialogData: {
                visible: false,
                title: '',
                edit: false,
                line: {
                    name: '',
                    type: 1,
                    projectId: '',
                    points: [],
                    count: 0,
                    staffId: '',
                    phone: ''
                },
                rule: {
                    name: [{
                        required: true,
                        message: '名称不能空',
                        trigger: 'blur'
                    }],
                    type: [{
                        required: true,
                        message: '类型不能为空',
                        trigger: 'change'
                    }],
                    phone: [{
                            required: false,
                            message: '请输入手机号',
                            trigger: 'blur'
                        },
                        {
                            pattern: /^((0\d{2,3}-\d{7,8})|(1[3584]\d{9}))$/,
                            message: '请输入正确的手机号码或者座机号',
                        },
                    ],
                },
            }
        })
        onMounted(() => {
            getDicUtil()
            getLine()
        })
        const projectId = computed(() => {
            return store.state.user.projectId || getCookie('gh_projectId')
        })
        watch(projectId, (val) => {
            if (val) {
                getDicUtil()
                getLine()
            }
        })
        const getDicUtil = () => {
            proxy.$api.getDicUtil({
                dicCode: 'line_type',
                type: state.type,
                projectId: getCookie('gh_projectId'),
            }).then((res) => {
                state.types = res.data
            })
        }
        const getLine = () => {
            proxy.$api.getPatrolLine({
                type: state.type,
                keyword: state.keyword,
                projectId: getCookie('gh_projectId'),
            }).then((res) => {
                state.list = res.data
            })
        }
        const handleCurrentChange = (page) => {
            state.page = page
            getLine()
        }
        const del = () => {
            if (state.items.length == 0) {
                ElMessage.message({
                    type: 'warning',
                    message: '请选择要删除的巡检线路',
                })
                return
            }
            ElMessageBox.confirm('是否确认要删除该数据？', '提示', {
                confirmButtonClass: 'confirmBtn',
                cancelButtonClass: 'cancelBtn',
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            }).then(() => {
                proxy.$api.deletePatrolLine({
                    ids: state.items,
                }).then((res) => {
                    getLine()
                    state.items = []
                })
            })
        }
        const edit = (row, type) => {
            if (type === 'edit') {
                state.dialogData.visible = true
                state.dialogData.title = "编辑"
                state.dialogData.edit = true
                proxy.$api.getPatrolLine({
                    id: row.id,
                    projectId: getCookie('gh_projectId')
                }).then(res => {
                    if (res.data.length > 0) {
                        state.dialogData.line = res.data[0];
                    }
                })
            }
        }
        const addLine = (row, type) => {
            if (type == 'add') {
                state.dialogData.visible = true
                state.dialogData.title = '新增'
                state.dialogData.edit = false
                state.dialogData.line = {

                }
            }
        }
        const select = (selection, row) => {
            state.items = []
            if (selection.length > 0) {
                selection.forEach((item) => {
                    state.items.push(item.id)
                })
            }
        }
        const search = () => {
            state.page = 1
            getLine()
        }
        const getTypeName = (type) => {
            let name = ''
            state.types.forEach((t) => {
                if (t.tagValue == type) {
                    name = t.tagName
                }
            })
            return name
        }
        return {
            ...toRefs(state),
            getLine,
            getTypeName,
            search,
            select,
            addLine,
            edit,
            handleCurrentChange,
            del,
            getDicUtil,
            projectId
        }
    },
}
</script>

<style lang="scss" scoped>

</style>
