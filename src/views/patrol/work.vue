<template>
  <div class="order_container layout_wrapper">
    <!-- <div class="btn-group">
      <el-button
        type="primary"
        icon="Plus"
        size="mini"
        class="addBtn"
        @click="addWork"
        >新增</el-button
      >
    </div> -->
    <work-dialog v-if="dialogData.visible" :dialog-data="dialogData" />

    <el-tabs v-model="active" @tab-click="handleClick">
      <el-tab-pane label="待派发" name="first">
        <work-table :status="status"></work-table>
      </el-tab-pane>
      <el-tab-pane label="待接单" name="second">
        <work-table :status="status"></work-table>
      </el-tab-pane>
      <el-tab-pane label="处理中" name="third">
        <work-table :status="status"></work-table>
      </el-tab-pane>
      <el-tab-pane label="待审核" name="fourth">
        <work-table :status="status"></work-table>
      </el-tab-pane>
      <el-tab-pane label="已完成" name="five">
        <work-table :status="status"></work-table>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import workTable from '@/components/order/workTable'
import {
  reactive,
  toRefs
} from 'vue'
import workDialog from './components/workDialog.vue'
export default {
  components: {
    workTable,
    workDialog
  },
  setup () {
    const state = reactive({
      active: 'first',
      list: [],
      status: [-1],
      dialogData: {
        visible: false,
        title: '',
        work: {
          deviceType: ''
        },
        rule: {
          deviceType: [{
            required: true,
            message: '设备类型不能空',
            trigger: 'change'
          }],
          name: [{
            required: true,
            message: '名称不能空',
            trigger: 'blur'
          }],
          area: [{
            required: true,
            message: '区域不能空',
            trigger: 'change'
          }],
          description: [{
            required: true,
            message: '描述不能空',
            trigger: 'blur'
          }]
        }
      }
    })
    const handleClick = (tab, event) => {
      if (tab.paneName == 'first') {
        state.status = [-1]
      } else if (tab.paneName == 'second') {
        state.status = [1]
      } else if (tab.paneName == 'third') {
        state.status = [2, 8, 5, 9]
      } else if (tab.paneName == 'fourth') {
        state.status = [3, 4]
      } else if (tab.paneName == 'five') {
        state.status = [6]
      }
    }
    const addWork = () => {
      state.dialogData.visible = true
      state.dialogData.title = '新增'
    }
    return {
      ...toRefs(state),
      handleClick,
      addWork
    }
  },
}
</script>

<style scoped>
</style>
