<template>
<div class="wrapper">
    <el-form :inline="true" class="search_box form_inline" size="small">
        <el-form-item label="名称">
            <el-input v-model="keyword" placeholder="支持 名称 查询"></el-input>
        </el-form-item>
        <el-form-item>
            <div class="searchBtn" @click="search">查询</div>
        </el-form-item>

        <div class="btn-group">
            <div class="searchBtn" @click="addPatrolEvent">新增</div>
            <div class="delBtn" @click="del">删除</div>
        </div>

    </el-form>

    <el-table class="table" :data="list" style="width: 100%" fit @select="select" @select-all="select">
        <el-table-column type="selection" width="55" align="center">
        </el-table-column>
        <el-table-column prop="name" label="名称" align="center">
        </el-table-column>
        <el-table-column prop="description" label="描述" align="center">
        </el-table-column>
        <el-table-column label="操作" align="center">
            <template #default="scope">
                <el-tag type="warning" class="cursor" @click="edit(scope.$index, scope.row)">编辑</el-tag>
            </template>
        </el-table-column>
    </el-table>
    <div class="center page">
        <el-pagination prev-icon="CaretLeft" next-icon="CaretRight" :page-size="size" :current-page="page" layout="total,prev, pager, next" @current-change="handleCurrentChange" :total="total">
        </el-pagination>
    </div>

    <el-dialog align-center append-to-body   draggable v-model="dialogEventVisible" title="巡检事件" custom-class="addDiagram">
        <el-form ref="form" :model="event" :rules="rule" label-width="100px">
            <el-form-item label="名称" prop="name">
                <el-input placeholder="请输入名称" v-model="event.name"></el-input>
            </el-form-item>
            <el-form-item label="描述" prop="description">
                <el-input type="textarea" v-model="event.description" :row="6" placeholder="请输入描述" size="small"> </el-input>
            </el-form-item>
        </el-form>

        <template #footer>
            <div class="dialog-footer search_box">
                <div class="searchBtn" @click="saveEvent">确 定</div>
            </div>
        </template>

    </el-dialog>
</div>
</template>

<script>
import {
    getCookie
} from '@/utils/cookie'

import {
    getPatrolEvent,
    editPatrolEvent,
    addPatrolEvent,
    deletePatrolEvent
} from '@/api/patrol.ts'

export default {
    name: 'patrolevent',
    data() {
        return {
            page: 1,
            size: 10,
            total: 2,
            list: [],
            dialogEventVisible: false,
            items: [], //表格select
            keyword: '',
            event: {
                name: '',
                description: ''
            },
            rule: {
                name: [{
                    required: true,
                    message: '名称不能空',
                    trigger: 'change'
                }],
            }
        }
    },
    created() {
        this.getPatrolEvent();
    },
    methods: {
        getPatrolEvent() {
            getPatrolEvent({
                keyword: this.keyword,
                projectId: getCookie('gh_projectId'),
                page: this.page,
                size: this.size
            }).then(res => {
                this.list = res.data;
                this.total = res.total;
            });
        },
        handleCurrentChange(page) {
            this.page = page;
            this.getPatrolEvent();
        },

        del() {
            if (this.items.length == 0) {
                this.$message({
                    type: 'warning',
                    message: '请选择要删除的巡检事件'
                });
                return;
            }
            this.$confirm('是否确认要删除该数据？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                deletePatrolEvent({
                    ids: this.items
                }).then(res => {
                    this.getPatrolEvent();
                    this.items = [];
                });
            });

        },

        edit(index, row) {
            this.event = JSON.parse(JSON.stringify(row));
            this.dialogEventVisible = true;
            if (this.$refs.form) {
                this.$refs.form.resetFields();
            }
        },
        addPatrolEvent() {
            this.dialogEventVisible = true;
            this.event.name = "";
            this.event.description = "";
            if (this.$refs.form) {
                this.$refs.form.resetFields();
            }
        },

        select(selection, row) {
            this.items = [];
            if (selection.length > 0) {
                selection.forEach(item => {
                    this.items.push(item.id);
                });
            }
        },

        search() {
            this.page = 1;
            this.getPatrolEvent();
        },
        saveEvent() {
            this.$refs.form.validate(validate => {
                if (validate) {
                    this.event.projectId = getCookie('gh_projectId');
                    if (this.event.id > 0) {
                        editPatrolEvent(this.event).then(res => {
                            this.getPatrolEvent();
                            this.dialogEventVisible = false;
                        });
                    } else {
                        addPatrolEvent(this.event).then(res => {
                            this.getPatrolEvent();
                            this.dialogEventVisible = false;
                        });
                    }
                }

            });

        }

    }
}
</script>

<style lang="scss" scoped>

</style>
