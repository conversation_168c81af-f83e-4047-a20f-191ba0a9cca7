<template>
  <el-dialog align-center append-to-body   custom-class="custom_dialog" v-model="dialogData.visible" width="940px" :title="dialogData.title">
    <div class="wrapper">
      <div class="content">
        <el-timeline class="time">
          <el-timeline-item v-for="(item, index) in points" :key="index" :timestamp="item.start_TIME_" placement="top"
            v-show="item.act_NAME_">
            <div class="point-item">
              <div>{{ item.act_NAME_ }}</div>
              <div>处理人:{{ item.name ? item.name : '系统' }}</div>
            </div>
          </el-timeline-item>
        </el-timeline>
      </div>
      <div class="btn">
        <el-button size="small" v-for="(item, i) in cmd" :key="i" type="primary" @click="process(item.value)">
          {{ item.name }}</el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import {
  getCookie
} from '@/utils/cookie'
import {
  toRefs,
  reactive,
  onMounted,
  getCurrentInstance,
  computed
} from 'vue'
import { useStore } from 'vuex'
export default {
  props: {
    dialogData: {
      visible: false,
      title: '',
      edit: false,
    }
  },
  setup (props) {
    const { proxy } = getCurrentInstance()
    const store = useStore()
    const state = reactive({
      points: [],
      cmd: [],
      order: null,
    })
    const projectId = computed(() => {
      return store.state.user.projectId || getCookie('gh_projectId')
    })
    onMounted(() => {
      if (props.dialogData.order.instanceId) {
        getOrderProcess(props.dialogData.order.instanceId);
      }
      if (props.dialogData.order.cmd) {
        props.dialogData.order.cmd = props.dialogData.order.cmd.replace(".", ",")
      }
      if (props.dialogData.order.cmd && props.dialogData.order.cmd.includes(",")) {
        let split = props.dialogData.order.cmd.split(',');
        split.forEach(val => {
          state.cmd.push({
            name: val.split('=')[0],
            value: val.split('=')[1],
          });
        })
      } else if (props.dialogData.order.cmd && props.dialogData.order.cmd.includes(".")) {
        let split = props.dialogData.order.cmd.split('.');
        split.forEach(val => {
          state.cmd.push({
            name: val.split('=')[0],
            value: val.split('=')[1],
          });
        })
      } else if (props.dialogData.order.cmd.includes("=")) {
        let split = props.dialogData.order.cmd.split('=');
        state.cmd.push({
          name: split[0],
          value: split[1],
        });

      }
    })
    const getOrderProcess = (id) => {
      proxy.$api.getPatrolProcess({
        instanceId: id,
      }).then(res => {
        state.points = res.data;
      });
    }
    const process = (cmd) => {
      if (cmd == 1) //派单
      {
        proxy.$api.getProjectStaff({
          staffType: props.dialogData.order.staffType,
          projectId: getCookie("gh_projectId")
        });
        props.dialogData.visible = true;

      } else {
        let data = {
          "cmd": cmd,
          "taskId": props.dialogData.order.taskId,
          "description": "",
          "instanceId": props.dialogData.order.instanceId,
          "operator": getCookie("gh_id"),
          "projectId": getCookie("gh_projectId"),
          "staffType": props.dialogData.order.staffType,
          "status": props.dialogData.order.status,
          "id": props.dialogData.order.id,
          "userId": getCookie("gh_id")
        }
        processTasks(data);
      }
    }
    const processTasks = (data) => {
      proxy.$api.processTask(data).then(res => {
        ElMessage({
          type: 'success',
          message: '处理成功'
        });
        props.dialogData.visible = false
      });
    }
    return {
      ...toRefs(state),
      getOrderProcess,
      processTasks,
      process,
      projectId
    }
  }
}
</script>

<style lang="scss" scoped>
.wrapper {
  display: flex;
  flex-direction: column;

  .content {
    display: flex;
    justify-content: center;
    overflow-y: auto;
    margin-top: 20px;

    .point-item {
      font-size: 16px;
      color: #fff;
      line-height: 28px;
    }
  }
}
</style>
