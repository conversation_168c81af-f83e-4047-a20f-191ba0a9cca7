<template>
<el-dialog align-center append-to-body   custom-class="addDiagram" v-model="dialogData.visible" width="940px" :title="dialogData.title">
    <panel title="巡检线路"></panel>
    <el-form class="form" ref="form" :model="dialogData.line" :rules="dialogData.rule">
        <el-row type="flex" :gutter="30">
            <el-col :span="12">
                <el-form-item label="名称:" prop="name">
                    <el-input v-model="dialogData.line.name" placeholder="请输入名称"></el-input>
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="联系人:">
                    <el-select v-model="dialogData.line.staffId" class="w100">
                        <el-option v-for="item in staffs" :label="item.name" :value="item.id" :key="item.id"></el-option>
                    </el-select>
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="类型:" prop="type">
                    <el-select v-model="dialogData.line.type" class="w100">
                        <el-option v-for="item in types" :label="item.tagName" :value="parseInt(item.tagValue)" :key="item.id">
                        </el-option>
                    </el-select>
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="手机号:" prop="phone">
                    <el-input v-model="dialogData.line.phone" placeholder="请输入内容"></el-input>
                </el-form-item>
            </el-col>
        </el-row>
    </el-form>
    <panel title="巡检点关联"></panel>
    <div class="btn-group search_box">
        <div type="primary" icon="Plus" size="mini" class="searchBtn" @click="addPoint">新增</div>
    </div>
    <div class="patrol-line">
        <el-timeline class="time">
            <el-timeline-item v-for="(item, index) in dialogData.line.points" :key="item.id" :timestamp="item.createTime" placement="top">
                <div class="point-item">
                    <div class="name">{{ item.name }}</div>
                    <el-button type="danger" class="deleteBtn" size="mini" @click="remove(index, item)">移除</el-button>
                </div>
            </el-timeline-item>
        </el-timeline>
    </div>
    <template #footer>
        <div class="dialog-footer search_box">
            <div type="primary" class="searchBtn" size="small" @click="saveLine('form')">保存</div>
        </div>
    </template>
    <el-dialog align-center append-to-body   v-model="dialogDeviceVisible" title="巡检点" custom-class="addDiagram" width="940px">
        <div class="table">
            <el-table :data="list" size="small" fit @select="selectPoint" @select-all="selectPoint">
                <template #empty>
                    <no-data />
                </template>
                <el-table-column type="selection" width="55" align="center">
                </el-table-column>
                <el-table-column prop="code" label="编号" align="center">
                </el-table-column>
                <el-table-column prop="name" label="名称" align="center">
                </el-table-column>
                <el-table-column prop="areaName" label="所属区域" align="center">
                </el-table-column>
                <el-table-column prop="address" label="所属地址" align="center">
                </el-table-column>
                <el-table-column prop="createTime" label="创建时间" align="center">
                </el-table-column>
            </el-table>
        </div>
        <div class="center page">
            <el-pagination prev-icon="CaretLeft" next-icon="CaretRight" :page-size="size" :current-page="page" layout="total,prev, pager, next" @current-change="handleCurrentChange" :total="total">
        </el-pagination>
        </div>
        <template #footer>
            <div class="dialog-footer search_box">
                <div type="primary" class="searchBtn" size="mini" @click="importPoint">确 定</div>
            </div>
        </template>
    </el-dialog>
</el-dialog>
</template>

<script>
import {
    ElMessage
} from 'element-plus'
import {
    getCookie
} from '@/utils/cookie'
import {
    reactive,
    toRefs
} from 'vue'
import {
    computed,
    getCurrentInstance,
    onMounted,
    watch
} from 'vue'
import {
    useStore
} from 'vuex'
export default {
    name: 'pointEdit',
    props: {
        dialogData: {
            visible: false,
            title: '',
            edit: false,
            line: {
                name: '',
                type: 1,
                projectId: '',
                points: [],
                count: 0,
                staffId: '',
                phone: ''
            }
        }
    },
    setup(props) {
        const {
            proxy
        } = getCurrentInstance()
        const store = useStore()
        const state = reactive({
            page: 1,
            size: 10,
            total: 0,
            types: [],
            list: [],
            dialogDeviceVisible: false,
            staffs: [],
            points: []
        })

        onMounted(() => {
            getStaff();
            getDicUtil()
        })
        const projectId = computed(() => {
            return store.state.user.projectId || getCookie('gh_projectId')
        })
        watch(projectId, (val) => {
            if (val) {
                getStaff();
                getDicUtil()
            }
        })
        const getDicUtil = () => {
            proxy.$api.getDicUtil({
                dicCode: 'line_type',
                projectId: getCookie('gh_projectId')
            }).then(res => {
                state.types = res.data
            })
        }

        const getStaff = () => {
            proxy.$api.getProjectStaff({
                projectId: getCookie('gh_projectId')
            }).then(res => {
                state.staffs = res.data;
            });
        }
        const getPoint = () => {
            proxy.$api.getPatrolPoint({
                projectId: getCookie('gh_projectId')
            }).then(res => {
                if (res.data.length > 0) {
                    state.list = res.data;
                }
            });
        }

        const addPoint = () => {
            state.dialogDeviceVisible = true;
            getPoint();
        }

        const importPoint = () => {
            // props.dialogData.line.points = []
            state.points.forEach(n => {
                let exist = false;
                if (props.dialogData.line.points && props.dialogData.line.points.length > 0)
                    for (let i = 0; i < props.dialogData.line.points.length; i++) {
                        if (props.dialogData.line.points[i].id == n.id) {
                            exist = true;
                            break;
                        }
                    }
                if (!exist) {
                    props.dialogData.line.points.push(n)
                }
            });
            state.dialogDeviceVisible = false;
        }

        const saveLine = (formName) => {
            proxy.$refs[formName].validate(validate => {
                if (validate) {
                    props.dialogData.line.projectId = getCookie('gh_projectId');
                    props.dialogData.line.count = props.dialogData.line.points.length;
                    if (props.dialogData.edit) {
                        proxy.$api.editPatrolLine(props.dialogData.line).then(res => {
                            if (res.success) {
                                props.dialogData.visible = false
                                proxy.$emit('getLine')
                            }
                        });

                    } else {
                        proxy.$api.addLine(props.dialogData.line).then(res => {
                            if (res.success) {
                                props.dialogData.visible = false
                                proxy.$emit('getLine')
                                ElMessage({
                                    type: 'success',
                                    message: res.msg
                                })
                            }
                        });
                    }
                }
            });
        }

        const remove = (index, row) => {
            props.dialogData.line.points.splice(index, 1);
        }
        const selectPoint = (selection, row) => {
            state.points = [];
            if (selection.length > 0) {
                selection.forEach(item => {
                    state.points.push(item);
                });
            }
        }
        const handleCurrentChange = (page) => {
            state.page = page;
            getPoint();
        }
        return {
            ...toRefs(state),
            getStaff,
            getPoint,
            addPoint,
            importPoint,
            saveLine,
            remove,
            selectPoint,
            handleCurrentChange,
            getDicUtil,
            projectId
        }
    }
}
</script>

<style lang="scss" scoped>
.btn-group {
    margin-top: 20px;
}

.form {
    margin: 20px 0;
}

.patrol-line {
    .point-item {
        display: flex;

        .deleteBtn:hover {
            background-color: rgba(180, 6, 26, 0.5);
            border: 1px solid #b4061a;
        }

        .deleteBtn:focus {
            background-color: rgba(180, 6, 26, 0.5);
            border: 1px solid #b4061a;
        }

        .deleteBtn {
            background: rgba(180, 6, 26, 0.5);
            border-radius: 2px;
            border: 1px solid #b4061a;
        }

        .name {
            width: 150px;
            color: #fff;
            font-size: 16px;
        }
    }
}
</style>
