<template>
<el-dialog align-center append-to-body   custom-class="addDiagram" draggable v-model="dialogData.visible" width="940px" :title="dialogData.title">
    <panel title="巡检点"></panel>
    <el-form class="form" ref="form" :model="dialogData.point" :rules="dialogData.rule">
        <el-row type="flex" :gutter="30">
            <el-col :span="12">
                <el-form-item label="名称:" prop="name">
                    <el-input v-model="dialogData.point.name" placeholder="请输入名称"></el-input>
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="区域:">
                    <el-cascader popper-class="cascader" v-model="dialogData.point.areaId" style="width: 100%" :options="areas" clearable :props="props1" placeholder="请选择"></el-cascader>
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="类型:" prop="type">
                    <el-select v-model="dialogData.point.type" class="w100">
                        <el-option v-for="item in types" :label="item.tagName" :value="parseInt(item.tagValue)" :key="item.id">
                        </el-option>
                    </el-select>
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="地址:">
                    <el-input v-model="dialogData.point.address" :row="6" placeholder="请输入内容">
                    </el-input>
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="离线巡检:" class="lxxj">
                    <el-radio-group v-model="dialogData.point.off">
                        <el-radio :label="true">开启</el-radio>
                        <el-radio :label="false">关闭</el-radio>
                    </el-radio-group>
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="RFID:">
                    <el-input v-model="dialogData.point.rfid" :row="6" placeholder="请输入内容">
                    </el-input>
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="拍照数量:" prop="picCount">
                    <el-input v-model.number="dialogData.point.picCount" placeholder="请输入拍照数量">
                    </el-input>
                </el-form-item>
            </el-col>
        </el-row>
    </el-form>
    <panel title="设备关联"></panel>
    <div class="btn-group search_box">
        <div type="primary" icon="Plus" class="searchBtn"  @click="addDevice">新增</div>
    </div>
    <div class="table">
        <el-table size="small" :data="dialogData.point.devices"  fit>
            <template #empty>
                <no-data />
            </template>
            <el-table-column prop="name" label="设备名称" align="center">
            </el-table-column>
            <el-table-column prop="deviceTypeName" label="设备类型" align="center">
            </el-table-column>
            <el-table-column prop="areaName" label="所属区域" align="center">
            </el-table-column>
            <el-table-column label="操作" align="center">
                <template #default="scope">
                    <el-button type="text" size="mini" @click="remove(scope.$index, scope.row)" class="del">移除</el-button>
                </template>
            </el-table-column>
        </el-table>
    </div>
    <template #footer>
        <div class="dialog-footer search_box">
            <div  class="searchBtn" size="mini" @click="savePoint('form')">保存</div>
        </div>
    </template>
    <el-dialog align-center append-to-body   v-model="dialogDeviceVisible" title="设备选择"  width="700px">
        <el-scrollbar class="tree" style="height: 250px">
            <!-- <el-input v-model="filter" size="small"></el-input> -->
            <el-tree :filter-node-method="filterNode" ref="authTreeDevice" :load="loadNode" lazy node-key="id" check-strictly :props="props" show-checkbox />
        </el-scrollbar>
        <template #footer>
            <div class="dialog-footer search_box">
                <div  class="searchBtn" @click="saveDevice">确 定</div>
            </div>
        </template>
    </el-dialog>
</el-dialog>
</template>

<script>
import {
    getCookie
} from '@/utils/cookie'
import {
    reactive,
    toRefs,
    ref
} from 'vue'
import {
    computed,
    getCurrentInstance,
    onMounted,
    watch
} from 'vue'
import {
    ElMessage
} from 'element-plus'
import {
    useStore
} from 'vuex'
export default {
    emits: ['getPoint'],
    props: {
        dialogData: {
            title: "",
            visible: false,
            loading: false,
            point: {
                name: '',
                type: 1,
                projectId: '',
                areaId: 0,
                code: 1,
                address: '',
                devices: [],
                off: false,
                count: 0,
                rfid: "",
                picCount: null,
            },
            rule: {
                name: [{
                    required: true,
                    message: '名称不能空',
                    trigger: 'change'
                }],
                type: [{
                    required: true,
                    message: '名称不能空',
                    trigger: 'change'
                }],
                picCount: [{
                    required: true,
                    message: '拍照数量不能空',
                    trigger: 'change'
                }, {
                    type: 'number',
                    message: '请输入正确的数字'
                }]
            },
        }
    },
    setup(props) {
        const {
            proxy
        } = getCurrentInstance()
        const store = useStore()
        const authTreeDevice =ref(null);
        const state = reactive({
            types: [],
            dialogDeviceVisible: false,
            props: {
                label: "name",
                children: "children"
            },
            props1: {
                label: 'name',
                value: 'id',
                checkStrictly: true
            },
            filter: '',
            areas: []
        })
        onMounted(() => {
            getDicUtilList()
            getArea();
        })
        watch(projectId, (val) => {
            if (val) {
                getDicUtilList()
                getArea();
            }
        })
        watch(state.filter, (val) => {
            authTreeDevice.value.filter(val)
        })
        const projectId = computed(() => {
            return store.state.user.projectId || getCookie('gh_projectId')
        })
        const getDicUtilList = () => {
            proxy.$api.getDicUtil({
                dicCode: 'point_type',
                projectId: getCookie('gh_projectId')
            }).then(res => {
                state.types = res.data
            })
        }

        const addDevice = () => {
            state.dialogDeviceVisible = true;
        }
        const loadNode = (node, resolve) => {
            if (node.level === 0 && getCookie('gh_projectId') > 0) {
                state.node = node;
                resolve = resolve;
                proxy.$api.getDeviceTypeTree({
                    projectId: getCookie('gh_projectId')
                }).then(res => {
                    let data = [];
                    res.data.forEach(d => {
                        data.push({
                            id: d.id,
                            name: d.name,
                            disabled: true
                        });
                    });
                    return resolve(data);
                })

            } else if (node.level == 1) {
                proxy.$api.getDevices({
                    projectId: getCookie('gh_projectId'),
                    deviceType: node.data.id
                }).then(res => {
                    let data = [];
                    res.data.forEach(d => {
                        data.push({
                            id: d.id,
                            name: d.name,
                            isLeaf: true,
                            code: d.code,
                            deviceType: d.deviceTypeName,
                            areaName: d.areaName
                        });
                    });
                    return resolve(data);
                });
            }
            return resolve([]);
        }
        const filterNode = (value, data) => {
            if (!value) return true;
            return data.name.indexOf(value) !== -1;
        }
        const saveDevice = () => {
            let nodes = authTreeDevice.value.getCheckedNodes();
            nodes.forEach(n => {
                let exist = false;
                for (let i = 0; i < props.dialogData.point.devices.length; i++) {
                    if (props.dialogData.point.devices[i].id == n.id) {
                        exist = true;
                        break;
                    }
                }
                if (!exist) {
                    props.dialogData.point.devices.push({
                        id: n.id,
                        name: n.name,
                        deviceTypeName: n.deviceTypeName,
                        areaName: n.areaName
                    })
                }
            });
            state.dialogDeviceVisible = false;

        }
        const getArea = () => {
            proxy.$api.getProjectArea({
                projectId: getCookie('gh_projectId')
            }).then(res => {
                state.areas = getTreeData(res.data);
            });
        }
        const getTreeData = (data) => {
            for (var i = 0; i < data.length; i++) {
                if (data[i].children.length < 1) {
                    data[i].children = undefined;
                } else {
                    getTreeData(data[i].children);
                }
            }
            return data;
        }
        const savePoint = (formName) => {
            proxy.$refs[formName].validate(valid => {
                if (valid) {
                    props.dialogData.point.projectId = getCookie('gh_projectId');
                    props.dialogData.point.count = props.dialogData.point.devices.length;
                    if (props.dialogData.point.areaId.length) {
                        props.dialogData.point.areaId = props.dialogData.point.areaId[props.dialogData.point.areaId.length - 1]
                    }
                    if (props.dialogData.edit) {
                        proxy.$api.editPatrolPoint(props.dialogData.point).then(res => {
                            if (res.success) {
                                props.dialogData.visible = false
                                proxy.$emit('getPoint')
                            }
                        });
                    } else {
                        proxy.$api.addPoint(props.dialogData.point).then(res => {
                            if (res.success) {
                                props.dialogData.visible = false
                                proxy.$emit('getPoint')
                                ElMessage({
                                    type: 'success',
                                    message: res.msg
                                })
                            }
                        });
                    }
                }
            });

        }

        const remove = (index, row) => {
            props.dialogData.point.devices.splice(index, 1);
        }
        return {
            ...toRefs(state),
            getDicUtilList,
            authTreeDevice,
            remove,
            savePoint,
            getArea,
            saveDevice,
            filterNode,
            loadNode,
            addDevice,
            projectId
        }
    }
}
</script>

<style lang="scss" scoped>
.form,
.btn-group {
    margin-top: 10px;
}


</style>
