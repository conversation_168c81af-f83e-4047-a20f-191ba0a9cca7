<template>
<div class="wrapper">
    <el-form :inline="true" class="search_box form_inline" size="small">
        <div>
            <el-form-item label="名称">
                <el-input v-model="keyword" placeholder="请输入名称查询"></el-input>
            </el-form-item>
            <el-form-item label="类型">
                <el-select v-model="type" clearable placeholder="请选择">
                    <el-option v-for="item in types" :label="item.tagName" :value="parseInt(item.tagValue)" :key="item.id">
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item>
                <div  class="searchBtn" type="text" @click="search">查询</div>
            </el-form-item>
        </div>
        <div class="btn-group">
            <div   class="searchBtn" @click="addPoint(null, 'add')">新增
            </div>
            <div  class="delBtn"  @click="del">删除</div>
        </div>
    </el-form>

    <el-table class="table" :data="list" height="530" fit @select="select" @select-all="select">
        <template #empty>
            <no-data />
        </template>
        <el-table-column type="selection" width="55" align="center">
        </el-table-column>
        <el-table-column prop="code" label="编号" align="center">
        </el-table-column>
        <el-table-column prop="name" label="名称" align="center">
        </el-table-column>
        <el-table-column prop="areaName" label="所属区域" align="center">
        </el-table-column>
        <el-table-column prop="address" label="所属地址" align="center">
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" align="center">
        </el-table-column>
        <el-table-column label="操作" align="center">
            <template #default="scope">
                <el-button type="text" class="editBtn" @click="edit(scope.row, 'edit')">编辑</el-button>
                <el-button type="text" class="editBtn" @click="showQR(scope.row)">二维码</el-button>
            </template>
        </el-table-column>
    </el-table>

    <div class="page center">
        <el-pagination prev-icon="CaretLeft" next-icon="CaretRight" :page-size="size" :current-page="page" layout="total,prev, pager, next" @current-change="handleCurrentChange" :total="total">
        </el-pagination>
    </div>
</div>
<el-dialog align-center append-to-body   v-model="dialogQrCodeVisible" :title="title" center width="450px">
    <div class="qrcode">
        <img id="qrImg" style="width:100%" />
    </div>
</el-dialog>
<!-- 添加编辑巡检点弹窗 -->
<point-dialog v-if="dialogData.visible" :dialog-data="dialogData" @getPoint="getPoint"></point-dialog>
</template>

<script>
import {
    ElMessage,
    ElMessageBox
} from 'element-plus'
import {
    getCookie
} from '@/utils/cookie'
import QRCode from 'qrcode'
import {
    onMounted,
    reactive,
    toRefs,
    ref,
    getCurrentInstance,
    computed,
    watch
} from 'vue'
import pointDialog from './components/pointDialog.vue'
import {
    useStore
} from 'vuex'
export default {
    name: 'patrolpoint',
    components: {
        pointDialog
    },
    props: ['nav'],
    setup() {
        const title = ref(null)
        const {
            proxy
        } = getCurrentInstance()
        const store = useStore()
        const state = reactive({
            page: 1,
            size: 10,
            total: 0,
            list: [],
            props: {
                label: 'name',
            },
            data: null,
            items: [], //表格select
            types: [], // 巡检项数据类型  状态 数值
            type: '',
            keyword: '',
            dialogQrCodeVisible: false,
            dialogData: {
                visible: false,
                title: '',
                edit: false,
                point: {
                    name: '',
                    type: 1,
                    projectId: '',
                    areaId: 0,
                    code: 1,
                    address: '',
                    devices: [],
                    off: false,
                    count: 0,
                    rfid: "",
                    picCount: null,
                },
                rule: {
                    name: [{
                        required: true,
                        message: '名称不能空',
                        trigger: 'change'
                    }],
                    type: [{
                        required: true,
                        message: '名称不能空',
                        trigger: 'change'
                    }],
                    picCount: [{
                        required: true,
                        message: '拍照数量不能空',
                        trigger: 'change'
                    }, {
                        type: 'number',
                        message: '请输入正确的数字'
                    }]
                },
            }
        })
        onMounted(() => {
            getDicUtil()
            getPoint()
        })
        watch(projectId, (val) => {
            if (val) {
                getDicUtil()
                getPoint()
            }
        })
        const projectId = computed(() => {
            return store.state.user.projectId || getCookie('gh_projectId')
        })
        const getDicUtil = () => {
            proxy.$api.getDicUtil({
                dicCode: 'point_type',
                type: state.type,
                projectId: getCookie('gh_projectId'),
            }).then((res) => {
                state.types = res.data
            })
        }
        const getPoint = () => {
            proxy.$api.getPatrolPoint({
                type: state.type,
                keyword: state.keyword,
                projectId: getCookie('gh_projectId'),
            }).then((res) => {
                state.list = res.data
                state.total = res.total
            })
        }
        const handleCurrentChange = (page) => {
            state.page = page
            getPoint()
        }
        const del = () => {
            if (state.items.length == 0) {
                ElMessage({
                    type: 'warning',
                    message: '请选择要删除的巡检点',
                })
                return
            }
            ElMessageBox.confirm('是否确认要删除该数据？', '提示', {
                confirmButtonClass: 'confirmBtn',
                cancelButtonClass: 'cancelBtn',
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            }).then(() => {
                proxy.$api.deletePatrolPoint({
                    ids: state.items,
                }).then((res) => {
                    getPoint()
                    state.items = []
                })
            })
        }
        const edit = (row, type) => {
            if (type === 'edit') {
                state.dialogData.visible = true
                state.dialogData.title = "编辑"
                state.dialogData.edit = true
                proxy.$api.getPatrolPoint({
                    id: row.id,
                    projectId: getCookie('gh_projectId')
                }).then(res => {
                    if (res.data.length > 0) {
                        state.dialogData.point = res.data[0];
                    }
                })
            }
        }
        const showQR = (row) => {
            state.dialogQrCodeVisible = true
            title.value = row.name
            QRCode.toDataURL(row.code, {
                    width: 400,
                    margin: 0,
                })
                .then((url) => {
                    document.getElementById('qrImg').src = url
                })
                .catch((err) => {
                    console.error(err)
                })
        }
        const addPoint = (row, type) => {
            if (type === 'add') {
                state.dialogData.visible = true
                state.dialogData.title = "新增"
                state.dialogData.edit = false
                state.dialogData.point = {
                    name: '',
                    type: 1,
                    projectId: '',
                    areaId: 0,
                    code: 1,
                    address: '',
                    devices: [],
                    off: false,
                    count: 0,
                    rfid: "",
                    picCount: null,
                }
            }
        }

        const select = (selection, row) => {
            state.items = []
            if (selection.length > 0) {
                selection.forEach((item) => {
                    state.items.push(item.id)
                })
            }
        }
        const saveContent = () => {
            state.content.projectId = getCookie('gh_projectId')
            state.content.deviceType = state.selectId[0].id
            if (state.content.id > 0) {
                proxy.$api.editPatrolContent(state.content).then((res) => {
                    getPoint()
                    state.dialogContentVisible = false
                })
            } else {
                proxy.$api.addContent(state.content).then((res) => {
                    getPoint()
                    state.dialogContentVisible = false
                })
            }
        }
        const search = () => {
            state.page = 1
            getPoint()
        }
        return {
            ...toRefs(state),
            getPoint,
            title,
            search,
            handleCurrentChange,
            del,
            edit,
            showQR,
            addPoint,
            select,
            saveContent,
            projectId,
            getDicUtil
        }
    },
}
</script>

<style lang="scss" scoped>
.qrcode {
    background: #091822;
}



</style>
