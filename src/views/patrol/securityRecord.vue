<template>
<div class="layout_wrapper">
    <el-form :inline="true" class="search_box form_inline" size="small">
        <el-form-item size="small" label="时间">
            <el-date-picker v-model="date" type="datetimerange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
            </el-date-picker>
        </el-form-item>
        <el-form-item size="small" label="巡更人员">
            <el-select size="small" placeholder="请选择" clearable v-model="userId">
                <el-option v-for="item in users" :key="item.id" :label="item.name" :value="item.id"></el-option>
            </el-select>
        </el-form-item>
        <el-form-item>
            <el-button @click="search" size="small" class="searchBtn" type="text">查询</el-button>
        </el-form-item>
    </el-form>
    <div class="table">
        <el-table height="calc(100% - 100px)" :data="list" style="width: 100%" fit>
            <el-table-column prop="logTime" label="巡检时间" align="center">
            </el-table-column>
            <el-table-column prop="staffName" label="巡检人员" align="center">
            </el-table-column>
            <el-table-column prop="pointName" label="巡检点" align="center">
            </el-table-column>
            <el-table-column prop="description" label="描述" align="center">
            </el-table-column>
            <el-table-column prop="events" label="巡检事件" align="center">
            </el-table-column>
            <el-table-column type="expand" label="维保照片" width="200">
                <template #scope>
                    <el-image class="image" :preview-src-list="JSON.parse(props.row.paths) " style="width: 100px; height: 100px" :src="item" v-for="(item,i) in JSON.parse(props.row.paths) " :fit="fit" :key="i"></el-image>
                </template>
            </el-table-column>
        </el-table>
    </div>

    <div class="page center">
        <el-pagination prev-icon="CaretLeft" next-icon="CaretRight" :page-size="size" :current-page="page" layout="total,prev, pager, next" @current-change="handleCurrentChange" :total="total">
        </el-pagination>
    </div>
</div>
</template>

<script>
import {
    getSecurityRecord
} from '@/api/patrol'

import {
    getUser
} from '@/api/user'

import dayjs from 'dayjs'

import {
    getCookie
} from '@/utils/cookie'

export default {
    name:'patrolrecord',
    data() {
        return {
            date: [],
            page: 1,
            size: 10,
            total: 2,
            status: '',
            list: [],
            users: [],
            userId: ''
        }
    },
    created() {
        this.date.push(dayjs().format('YYYY-MM-DD 00:00:00'));
        this.date.push(dayjs().format('YYYY-MM-DD 23:59:59'));
        this.getUser();
        this.getPatrolRecord();
    },
    methods: {
        search() {
            this.page = 1;
            this.getPatrolRecord();
        },
        getPatrolRecord() {
            getSecurityRecord({
                page: this.page,
                size: this.size,
                // userId: this.userId,
                bt: typeof this.date[0] == 'string' ? this.date[0] : dayjs(this.date[0]).format('YYYY-MM-DD HH:mm:ss'),
                et: typeof this.date[1] == 'string' ? this.date[1] : dayjs(this.date[1]).format('YYYY-MM-DD HH:mm:ss'),
            }).then(res => {
                this.list = res.data;
                this.total = res.total;
            });

        },
        handleCurrentChange(page) {
            this.page = page;
            this.getPatrolRecord();
        },
        getUser() {
            getUser({
                projectId: [getCookie('gh_projectId')],
                status: 1,
            }).then(res => {
                this.users = res.data;
            });
        },
       

    }
}
</script>
