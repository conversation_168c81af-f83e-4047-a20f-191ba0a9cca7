<template>
<div class="z100">
    <div class="left">
        <div class="header flex-start">
            <img src="../../assets/images/common/head.png">
            <div> 巡检点列表</div>
        </div>
        <div class="input">
            <el-input v-model="keyword" @change="search" :prefix-icon="Search" placeholder="按巡检点名称搜索"></el-input>
        </div>
        <div class="device">
            <el-scrollbar>
                <div class="list space-between" v-for="item in list" :key="item.id">
                    <div class="center cursor">
                        <div>
                            <span class="iconfont icondingwei2"></span>
                        </div>
                        <div class="name">{{item.name}}</div>
                    </div>
                    <div class="center state">
                        <!-- <div v-for="(p,j) in item.state" :key="j">11</div> -->
                    </div>
                    <div class="position cursor">
                        <img src="../../assets/images/common/position.png" />
                    </div>
                </div>
            </el-scrollbar>

        </div>

        <div class="page center">
            <el-pagination prev-icon="CaretLeft" next-icon="CaretRight" :page-size="size" :current-page="page" layout="total,prev, pager, next" @current-change="handleCurrentChange" :total="total">
            </el-pagination>
        </div>

    </div>

    <!-- 中间查询记录 -->
    <pop :show="activeMenus.popName?true:false" :title="activeMenus.name||''">
        <Transition name="fade" mode="out-in">
        <component :is="activeMenus.popName"></component>
        </Transition>
    </pop>

    <div class="right">
        <div class="item" style="flex:1">
            <sub-title1 title='运行统计' />
            <div class="item-body order">
                <div>
                    <div class="total center">设备总数</div>
                    <div class="order-left">
                        <div class="center">
                            <div>3741</div>
                        </div>
                    </div>
                </div>
                <div class="order-right">
                    <div class="order-text">
                        <div class="dot" style="background:#1AAC1A"></div><span class="text">巡检点数:</span><span class="num">20</span>
                    </div>
                    <div class="order-text">
                        <div class="dot" style="background:#C47F13"></div><span class="text">巡检计划:</span><span class="num">10</span>
                    </div>
                    <div class="order-text">
                        <div class="dot" style="background:#C33838"></div><span class="text">巡检线路:</span><span class="num">12</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="item" style="flex:2">
            <sub-title1 title='(本月)巡检统计' />
            <div class="item-body  energy_category">
                <energy-sub style="flex:1"></energy-sub>
                <div class="energy_category-item center">
                    <div>
                        <div>
                            <div class="center">
                                <div style="background: linear-gradient(0deg, #6CF8E7, #4CEDD9);" class="dot"></div>
                                <div class="text">已巡检</div>
                            </div>

                            <div class="num num1">{{inspectionTask.completed}}</div>
                        </div>
                        <div>
                            <div class="center">
                                <div style="background: linear-gradient(0deg, #FABB32, #E9A819);" class="dot"></div>
                                <div class="text">待巡检</div>
                            </div>

                            <div class="num num2">{{inspectionTask.unCompleted}}</div>
                        </div>
                        <div>
                            <div class="center">
                                <div style="background: linear-gradient(0deg, #71B0FF, #358FFF);" class="dot"></div>
                                <div class="text">超时</div>
                            </div>

                            <div class="num num3">{{inspectionTask.delay}}</div>
                        </div>
                        <div>
                            <div class="center">
                                <div style="background: linear-gradient(-26deg, #F36447, #EF5B34);" class="dot"></div>
                                <div class="text">漏巡</div>
                            </div>

                            <div class="num num4">{{inspectionTask.omit}}</div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
        <div class="item" style="flex:2">
            <sub-title1 title='实时巡检' />
            <div class="item-body event">
                <el-scrollbar>
                    <div class="list " v-for="i in records" :key="i.id">
                        <div class="name">
                            <span style="color:#0CCA0F" class="iconfont icondingwei2"></span>
                            <div>{{i.pointName}}</div>
                        </div>
                        <div>
                            {{i.username||'未巡检'}}
                        </div>
                        <div class="time">
                            {{i.logTime||'0000-00-00 00:00:00'}}
                        </div>

                        <div class="bar"></div>
                    </div>
                </el-scrollbar>
            </div>
        </div>
    </div>
</div>
</template>

<script>
import {
    defineComponent,
    getCurrentInstance,
    reactive,
    toRefs,
    computed,
    onMounted,
} from 'vue';

import {
    getCookie
} from "@/utils/cookie";
import {
    useStore
} from 'vuex';
import Alarm from '@/components/echarts/weekEventEchart.vue'
import pop from '@/components/pop'
import energySub from '@/components/home/<USER>';
import {
    getParkRecord
} from '@/api/park';
export default defineComponent({
    name: "patrol",
    components: {
        Alarm,
        pop,
        energySub
    },

    setup() {
        const {
            proxy
        } = getCurrentInstance()
        const store = useStore();
        const state = reactive({
            keyword: "",
            size: 10,
            page: 1,
            total: 100,
            list: [],
            count: {
                yesterday: 0,
                month: 0,
                lastMonth: 0,
                year: 0
            },
            inspectionTask: {
                completed: 0,
                unCompleted: 0,
                delay: 0,
                omit: 0
            },
            records: []

        })
        //当前激活的一级菜单
        const activeMenus = computed(() => {
            let menu = getCookie("funMenus");
            return store.state.menu.funMenus ?
                store.state.menu.funMenus :
                menu ?
                JSON.parse(menu) :
                "";
        });
        onMounted(() => {
            getPatrolPoint();
            getSecurityStatics();
            getPatrolRecord()
        });
        const getPatrolPoint = async () => {
            let {
                data,
                total
            } = await proxy.$api.getPatrolPoint({
                keyword: state.keyword,
                size: state.size,
                page: state.page,
                projectId: getCookie("gh_projectId")
            })
            state.total = total;
            state.list = data;

        };

        const handleCurrentChange = (page) => {
            state.page = page;
            getPatrolPoint();
        }

        const search = () => {
            state.page = 1;
            getPatrolPoint();
        }

        const getSecurityStatics = async () => {
            let {
                data
            } = await proxy.$api.getSecurityStatics({
                projectId: getCookie("gh_projectId")
            })
            state.inspectionTask = data;
        }

        const getPatrolRecord = () => {
            proxy.$api.getSecurityDetailRecord({
                // page: state.page,
                // size: state.size,
                keyword: state.keyword,
                type: 5,
                // bt: typeof state.date[0] == 'string' ? state.date[0] : dayjs(state.date[0]).format('YYYY-MM-DD HH:mm:ss'),
                // et: typeof state.date[1] == 'string' ? state.date[1] : dayjs(state.date[1]).format('YYYY-MM-DD HH:mm:ss'),
            }).then(res => {
                state.records = res.data;

            });

        }

        return {
            ...toRefs(state),
            handleCurrentChange,
            search,
            activeMenus
        }
    }
});
</script>

<style lang="scss" scoped>
.energy_category {

    display: flex;
    height: calc(100% - 40px);
    align-items: center;

    .dot {
        width: 6px;
        height: 6px;
    }

    &-item {
        flex: 1;
        flex-direction: column;

        &>div>div {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
            border-bottom: 1px solid rgba(255, 255, 255, 0.3);
            margin-bottom: 14px;
        }
    }

    .text {

        font-size: 14px;
        font-family: "Alibaba-PuHuiTi";
        font-weight: normal;
        color: #FFFFFF;
        margin-left: 5px;
    }

    .num1 {
        background: linear-gradient(0deg, #6CF8E7 0%, #4CEDD9 100%);
    }

    .num2 {
        background: linear-gradient(0deg, #FABB32 0%, #E9A819 100%);
    }

    .num3 {
        background: linear-gradient(0deg, #71B0FF 0%, #358FFF 100%);
    }

    .num4 {
        background: linear-gradient(-26deg, #F36447 0%, #EF5B34 100%);
    }

    .num {

        font-size: 20px;
        font-family: "BEBAS";
        font-weight: 400;
        color: #FFFFFF;

        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        display: flex;
        align-self: flex-end;
        margin-left: 30px;
    }

}
</style>
