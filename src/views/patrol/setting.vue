<template>
<div class="setting">
    <div class="btns">
        <div class="center cursor" :class="[component=='point'?'active':'']" @click="change('point')">巡检点</div>
        <div class="center cursor" :class="[component=='event'?'active':'']" @click="change('event')">巡检事件</div>
        <div class="center cursor" :class="[component=='line'?'active':'']" @click="change('line')">巡检线路</div>
        <div class="center cursor" :class="[component=='plan'?'active':'']" @click="change('plan')">巡检计划</div>
    </div>
    <div>
        <component :is="component"></component>
    </div>
</div>
</template>

<script>
import {
    defineComponent,
    reactive,
    toRefs
} from "vue";
import point from './point.vue'
import event from './event.vue'
import line from './line.vue'
import plan from './eventplan.vue'
export default defineComponent({
    name: "patrolsetting",
    components: {
        point,
        event,
        line,
        plan
    },
    setup() {
        const state = reactive({
            component: 'point',
        })

        const change = (name) => {
            state.component = name;
        }

        return {
            ...toRefs(state),
            change,
        }
    }
})
</script>

<style lang="scss" scoped>
.setting {
    display: flex;

    .btns {

        font-size: 16px;
        font-family: "Alibaba-PuHuiTi";
        font-weight: bold;
        color: #84B2FF;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        flex-direction: column;
        padding: 25px;

        &>div {
            margin-bottom: 15px;
            width: 136px;
            height: 34px;
            background: rgba(27, 109, 194, 0.2);
            border: 1px solid #58B3F5;
            box-shadow: 0px 0px 8px 0px #58B3F5 inset;
        }

    }

    &>div:nth-child(2) {
        flex: 1
    }

    .active {
        background: #1B6DC2 !important;
    }
}
</style>
