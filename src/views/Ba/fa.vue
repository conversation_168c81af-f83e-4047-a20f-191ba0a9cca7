<template>
<div class="right">
    <div class="item" style="flex:1">
        <sub-title1 title='设备统计' />
        <div class="item-body order">
            <div>
                <div class="total center">设备总数</div>
                <div class="order-left">
                    <div class="center">
                        <div>379</div>
                    </div>
                </div>
            </div>
            <div class="order-right">
                <div class="order-text">
                    <div class="dot" style="background:#1AAC1A"></div><span class="text">烟感数量:</span><span class="num">303</span>
                </div>
                <div class="order-text">
                    <div class="dot" style="background:#C47F13"></div><span class="text">温感数量:</span><span class="num">012</span>
                </div>
                <div class="order-text">
                    <div class="dot" style="background:#C33838"></div><span class="text">手报数量:</span><span class="num">017</span>
                </div>
                <div class="order-text">
                    <div class="dot" style="background:#dce417"></div><span class="text">消报数量:</span><span class="num">047</span>
                </div>
            </div>
        </div>
    </div>
    <div class="item" style="flex:2">
        <sub-title1 title='报警分析' />
        <div class="item-body kong">
            <Alarm :xAxisData="xAxisData" :echartData="echartData" />
        </div>
    </div>
    <div class="item" style="flex:2">
        <sub-title1 title='事件列表' />
        <div class="item-body event">
            <el-scrollbar v-if="alarms.length>0">
                <div class="list space-between" v-for="(item,i) in alarms" :key="i">
                    <div class="name">
                        <img v-if="i%2==0" src="../../assets/images/common/on.png" />
                        <img v-else src="../../assets/images/common/off.png" />
                        <div>{{ item.deviceName }}</div>
                    </div>

                    <div class="time">
                        {{ item.createTime }}
                    </div>

                    <div class="bar"></div>
                </div>
            </el-scrollbar>
            <noData v-else />
        </div>
    </div>
</div>
</template>

<script>
import {
    defineComponent,
    getCurrentInstance,
    reactive,
    toRefs,
    onMounted,

} from 'vue';

import {
    getCookie
} from "@/utils/cookie";
import {
    useStore
} from 'vuex';

import pop from '@/components/pop'
import Alarm from '@/components/echarts/weekEventEchart.vue'
export default defineComponent({
    name: "fa",
    components: {
        pop,
        Alarm
    },

    setup() {
        const {
            proxy
        } = getCurrentInstance()
        const store = useStore();
        const state = reactive({
            echartData: [10, 11, 12, 5, 8, 6, 2],
            xAxisData: ['周一', '周二', '周三', '周四', '周五', '周六', '周天'],
            alarms: [],
        })

        onMounted(() => {
            getAlarmWeek()
            getHistroyRecordPage();

        });

        const getHistroyRecordPage = () => {
            proxy.$api.getHistroyRecord({
                projectId: getCookie("gh_projectId"),
                page: 1,
                size: 10,
                alarmSource: 4

            }).then((res) => {
                state.alarms = res.data;
            })
        }
        const getAlarmWeek = () => {
            proxy.$api.getAlarmWeek({
                projectId: getCookie("gh_projectId"),
                alarmSource: 4

            }).then((res) => {
                // console.log(res.data.map(el => el.count))
                state.echartData = res.data.map(el => el.count);
            })
        }

        return {
            ...toRefs(state),
        }
    }
});
</script>

<style lang="scss" scoped>
.detail {
    display: flex;
    flex-direction: column;
    height: 100%;

    &>div:nth-child(2) {
        height: calc(100% - 40px);
        overflow-y: auto;
        overflow-x: hidden;
    }

    .tabs {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .tab {
            background: url("../../assets/images/common/tab.png") no-repeat;
            width: 104px;
            height: 40px;

            .name {
                font-size: 14px;
                font-family: "Alibaba-PuHuiTi";
                font-weight: 400;
                color: #03FFFF;
                background: linear-gradient(0deg, #DFFFFF 0%, #ABF6FF 100%);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
            }

        }
    }

    .active {
        background: url("../../assets/images/common/tab_active.png") no-repeat !important;
    }

}
</style>
