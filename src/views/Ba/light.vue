<template>
    <div class="right">
        <div class="item" style="flex:1">
            <sub-title1 title='运行统计' />
            <div class="item-body order">
                <div>
                    <div class="total center">回路总数</div>
                    <div class="order-left">
                        <div class="center">
                            <div>{{ all }}</div>
                        </div>
                    </div>
                </div>
                <div class="order-right">
                    <div class="order-text">
                        <div class="dot" style="background:#1AAC1A"></div><span class="text">开启数量:</span><span
                            class="num">{{ on }}</span>
                    </div>
                    <div class="order-text">
                        <div class="dot" style="background:#C47F13"></div><span class="text">关闭数量:</span><span
                            class="num">{{ off }}</span>
                    </div>
                    <!-- <div class="order-text">
                        <div class="dot" style="background:#C33838"></div><span class="text">故障数量:</span><span class="num">100</span>
                    </div> -->
                </div>
            </div>
        </div>
        <div class="item" style="flex:2">
            <sub-title1 title='开关群控' />
            <div class="item-body kong">
                <el-scrollbar v-if="manual.length > 0">
                    <div class="list space-between" v-for="(item, index) in manual" :key="index">
                        <div class="name">
                            <img src="../../assets/images/common/d3.png" />
                            <div>{{ item.name }}</div>
                        </div>
                        <div class="dot"></div>
                        <div class="btn space-between">
                            <div class="center cursor" @click="writeValue(item.startGroup, item.status)">
                                <div class="center">开</div>
                            </div>

                            <div class="center cursor" @click="writeValue(item.endGroup, item.status)">
                                <div class="center">关</div>
                            </div>
                        </div>
                    </div>
                </el-scrollbar>
                <noData v-else />
            </div>
        </div>
        <div class="item" style="flex:2">
            <sub-title1 title='事件列表' />
            <div class="item-body event">
                <el-scrollbar v-if="manualLog.length > 0">
                    <div class="list space-between" v-for="(item, index) in manualLog" :key="index">
                        <div class="name">
                            <img v-if="i % 2 == 0" src="../../assets/images/common/on.png" />
                            <img v-else src="../../assets/images/common/off.png" />
                            <div>{{ item.deviceName || item.deviceName1 }}</div>
                        </div>
                        <div>
                            {{ item.standardName || item.standardName1 }}
                        </div>
                        <div>
                            {{ item.newValue }}
                        </div>
                        <div class="time">
                            {{ item.logTime }}
                        </div>

                        <div class="bar"></div>
                    </div>
                </el-scrollbar>
                <noData v-else />
            </div>
        </div>
    </div>
</template>
    
<script>
import {
    defineComponent,
    getCurrentInstance,
    reactive,
    toRefs,
    computed,
    onMounted,
    inject, onUnmounted, nextTick
} from 'vue';
import {
    getCookie,
} from "@/utils/cookie";
import {
    useStore
} from 'vuex';
import { ElMessage } from 'element-plus'
import socket from "@/utils/socket";
export default defineComponent({
    name: "light",
    components: {},
    setup() {
        const {
            proxy
        } = getCurrentInstance()
        const state = reactive({
            keyword: "",
            size: 10,
            page: 1,
            total: 100,
            all: 0,
            on: 0,
            off: 0,
            manual: [],
            sockets: null,
            manualLog: [],
            interval: null,
        })
        state.sockets = inject("socket");
        onMounted(() => {
            getDeviceTypeStatus()
            getRunConfigPage("manual")
            getRunManualPage(3, "manualLog");
            startInterval();
        })
        const activeMenus = computed(() => {
            let menu = getCookie("funMenus");
            return store.state.menu.funMenus ?
                store.state.menu.funMenus :
                menu ?
                    JSON.parse(menu) :
                    "";
        });
        const store = useStore();
        const getDeviceTypeStatus = () => {
            let all = 0, on = 0, off = 0;
            proxy.$api.getDeviceTypeStatus({
                projectId: getCookie("gh_projectId"),
                typeCode: ['Light8','light4']
            }).then(res => {
                res.data.forEach(d => {
                    //新风系统
                  
                        all++;
                        if (d.dataVal == '1') {
                            on++;

                        } else if (d.dataVal == '0') {
                            off++;
                        }
                    
                })
               

                state.all = all;
                state.on = on;
                state.off = off;

            });
        }
        const getRunConfigPage = (type) => {
            proxy.$api.getRunConfig({
                projectId: getCookie("gh_projectId"),
                tag: 2,
                type,
                menuId: activeMenus.value.id
            }).then((res) => {
                if (type == "strategy") {
                    state.strategies = res.data.strategies;
                    // getRunModelPage(2);
                }
                if (type == "manual") {
                    state.manual = res.data.manuals;

                }
            });
        };
        const getRunManualPage = (type, log) => {
            // state.manualLog = [];
            proxy.$api.getDeviceLog({
                projectId: getCookie("gh_projectId"),
                menuId: activeMenus.value.id,
                page: 1,
                size: 10,
            }).then((res) => {

                state.manualLog = [...res.data];
            });
        };
        const writeValue = (group, status) => {
            // if (!status) {
            //     return;
            // }
            if (group && group.length > 0) {
                group.forEach((g, i) => {
                    g.groupVars.forEach((v, j) => {
                        socket.ManualWrite(
                            state.sockets,
                            v.varValue,
                            v.variable,
                            v.name,
                            v.standardName,
                            getCookie("gh_id"),
                            g.menuId,
                            "manual",
                            state.sockets.id,
                            "manual" + i + j,
                            getCookie("gh_projectId"),
                            v.deviceId
                        );
                    });
                });
                ElMessage.success('操作成功')
            }
        };

        const stopInterval = () => {
            if (state.interval) {
                clearInterval(state.interval);
                state.interval = null;
            }
        };
        const startInterval = () => {
            stopInterval();
            state.interval = setInterval(() => {
                getRunManualPage(3, "manualLog");
                getDeviceTypeStatus();
            }, 2000);
        };
        onUnmounted(() => {
            stopInterval();
        })

        return {
            ...toRefs(state),
            writeValue,
            activeMenus,
        }
    }
});
</script>
    