<template>
    <div class="right">
        <div class="item" style="flex:1">
            <sub-title1 title='设备统计' />
            <div class="item-body order">
                <div>
                    <div class="total center">设备总数</div>
                    <div class="order-left">
                        <div class="center">
                            <div>{{ all }}</div>
                        </div>
                    </div>
                </div>
                <div class="order-right">
                    <div class="order-text">
                        <div class="dot" style="background:#1AAC1A"></div><span class="text">考勤数量:</span><span
                            class="num">{{ on }}</span>
                    </div>
                    <div class="order-text">
                        <div class="dot" style="background:#C47F13"></div><span class="text">启用数量:</span><span
                            class="num">{{ off }}</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="item" style="flex:2">
            <sub-title1 title='门禁分析' />
            <div class="item-body kong">
                <Alarm :value="value" :value1="value1" />
            </div>
        </div>
        <div class="item" style="flex:2">
            <sub-title1 title='事件列表' />
            <div class="item-body event">
                <el-scrollbar v-if="events.length > 0">
                    <div class="list space-between" v-for="(item, index) in events" :key="index">
                        <div class="name">
                            <div>
                                <!-- <span v-if="item.inAndOutType==1" style="color:#0CCA0F" class="iconfont iconmenjin"></span> -->
                                <span style="color:#F84141" class="iconfont iconmenjin"></span>
                            </div>
                            <div>{{ item.devName }}</div>
                        </div>

                        <div>
                            <span>{{ item.eventDemo }}</span>

                        </div>

                        <div class="time">
                            {{ item.eventDay }}
                        </div>

                        <div class="bar"></div>
                    </div>
                </el-scrollbar>
                <noData v-else></noData>
            </div>
        </div>
    </div>
</template>

    
    
<script>
import {
    defineComponent,
    getCurrentInstance,
    reactive,
    toRefs,
    onMounted,
    inject,

} from 'vue';
import dayjs from 'dayjs'
import Alarm from './echart/door.vue'
export default defineComponent({
    name: "door",
    components: {
        Alarm
    },

    setup() {
        const {
            proxy
        } = getCurrentInstance()

        const state = reactive({
            value: [],
            value1: [],
            on: 0,
            off: 0,
            all: 0,
            dayjs,
            events: []
        })
        state.sockets = inject("socket");
        onMounted(() => {
            getDoor();
            getDaShiRecord();
            getDaShiEvent()

        });
        const getDoor = () => {
            proxy.$api.getHKDoor({
                pageIndex: 1,
                pageSize: 3000,
                timestamp: dayjs().unix(),
            }).then((res) => {
                if (res.data) {
                    let data = JSON.parse(res.data).data;
                    data.datalist.forEach(d => {
                        if (d.devIsUsed == 1) {
                            state.on++;
                        } else if (d.isKqUse == 1) {
                            state.off++;
                        }
                    })
                    state.all = data.totalCount;
                }

            })
        }

        const getDaShiEvent = async () => {
            let {
                data
            } = await proxy.$api.getDaShiEvent({
                pageIndex: 1,
                pageSize: 10,
                startTime: dayjs().startOf('month').format('YYYY-MM-DD HH:mm:ss'),
                endTime: dayjs().endOf('month').format('YYYY-MM-DD HH:mm:ss'),
                timestamp: dayjs().unix()
            });
            state.events = JSON.parse(data).data.datalist;
        }




        const getDaShiRecord = async () => {
            let {
                data
            } = await proxy.$api.getDaShiRecord({
                pageIndex: 1,
                pageSize: 9000,
                startTime: dayjs().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
                endTime: dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss'),
                timestamp: dayjs().unix()
            });
            let list=JSON.parse(data).data.datalist;
            let value = new Array(24).fill(0)
            let value1 = new Array(24).fill(0)
            list.forEach(d => {
                if (d.inoutFlag == 1) {
                    let num = value[dayjs(d.cardDay).hour()]
                    value[dayjs(d.cardDay).hour()] = num + 1
                } else if (d.inoutFlag == 2) {
                    let num = value1[dayjs(d.cardDay).hour()]
                    value1[dayjs(d.cardDay).hour()] = num + 1
                }

            })
            state.value = value;
            state.value1 = value1;

        }

        return {
            ...toRefs(state),

        }
    }
});
</script>
    
    
<style lang="scss" scoped>
.detail {
    display: flex;
    flex-direction: column;
    height: 100%;

    &>div:nth-child(2) {
        height: calc(100% - 40px);
        overflow-y: auto;
        overflow-x: hidden;
    }

    .tabs {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .tab {
            background: url("../../assets/images/common/tab.png") no-repeat;
            width: 104px;
            height: 40px;

            .name {
                font-size: 14px;
                font-family: "Alibaba-PuHuiTi";
                font-weight: 400;
                color: #03FFFF;
                background: linear-gradient(0deg, #DFFFFF 0%, #ABF6FF 100%);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
            }

        }
    }

    .active {
        background: url("../../assets/images/common/tab_active.png") no-repeat !important;
    }

}
</style>
