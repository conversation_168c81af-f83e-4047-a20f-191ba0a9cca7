<template>
    <div class="z100">
        <div class="left">
            <div class="header flex-start">
                <img src="../../assets/images/common/head.png">
                <div> 设备列表</div>
            </div>
            <div class="input">
                <el-input v-model="keyword" @change="searchDevice" prefix-icon="Search" placeholder="按设备名称搜索">
                </el-input>
            </div>
            <div class="device">
                <el-scrollbar v-if="list.length > 0">
                    <div class="list space-between" v-for="item in list" :key="item.id">
                        <div class="center cursor" @click="showDetail(item)">
                            <span class="iconfont " v-if="item.icon" :class="item.icon"></span>
                            <img v-else src="../../assets/images/common/feng.png" />
                            <div class="name" >{{ item.name }}</div>
                        </div>
                        <div class="center state">
                            <div :style="{ color: getColor(realData[p.id], p.config) }" v-for="(p, j) in item.state"
                                :key="j">
                                {{ getName(realData[p.id], p.config) }}</div>
                        </div>
                        <div class="position cursor" @click="zoomToPosition(item)">
                            <img src="../../assets/images/common/position.png" />
                        </div>
                    </div>
                </el-scrollbar>
                <noData v-else></noData>
            </div>

            <div class="page center">
                <el-pagination prev-icon="CaretLeft" next-icon="CaretRight" :page-size="size" :current-page="page"
                    layout="total,prev, pager, next" @current-change="handleCurrentChange" :total="total">
                </el-pagination>
            </div>

        </div>

        <diagram v-model:show="showDevice" :name="deviceDetail.name">
            <div class="detail">
                <div class="tabs">
                    <div v-for="item in tabs" :class="item.component == tabComponent ? 'active' : ''"
                        class="center tab cursor" :key="item.component" @click="changeTab(item)">
                        <span class="name">{{ item.name }}</span>
                    </div>
                </div>

                <component :is="tabComponent" :deviceId="deviceDetail.id"></component>
            </div>
        </diagram>

        <!-- 中间查询记录 -->
        <pop :show="activeMenus.popName ? true : false" :title="activeMenus.name || ''">
            <Transition name="fade" mode="out-in">
                <component :is="activeMenus.popName"></component>
            </Transition>
        </pop>

        <!-- 右侧列表 -->
        <Transition name="fade" mode="out-in" appear>
            <component :is="activeMenus.secondComponent"></component>
        </Transition>

    </div>
</template>

<script>
import {
    defineComponent,
    getCurrentInstance,
    reactive,
    toRefs,
    computed,
    onMounted,
    inject,
    watch,
    nextTick,
    onUnmounted
} from 'vue';
import device from '@/components/device/device.vue'
import alarm from '@/components/device/alarm.vue'
import history from '@/components/device/history.vue'
import real from '@/components/device/real.vue'
import health from '@/components/device/health.vue'
import repair from '@/components/device/repair.vue'
import run from '@/components/device/run.vue'
import work from '@/components/device/work.vue'
import cmd from '@/components/device/cmd.vue'
import {
    getCookie
} from "@/utils/cookie";
import {
    useStore
} from 'vuex';
import calc from '@/utils/eval';
import socket from "@/utils/socket";
import diagram from '@/components/diagram'
import pop from '@/components/pop'
import {
    Search
} from '@element-plus/icons-vue'
import {
    ElMessage
} from "element-plus";
export default defineComponent({
    name: "common",
    components: {

        diagram,
        device,
        health,
        run,
        work,
        alarm,
        repair,
        real,
        history,
        cmd,
        pop,
        Search

    },
    sockets: {
        live(res) {
            this.subscribeData(res);
        },
        onVarsChangedCallback(res) {
            this.subscribeData(res);
        },
    },

    setup() {
        const {
            proxy
        } = getCurrentInstance()
        const store = useStore();
        const state = reactive({
            keyword: "",
            size: 10,
            page: 1,
            total: 100,
            sockets: null,
            showDevice: false,
            list: [], //设备列表
            realData: {}, //订阅返回的实时数据
            mode: 0, //0---AR  3 vr  4---2D   1---BIM   2---GIS,
            tabComponent: "device",
            tabs: [{
                name: '设备信息',
                component: 'device'
            },
            {
                name: '实时数据',
                component: 'real'
            },
            {
                name: '历史工况',
                component: 'history'
            },
            {
                name: '运行记录',
                component: 'run'
            },
            {
                name: '报警记录',
                component: 'alarm'
            },
            {
                name: '维保记录',
                component: 'work'
            },
            {
                name: '维修记录',
                component: 'repair'
            },
            {
                name: '健康度',
                component: 'health'
            },
            {
                name: '设备面板',
                component: 'cmd'
            },
            ],
            deviceDetail: {
                name: null,
                id: null,
            },
            popName: 'AcsRecord'
        })
        state.sockets = inject("socket");
        onMounted(() => {
            let mode = getCookie("mode");
            if (mode) {
                state.mode = mode;
            }

            if (state.mode == 4 || state.mode == 1 || state.mode == 0) {
                getDeviceList();
            }

        });
        onUnmounted(() => {
            socket.unsubscribe(state.sockets, "ba", "real");
        });
        //当前激活的一级菜单
        const activeMenus = computed(() => {
            let menu = getCookie("funMenus");
            return store.state.menu.funMenus ?
                store.state.menu.funMenus :
                menu ?
                    JSON.parse(menu) :
                    "";
        });
        //当前激活的楼层
        const areaId = computed(() => {
            return store.state.area.area;
        });
        const emitter = inject('mitt');
        emitter.off("changeMode");
        emitter.on("changeMode", (data) => {
            if (state.list.length == 0) {
                getDeviceList();
            }
        });

        watch(areaId, (val) => {
            getDeviceList();
        });

        watch(activeMenus, (val) => {

            if (!val.popName) { //中间记录不需要加载设备
                getDeviceList();
            }

        });
        //解析设备指标
        const deviceStd = (data) => {
            let ws = [];
            state.list = [];
            data.forEach((d, i) => {
                if (d.deviceStandards) {
                    let data = {};
                    data.name = d.name;
                    data.icon = d.icon;
                    data.id = d.id;
                    data.code = d.code;
                    data.state = [];
                    //设备指标          
                    d.deviceStandards.forEach((s, j) => {
                        //当前指标的参数集合
                        if (s.deviceParams) {
                            s.deviceParams.forEach((p, k) => {
                                //只解析当前值
                                if (p.paramKey == "Value") {
                                    //状态输入  
                                    if (p.dataType == "num_input") {
                                        data.state.push({
                                            name: s.name,
                                            id: "s_" + i + '_' + j + '_' + k,
                                            config: p.config ? JSON.parse(p.config) : null
                                        });
                                        let v = s.variable.split(":");
                                        let item = {
                                            id: "s_" + i + '_' + j + '_' + k,
                                            iosvrKey: v[0],
                                            chlKey: v[1],
                                            ctrlKey: v[2],
                                            varKey: v[3],
                                            realTime: false,
                                        };
                                        state.realData = Object.assign({}, state.realData, {
                                            ["s_" + i + '_' + j + '_' + k]: 0,
                                        });
                                        ws.push(item);
                                    }
                                }
                            });
                        }
                    });
                    state.list.push(data);
                }
            });
            if (ws.length > 0) {
                nextTick(() => {
                    socket.subscribe(state.sockets, "real", "ba", ws);
                });
            }
        };

        const getDeviceList = async () => {
            socket.unsubscribe(state.sockets, "ba", "real");
            let {
                data,
                total
            } = await proxy.$api.getDevicesStd({
                areaId: areaId.value ? (areaId.value.id == -1 ? null : areaId.value.id) : "",
                menuId: activeMenus.value ? activeMenus.value.id : "",
                keyword: state.keyword,
                size: state.size,
                page: state.page,
                projectId: getCookie("gh_projectId")
            })
            state.total = total;
            deviceStd(data);
        };

        const handleCurrentChange = (page) => {
            state.page = page;
            getDeviceList();
        }

        const searchDevice = () => {
            state.page = 1;
            getDeviceList();
        }

        const getName = (value, config) => {
            let name = "";
            if (config && config.length && config.length > 0) {
                config.forEach(c => {
                    if (calc(value, c.factor, c.value)) {
                        name = c.text;
                    }
                })
            }
            return name;
        }
        const getColor = (value, config) => {
            let color = "";
            if (config && config.length && config.length > 0) {
                config.forEach(c => {
                    if (calc(value, c.factor, c.value)) {
                        color = c.color;
                    }
                })
            }
            return color;
        }
        const subscribeData = (res) => {
            if (res) {
                let data = JSON.parse(res);
                if (data.batchDefinitionId == "real" && data.clientId == "ba") {
                    data.data.forEach((d) => {
                        state.realData[d.id] = Number(d.value);
                    });
                }
            }
        };

        const showDetail = (item) => {
            state.showDevice = true;
            state.deviceDetail = item;
        }

        const changeTab = (item) => {
            state.tabComponent = item.component
        }
        const zoomToPosition = async (item) => {
            let mode= getCookie("mode");
            // if (mode != 1) {
            //     ElMessage.warning("该模式下不支持定位");
            //     return;
            // }
            let {
                data
            } = await proxy.$api.getObjectId({
                projectId: getCookie("gh_projectId"),
                deviceId: item.id,
                menuId: activeMenus.value.id,
                // fileId: state.id,
            });
            if (data) {
                emitter.emit("zoomToComponents", {
                    objectId: data.objectId
                })
            } else {
                ElMessage.warning("该设备未关联模型构件");
                return;
            }

        };

        return {
            ...toRefs(state),
            activeMenus,
            handleCurrentChange,
            searchDevice,
            getName,
            getColor,
            subscribeData,
            showDetail,
            changeTab,
            zoomToPosition,
        }
    }
});
</script>

<style lang="scss" scoped>
.detail {
    display: flex;
    flex-direction: column;
    height: 100%;

    &>div:nth-child(2) {
        height: calc(100% - 40px);
        overflow-y: auto;
        overflow-x: hidden;
    }

    .tabs {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .tab {
            background: url("../../assets/images/common/tab.png") no-repeat;
            width: 104px;
            height: 40px;
            background-size: 100% 100% !important;
            .name {
                font-size: 14px;
                font-family: "Alibaba-PuHuiTi";
                font-weight: 400;
                color: #03FFFF;
                background: linear-gradient(0deg, #DFFFFF 0%, #ABF6FF 100%);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
            }

        }
    }

    .active {
        background: url("../../assets/images/common/tab_active.png") no-repeat !important;
        width: 104px;
        height: 40px;
        background-size: 100% 100% !important;
    }

}</style>
