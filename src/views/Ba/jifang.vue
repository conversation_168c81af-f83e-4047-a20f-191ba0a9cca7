<template>
    <div class="right">
        <div class="item" style="flex:1">
            <sub-title1 title='平均温湿度' />
            <div class="item-body jifang">
                <div class="h_center">
                    <img src="../../assets/images/common/t.png" />
                    <div>{{ (temperature / all1).toFixed(2) }}℃</div>
                </div>
                <div class="h_center">
                    <img src="../../assets/images/common/h.png" />
                    <div>{{ (humidity / all2).toFixed(2) }}%</div>
                </div>
            </div>
        </div>
        <div class="item" style="flex:2">
            <sub-title1 title='温度排名' />
            <div class="item-body kong">
                <bar :names="names1" :values="values1"/>
            </div>
        </div>
        <div class="item" style="flex:2">
            <sub-title1 title='湿度排名' />
            <div class="item-body kong" >
                <bar :names="names2" :values="values2"/>
            </div>
        </div>
    </div>
</template>

<script>
import {
    defineComponent,
    getCurrentInstance,
    reactive,
    toRefs,
    computed
} from 'vue';
import {
    getCookie,
} from "@/utils/cookie";
import bar from './echart/bar.vue'
export default defineComponent({
    name: "jifang",
    components: {
        bar
    },
    setup() {
        const {
            proxy
        } = getCurrentInstance()
        const state = reactive({
            echartData: [10, 11, 12, 0, 8, 6, 2],
            xAxisData: ['星期一', '星期二', '星期三', '星期四', '星期五', '星期六', '星期天'],
            temperature: 0,
            humidity: 0,
            events: [],
            all1: 1,
            all2: 1,
            datas: [],
            names1: [],
            values1: [],
            names2: [],
            values2: [],
        })

        const activeMenus = computed(() => {
            let menu = getCookie("funMenus");
            return store.state.menu.funMenus ?
                store.state.menu.funMenus :
                menu ?
                    JSON.parse(menu) :
                    "";
        });


        const getDeviceTypeStatus = () => {
            let t = 0;let  l = 0; let all1 = 0;let  all2 = 0;
            proxy.$api.getDeviceTypeStatus({
                projectId: getCookie("gh_projectId"),
                typeCode: ['Sensor']
            }).then(res => {

                res.data.forEach(d => {
                    if (d.keyCode == 't') {
                        t +=Number(d.dataVal) 
                        all1++;
                    } else if (d.keyCode == 'h') {
                        l += Number(d.dataVal) 
                        all2++;
                    }
                })
                state.all1 = all1;
                state.all2 = all2;
                state.temperature = t;
                state.humidity = l;

                let d1 = res.data.filter(item => item.keyCode == 't');
                d1.sort((a, b) => {
                    return  Number(b.dataVal)  - Number(a.dataVal) 
                });
                d1.slice(0, 3).forEach(item => {
                    state.names1.push(item.deviceName);
                    state.values1.push(item.dataVal);
                })


                let d2 = res.data.filter(item => item.keyCode == 'h');
                d2.sort((a, b) => {
                    return Number(b.dataVal)  - Number(a.dataVal) 
                });
                d2.slice(0, 3).forEach(item => {
                    state.names2.push(item.deviceName);
                    state.values2.push(item.dataVal);
                })

            });
        }

        getDeviceTypeStatus();

        return {
            ...toRefs(state)
        }
    }
});
</script>

<style lang="scss" scoped>
.home {
    .home_wrapper {
        .left {
            .header {
                font-size: 16px;
                font-family: "DOUYU";
                font-weight: 400;
                color: #E6F4FF;
            }

            .input {
                margin-bottom: 12px;
            }

            .device {
                height: calc(100% - 130px);

                .list {
                    background: rgba(16, 52, 87, 0.25);
                    font-size: 14px;
                    font-family: "Alibaba-PuHuiTi";
                    font-weight: normal;
                    color: #FFFFFF;
                    margin-bottom: 8px;
                    border-left: 1px solid #4274A3;
                    height: 52px;

                    .state {
                        div {
                            margin-right: 48px;
                        }

                        &>:last-child {
                            margin-right: unset;
                        }
                    }
                }

            }
        }

        .right {
            box-sizing: border-box;
            display: flex;
            flex-direction: column;

            .item {
                height: calc(100% / 3);
                // flex: 1;

                &-body {
                    display: flex;
                    justify-content: center;
                    padding: 10px;
                }
            }

            .jifang {
                .h_center {
                    div {
                        font-size: 28px;
                        font-family: "BEBAS";
                        font-weight: 400;
                        color: #FFFFFF;
                        background: linear-gradient(0deg, #FFFFFF 0%, #1196FC 100%);
                        -webkit-background-clip: text;
                        -webkit-text-fill-color: transparent;
                    }

                    img {
                        width: 100px;
                        height: 100px;
                    }
                }

                &>div:first-child {
                    margin-right: 23px;
                }
            }

            .kong {
                font-family: "Alibaba-PuHuiTi";
                font-weight: normal;
                color: #E6F0F6;
                flex-direction: column;
                height: calc(100% - 60px);

                .name {
                    margin-right: 7px;
                    display: flex;
                    align-items: center;
                    white-space: nowrap;
                }

                .btn {
                    width: 85px;
                    height: 44px;
                    background: url('../../assets/images/common/d5.png') no-repeat;
                    background-size: 100%;
                    margin-left: 7px;

                    &>div {
                        flex: 1
                    }

                    &>div>div {
                        height: 32px;
                        width: 32px;
                        background: url("../../assets/images/common/d2.png") no-repeat;
                        background-size: 100%;
                    }
                }

                .list {
                    width: 100%;
                    background: rgba(16, 52, 87, 0.25);
                    margin-bottom: 6px;
                }

                .dot {
                    height: 1px;
                    border-bottom: 1px dashed #6E94BA;
                    flex: 1
                }
            }

            .event {
                font-family: "Alibaba-PuHuiTi";
                font-weight: normal;
                color: #E6F0F6;

                flex-direction: column;

                height: calc(100% - 60px);

                .name {
                    // flex: 1;
                    display: flex;
                    align-items: center;
                    font-size: 14px;
                    font-family: "Alibaba-PuHuiTi";
                    font-weight: normal;
                    color: #F0F9FF;
                    width: 232px !important;
                }

                .time {

                    font-size: 18px;
                    font-family: "BEBAS";
                    font-weight: 400;
                    color: #C3D2E0;
                }

                .list {
                    width: 100%;
                    background: rgba(16, 52, 87, 0.25);
                    margin-bottom: 6px;
                    height: 40px;
                    position: relative;
                }

                .bar {
                    width: 7px;
                    height: 1px;
                    background: #466582;
                    position: absolute;
                    top: 0;
                    right: 0;
                }

            }

        }

    }
}
</style>
