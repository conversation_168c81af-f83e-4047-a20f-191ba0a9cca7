<template>
    <div class="right">
        <div class="item" style="flex:1">
            <sub-title1 title='运行统计' />
            <div class="item-body order">
                <div>
                    <div class="total center">设备总数</div>
                    <div class="order-left">
                        <div class="center">
                            <div>{{ all }}</div>
                        </div>
                    </div>
                </div>
                <div class="order-right">
                    <div class="order-text">
                        <div class="dot" style="background:#1AAC1A"></div><span class="text">{{ onText }}:</span><span
                            class="num">{{ on }}</span>
                    </div>
                    <div class="order-text">
                        <div class="dot" style="background:#C47F13"></div><span class="text">{{ offText }}:</span><span
                            class="num">{{ off }}</span>
                    </div>
                    <!-- <div class="order-text">
                        <div class="dot" style="background:#C33838"></div><span class="text">故障状态:</span><span class="num">{{ fault }}</span>
                    </div> -->
                </div>
            </div>
        </div>
        <div class="item" style="flex:2">
            <sub-title1 title='设备群控' />
            <div class="item-body kong">
                <el-scrollbar v-if="manual.length > 0">
                    <div class="list space-between" v-for="item in manual" :key="item.id">
                        <div class="name">
                            <img src="../../assets/images/common/d3.png" />
                            <div>{{ item.name }}</div>
                        </div>
                        <div class="dot"></div>
                        <div class="btn space-between">
                            <div class="center cursor" @click="writeValue(item.startGroup, item.status)">
                                <div class="center">开</div>
                            </div>

                            <div class="center cursor" @click="writeValue(item.endGroup, item.status)">
                                <div class="center">关</div>
                            </div>
                        </div>
                    </div>
                </el-scrollbar>
                <noData v-else></noData>

            </div>
        </div>
        <div class="item" style="flex:2">
            <sub-title1 title='运行事件' />
            <div class="item-body event">
                <el-scrollbar>
                    <div class="list space-between" v-for="(item, index) in manualLog" :key="index">
                        <div class="name">
                            <img v-if="i % 2 == 0" src="../../assets/images/common/on.png" />
                            <img v-else src="../../assets/images/common/off.png" />
                            <div>{{ item.deviceName || item.deviceName1 }}</div>
                        </div>
                        <div>
                            {{ item.standardName || item.standardName1 }}
                        </div>
                        <div>
                            {{ item.newValue }}
                        </div>
                        <div class="time">
                            {{ item.logTime }}
                        </div>

                        <div class="bar"></div>
                    </div>
                </el-scrollbar>
            </div>
        </div>
    </div>
</template>
    
<script>
import {
    defineComponent,
    getCurrentInstance,
    reactive,
    toRefs,
    computed,
    inject, onUnmounted, watch
} from 'vue';
import {
    getCookie,
} from "@/utils/cookie";
import socket from "@/utils/socket";
import { ElMessage } from 'element-plus';
import {
    useStore
} from 'vuex';
export default defineComponent({
    name: "ba",
    components: {},
    setup() {
        const {
            proxy
        } = getCurrentInstance()
        const store = useStore();
        const state = reactive({
            keyword: "",
            size: 10,
            page: 1,
            total: 100,
            manual: [],
            sockets: null,
            manualLog: [],
            interval: null,
            on: 0,
            off: 0,
            all: 0,
            fault: 0,
            onText: '运行数量',
            offText: '停止数量'
        })

        state.sockets = inject("socket");
        const activeMenus = computed(() => {
            let menu = getCookie("funMenus");
            return store.state.menu.funMenus ?
                store.state.menu.funMenus :
                menu ?
                    JSON.parse(menu) :
                    "";
        });
        watch(activeMenus, (val) => {
            if (val) {
                getRunConfigPage("manual");
                getDeviceTypeStatus();
            }
        });

        const getRunConfigPage = (type) => {
            proxy.$api.getRunConfig({
                projectId: getCookie("gh_projectId"),
                tag: 2,
                type,
                menuId: activeMenus.value.id
                // menuId:activeMenus.value.id
            }).then((res) => {
                if (type == "strategy") {
                    state.strategies = res.data.strategies;
                    // getRunModelPage(2);
                }
                if (type == "manual") {
                    state.manual = res.data.manuals;

                }
            });
        };
        const writeValue = (group, status) => {
            // if (!status) {
            //     return;
            // }
            if (group && group.length > 0) {
                group.forEach((g, i) => {
                    g.groupVars.forEach((v, j) => {
                        socket.ManualWrite(
                            state.sockets,
                            v.varValue,
                            v.variable,
                            v.name,
                            v.standardName,
                            getCookie("gh_id"),
                            g.menuId,
                            "manual",
                            state.sockets.id,
                            "manual" + i + j,
                            getCookie("gh_projectId"),
                            v.deviceId
                        );
                    });
                });
                ElMessage.success('操作成功')
            }
        };

        const getRunManualPage = (type, log) => {
            proxy.$api.getDeviceLog({
                projectId: getCookie("gh_projectId"),
                // type,
                // tag: 1,
                menuId: activeMenus.value.id,
                page: 1,
                size: 10,
            }).then((res) => {
                state.manualLog = res.data;
            });
        };
        const getDeviceTypeStatus = () => {
            let on = 0,
                off = 0,
                fault = 0,
                all = 0;
            let type = "AirConditioner";
            if (activeMenus.value.name == '通风空调') {
                type = "AirConditioner";
                state.onText = '运行数量';
                state.offText = "停止数量";
            } else if (activeMenus.value.name == '环境监测') {
                type = "dlj";
            } else if (activeMenus.value.name == '电动窗帘') {
                type = "ddcl";
                state.onText = '开启数量';
                state.offText = "关闭数量";
            } else if (activeMenus.value.name == "升降支架") {
                type = "sjzj";
                state.onText = '上升数量';
                state.offText = "下降数据";
            } else if (activeMenus.value.name == "漏水监测") {
                type = "lwm";
                state.onText = '正常数量';
                state.offText = "报警数量";
            }
            proxy.$api.getDeviceTypeStatus({
                projectId: getCookie("gh_projectId"),
                typeCode: [type]
            }).then(res => {
                res.data.forEach(d => {
                    if (d.keyCode == "runState") {
                        all++;
                        if (d.dataVal == '1') {
                            on++;

                        } else if (d.dataVal == '0') {
                            off++;
                        }
                    } else if (d.keyCode == "lslam") {
                        all++;
                        if (d.dataVal == '1') {
                            off++;

                        } else if (d.dataVal == '0') {
                            on++;
                        }
                    }

                })
                state.on = on;
                state.off = off;
                state.all = all;
                state.fault = fault;
            });
        }

        const stopInterval = () => {
            if (state.interval) {
                clearInterval(state.interval);
                state.interval = null;
            }
        };

        getDeviceTypeStatus();
        getRunConfigPage("manual")
        getRunManualPage(3, "manualLog");

        stopInterval();

        state.interval = setInterval(() => {
            getRunManualPage(3, "manualLog");
            getDeviceTypeStatus();
        }, 2000);

        onUnmounted(() => {
            stopInterval();
        })

        return {
            ...toRefs(state),
            writeValue
        }
    }
});
</script>
    
<style lang="scss" scoped></style>
    