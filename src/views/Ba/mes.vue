<template>
<div class="right">
    <div class="item" style="flex:1">
        <sub-title1 title='设备统计' />
        <div class="item-body order">
            <div>
                <div class="total center">设备总数</div>
                <div class="order-left">
                    <div class="center">
                        <div>{{on+off}}</div>
                    </div>
                </div>
            </div>
            <div class="order-right">
                <div class="order-text">
                    <div class="dot" style="background:#1AAC1A"></div><span class="text">在线数量:</span><span class="num">{{on}}</span>
                </div>
                <div class="order-text">
                    <div class="dot" style="background:#C47F13"></div><span class="text">离线数量:</span><span class="num">{{off}}</span>
                </div>
            </div>
        </div>
    </div>
    <div class="item" style="flex:2">
        <sub-title1 title='终端控制' />
        <div class="item-body kong">
            <el-scrollbar v-if="list.length>0">
                <div class="list space-between" v-for="item in list" :key="item.orgIndexCode">
                    <div class="name">
                        <img src="../../assets/images/common/d3.png" />
                        <div>{{item.terminalName}}</div>
                    </div>
                    <div class="dot"></div>
                    <div class="bra-btn space-between">
                        <div class="center cursor" @click="taskCmd('powerOn',item.terminalId)">
                            <div class="center">开机</div>
                        </div>

                        <div class="center cursor" @click="taskCmd('powerOff',item.terminalId)">
                            <div class="center">关机</div>
                        </div>
                    </div>
                </div>
            </el-scrollbar>
            <noData v-else></noData>
        </div>
    </div>
    <div class="item" style="flex:2">
        <sub-title1 title='节目列表' />
        <div class="item-body event">
            <el-scrollbar v-if="program.length>0">
                <div class="list space-between" v-for="item in program" :key="item.programId">
                    <div class="name">
                        <span class="iconfont iconjiemufabu-jiemuguanli"></span>
                        <div>{{item.programName}}</div>
                    </div>

                    <div class="time">
                        {{dayjs(item.updateTime).format("YYYY-MM-DD HH:mm:ss")}}
                    </div>

                    <div class="bar"></div>
                </div>
            </el-scrollbar>
            <noData v-else></noData>
        </div>
    </div>
</div>
</template>

<script>
import {
    defineComponent,
    getCurrentInstance,
    reactive,
    toRefs,
    computed
} from 'vue';

import {
    getCookie,
} from "@/utils/cookie";
import Alarm from '@/components/echarts/weekEventEchart.vue'
import dayjs from 'dayjs'
import {
    ElMessage
} from 'element-plus';
export default defineComponent({
    name: "message_bak",
    components: {
        Alarm
    },
    setup() {
        const {
            proxy
        } = getCurrentInstance()
        const state = reactive({
            list: [],
            on: 0,
            off: 0,
            program: [],
            dayjs,

        })

        const activeMenus = computed(() => {
            let menu = getCookie("funMenus");
            return store.state.menu.funMenus ?
                store.state.menu.funMenus :
                menu ?
                JSON.parse(menu) :
                "";
        });

        const getTerminal = () => {
            state.on = 0;
            state.off = 0;
            proxy.$api.getHkTerminal({
                page: 1,
                size: 200,
            }).then((res) => {
                state.list = res.data.list;
                state.list.forEach(d => {
                    if (d.isOnline == 'online') {
                        state.on++;
                    } else {
                        state.off++;
                    }
                })

            })
        }
        // getHkProgram
        const getHkProgram = () => {
            proxy.$api.getHkProgram({
                page: 1,
                size: 200,
            }).then((res) => {
                state.program = res.data.list;
            })
        }
        const taskCmd = (cmd, id) => {
            proxy.$api.hkTerminalCmd({
                id,
                cmd
            }).then((res) => {
                if (res.success) {
                    ElMessage.success("操作成功")
                }
            })
        }
        getTerminal();
        getHkProgram();
        return {
            ...toRefs(state),
            taskCmd,
            activeMenus
        }
    }
});
</script>

<style lang="scss" scoped>
.bra-btn {
    background-size: 100%;
    margin-left: 7px;

    &>div {
        flex: 1
    }

    &>div:first-child {
        margin-right: 10px;
    }

    &>div>div {

        width: 54px;
        height: 28px;
        background: #1B6DC2;
        border-radius: 4px;

        box-shadow: 0px 0px 3px 3px #2CBDEB inset;
    }
}
</style>
