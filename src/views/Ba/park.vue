<template>
<div class="right">
    <div class="item" style="flex:1">
        <sub-title1 title='车位统计' />
        <div class="item-body order">
            <div>
                <div class="total center">车位总数</div>
                <div class="order-left">
                    <div class="center">
                        <div>0</div>
                    </div>
                </div>
            </div>
            <div class="order-right">
                <div class="order-text">
                    <div class="dot" style="background:#1AAC1A"></div><span class="text">空闲车位:</span><span class="num">0</span>
                </div>
                <div class="order-text">
                    <div class="dot" style="background:#C47F13"></div><span class="text">占用车位:</span><span class="num">0</span>
                </div>
            </div>
        </div>
    </div>
    <div class="item" style="flex:2">
        <sub-title1 title='入场分析' />
        <div class="item-body kong">
            <Alarm :xAxisData="xAxisData" :echartData="value1" />
        </div>
    </div>
    <div class="item" style="flex:2">
        <sub-title1 title='出场分析' />
        <div class="item-body event">
            <Alarm :xAxisData="xAxisData" :echartData="value" />
        </div>
    </div>
</div>
</template>

<script>
import {
    defineComponent,
    getCurrentInstance,
    reactive,
    toRefs,
    computed
} from 'vue';

import {
    getCookie,
} from "@/utils/cookie";
import Alarm from '@/components/echarts/weekEventEchart.vue'
import dayjs from 'dayjs'
import {
    onMounted
} from 'vue';
export default defineComponent({
    name: "park",
    components: {
        Alarm
    },
    setup() {
        const {
            proxy
        } = getCurrentInstance()
        const state = reactive({
            echartData: [10, 11, 12, 0, 8, 6, 2],
            value:[],
            value1:[],
            xAxisData: ['星期一', '星期二', '星期三', '星期四', '星期五', '星期六', '星期天'],
        })

        const activeMenus = computed(() => {
            let menu = getCookie("funMenus");
            return store.state.menu.funMenus ?
                store.state.menu.funMenus :
                menu ?
                JSON.parse(menu) :
                "";
        });

        onMounted(() => {
            crossRecords()
          
        });

        const crossRecords = async () => {
            let {
                data
            } = await proxy.$api.crossRecords({
                page: 1,
                size: 1000,
                bt: dayjs().startOf('day').format('YYYY-MM-DDTHH:mm:ss') + '+08:00',
                et: dayjs().endOf('day').format('YYYY-MM-DDTHH:mm:ss') + '+08:00'
            });
            // state.list = data.list.slice(0, 10);
            let value = new Array(24).fill(0)
            let value1 = new Array(24).fill(0)
            let xAxisData=[];
            for(let i = 0; i<24; i++)
            {
                xAxisData[i] =i+":00"
            }
            data.list.forEach(d => {
                if (d.vehicleOut == 1) {//出场
                    let num = value[dayjs(d.eventTime).hour()]
                    value[dayjs(d.eventTime).hour()] = num + 1
                } else if (d.vehicleOut == 0) {//进场
                    let num = value1[dayjs(d.eventTime).hour()]
                    value1[dayjs(d.eventTime).hour()] = num + 1
                }

            })
            state.value = value;
            state.value1 = value1;
            state.xAxisData=xAxisData;

        }

        return {
            ...toRefs(state)
        }
    }
});
</script>

<style lang="scss" scoped>

</style>
