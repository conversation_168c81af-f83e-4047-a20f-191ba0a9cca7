<template>
  <div class="layout_wrapper text_wrapper">
    <div class="left-text">
      <sub-title2 title="终端列表" />
      <div class="content">
        <el-scrollbar class="scrollbar">
          <div class="list" v-for="(item,i) in terminal" :key="i">
            <div class="icon">
              <i class="iconfont iconzhongduan1"></i>
            </div>
            {{item.endpointName}}
          </div>
        </el-scrollbar>
      </div>
    </div>
    <div class="right-text">
      <div class="edit">
        <div class="quill_editor">
          <QuillEditor toolbar="full" v-model:content="content" />
        </div>

      </div>
      <div class="btn">
        <el-button type="primary" class="saveBtn" size="small" @click="submit">发布</el-button>
      </div>
      <div class="table">
        <el-table :data="tableData"  fit>
          <template #empty>
            <no-data />
          </template>
          <el-table-column label="发布时间" prop="time" />
          <el-table-column label="起止时间">
            <template #default="scope">
              <span>{{scope.row.startTime}} - {{scope.row.endTime}}</span>
            </template>
          </el-table-column>
          <el-table-column label="文本内容" prop="content" />
          <el-table-column fixed="right" label="操作" width="100">
            <template #default="scope">
              <el-button @click="handleClick(scope.row)" type="text" class="editBtn">导入到文本</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>
<script>
import { QuillEditor } from '@vueup/vue-quill'
import '@vueup/vue-quill/dist/vue-quill.snow.css';
import { reactive, toRefs } from "vue";
import { getCurrentInstance, onMounted } from "vue";
export default {
  name:'InfoText',
  components: {
    QuillEditor
  },
  setup () {
    const { proxy } = getCurrentInstance();
    const state = reactive({
      terminal: [],
      content: '',
      editorOption: {},
      tableData: [
        {
          time: "2021-12-13 04:13:12",
          startTime: "2021-12-13 04:13:12",
          endTime: " 2021-12-18 12:16:34",
          content: "B站、A站、豆瓣全崩了？连上海消防也被惊动 最新回应来了",
        },
        {
          time: "2021-12-13 04:13:12",
          startTime: "2021-12-13 04:13:12",
          endTime: " 2021-12-18 12:16:34",
          content: "B站、A站、豆瓣全崩了？连上海消防也被惊动 最新回应来了",
        },
        {
          time: "2021-12-13 04:13:12",
          startTime: "2021-12-13 04:13:12",
          endTime: " 2021-12-18 12:16:34",
          content: "B站、A站、豆瓣全崩了？连上海消防也被惊动 最新回应来了",
        },
        {
          time: "2021-12-13 04:13:12",
          startTime: "2021-12-13 04:13:12",
          endTime: " 2021-12-18 12:16:34",
          content: "B站、A站、豆瓣全崩了？连上海消防也被惊动 最新回应来了",
        },
      ],
      content: "",
    });
    onMounted(() => {
      getTerminalPage();
    });
    // 终端列表
    const getTerminalPage = () => {
      proxy.$api.getTerminal().then((res) => {
        state.terminal = res.data;
      });
    };
    return {
      ...toRefs(state),
      getTerminalPage,
    };
  },
};
</script>
<style lang="scss" scoped>
.text_wrapper {
  display: flex;
  padding: 0 15px;
  .left-text {
    width: 334px;
    margin-right: 15px;
    .content {
      height: calc(100% - 100px);
      background-color: rgba(0, 0, 0, 0.2);
      border: 1px solid rgba(255, 255, 255, 0.3);
      padding: 10px 15px;
      .list {
        display: flex;
        font-size: 16px;
        font-family: "PingFangSC-Medium", "PingFang SC";
        font-weight: 500;
        color: #c7dfff;
        padding: 14px 0;
        border-bottom: 1px dashed rgba(96, 105, 109, 0.2);

        .icon {
          width: 40px;
          height: 40px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 9px;
          background: rgba(255, 255, 255, 0.1);
        }
      }
    }
  }
  .right-text {
    flex: 1;
    display: flex;
    flex-direction: column;
    .edit {
      height: calc(60% - 15px);
      margin-bottom: 15px;
      color: #fff;
      .quill_editor {
        height: calc(100% - 50px);
      }
    }
    .btn {
      text-align: center;
    }
    .table {
      height: 40%;
    }
  }
}
</style>
<style>
.ql-toolbar.ql-snow,
.ql-container.ql-snow {
  border: 0.01rem solid #d1d5db;
  border: 1px solid #2f363c;
}
</style>