<template>
    <div class="alarm_container">
        <div class="alarm_container-left">

            <el-table class="table" height="calc(100% - 50px)" ref="singleTable" :data="list" highlight-current-row
                @row-click="handleCurrentRow">
                <template #empty>
                    <noData />
                </template>
                <el-table-column align="center" property="createTime" label="报警时间">
                    <template #default="scope">
                        <span class="createTime">{{
                            getTime(scope.row.createTime)
                        }}</span>
                    </template>
                </el-table-column>
                <el-table-column align="center" property="alarmVariable" label="报警变量">
                </el-table-column>
                <el-table-column align="center" property="alarmDesc" label="报警内容">
                </el-table-column>
                <el-table-column align="center" property="alarmLevel" label="等级">
                    <template #default="scope">
                        <span :class="scope.row.alarmLevel == 1
                                    ? 'jj'
                                    : scope.row.alarmLevel == 0
                                        ? 'pt'
                                        : scope.row.alarmLevel == 2
                                            ? 'yz'
                                            : ''
                                ">{{ getLevelName(scope.row.alarmLevel) }}</span>
                    </template>
                </el-table-column>
                <el-table-column align="center" property="alarmDesc" label="描述">
                </el-table-column>
                <el-table-column align="center" property="alarmSource" label="报警类型">
                    <template #default="scope">
                        <span>{{ getSourceName(scope.row.alarmSource) }}</span>
                    </template>
                </el-table-column>
            </el-table>

            <div class="page center">
                <el-pagination prev-icon="CaretLeft" next-icon="CaretRight" :page-size="size" :current-page="page"
                    layout="total,prev, pager, next" @current-change="handleCurrentChange" :total="count">
                </el-pagination>
            </div>
        </div>
        <div class="alarm_container-right">
            <el-scrollbar class="scrollbar">
                <div class="item">
                    <panel title="报警设备"></panel>
                    <div class="card-body">
                        <div class="card-data">
                            <div class="card-data-title">报警设备:</div>
                            <div class="card-data-val">{{ alarm.deviceName }}</div>
                        </div>
                        <div class="card-data">
                            <div class="card-data-title">报警指标:</div>
                            <div class="card-data-val">{{ alarm.standardName }}</div>
                        </div>
                        <div class="card-data">
                            <div class="card-data-title">当前值:</div>
                            <div class="val">{{ alarm.value }}</div>
                        </div>
                    </div>
                </div>
                <div class="item alarm">
                    <panel title="联动指令">
                        <!-- <el-radio-group v-model="alarm.auto" size="mini">
                        <el-radio-button :label="false">手动</el-radio-button>
                        <el-radio-button :label="true">自动</el-radio-button>
                    </el-radio-group> -->
                    </panel>
                    <div class="card-body">
                        <el-timeline>
                            <el-timeline-item>
                                <div class="link-data">
                                    <div>播放视频</div>
                                    <div @click="openVideo">
                                        <i class="pointer iconfont icontingzhi2-copy"></i>
                                    </div>
                                </div>
                            </el-timeline-item>
                            <el-timeline-item>
                                <div class="link-data">
                                    <div>报警录像</div>
                                    <div @click="openRecord">
                                        <i class="pointer iconfont icontingzhi2-copy"></i>
                                    </div>
                                </div>
                            </el-timeline-item>
                            <el-timeline-item>
                                <div class="link-data">
                                    <div>报警抓图</div>
                                    <div @click="openSnap">
                                        <i class="pointer iconfont icontingzhi2-copy"></i>
                                    </div>
                                </div>
                            </el-timeline-item>
                            <el-timeline-item>
                                <div class="link-data">
                                    <div>联动场景</div>
                                    <div @click="openScene">
                                        <i class="pointer iconfont icontingzhi2-copy"></i>
                                    </div>
                                </div>
                            </el-timeline-item>
                        </el-timeline>
                    </div>
                </div>
                <div class="item alarm">
                    <panel title="报警处理"></panel>
                    <div class="card-body search_box">
                        <el-timeline>
                            <el-timeline-item type="primary">
                                <div class="progress-data">
                                    <div>关闭声音</div>
                                    <div @click="stopAudio" class="cursor searchBtn" size="mini" type="primary"
                                        effect="plain">
                                        消音
                                    </div>
                                </div>
                            </el-timeline-item>
                            <el-timeline-item type="primary">
                                <div class="progress-data">
                                    <div>现场复核</div>
                                    <div class="addDiagram" style="flex: 1; margin-left: 10px">
                                        <el-input v-model="content" type="textarea" placeholder="请输入具体描述文字"></el-input>
                                    </div>
                                </div>
                            </el-timeline-item>
                            <el-timeline-item type="primary">
                                <div class="progress-data">
                                    <div>生成工单</div>
                                    <div class="cursor searchBtn" size="mini" type="primary" @click="createOrder"
                                        effect="plain">
                                        生成工单
                                    </div>
                                </div>
                            </el-timeline-item>
                            <el-timeline-item type="primary">
                                <div class="progress-data">
                                    <div>结案,移至历史记录</div>
                                    <div @click="process" class="cursor searchBtn" size="mini" type="primary"
                                        effect="plain">
                                        结案
                                    </div>
                                </div>
                            </el-timeline-item>
                        </el-timeline>
                    </div>
                </div>

            </el-scrollbar>
        </div>
        <el-dialog align-center append-to-body draggable class="form cctv-dialog" v-model="show" title="报警录像" center width="670px"
            custom-class="custom_dialog">
            <video :src="src" controls="controls" style="width:100%;height:500px">
                您的浏览器不支持 video 标签。
            </video>
        </el-dialog>
        <el-dialog align-center append-to-body class="form cctv-dialog" v-model="showImg" title="报警抓图" center width="670px"
            custom-class="custom_dialog">
            <img :src="src1" height="100%" width="100%" style="width:100%;height:500px" />
        </el-dialog>
        <el-dialog align-center append-to-body class="cctv-dialog" v-model="showScene" title="联动场景" center width="670px"
            custom-class="custom_dialog">
            <el-table fit :data="scenes" style="width: 100%">
                <el-table-column align="center" property="name" label="设备名称" width="120">
                </el-table-column>
                <el-table-column align="center" property="standardName" label="指标" width="120">
                </el-table-column>
                <el-table-column align="center" property="variable" label="联动变量">
                </el-table-column>
                <el-table-column align="center" property="varValue" label="联动指令">
                </el-table-column>
            </el-table>
            <template #footer>
                <div class="dialog-footer">
                    <el-button v-if="alarm.auto" type="primary" class="saveBtn" size="small" @click="send">发送指令</el-button>
                </div>
            </template>
        </el-dialog>
        <el-cctv ref="camRef"></el-cctv>
    </div>
</template>

<script>
import {
    getCookie
} from "@/utils/cookie";
import socket from "@/utils/socket";
import dayjs from "dayjs";
import cctv from '@/components/cctv/src/main.vue'
import {
    computed,
    getCurrentInstance,
    inject,
    onMounted,
    reactive,
    toRefs,
    watch
} from "vue";
import {
    ElMessage
} from 'element-plus';

import {
    useStore
} from 'vuex';

export default {
    name: "alarmactive",
    components: {
        'el-cctv': cctv,
    },
    sockets: {
        // 通过vue实例对象sockets实现组件中的事件监听
        h_alarm(res) {
            if (res) {
                let data = JSON.parse(res);
                data.sort((a, b) => a.createTime - b.createTime);
                this.tableData = data;
                this.count = data.length;
                this.list = this.tableData.slice(
                    (this.page - 1) * this.size,
                    this.page * this.size
                );
            }
        },

        alarm(res) {
            if (res) {
                // let data = JSON.parse(res);
                this.tableData.push(res);
                this.count = this.tableData.length;
                this.list = this.tableData.slice(
                    (this.page - 1) * this.size,
                    this.page * this.size
                );
            }
        },
    },
    setup() {
        const {
            proxy
        } = getCurrentInstance()
        const store = useStore()
        const state = reactive({
            tableData: [],
            list: [],
            currentRow: null,
            size: 15,
            count: 0,
            page: 1,
            interval: null,
            sources: [],
            levels: [],
            alarm: {
                standardName: "",
                deviceName: "",
                value: "",
                auto: false,
            },
            cctvEl: null,
            show: false,
            src: "",
            src1: '',
            showImg: false,
            content: "",
            showScene: false,
            scenes: [],
            sockets: null,
            url: '',
            visiable: false,
        });
        const emitter = inject('mitt');
        state.sockets = inject("socket");
        onMounted(() => {

            if (getCookie("gh_projectId") != "0")
                state.sockets.emit(
                    "SendHisAlarm",
                    getCookie("gh_projectId"),
                    getCookie("gh_id")
                );
            getDicUtilSources()
            getDicUtilLevel()
        });
        const projectId = computed(() => {
            return store.state.user.projectId || getCookie('gh_projectId')
        })
        watch(projectId, (val) => {
            if (val) {
                getDicUtilSources()
                getDicUtilLevel()
            }
        })
        const getDicUtilSources = () => {
            proxy.$api.getDicUtil({
                projectId: getCookie("gh_projectId"),
                dicCode: "alarm_type",
            }).then((res) => {
                state.sources = res.data;
            });
        }
        const getDicUtilLevel = () => {
            proxy.$api.getDicUtil({
                projectId: getCookie("gh_projectId"),
                dicCode: "alarm_level",
            }).then((res) => {
                state.levels = res.data;
            });
        }
        const handleCurrentChange = (page) => {
            state.page = page;
            state.list = state.tableData.slice(
                (state.page - 1) * state.size,
                state.page * state.size
            );
        };

        const getSourceName = (data) => {
            let name = "";
            state.sources.forEach((d) => {
                if (d.tagValue == data) {
                    name = d.tagName;
                    return false;
                }
            });
            return name;
        };
        const getLevelName = (data) => {
            let name = "";
            state.levels.forEach((d) => {
                if (d.tagValue == data) {
                    name = d.tagName;
                    return false;
                }
            });
            return name;
        };
        const handleCurrentRow = (row) => {
            if (row) {
                state.alarm = row;
                if (row.alarmVariable) {
                    let data = row.alarmVariable.split(":");
                    getVariable(data[0], data[1], data[2], data[3]);
                    getAlarmById(row.alarmGuid);
                }
            }
        };
        const getAlarmById = (uuid) => {
            proxy.$api.getAlarmById({
                uuid,
            }).then((res) => {
                if (res.data) {
                    state.alarm = Object.assign(state.alarm, res.data);
                }
            });
        };
        const getVariable = (server, chal, controller, variable) => {
            proxy.$api.getVariableData(server, chal, controller, variable).then((res) => {
                if (res.data && res.data.Value) {
                    state.alarm.value = res.data.Value;
                }
            });
        };
        const callback = () => {
            if (state.cctvEl) {
                document.getElementById("app").removeChild(state.cctvEl.$parent.$el);
                state.cctvEl = null;
            }
        };
        const openVideo = () => {
            if (state.alarm.alarmVideo) {
                let data = state.alarm.alarmVideo.split("#");
                let servers = data[0].split('_')
                let cams = data[1].split('_')
                // cams参数格式 ${d.camToken}_${d.strName}_${d.serverType}_${d.strSrcIpAddress}_${d.strUser}_${d.strPasswd}_${d.profileToken}
                //state.url = `${servers[0]}|${servers[2]}|${servers[3]}|${cams[0]}|${cams[1]}|${cams[1]}|${cams[2]}|${cams[3]}|${cams[4]}|${cams[5]}|${cams[6]}`
                state.camRef.opened({
                    server: servers[2], //流媒体ip --h5参数
                    port: servers[3], //流媒体port ---h5参数
                    token: cams[0], //zlm和h5平台通用
                    name: cams[1], //摄像机名称 --通用
                    serverType: cams[2], //平台类型 1 h5 2--zlm平台
                    ip: cams[3], //摄像机ip   zlm onvif
                    username: cams[4], //摄像机用户名 zlm onvif
                    password: cams[5], //摄像机密码 zlm onvif
                    profileToken: cams[6], //摄像机 onvif profileToken zlm onvif
                    ptzEnable: cams[7]
                });
            }
        };
        const openRecord = () => {
            if (state.alarm.auto) {
                //手动录像
                if (state.alarm.alarmVariable) {
                    state.sockets.emit("SnapVideo", state.alarm.alarmGuid, (res) => {
                        if (res) {
                            ElMessage({
                                type: "success",
                                message: "录像成功",
                            });
                        }
                    });
                }
            } else {
                state.show = true;
                if (state.alarm.alarmVideo) {
                    {
                        if (state.alarm.alarmVideoUrl) {
                            state.src = state.alarm.alarmVideoUrl;
                        }
                    }
                }
            }
        };
        const openSnap = () => {
            if (state.alarm.auto) {
                //手动抓拍
                if (state.alarm.alarmVariable) {
                    state.sockets.emit("SnapPhoto", state.alarm.alarmGuid, (res) => {
                        if (res) {
                            ElMessage({
                                type: "success",
                                message: "抓拍成功",
                            });
                        }
                    });
                }
            } else {
                state.showImg = true;
                if (state.alarm.alarmVideo) {
                    {
                        if (state.alarm.alarmSnapUrl) {
                            state.src1 = state.alarm.alarmSnapUrl;
                        }
                    }
                }
            }
        };
        const openScene = () => {
            state.showScene = true;
            if (state.alarm.sceneId) {
                getSceneVar(state.alarm.sceneId);
            }
        };
        const getSceneVar = (sceneId) => {
            proxy.$api.getSceneVar({
                projectId: getCookie("gh_projectId"),
                sceneId: sceneId,
            }).then((res) => {
                state.scenes = res.data;
            });
        };
        const send = () => {
            if (state.scenes && state.scenes.length > 0) {
                state.scenes.forEach((s) => {
                    socket.writeValue(state.sockets, s.variable, s.varValue,'alarmlink',state.sockets.id, getCookie("gh_projectId"),
                        getCookie("gh_id"));
                });
            }
        };
        const stopAudio = () => {
            emitter.emit('stopAudio1');
        };
        const process = () => {
            if (state.alarm.alarmVariable) {
                socket.ProcessAlarm(
                    state.sockets,
                    state.alarm.alarmVariable,
                    state.alarm.alarmGuid,
                    state.content,
                    getCookie("gh_id"),
                    getCookie("gh_projectId")
                );
                state.alarm = {
                    standardName: "",
                    deviceName: "",
                    value: "",
                };
                state.content = "";
                emitter.emit('stopAudio1');
            }
        };
        const getTime = (time) => {
            let showTime = "";
            if (time) {
                showTime = dayjs(time).format("YYYY-MM-DD HH:mm:ss");
            }
            return showTime;
        };
        const createOrder = () => {
            if (state.alarm && state.alarm.alarmGuid) {
                proxy.$api.createOrderByAlarm({
                    uuid: state.alarm.alarmGuid,
                    projectId: getCookie("gh_projectId"),
                });

                process();

            }
        };
        const closeView = () => {
            state.visiable = false
        }

        return {
            ...toRefs(state),
            projectId,
            getDicUtilSources,
            getDicUtilLevel,
            handleCurrentChange,
            getSourceName,
            getLevelName,
            handleCurrentRow,
            getAlarmById,
            getVariable,
            callback,
            openVideo,
            openRecord,
            openSnap,
            openScene,
            getSceneVar,
            send,
            stopAudio,
            process,
            getTime,
            createOrder,
            closeView
        };
    },
};
</script>

<style lang="scss" scoped>
.alarm_container {
    display: flex;
    flex-direction: row;
    font-size: 16px;
    font-family: "Alibaba-PuHuiTi";
    font-weight: 400;
    padding: 0 15px;
    height: 600px;

    &-left {
        width: calc(100% - 440px);
        margin-right: 20px;
        height: 100%;

        .table {
            .createTime {
                color: #889cc3;
            }

            .jj {
                color: #ff203a;
            }

            .pt {
                color: #13d4d9;
            }

            .yz {
                color: #e3731b;
            }
        }
    }

    &-right {
        width: 440px;
        height: 100%;

        .alarm {
            height: 37%;
        }
    }
}

video {
    height: 100%;
    width: 100%;
}

.searchBtn {
    margin-left: 10px;
}


.el-timeline {
    padding-left: 0;
}
</style>
