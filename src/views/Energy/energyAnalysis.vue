<template>
    <div class="energy_container z100">
        <div class="content_tab">
            <div class="left_tab">
                <span class="btn" v-for="(item, i) in dosageTab" :key="i" :class="{ active: dosageType == item.id }" @click="changeDosage(item.id)">{{ item.name }}</span>
            </div>
            <type-btn :typeData="typeData" @clickDeviceType="changeMaterType" />
        </div>
        <div class="content_box">
            <div class="energy_container_left">
                <div class="container_header tree_search">
                    <el-input v-model="keyWord" placeholder="请输入区域名称" popper-class="tree_search">
                        <template #suffix>
                            <i class="el-input__icon el-icon-search" />
                        </template>
                    </el-input>
                </div>
                <div class="type-list">
                    <el-scrollbar class="tree">
                        <el-tree ref="tree" :data="treeData" node-key="id" default-expand-all check-strictly :filter-node-method="filterNode" :props="props" show-checkbox @node-click="clickOption" @check="checkOption" />
                    </el-scrollbar>
                </div>
            </div>
            <div class="energy_container_right">
                <div class="chart-box">
                    <graphChart :unitName="unitName" :chart-data="YOY" name1="今年" name2="去年"/>
                </div>
                <div class="chart-box">
                    <graphChart :unitName="unitName" :chart-data="MOM" name1="本月" name2="上月" />
                </div>
            </div>
        </div>
    </div>
    </template>
    
    <script>
    import typeBtn from '@/components/energy/typeBtn'
    import graphChart from '@/components/energy/graphChart'
    import dayjs from 'dayjs'
    import {
        getCookie
    } from '@/utils/cookie'
    import {
        reactive,
        toRefs,
        ref,
        onMounted,
        getCurrentInstance,
        watch,
        computed
    } from 'vue'
    import {
        useStore
    } from 'vuex'
    
    export default {
        name: 'energyAnalysis',
        components: {
            graphChart,
            typeBtn,
        },
        setup() {
            const {
                proxy
            } = getCurrentInstance()
            const store = useStore()
            const tree = ref(null)
            const state = reactive({
                dosageTab: [{
                        name: '区域能耗',
                        id: 'area',
                    },
                    {
                        name: '组织能耗',
                        id: 'dept',
                    },
                    {
                        name: '分项能耗',
                        id: 'category',
                    },
                ],
                dosageType: '',
                typeData: [{
                        name: '电',
                        id: 1,
                    },
                    {
                        name: '水',
                        id: 2,
                    },
                    // {
                    //     name: '气',
                    //     id: 3,
                    // },
                ],
                currentType: 1,
                showOrder: false,
                customer: {
                    page: 1,
                    size: 10,
                    total: 0,
                },
                keyWord: '',
                selectedMonth: '',
                YOY: {
                    xData: [],
                    thisPeriod: [],
                    firstHalf: [],
                    unit: 'KWh',
                    title: '同比',
                },
                MOM: {
                    xData: [],
                    thisPeriod: [],
                    firstHalf: [],
                    unit: 'KWh',
                    title: '环比',
                },
                treeData: [],
                props: {
                    label: 'name',
                    value: 'id',
                },
                nodeId: '',
                unitName: 'kwh',
            })
            onMounted(() => {
                changeDosage(state.dosageTab[0].id)
                state.selectedMonth = dayjs().format('YYYY-MM')
                getOverviewChain()
            })
            const projectId = computed(() => {
                return store.state.user.projectId || getCookie('gh_projectId')
            })
            watch(() => state.keyWord, (newVal, oldVal) => {
                proxy.$refs.tree.filter(newVal);
            })
            watch(projectId, (val) => {
                if (val) {
                    changeDosage(state.dosageTab[0].id)
                }
            })
            // 过滤
            const filterNode = (value, data) => {
                if (!value) return true;
                return data.name.indexOf(value) !== -1;
            }
            const changeDosage = (id) => {
                state.dosageType = id
                switch (id) {
                    case 'area':
                        getProjectAreaTree()
                        break
                    case 'dept':
                        getDeptList()
                        break
                    case 'category':
                        getCategoryTree()
                        break
                }
            }
            const changeMaterType = (id) => {
                if(id==2)
                {
                    state.unitName = 't'
                }else{
                    state.unitName = 'kwh'
                }
                state.currentType = id
                switch (id) {
                    case 1:
                        state.MOM.unit = state.YOY.unit = 'KWh'
                        break
                    case 2:
                        state.MOM.unit = state.YOY.unit = 't'
                        break
                    case 3:
                        state.MOM.unit = state.YOY.unit = 'kwh'
                }
                getOverviewChain()
            }
            // 获取区域
            const getProjectAreaTree = () => {
                proxy.$api.getProjectArea({
                    projectId: getCookie("gh_projectId")
                }).then((res) => {
                    state.treeData = res.data
                })
            }
            // 获取公司列表
            const getDeptList = () => {
                proxy.$api.getDeptTree({
                    projectId: getCookie("gh_projectId")
                }).then((res) => {
                    state.treeData = res.data
                })
            }
            // 获取分项
            const getCategoryTree = () => {
                proxy.$api.getCategory({
                    projectId: getCookie("gh_projectId")
                }).then((res) => {
                    state.treeData = res.data
                })
            }
            const getOverviewChain = () => {
                let params = {
                    bt: dayjs(state.selectedMonth).startOf('month').format('YYYY-MM-DD HH:mm:ss'),
                    et: dayjs(state.selectedMonth).endOf('month').format('YYYY-MM-DD HH:mm:ss'),
                    deviceType: state.currentType,
                    measurement: state.dosageType,
                    id: state.nodeId,
                }
                proxy.$api.overviewChain(params).then((res) => {
                    state.YOY.xData = []
                    state.YOY.thisPeriod = []
                    state.YOY.firstHalf = []
                    state.MOM.xData = []
                    state.MOM.thisPeriod = []
                    state.MOM.firstHalf = []
                    if (res.data) {
                        res.data.thisChartData.forEach(item => {
                            let time = item.ts.split(' ')[0].split('-')
                            state.YOY.xData.push(time[1] + '-' + time[2]);
                            state.MOM.xData.push(time[1] + '-' + time[2]);
                            state.YOY.thisPeriod.push(item._aggregate.toFixed(2));
                            state.MOM.thisPeriod.push(item._aggregate.toFixed(2));
                            if (res.data.lastChartData.length == 0) {
                                state.MOM.firstHalf.push(0);
                            } else {
                                let data = res.data.lastChartData.find(d => dayjs(d.time).add(1, 'month').format('YYYY-MM-DD HH:mm:ss') == item.ts);
                                if (data) {
                                    state.MOM.firstHalf.push(data._aggregate.toFixed(2));
                                } else {
                                    state.MOM.firstHalf.push(0);
                                }
                            }
                            if (res.data.yearChartData.length == 0) {
                                state.YOY.firstHalf.push(0);
                            } else {
                                let data = res.data.yearChartData.find(d => dayjs(d.time).add(1, 'year').format('YYYY-MM-DD HH:mm:ss') == item.ts);
                                if (data) {
                                    state.YOY.firstHalf.push(data._aggregate.toFixed(2));
                                } else {
                                    state.YOY.firstHalf.push(0);
                                }
                            }
                        });
    
                    }
                })
            }
            const clickOption = (node, data) => {
                if (data.checked) {
                    tree.value.setCheckedKeys([])
                } else {
                    tree.value.setCheckedKeys([node.id])
                }
                setCurrentNode()
            }
            const checkOption = (node, data, type) => {
                if (data.checkedKeys.includes(node.id)) {
                    tree.value.setCheckedKeys([node.id])
                } else {
                    tree.value.setCheckedKeys([])
                }
                setCurrentNode([node.fullPath])
            }
            const setCurrentNode = ([fullPath]) => {
                let ids = [fullPath]
                state.nodeId = ids.length > 0 ? ids[0] : ''
                getOverviewChain(fullPath)
            }
            return {
                ...toRefs(state),
                tree,
                setCurrentNode,
                checkOption,
                clickOption,
                getOverviewChain,
                getCategoryTree,
                getDeptList,
                getProjectAreaTree,
                changeMaterType,
                changeDosage,
                filterNode,
                projectId
            }
        }
    }
    </script>
    
    <style lang="scss" scoped>
    .energy_container {
        padding-top: 100px;
        padding-bottom: 170px;
        width: 100%;
    
        .content_box {
            width: 100%;
            height: calc(100% - 50px);
            display: flex;
            justify-content: space-between;
    
            .energy_container_left {
                width: 377px;
                padding: 8px;
                margin-right: 15px;
                border: 1px solid #2b2e32;
                overflow: hidden;
    
                .type-list {
                    width: 100%;
                    height: calc(100% - 50px);
                    overflow: auto;
                }
            }
    
            .energy_container_right {
                width: calc(100% - 377px);
                height: 100%;
    
                .header_search {
                    height: 50px;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 20px;
                    color: #abb9cb;
                }
    
                .chart-box {
                    flex: 1;
                    height: 50%;
                }
            }
        }
    
        :deep() {
            .el-input {
                --el-input-bg-color: #021424 !important;
                --el-input-icon-color: #7A9BBD !important;
                --el-input-placeholder-color: #7A9BBD !important;
                --el-input-hover-border-color: #3E5B7B !important;
                --el-border-color: #3E5B7B !important;
                --el-color-primary: #3E5B7B !important;
            }
    
            .el-input__inner {
                color: #7A9BBD;
                font-size: 16px;
                font-family: "Alibaba-PuHuiTi";
                font-weight: normal;
            }
        }
    
    }
    </style>
    