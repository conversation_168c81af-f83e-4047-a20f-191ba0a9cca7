<template>
    <div class="energy_container z100">
        <div class="energy_container_left">
            <div class="container_header">
                <!-- <el-input v-model="keyWord" placeholder="请输入区域名称" >
                    <template #suffix>
                        <i class="el-input__icon el-icon-search" />
                    </template>
                </el-input> -->
                <div class="input">
                    <el-input v-model="keyword"  prefix-icon="Search" placeholder="按设备名称搜索">
                    </el-input>
                </div>
            </div>
            <div class="type-list">
                <el-scrollbar class="tree">
                    <el-tree ref="tree" :data="treeData" node-key="id" default-expand-all check-strictly show-checkbox :filter-node-method="filterNode" @node-click="clickOption" @check="checkOption" />
                </el-scrollbar>
            </div>
        </div>
        <div class="energy_container_right">
            <el-form :inline="true" :model="formInline" class="search_box form_inline" size="small">
                <el-form-item label="时间选择">
                    <el-date-picker v-model="formInline.selectedDate" @change="getReadMeterRecordList" type="daterange" value-format="YYYY-MM-DD" start-placeholder="开始时间" end-placeholder="结束时间"></el-date-picker>
                </el-form-item>
                <type-btn :typeData="productId" @clickDeviceType="handeleDeviceType" />
                <div class="tabs">
                    <tabs :typeData="dateType" @clickDeviceType="changeDate" />
                </div>
            </el-form>
            <div class="table recordTable">
                <el-table :data="tableData" height="calc(100% - 90px)" :row-class-name="tableRowClassName" :header-cell-style="{
                background: '#223644',
                color: '#C7DFFF',
                fontSize: '14px',
                fontFamily: 'PingFangSC-Medium',
                fontWeight: '500'
              }">
                    <template #empty>
                        <no-data />
                    </template>
                    <el-table-column type="index" label="序号" align="center"></el-table-column>
                    <el-table-column prop="name" label="设备名称" align="center"></el-table-column>
                    <el-table-column prop="categoryname" label="所属分类" align="center"></el-table-column>
                    <el-table-column prop="areaname" label="所属区域" align="center"></el-table-column>
                    <el-table-column prop="featurename" label="所属功能" align="center"></el-table-column>
                    <el-table-column prop="deptname" label="所属部门" align="center"></el-table-column>
                    <el-table-column prop="ts" label="本次抄表时间" align="center"></el-table-column>
                    <el-table-column prop="lastvalue" label="上次计算度数" align="center"></el-table-column>
                    <el-table-column prop="thisvalue" label="本次计算度数" align="center"></el-table-column>
                    <el-table-column :label="'用量'+unitName" align="center">
                        <template #default="scope">
                            <span :style="yl">{{scope.row._value}}</span>
                        </template>
                    </el-table-column>
                </el-table>
                <div class="page center">
                    <el-pagination :page-size="size" :current-page="page"  layout="prev, pager, next" @current-change="handleCurrentChange" :total="total">
                    </el-pagination>
                </div>
            </div>
        </div>
    </div>
    </template>
    
    <script>
    import {
        getCookie
    } from '@/utils/cookie'
    import dayjs from 'dayjs'
    import typeBtn from '@/components/energy/typeBtn'
    import tabs from '@/components/energy/tabs.vue'
    import {
        reactive,
        toRefs,
        onMounted,
        ref,
        getCurrentInstance,
        watch,
        computed
    } from 'vue'
    import {
        useStore
    } from 'vuex'
    export default {
        name: 'readMeterRecord',
        components: {
            typeBtn,
            tabs
        },
        setup() {
            const tree = ref(null)
            const {
                proxy
            } = getCurrentInstance()
            const store = useStore()
            const state = reactive({
                objStyle: {
                    'fontSize': '25px',
                    'fontFamily': "'DINAlternate-Bold', 'DINAlternate'",
                    'fontWeight': 'bold'
                },
                yl: {
                    'fontSize': '25px',
                    'fontFamily': "'DINAlternate-Bold', 'DINAlternate'",
                    'fontWeight': 'bold',
                    'color': '#E3731B'
                },
                tableHeight: window.innerHeight * 0.65,
                treeData: [],
                props: {
                    label: 'name',
                },
                id: "",
                tag: 'Hour',
                formInline: {
                    selectedDate: []
                },
                page: 1,
                size: 10,
                total: 0,
                productId: [{
                        name: '电',
                        id: 1,
                    },
                    {
                        name: '水',
                        id: 2,
                    },
                    {
                        name: '气',
                        id: 3,
                    },
                ],
                unitName: '/kWh',
                // Hour  Day   Month  Year
                dateType: [{
                        name: '时',
                        id: 'Hour',
                    },
                    {
                        name: '天',
                        id: 'Day',
                    },
                    {
                        name: '月',
                        id: 'Month',
                    }, {
                        name: '年',
                        id: 'Year'
                    }
                ],
                meterType: 1,
                tableData: [],
                keyWord: ''
            })
            onMounted(() => {
                getProjectAreaList()
                state.formInline.selectedDate = [
                    dayjs().startOf('month') .format('YYYY-MM-DD'),dayjs().format('YYYY-MM-DD') ]
                handeleDeviceType(state.meterType)
            })
            const projectId = computed(() => {
                return store.state.user.projectId || getCookie('gh_projectId')
            })
            watch(projectId, (val) => {
                if (val) {
                    getProjectAreaList()
                }
            })
            watch(() => state.keyWord, (newVal, oldVal) => {
                proxy.$refs.tree.filter(newVal);
            })
            // 过滤
            const filterNode = (value, data) => {
                if (!value) return true;
                return data.label.indexOf(value) !== -1;
            }
            const tableRowClassName = ({
                row,
                rowIndex
            }) => {
                if ((rowIndex + 1) % 2 === 0) {
                    return 'succ'
                }
                return ''
            }
            const getProjectAreaList = () => {
                proxy.$api.getEnergyDeviceTree({
                    projectId: getCookie("gh_projectId")
                }).then((res) => {
                    state.treeData = res.data
                })
            }
            const handeleDeviceType = (id) => {
                if(id==2)
                {
                    state.unitName = '/t'
                }else{
                    state.unitName = '/kWh'
                }
                state.meterType = id
                getReadMeterRecordList()
            }
            const changeDate = (data) => {
                state.tag = data
                getReadMeterRecordList()
            }
            const getReadMeterRecordList = () => {
                let params = {
                    bt: dayjs(state.formInline.selectedDate[0]).format('YYYY-MM-DD 00:00:00'),
                    et: dayjs(state.formInline.selectedDate[1]).format('YYYY-MM-DD 23:59:59'),
                    id: state.id,
                    tag: state.tag,
                    page: state.page,
                    size: state.size,
                    type: state.meterType,
                }
                proxy.$api.getReadMeterRecord(params).then((res) => {
                    state.tableData = res.data
                    state.total = res.total
                })
            }
            const handleCurrentChange = (page) => {
                state.page = page
                getReadMeterRecordList()
            }
            const clickOption = (node, data) => {
                if (data.checked) {
                    tree.value.setCheckedKeys([])
                } else {
                    tree.value.setCheckedKeys([node.id])
                }
                setCurrentNode()
            }
            const checkOption = (node, data, type) => {
                if (data.checkedKeys.includes(node.id)) {
                    tree.value.setCheckedKeys([node.id])
                } else {
                    tree.value.setCheckedKeys([])
                }
                setCurrentNode()
            }
            const setCurrentNode = () => {
                let ids = tree.value.getCheckedKeys()
                state.id = ids.length > 0 ? ids[0] : ''
                getReadMeterRecordList()
            }
            return {
                ...toRefs(state),
                tree,
                getProjectAreaList,
                handeleDeviceType,
                getReadMeterRecordList,
                handleCurrentChange,
                clickOption,
                checkOption,
                setCurrentNode,
                changeDate,
                tableRowClassName,
                filterNode,
                projectId
            }
        },
    }
    </script>
    
    <style lang="scss" scoped>
    .energy_container {
        display: flex;
        justify-content: space-between;
        padding-top: 100px;
        padding-bottom: 140px;
        width: 100%;
    
        &_left {
            width: 377px;
            border: 1px solid #33383b;
            padding: 8px;
            margin-right: 15px;
    
            .type-list {
                width: 100%;
                height: calc(100% - 50px);
                overflow: auto;
            }
        }
    
        &_right {
            width: calc(100% - 377px);
            height: 100%;
    
            .tabs {
                display: flex;
                flex: 1;
                justify-content: flex-end;
            }
    
            .header_search {
                height: 40px;
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 20px;
            }
        }
    
    
        
    }
    :deep(){
        .el-input {
        --el-input-bg-color: #021424 !important;
        --el-input-icon-color: #7A9BBD !important;
        --el-input-placeholder-color: #7A9BBD !important;
        --el-input-hover-border-color: #3E5B7B !important;
        --el-border-color: #3E5B7B !important;
        --el-color-primary: #3E5B7B !important;
      }
    
      .el-input__inner {
        color: #7A9BBD;
        font-size: 16px;
        font-family: "Alibaba-PuHuiTi";
        font-weight: normal;
      }
    }
    </style>
    <style>
    .search_box {
        box-shadow: none;
    }
    </style>
    