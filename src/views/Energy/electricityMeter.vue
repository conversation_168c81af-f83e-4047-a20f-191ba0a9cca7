<template>
    <div class="z100 electricity_meter">
        <div class="left_meter">
            <el-scrollbar class="tree">
                <el-tree ref="tree" :data="treeData" node-key="id" check-strictly default-expand-all :props="props"
                    show-checkbox @check="checkOption" />
            </el-scrollbar>
        </div>
        <div style="width: 100%;">
            <div style="padding-left: 15px;" @click="changeMode">
                <span style="color: rgb(136, 156, 195);font-size: 24px;" class="iconfont iconview"></span>
            </div>
            <div class="right_meter">


                <div v-for="item in list" :key="item" class="list">
                    <meter-panel :data="item" :units="units" />
                </div>

            </div>
        </div>


    </div>
</template>

<script>
import meterPanel from './componet/meter.vue'
import {
    computed,
    getCurrentInstance,
    onMounted,
    reactive,
    ref,
    toRefs,
    watch,
    onUnmounted
} from "vue";
import {
    getCookie
} from '@/utils/cookie';
import {
    useStore
} from 'vuex';
export default {
    name: 'electricityMeter',
    components: {
        meterPanel
    },

    setup() {
        const tree = ref(null);
        const {
            proxy
        } = getCurrentInstance()
        const store = useStore()
        const state = reactive({
            treeData: [],
            list: [],
            props: {
                label: 'name',
            },
            nodeId: '',
            units: [],
            electric: true,
            interval: null,
            areaId: ''
        });
        onMounted(() => {
            getProjectAreaList()

            getDicUtilPage()

        })
        onUnmounted(() => {
            clearInterval(state.interval)
        })
        const projectId = computed(() => {
            return store.state.user.projectId || getCookie('gh_projectId')
        })
        watch(projectId, (val) => {
            if (val) getProjectAreaList()
        })

        const checkOption = (data, state1) => {
            let keys = tree.value.getCheckedKeys();
            if (keys.length == 0 && state1.checkedKeys.length == 0) {
                tree.value.setChecked(data.id, false);
                state.areaId=null;
                getDeviceStandardPage()
            } else {
                if (keys.length > 0)
                    keys.forEach(k => {
                        if (k != data.id) tree.value.setChecked(k, false);
                    });
                tree.value.setChecked(data.id, true);
                state.areaId = data.id;
                getDeviceStandardPage(data.id)

            }


        };

        const getProjectAreaList = () => {
            proxy.$api.getProjectArea({
                projectId: getCookie("gh_projectId")
            }).then((res) => {
                state.treeData = res.data
            })
        }
        const getDeviceStandardPage = (id) => {
            state.list = [];
            if (state.interval) {
                clearInterval(state.interval)
            }
            proxy.$api.getDeviceStandardList({
                areaId: state.areaId,
                code: state.electric ? 'queryECAmmeterStatus' : 'water',
                projectId: getCookie("gh_projectId")
            }).then(res => {
                const data = res.data
                state.list = data
                state.interval = setInterval(() => {
                    getReal()
                }, 1000 * 5)
            })
        }
        const getReal = (id) => {
            proxy.$api.getDeviceStandardList({
                areaId: state.areaId,
                code: state.electric ? 'queryECAmmeterStatus' : 'water',
                // 'code': 'kongtiao',
                projectId: getCookie("gh_projectId")
            }).then(res => {
                res.data.forEach((device, i) => {
                    device.deviceStandards.forEach((item, j) => {
                        state.list[i].deviceStandards[j].dataVal = item.dataVal
                    })
                })
            })
        }
        const getDicUtilPage = () => {
            proxy.$api.getDicUtil({
                dicCode: 'unit',
                projectId: getCookie('gh_projectId'),
            }).then((res) => {
                state.units = res.data
                getDeviceStandardPage()
            })
        }
        const changeMode = () => {
            state.electric = !state.electric
            if (state.interval) {
                clearInterval(state.interval)
            }
            getDeviceStandardPage()
        }

        return {
            ...toRefs(state),
            checkOption,
            getProjectAreaList,
            tree,
            projectId,
            changeMode,
            getDeviceStandardPage
        };
    },
};
</script>

<style lang="scss" scoped>
.electricity_meter {
    padding-top: 100px;
    padding-bottom: 140px;
    width: 100%;
    height: 100%;
    display: flex;
    background: linear-gradient(270deg, #000812 0%, rgba(0, 14, 30, 0.8) 72.19%, rgba(0, 15, 32, 0.5) 83.65%, rgba(0, 12, 26, 0) 100%);

    .left_meter {
        width: 280px;
        height: calc(100% - 40px);
        border: 1px solid #33383b;
        padding: 16px;
    }

    .right_meter {
        flex: 1;
        display: flex;
        flex-wrap: wrap;
        align-content: flex-start;
        margin-left: 15px;
        overflow-x: hidden;
        height: calc(100% - 40px);

        .list {
            width: calc(25% - 8px);
            margin: 4px;
        }
    }
}
</style>
