<template>
    <div class="energy_container z100">
        <div class="content_tab">
            <div class="left_tab">
                <span class="btn" v-for="(item, i) in dosageTab" :key="i" :class="{ active: dosageType == item.id }"
                    @click="changeDosage(item.id)">
                    {{ item.name }}
                </span>
            </div>
            <type-btn  :typeData="typeData" @clickDeviceType="changeMeterType" />
        </div>
        <div class="content_card">
            <div class="content_card_row">
                <div class="card_item card_box">
                    <sub-title2 title="日分项用量"></sub-title2>
                    <div class="card_chart">
                        <histogram :unitName="unitName" :chartData="dayChartData" />
                    </div>
                </div>
                <div class="card_item card_box">
                    <sub-title2 title="月分项用量"></sub-title2>
                    <div class="card_chart">
                        <histogram :unitName="unitName" :chartData="monthChartData" />
                    </div>
                </div>
                <div class="card_item card_box">
                    <sub-title2 title="年分项用量"></sub-title2>
                    <div class="card_chart">
                        <pie-chart :yData="pieData" />
                    </div>
                </div>
            </div>
            <div class="content_card_row">
                <div class="big_data">
                    <sub-title2 title="大数据用能分析">
                        <type-btn :typeData="dateTypeData" @clickDeviceType="selectDateType" />
                    </sub-title2>
                    <el-form :inline="true" class=" search_box" size="mini">
                        <el-form-item label="时间选择">
                            <el-date-picker @change="selectedDate" popper-class="select_panel" v-model="date"
                                :type="dateType" :value-format="showFormat" placeholder="请选择"></el-date-picker>
                        </el-form-item>
                        <el-form-item label="">

                            <el-cascader @change="changeArea" v-if="dosageType=='area'" popper-class="cascader" v-model="fullPath" style="width: 100%" :options="areas" clearable :props="props1" placeholder="请选择"></el-cascader>


                            <el-select v-else v-model="fullPath" @change="changeCategory">
                                <el-option v-for="item in list" :label="item.name" :value="item.fullPath"
                                    :key="item.fullPath"></el-option>
                            </el-select>

                        </el-form-item>
                    </el-form>
                    <div class="curve_chart">
                        <curve-chart :unitName="unitName" :chartData="analysisChartData" />
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import typeBtn from '@/components/energy/typeBtn'
import card from '@/components/energy/card'
import histogram from '@/components/energy/histogram'
import curveChart from '@/components/energy/curveChart'
import pieChart from '@/components/energy/pieChart'
import dayjs from 'dayjs'
import {
    reactive,
    toRefs,
    ref
} from 'vue'
import {
    getCurrentInstance,
    onMounted
} from '@vue/runtime-dom'
import { getCookie } from '@/utils/cookie'
export default {
    name: 'suboption',
    components: {
        typeBtn,
        card,
        histogram,
        curveChart,
        pieChart,
    },
    setup() {
        const date = ref('')
        const {
            proxy
        } = getCurrentInstance()
        const state = reactive({
            typeData: [{
                name: '电',
                id: 1,
            },
            {
                name: '水',
                id: 2,
            },
            {
                name: '气',
                id: 3,
            },
            ],
            unitName: 'kWh',
            meterType: 1,
            dosageTab: [{
                name: '分项用量',
                id: 'category',
            },
            {
                name: '用能用量',
                id: 'feature',
            },
            {
                name: '区域用量',
                id: 'area',
            },
            ],
            pieData: [{
                name: '照明',
                value: '4000',
            },
            {
                name: '空调插座',
                value: '3487',
            },
            {
                name: '动力',
                value: '2548',
            },
            {
                name: '特殊用电',
                value: '3245',
            },
            ],
            dosageType: null,

            analysisDate: [],
            dayChartData: {
                textData: [],
                firstHalf: {
                    name: '昨日',
                    data: [],
                },
                thisPeriod: {
                    name: '今日',
                    data: [],
                },
            },
            monthChartData: {
                textData: [],
                firstHalf: {
                    name: '上月',
                    data: [],
                },
                thisPeriod: {
                    name: '本月',
                    data: [],
                },
            },
            dateTypeData: [{
                name: '年',
                id: 'Year',
            },
            {
                name: '月',
                id: 'Month',
            },
            {
                name: '日',
                id: 'Day',
            },
            ],
            timeTag: 'Year',
            dateType: 'year',
            showFormat: 'YYYY',
            analysisChartData: {
                xData: [],
                yData: [],
            },
            fullPath: '',
            list: [],
            areas:[],
            props1: {
                label: 'name',
                value: 'fullPath',
                checkStrictly: true
            },
        })
        onMounted(async () => {
            state.dosageType = state.dosageTab[0].id
            selectDateType('Year')
            if (state.dosageType == "category") {
                await getCategory();
                getEnergyCategoryStatistic()
            } else if (state.dosageType == "feature") {
                await getDeptTree();
                getDeptEnergyStatistic()
            } else if (state.dosageType == "area") {
                await getArea();
                getEnergyStartStatistic()
            }

        })
        const changeDosage = async (id) => {
            state.dosageType = id
            switch  (id) {
                case 'category':
                    await getCategory();
                    getEnergyCategoryStatistic()
                    break
                case 'feature':
                    await getDeptTree();
                    getDeptEnergyStatistic()
                    break
                case 'area':
                    await getArea();
                    getEnergyStartStatistic()
                    break
            }
            getEnergyAnalysis()
        }
        const changeMeterType = async (id) => {
            if(id==2)
            {
                state.unitName = 't';
            }else
            {
                state.unitName = 'kWh';
            }
            state.meterType = id
            switch (state.dosageType) {
                case 'category':
                    await getCategory()
                    getEnergyCategoryStatistic()
                    break
                case 'feature':
                    await getDeptTree()
                    getDeptEnergyStatistic()
                    break
                case 'area':
                    await getArea()
                    getEnergyStartStatistic()
                    break
            }
            getEnergyAnalysis()
        }
        const getEnergyStartStatistic = () => {
            proxy.$api.energyStartStatistic({
                deviceType: state.meterType,
            }).then((res) => {
                let textData = [],
                    dayFirstHalf = [],
                    dayThisPeriod = [],
                    monthFirstHalf = [],
                    monthThisPeriod = []
                state.pieData = []
                if (res.data && res.data.length) {
                    res.data.forEach((element) => {
                        textData.push(element.name)
                        dayFirstHalf.push(element.lastDay)
                        dayThisPeriod.push(element.currentDay)
                        monthFirstHalf.push(element.lastMonth)
                        monthThisPeriod.push(element.currentMonth)
                        state.dayChartData.textData = textData
                        state.monthChartData.textData = textData
                        state.dayChartData.firstHalf.data = dayFirstHalf
                        state.dayChartData.thisPeriod.data = dayThisPeriod
                        state.monthChartData.firstHalf.data = monthFirstHalf
                        state.monthChartData.thisPeriod.data = monthThisPeriod
                        let obj = {
                            name: element.name,
                            value: element.year,
                        }
                        state.pieData.push(obj)
                    })
                }
            })
        }
        const getEnergyCategoryStatistic = () => {
            proxy.$api.energyCategoryStatistic({
                deviceType: state.meterType,
            }).then((res) => {
                let textData = [],
                    dayFirstHalf = [],
                    dayThisPeriod = [],
                    monthFirstHalf = [],
                    monthThisPeriod = []
                state.pieData = []
                if (res.data && res.data.length) {
                    res.data.forEach((element) => {
                        if (state.dosageType == "area") {
                            if (!element.name.includes("/")) {
                                return;
                            } else {
                                element.name = element.name.split('/')[1]
                            }
                        }
                        textData.push(element.name)
                        dayFirstHalf.push(element.lastDay)
                        dayThisPeriod.push(element.currentDay)
                        monthFirstHalf.push(element.lastMonth)
                        monthThisPeriod.push(element.currentMonth)
                        state.dayChartData.textData = textData
                        state.monthChartData.textData = textData
                        state.dayChartData.firstHalf.data = dayFirstHalf
                        state.dayChartData.thisPeriod.data = dayThisPeriod
                        state.monthChartData.firstHalf.data = monthFirstHalf
                        state.monthChartData.thisPeriod.data = monthThisPeriod
                        let obj = {
                            name: element.name,
                            value: element.year,
                        }
                        state.pieData.push(obj)
                    })
                }
            })
        }
        const getDeptEnergyStatistic = () => {
            proxy.$api.featureEnergyStatistic({
                deviceType: state.meterType,
            }).then((res) => {
                let textData = [],
                    dayFirstHalf = [],
                    dayThisPeriod = [],
                    monthFirstHalf = [],
                    monthThisPeriod = []
                state.pieData = []
                if (res.data && res.data.length) {
                    res.data.forEach((element) => {
                        textData.push(element.name)
                        dayFirstHalf.push(element.lastDay)
                        dayThisPeriod.push(element.currentDay)
                        monthFirstHalf.push(element.lastMonth)
                        monthThisPeriod.push(element.currentMonth)
                        state.dayChartData.textData = textData
                        state.monthChartData.textData = textData
                        state.dayChartData.firstHalf.data = dayFirstHalf
                        state.dayChartData.thisPeriod.data = dayThisPeriod
                        state.monthChartData.firstHalf.data = monthFirstHalf
                        state.monthChartData.thisPeriod.data = monthThisPeriod
                        let obj = {
                            name: element.name,
                            value: element.year,
                        }
                        state.pieData.push(obj)
                    })
                }
            })
        }
        const getEnergyAnalysis = () => {
             let timeTag="Year";
            if (state.timeTag == "Year") {
                timeTag = "Month"
            }
            if (state.timeTag == 'Month') {
                timeTag = 'Day'
            }
            if (state.timeTag == "Day") {
                timeTag= "Hour"
            }
            let fullPath=state.fullPath;
            if(Array.isArray(state.fullPath)){
                fullPath = state.fullPath[state.fullPath.length-1]
            }
            proxy.$api.energyAnalysis({
                bt: dayjs(state.analysisDate[0]).format('YYYY-MM-DD HH:mm:ss'),
                et: dayjs(state.analysisDate[1]).format('YYYY-MM-DD HH:mm:ss'),
                deviceType: state.meterType,
                measurement: state.dosageType,
                timeTag: timeTag.toLocaleLowerCase(),
                fullPath: fullPath
            }).then((res) => {
                state.analysisChartData.xData = [],
                    state.analysisChartData.yData = []
                switch (state.dateType) {
                    case 'year':
                        state.analysisChartData.yData = new Array(12).fill(0)
                        state.analysisChartData.xData = [
                            date.value + '-01',
                            date.value + '-02',
                            date.value + '-03',
                            date.value + '-04',
                            date.value + '-05',
                            date.value + '-06',
                            date.value + '-07',
                            date.value + '-08',
                            date.value + '-09',
                            date.value + '-10',
                            date.value + '-11',
                            date.value + '-12',
                        ]
                        if (res.data && res.data.length) {
                            res.data.forEach((item) => {
                                state.analysisChartData.yData[dayjs(item.ts).month()] = item._aggregate
                            })
                        }
                        break
                    case 'month':
                        if (res.data && res.data.length) {
                            state.analysisChartData.yData = new Array(dayjs().daysInMonth()).fill(0);
                            for (let i = 0; i < dayjs().daysInMonth(); i++) {
                                state.analysisChartData.xData.push(dayjs().format("YYYY-MM-") + (i + 1));
                            }
                            res.data.forEach(item => {
                                state.analysisChartData.yData[dayjs(item.ts).date() - 1] = item._aggregate;

                            })
                        }

                        break
                    case 'date':
                        if (res.data && res.data.length) {
                            state.analysisChartData.yData = new Array(24).fill(0);
                            for (let i = 0; i < 24; i++) {
                                state.analysisChartData.xData.push(i + "时");
                            }
                            res.data.forEach(item => {
                                state.analysisChartData.yData[dayjs(item.ts).hour()] = item._aggregate;
                            })
                        }
                }
            })
        }
        const selectDateType = (data) => {
            state.timeTag = data
            switch (data) {
                case 'Year':
                    state.dateType = 'year'
                    date.value = dayjs().format('YYYY')
                    state.showFormat = 'YYYY'
                    state.analysisDate[0] = dayjs(date.value)
                        .startOf('year')
                        .format('YYYY-MM-DD 00:00:00')
                    state.analysisDate[1] = dayjs(date.value)
                        .endOf('year')
                        .format('YYYY-MM-DD 23:59:59')
                    break
                case 'Month':
                    state.dateType = 'month'
                    date.value = dayjs().format('YYYY-MM')
                    state.showFormat = 'YYYY-MM'
                    state.analysisDate[0] = dayjs(date.value)
                        .startOf('month')
                        .format('YYYY-MM-DD 00:00:00')
                    state.analysisDate[1] = dayjs(date.value)
                        .endOf('month')
                        .format('YYYY-MM-DD 23:59:59')

                    break
                case 'Day':
                    state.dateType = 'date'
                    date.value = dayjs().format('YYYY-MM-DD')
                    state.showFormat = 'YYYY-MM-DD'
                    state.analysisDate[0] = dayjs(date.value).format('YYYY-MM-DD 00:00:00')
                    state.analysisDate[1] = dayjs(date.value).format('YYYY-MM-DD 23:59:59')
                    break
            }
            getEnergyAnalysis()
        }
        const selectedDate = (option) => {
            switch (state.dateType) {
                case 'year':
                    state.analysisDate[0] = dayjs(option)
                        .startOf('year')
                        .format('YYYY-MM-DD 00:00:00')
                    state.analysisDate[1] = dayjs(option)
                        .endOf('year')
                        .format('YYYY-MM-DD 23:59:59')
                    break
                case 'month':
                    state.analysisDate[0] = dayjs(option)
                        .startOf('month')
                        .format('YYYY-MM-DD 00:00:00')
                    state.analysisDate[1] = dayjs(option)
                        .endOf('month')
                        .format('YYYY-MM-DD 23:59:59')
                    break
                case 'date':
                    state.analysisDate[0] = dayjs(option).format('YYYY-MM-DD 00:00:00')
                    state.analysisDate[1] = dayjs(option).format('YYYY-MM-DD 23:59:59')
                    break
            }
            getEnergyAnalysis()
        }

        const getCategory = async () => {
            const { data } = await proxy.$api.getCategory();
            state.list = data;
            state.fullPath = data[0].fullPath;
        }
        const getDeptTree = () => {
            proxy.$api.getFeature().then(res => {
                state.list = res.data;
                state.fullPath = res.data[0].fullPath;
            })
        }

        const getArea = async () => {
            const { data } = await proxy.$api.getProjectArea({
                projectId: getCookie('gh_projectId')
            });
            state.areas = getTreeData(data);
            state.fullPath = [state.areas[0].fullPath];
        }
        const getTreeData = (data) => {
            for (var i = 0; i < data.length; i++) {
                if (data[i].children.length < 1) {
                    data[i].children = undefined;
                } else {
                    getTreeData(data[i].children);
                }
            }
            return data;
        }

        const changeCategory = (val) => {
            if (val) {
                state.fullPath = val;
                getEnergyAnalysis();
            } else {
                state.analysisChartData.xData = [];
                state.analysisChartData.yData = []
            }

        }
        const changeArea = (val) => {
            if (val) {
                state.fullPath = val[val.length - 1];
                getEnergyAnalysis();
            } else {
                state.analysisChartData.xData = [];
                state.analysisChartData.yData = []
            }
        }


        return {
            ...toRefs(state),
            date,
            changeDosage,
            changeMeterType,
            getEnergyStartStatistic,
            getEnergyCategoryStatistic,
            selectDateType,
            getEnergyAnalysis,
            getDeptEnergyStatistic,
            selectedDate,
            changeCategory,
            changeArea
        }
    }
}
</script>

<style lang="scss" scoped>
.energy_container {
    padding-top: 100px;
    padding-bottom: 170px;
    width: 100%;

    .content_card {
        display: flex;
        flex-direction: column;
        height: calc(100% - 49px);
        padding: 0 15px;

        .big_data {
            width: 100%;
        }

        &_row {
            width: 100%;
            height: 50%;
            display: flex;
            justify-content: space-between;

            .card_item {
                width: calc((100% - 24px) / 3);
                height: 100%;
            }

            .card_chart {
                width: 100%;
                height: calc(100% - 49px);
            }

            .curve_chart {
                height: calc(100% - 92px);
            }
        }
    }
}
</style>
