<template>
<div class="energy_container z100">
    <div class="container_header">
        <div class="l_header">
            <span class="btn" v-for="(item, i) in dosageTab" :key="i" :class="{ active: dosageType == item.id }" @click="changeDosage(item.id)">{{ item.name }}</span>
        </div>
        <div class="r_header">
            <el-form :inline="true" class="form-inline" :model="formInline">
                <el-form-item label="时间选择">
                    <el-date-picker v-model="formInline.selectedDate" @change="changeDate" type="daterange" value-format="yyyy-MM-dd" start-placeholder="开始时间" end-placeholder="结束时间">
                    </el-date-picker>
                </el-form-item>
                <el-button type="text" class="searchBtn" @click="getEnergyFlow" style="margin-left: 10px">查询</el-button>
                <el-button type="text" class="searchBtn" @click="download" style="margin-left: 10px">导出</el-button>
            </el-form>
            <type-btn :typeData="typeData" @clickDeviceType="changeMeterType" />
        </div>
    </div>
    <div class="content_card">
        <bar class="chart" :chartData="chartData" ref="reportChart"></bar>
        <div class="table">
            <el-table :data="tableData" :header-cell-style="{
          background: 'rgba(255, 255, 255, 0.03)',
          boxShadow: '0px 1px 0px 0px rgba(240, 240, 240, 0.2)',
          color: '#fff',
          fontSize: '14px',
          fontFamily: 'PingFangSC-Medium',
          fontWeight: '500'
        }" height="100%">
                <template #empty>
                    <no-data />
                </template>
                <el-table-column prop="time" label="日期" align="center"></el-table-column>
                <el-table-column prop="name" label="名称" align="center"></el-table-column>
                <el-table-column prop="value" label="数值" align="center"></el-table-column>
            </el-table>
        </div>
    </div>
</div>
</template>

<script>
import typeBtn from "@/components/energy/typeBtn"
import bar from '@/components/energy/bar'
import dayjs from "dayjs"
import {
    saveAs
} from 'file-saver'
import {
    reactive,
    toRefs
} from '@vue/reactivity'
import {
    getCurrentInstance,
    onMounted
} from '@vue/runtime-dom'

export default {
    name:'energyReport',
    components: {
        typeBtn,
        bar
    },
    setup() {
        const {
            proxy
        } = getCurrentInstance()
        const state = reactive({
            chartData: {
                xAxis: null,
                data: null,
            },
            typeData: [{
                    name: '电',
                    id: 1
                },
                {
                    name: '水',
                    id: 2
                },
                // {
                //     name: '气',
                //     id: 3
                // }
            ],
            meterType: 1,
            dosageTab: [{
                    name: '分项用量',
                    id: 'category'
                },
                {
                    name: '功能用量',
                    id: 'dept'
                },
                {
                    name: '区域用量',
                    id: 'area'
                },
            ],
            dosageType: null,
            title: '',
            size: 10,
            total: 0,
            page: 1,
            tableData: [],
            formInline: {
                selectedDate: [],
            }
        })
        onMounted(() => {
            state.dosageType = state.dosageTab[0].id;
            state.formInline.selectedDate = [dayjs().startOf('month').format('YYYY-MM-DD'), dayjs().endOf('month').format('YYYY-MM-DD')]
            changeDosage(state.dosageType);
            getEnergyFlow();
        })
        const changeDate = () => {
            getEnergyFlow()
        }
        const changeDosage = (id) => {
            state.dosageType = id;
            getEnergyFlow();
        }
        const changeMeterType = (id) => {
            state.meterType = id;
            getEnergyFlow();
        }
        const download = () => {
            const params = {
                bt: dayjs(state.formInline.selectedDate[0]).format('YYYY-MM-DD 00:00:00'),
                et: dayjs(state.formInline.selectedDate[1]).format('YYYY-MM-DD 23:59:59'),
                deviceType: state.meterType,
                measurement: state.dosageType,
            }
            proxy.$api.energyFlowExcel(params).then(res => {
                const link = document.createElement('a')
                const blob = new Blob([res], {
                    type: 'application/vnd.ms-excel'
                })
                link.style.display = 'none'
                link.href = URL.createObjectURL(blob)
                link.setAttribute('download', `分项能耗.xlsx`)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link)
            })

        }
        const getEnergyFlow = () => {
            const params = {
                bt: dayjs(state.formInline.selectedDate[0]).format('YYYY-MM-DD 00:00:00'),
                et: dayjs(state.formInline.selectedDate[1]).format('YYYY-MM-DD 23:59:59'),
                deviceType: state.meterType,
                measurement: state.dosageType,
            }
            proxy.$api.energyFlow(params).then(res => {
                state.tableData =[];
                state.chartData.xAxis = [];
                state.chartData.data = [];
                for (let i = 0; i < res.data.length; i++) {
                    state.tableData.push({
                        name:res.data[i].name,
                        value:res.data[i].value ? res.data[i].value.toFixed(2) : res.data[i].value,
                        time:dayjs(state.formInline.selectedDate[1]).format('YYYY-MM')
                    });
                    state.chartData.xAxis.push(res.data[i].name);
                    state.chartData.data.push(res.data[i].value ? res.data[i].value.toFixed(2) : res.data[i].value);
                }
            })
        }
        return {
            ...toRefs(state),
            changeDate,
            changeDosage,
            changeMeterType,
            download,
            getEnergyFlow
        }
    }
}
</script>

<style lang="scss" scoped>
.energy_container {
    display: flex;
    flex-direction: column;
    padding-top: 100px;
    padding-bottom: 170px;
    width: 100%;
    background:#000812 ;

    .container_header {
        width: 100%;
        height: 40px;
        background: rgba(68, 114, 141, 0.1);
        box-shadow: 0px 1px 0px 0px rgba(199, 223, 255, 0.5);
        margin-bottom: 15px;
        display: flex;
        justify-content: space-between;
        color: #fff;

        .l_header {
            display: flex;

            .btn {
                display: inline-block;
                cursor: pointer;
                height: 40px;
                color: rgba(255, 255, 255, 0.5);
                font-size: 14px;
                padding: 0 16px;
                line-height: 40px;
                background: transparent;
                border: none;

                &.active {
                    background: linear-gradient(180deg,
                            rgba(199, 223, 255, 0) 0%,
                            rgba(199, 223, 255, 0.3) 100%);
                    box-shadow: 0px 1px 0px 0px #c7dfff;
                }
            }
        }

        .r_header {
            display: flex;
        }

        .el-date-editor--month .el-input__inner {
            height: 36px !important;
            line-height: 36px !important;
        }
    }

    .content_card {
        padding: 0 10px;
        height: calc(100% - 55px);

        .chart {
            height: 50%;
        }

        .table {
            height: calc(50% - 15px);
            margin-top: 15px;
        }
    }
}
</style>
