<template>
<div class="energy_container z100">
    <div class="wrapper">
        <div class="container_header">
            <div class="l_header">
                <span v-for="item in tabs" :key="item.id" @click="handleMonth(item.id)" class="btn" :class="{ active: monthBtn == item.id }">{{ item.name }}</span>
            </div>
            <div class="r_header">
                <el-form :inline="true" class="form-inline">
                    <el-form-item label="时间选择">
                        <el-date-picker v-model="selectedMonth" @change="changeMonth" type="monthrange" start-placeholder="开始月份" end-placeholder="结束月份" />
                    </el-form-item>
                </el-form>
                <type-btn :typeData="deviceType" @clickDeviceType="handeleDeviceType" />
                <el-button icon="el-icon-download" class="downLoad" @click="downLoadSprintTesReport">下载报告</el-button>
            </div>
        </div>
        <div class="report_content">
            <div id="download_file" ref="auditReport">
                <div class="card_list">
                    <sub-title2 title="峰平谷分布"></sub-title2>
                    <div class="energy_container_body">
                        <div class="body_content">
                            <div class="content_result">
                                <div><i class="iconfont iconxiaolian"></i></div>
                                <div class="result_btn">占比合理，继续保持！</div>
                            </div>
                        </div>
                        <div class="body_content">
                            <div class="content_text">
                                <div class="chart_title">峰平谷分布图</div>
                            </div>
                            <div class="content_result">
                                <distributionChart></distributionChart>
                            </div>
                        </div>
                        <div class="body_content">
                            <div class="content_text">
                                <div class="chart_title">能耗时段分布</div>
                                <div class="ratio">
                                    峰平谷时段能耗占比分别为：{{
                      elecDistribution.fProportion
                    }}，{{ elecDistribution.pProportion }}，{{
                      elecDistribution.gProportion
                    }}
                                </div>
                            </div>
                            <div class="content_result">
                                <pie-chart :yData="pieData" />
                            </div>
                        </div>
                    </div>
                </div>
                <!-- <div class="card_list">
                    <sub-title2 title="用能指标"></sub-title2>
                    <div class="energy_container_body">
                        <div class="body_content">
                            <div class="content_result">
                                <div><i class="iconfont iconxiaolian"></i></div>
                                <div class="result_btn">占比合理，继续保持！</div>
                                <div class="content_text" v-for="(item, i) in targetData" :key="i">
                                    <div class="msg">{{ item.text }}</div>
                                </div>
                            </div>
                        </div>
                        <div class="body_content">
                            <div class="content_text">
                                <div class="chart_title">公共建筑用能指南</div>
                            </div>
                            <Ranking :yData="rankData" :xData="seriesData" />
                        </div>
                        <div class="body_content total">
                            <div class="energy_total">
                                能耗总量：
                                <span class="num">{{ dept.energyAllNum }}{{ reportType==1?'kwh':'m³' }}</span>
                            </div>
                        </div>
                    </div>
                </div> -->
                <div class="card_list">
                    <sub-title2 title="区域能耗"></sub-title2>
                    <div class="energy_container_body">
                        <div class="body_content">
                            <div class="content_result">
                                <div>
                                    <i class="iconfont iconiconfront-"></i>
                                </div>
                                <div class="result_btn">占比一般，有待提升！</div>
                                <div class="content_text">
                                    <div class="msg">{{ areaIncreaseRate }}</div>
                                </div>
                            </div>
                        </div>
                        <div class="body_content">
                            <div class="content_text">
                                <div class="chart_title">区域能耗排行</div>
                            </div>
                            <div class="top">
                                <div class="item" v-for="(item, i) in area.energyList" :key="i" v-show="i<=2">
                                    <div class="item_header">
                                        <span class="item_tit">{{ i + 1 + '/' }}</span>
                                        {{ item.name }}-{{item.value?item.value.toFixed(2):item.value}}----{{area.energyAllNum}}
                                    </div>
                                    <div class="item_chart">
                                        <el-progress :percentage="
                          areaProportion(area.energyAllNum, item.value)
                        " />
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="body_content"></div>
                    </div>
                </div>
                <div class="card_list">
                    <sub-title2 title="用途能耗"></sub-title2>
                    <div class="energy_container_body">
                        <div class="body_content">
                            <div class="content_result">
                                <div><i class="iconfont iconiconfront-"></i></div>
                                <div class="result_btn">占比一般，有待提升！</div>
                                <div class="content_text">
                                    <div class="msg">{{ categoryIncreaseRate }}</div>
                                </div>
                            </div>
                        </div>
                        <div class="body_content">
                            <div class="content_text">
                                <div class="chart_title">用途能耗排行</div>
                            </div>
                            <div class="top">
                                <div class="item" v-for="(item, i) in category.energyList" :key="i">
                                    <div class="item_header">
                                        <span class="item_tit">{{ i + 1 + '/' }}</span>
                                        {{ item.name }}--{{ item.value }}
                                    </div>
                                    <div class="item_chart">
                                        <el-progress :percentage="
                          areaProportion(category.energyAllNum, item.value)
                        " />
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="body_content"></div>
                    </div>
                </div>
                <div class="card_list">
                    <sub-title2 title="组织能耗"></sub-title2>
                    <div class="energy_container_body">
                        <div class="body_content">
                            <div class="content_result">
                                <div><i class="iconfont iconiconfront-"></i></div>
                                <div class="result_btn">占比一般，有待提升！</div>
                                <div class="content_text">
                                    <div class="msg">{{ deptIncreaseRate }}</div>
                                </div>
                            </div>
                        </div>
                        <div class="body_content">
                            <div class="content_text">
                                <div class="chart_title">组织能耗排行</div>
                            </div>
                            <div class="top">
                                <div class="item" v-for="(item, i) in dept.energyList" :key="i">
                                    <div class="item_header">
                                        <span class="item_tit">{{ i + 1 + '/' }}</span>
                                        {{ item.name }}--{{ item.value }}
                                    </div>
                                    <div class="item_chart">
                                        <el-progress :percentage="
                          areaProportion(dept.energyAllNum, item.value)
                        " />
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="body_content"></div>
                    </div>
                </div>
                <!-- <div class="card_list">
            <sub-title title="能源流向"></sub-title>
            <div class="energy_container_body">
              <div class="flow_table">
                <table border="1" cellspacing="0" cellpadding="10">
                  <tr>
                    <th>购入</th>
                    <th colspan="6">分配</th>
                  </tr>
                  <tr>
                    <th :rowspan="tableData.length + 1">3000Kwh</th>
                    <th colspan="2"></th>
                    <th>空调插座</th>
                    <th>照明</th>
                    <th>动力</th>
                    <th>特殊用电</th>
                  </tr>
                  <tr v-for="(item, i) in tableData" :key="i">
                    <td>{{ item.area }}</td>
                    <td>{{ item.allEnergy }}</td>
                    <td>{{ item.columnOne }}</td>
                    <td>{{ item.columnTwo }}</td>
                    <td>{{ item.columnThree }}</td>
                    <td>{{ item.columnFour }}</td>
                  </tr>
                </table>
              </div>
            </div>
          </div> -->
            </div>
        </div>
    </div>
</div>
</template>

<script>
import distributionChart from '@/components/energy/distributionChart'
import Ranking from "@/components/energy/Ranking"
import typeBtn from "@/components/energy/typeBtn"
import card from "@/components/energy/card"
import pieChart from "@/components/energy/pieChart"
import dayjs from "dayjs"
import {
    reactive,
    toRefs,
    onMounted,
    ref,
    getCurrentInstance
} from 'vue'
import getPdfFromHtml from '@/utils/htmlToPdf'
export default {
    name: 'monthReport',
    components: {
        card,
        pieChart,
        typeBtn,
        distributionChart,
        Ranking
    },
    setup() {
        const auditReport = ref(null)
        const {
            proxy
        } = getCurrentInstance()
        const state = reactive({
            deviceType: [{
                    name: '电',
                    id: 1
                },
                {
                    name: '水',
                    id: 2
                },
                // {
                //     name: '气',
                //     id: 3
                // }
            ],
            tabs: [{
                name: '本月',
                id: 0
            }, {
                name: '上月',
                id: 1
            }],
            pieData: [{
                    name: '峰时段',
                    value: 0
                },
                {
                    name: '平时段',
                    value: 0
                },
                {
                    name: '谷时段',
                    value: 0
                }
            ],
            elecDistribution: {
                fProportion: 0,
                pProportion: 0,
                gProportion: 0
            },
            targetData: [{
                text: "本审计周期您的单位面积耗电为128度/平方米.年，低于先进值。高于平均值"
            }],
            reportType: 1,
            reportInforTitle: '',
            monthHeader: '',
            tableData: [],
            monthBtn: 0,
            selectedMonth: [],
            auditReportDate: [],
            area: {
                energyList: [],
                energyAllNum: 0,
                comparedData: {}
            },
            category: {
                energyList: [],
                energyAllNum: 0,
                comparedData: {}
            },
            dept: {
                energyList: [],
                energyAllNum: 0,
                comparedData: {}
            },
            rankData: ['平均值', '先进值', '合理值'],
            seriesData: [999, 888, 777],
        })
        onMounted(() => {
            handeleDeviceType(state.reportType)
            handleMonth(0)
        })
        const handleMonth = (data) => {
            state.monthBtn = data;
            switch (data) {
                case 0:
                    state.auditReportDate[0] = dayjs().startOf('month').format('YYYY-MM-DD 00:00:00');
                    state.auditReportDate[1] = dayjs().endOf('month').format('YYYY-MM-DD 23:59:59');
                    state.monthHeader = dayjs().format('YYYY-MM')
                    break
                case 1:
                    state.auditReportDate[0] = dayjs().add(-1, 'month').startOf('month').format('YYYY-MM-DD 00:00:00');
                    state.auditReportDate[1] = dayjs().add(-1, 'month').endOf('month').format('YYYY-MM-DD 23:59:59');
                    state.monthHeader = dayjs().format('YYYY-MM')
                    break
            }
            getTimeLimitAllPeriod();
            getEnergyFlow();
            getData()
        }
        const handeleDeviceType = (data) => {
            state.reportType = data;
            switch (data) {
                case 1:
                    state.reportInforTitle = '电能耗月报';
                    break
                case 2:
                    state.reportInforTitle = '水能耗月报';
                    break
                case 3:
                    state.reportInforTitle = '气能耗月报';
                    break
            }
        }
        const changeMonth = (option) => {
            if (option != null) {
                state.auditReportDate[0] = dayjs(option[0]).startOf('month').format('YYYY-MM-DD 00:00:00');
                state.auditReportDate[1] = dayjs(option[1]).endOf('month').format('YYYY-MM-DD 23:59:59');
                state.monthHeader = option[0] + '-' + option[1]
                getTimeLimitAllPeriod();
                getEnergyFlow();
                getData()
            }
        }
        const downLoadSprintTesReport = () => {
            getPdfFromHtml(auditReport.value, state.reportInforTitle)
        }
        const getTimeLimitAllPeriod = () => {
            proxy.$api.timeLimitAllPeriod({
                bt: state.auditReportDate[0],
                et: state.auditReportDate[1],
                type: state.reportType
            }).then(res => {
                let data = res.data;
                if (data && typeof data == 'object' && data.length > 0) {
                    state.pieData = [{
                            name: '峰时段',
                            value: data[0].fValue
                        },
                        {
                            name: '平时段',
                            value: data[0].pValue
                        },
                        {
                            name: '谷时段',
                            value: data[0].gValue
                        }
                    ]
                    state.elecDistribution.fProportion = Math.round(data[0].fvalue / data[0].value * 10000) / 100.00 + '%';
                    state.elecDistribution.pProportion = Math.round(data[0].pvalue / data[0].value * 10000) / 100.00 + '%';
                    state.elecDistribution.gProportion = Math.round(data[0].gvalue / data[0].value * 10000) / 100.00 + '%';
                }
            })
        }
        // 获取区域能耗
        const getEnergyFlow = () => {
            proxy.$api.energyFlow({
                bt: state.auditReportDate[0],
                et: state.auditReportDate[1],
                deviceType: state.reportType,
                measurement: 'area'
            }).then(res => {
                let params = res.data;
                // 获取区域分项能耗
                // proxy.$api.getAreaCategory({
                //   bt: state.auditReportDate[0],
                //   et: state.auditReportDate[1],
                //   type: state.reportType
                // }).then(result => {
                //   let data = result.data;
                //   state.tableData = [];
                //   params.forEach(element => {
                //     let obj = {
                //       area: "",
                //       allEnergy: "0Kwh",
                //       columnOne: "0Kwh",
                //       columnTwo: "0Kwh",
                //       columnThree: "0Kwh",
                //       columnFour: "0Kwh"
                //     }
                //     obj.area = element.fName;
                //     obj.allEnergy = element.value + 'Kwh';
                //     Object.keys(data).forEach((key, item) => {
                //       if (element.fName == key) {
                //         data[key].forEach(k => {
                //           switch (k.categoryName) {
                //             case '空调插座':
                //               obj.columnOne = k.value + 'Kwh';
                //               break
                //             case '照明':
                //               obj.columnTwo = k.value + 'Kwh';
                //               break
                //             case '动力':
                //               obj.columnThree = k.value + 'Kwh';
                //               break
                //             case '特殊用电':
                //               obj.columnFour = k.value + 'Kwh';
                //           }
                //         })
                //       }
                //     })
                //     state.tableData.push(obj)
                //   });
                // })
            })
        }
        // 获取区域分项能耗
        const areaDosageStatistic = () => {
            let params = {
                bt: state.auditReportDate[0],
                et: state.auditReportDate[1],
                deviceType: state.reportType,
                measurement: 'area'
            }
            proxy.$api.energyFlow(params).then(res => {
                state.area.energyList = res.data;
                state.area.energyAllNum = 0
                if (res.data && res.data.length > 0) {
                    res.data.forEach(item => {
                        state.area.energyAllNum = state.area.energyAllNum + item.value
                    })
                    areaDosageChain(res.data[0].codeField)
                }
            })
        }
        const areaDosageChain = (id) => {
            let params = {
                bt: state.auditReportDate[0],
                et: state.auditReportDate[1],
                deviceType: state.reportType,
                measurement: 'area',
                id: id
            }
            proxy.$api.dosageChain(params).then(res => {
                state.area.comparedData = res.data
            })
        }
        const categoryDosageStatistic = () => {
            let params = {
                bt: state.auditReportDate[0],
                et: state.auditReportDate[1],
                deviceType: state.reportType,
                measurement: 'category'
            }
            proxy.$api.energyFlow(params).then(res => {
                state.category.energyList = res.data;
                state.category.energyAllNum = 0
                if (res.data && res.data.length > 0) {
                    res.data.forEach(item => {
                        state.category.energyAllNum = state.category.energyAllNum + item.value
                    })
                    areaDosageChain(res.data[0].codeField)
                }
            })
        }
        const categoryDosageChain = () => {
            let params = {
                bt: state.auditReportDate[0],
                et: state.auditReportDate[1],
                deviceType: state.reportType,
                measurement: 'category',
                id: id
            }
            proxy.$api.dosageChain(params).then(res => {
                state.category.comparedData = res.data
            })
        }
        const deptDosageStatistic = () => {
            let params = {
                bt: state.auditReportDate[0],
                et: state.auditReportDate[1],
                deviceType: state.reportType,
                measurement: 'dept'
            }
            proxy.$api.energyFlow(params).then(res => {
                state.dept.energyList = res.data;
                state.dept.energyAllNum = 0
                if (res.data && res.data.length > 0) {
                    res.data.forEach(item => {
                        state.dept.energyAllNum = state.dept.energyAllNum + item.value
                    })
                    areaDosageChain(res.data[0].codeField)
                }
            })
        }
        const deptDosageChain = () => {
            let params = {
                bt: state.auditReportDate[0],
                et: state.auditReportDate[1],
                deviceType: state.reportType,
                measurement: 'dept',
                id: id
            }
            proxy.$api.dosageChain(params).then(res => {
                state.dept.comparedData = res.data
            })
        }
        const getData = () => {
            areaDosageStatistic();
            categoryDosageStatistic();
            deptDosageStatistic()
        }
        return {
            ...toRefs(state),
            auditReport,
            handleMonth,
            handeleDeviceType,
            changeMonth,
            downLoadSprintTesReport,
            getTimeLimitAllPeriod,
            getEnergyFlow,
            areaDosageStatistic,
            areaDosageChain,
            categoryDosageStatistic,
            categoryDosageChain,
            deptDosageStatistic,
            deptDosageChain,
            getData
        }
    },

    computed: {
        areaProportion() {
            return (allData, optionData) => {
                if (allData && optionData)
                    return Number(((optionData / allData) * 100).toFixed(2))
                else
                    return 0;
            }
        },
        areaIncreaseRate() {
            let chainIncrement, YOYIncrement, text //环同比增量
            if (this.area.energyList.length > 0 && this.area.energyList[0].value > 0 && this.area.comparedData.hasOwnProperty('chainValue') && this.area.comparedData.chainValue > 0) {
                chainIncrement = ((Number((this.area.energyList[0].value / this.area.comparedData.chainValue).toFixed(4))) - 1) * 100 + '%';
            } else {
                chainIncrement = 100 + '%'
            }
            if (this.area.energyList.length > 0 && this.area.energyList[0].value > 0 && this.area.comparedData.hasOwnProperty('lastYearValue') && this.area.comparedData.lastYearValue > 0) {
                YOYIncrement = ((Number((this.area.energyList[0].value / this.area.comparedData.lastYearValue).toFixed(4))) - 1) * 100 + '%';
            } else {
                YOYIncrement = 100 + '%'
            }
            if (this.area.energyList.length > 0) {
                text = '用量最多的区域为：' + this.area.energyList[0].fName + '，占总能耗的' + this.areaProportion(this.area.energyAllNum, this.area.energyList[0].value) + '%' + '，本区域能耗同比增长' + YOYIncrement + '，环比增长' + chainIncrement
            }

            return text
        },
        categoryIncreaseRate() {
            let chainIncrement, YOYIncrement, text //环同比增量
            if (this.category.energyList.length > 0 && this.category.energyList[0].value > 0 && this.category.comparedData.hasOwnProperty('chainValue') && this.category.comparedData.chainValue > 0) {
                chainIncrement = ((Number((this.category.energyList[0].value / this.category.comparedData.chainValue).toFixed(4))) - 1) * 100 + '%';
            } else {
                chainIncrement = 100 + '%'
            }
            if (this.category.energyList.length > 0 && this.category.energyList[0].value > 0 && this.category.comparedData.hasOwnProperty('lastYearValue') && this.category.comparedData.lastYearValue > 0) {
                YOYIncrement = ((Number((this.category.energyList[0].value / this.category.comparedData.lastYearValue).toFixed(4))) - 1) * 100 + '%';
            } else {
                YOYIncrement = 100 + '%'
            }
            if (this.category.energyList.length > 0) {
                text = '用量最多的区域为：' + this.category.energyList[0].fName + '，占总能耗的' + this.areaProportion(this.category.energyAllNum, this.category.energyList[0].value) + '%' + '，本区域能耗同比增长' + YOYIncrement + '，环比增长' + chainIncrement
            }

            return text
        },
        deptIncreaseRate() {
            let chainIncrement, YOYIncrement, text //环同比增量
            if (this.dept.energyList.length > 0 && this.dept.energyList[0].value > 0 && this.dept.comparedData.hasOwnProperty('chainValue') && this.dept.comparedData.chainValue > 0) {
                chainIncrement = ((Number((this.dept.energyList[0].value / this.dept.comparedData.chainValue).toFixed(4))) - 1) * 100 + '%';
            } else {
                chainIncrement = 100 + '%'
            }
            if (this.dept.energyList.length > 0 && this.dept.energyList[0].value > 0 && this.dept.comparedData.hasOwnProperty('lastYearValue') && this.dept.comparedData.lastYearValue > 0) {
                YOYIncrement = ((Number((this.dept.energyList[0].value / this.dept.comparedData.lastYearValue).toFixed(4))) - 1) * 100 + '%';
            } else {
                YOYIncrement = 100 + '%'
            }
            if (this.dept.energyList.length > 0) {
                text = '用量最多的区域为：' + this.dept.energyList[0].fName + '，占总能耗的' + this.areaProportion(this.dept.energyAllNum, this.dept.energyList[0].value) + '%' + '，本区域能耗同比增长' + YOYIncrement + '，环比增长' + chainIncrement
            }

            return text
        }
    }
}
</script>

<style lang="scss" scoped>
.energy_container {
    padding-top: 100px;
    padding-bottom: 170px;
    width: 100%;
    background:#000812 ;


    .wrapper {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
    }

    .container_header {
        width: 100%;
        height: 40px;
        background: rgba(68, 114, 141, 0.1);
        box-shadow: 0px 1px 0px 0px rgba(199, 223, 255, 0.5);
        margin-bottom: 15px;
        display: flex;
        justify-content: space-between;
        color: #fff;

        .l_header {
            display: flex;

            .btn {
                display: inline-block;
                cursor: pointer;
                height: 40px;
                color: rgba(255, 255, 255, 0.5);
                font-size: 14px;
                padding: 0 16px;
                line-height: 40px;
                background: transparent;
                border: none;

                &.active {
                    background: linear-gradient(180deg,
                            rgba(199, 223, 255, 0) 0%,
                            rgba(199, 223, 255, 0.3) 100%);
                    box-shadow: 0px 1px 0px 0px #c7dfff;
                }
            }
        }

        .r_header {
            display: flex;
        }

        .el-date-editor--month .el-input__inner {
            height: 36px !important;
            line-height: 36px !important;
        }
    }

    .report_content {
        padding: 0 15px;
        height: calc(100% - 40px);
        overflow: auto;

        .report_header {
            width: 100%;
            font-size: #ffffff;
            font-size: 26px;
            font-weight: 700;
            text-align: center;
        }
    }

    &_header {
        width: 100%;
        height: 40px;
    }

    &_body {
        width: 100%;
        display: flex;

        .total {
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .body_content {
            flex: 1;
            padding: 30px 0 0;
            color: #fff;

            .energy_total {
                font-size: 32px;
                font-family: "PingFangSC-Medium", "PingFang SC";
                font-weight: 500;
                color: #ffffff;

                .num {
                    font-size: 60px;
                    font-family: "DINAlternate-Bold", "DINAlternate";
                    font-weight: bold;
                    color: #e3731b;
                }
            }

            .content_text {
                padding: 0 28px;

                .msg {
                    margin-top: 17px;
                    font-size: 14px;
                    font-family: "PingFangSC-Regular", "PingFang SC";
                    font-weight: 400;
                    color: #ffffff;
                }

                .ratio {
                    font-size: 14px;
                    font-family: "PingFangSC-Regular", "PingFang SC";
                    font-weight: 400;
                    color: #ffffff;
                    margin-top: 23px;
                }

                .explana {
                    word-wrap: break-word;
                    margin-left: 10px;
                }
            }

            .chart_title {
                font-size: 16px;
                font-family: "PingFangSC-Medium", "PingFang SC";
                font-weight: 500;
                color: #889cc3;
            }

            .top {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                height: calc(100% - 21px);

                .item {
                    width: 100%;
                    margin-bottom: 17px;

                    .item_header {
                        margin-bottom: 10px;

                        .item_tit {
                            color: #ffeb6d;
                            font-size: 18px;
                            font-weight: bold;
                            font-family: "DINAlternate-Bold";
                        }
                    }
                }
            }

            .content_result {
                width: 100%;
                height: 300px;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;

                .result_btn {
                    margin-top: 50px;
                    font-size: 20px;
                    font-family: "PingFangSC-Medium", "PingFang SC";
                    font-weight: 500;
                    color: #27edbb;
                }

                i {
                    font-size: 120px;
                    color: #32ec7c;
                }

                .el-button {
                    padding: 16px 40px;
                    background: #3e83d4;
                    border-radius: 30px;
                    border: none;
                    color: #fff;
                }
            }
        }

        .flow_table {
            width: 100%;
            padding: 40px 10%;
            color: #fff;

            table {
                width: 100%;
                border-color: #fff;

                td {
                    text-align: center;
                }
            }
        }
    }
}
</style>
<style lang="scss">
.energy_container .item {
    .el-progress {
        .el-progress__text {
            display: none !important;
        }

        .el-progress-bar__outer {
            height: 16px !important;
            border-radius: 0 !important;
            background-color: rgba(66, 83, 229, 0.3);
        }

        .el-progress-bar__inner {
            border-radius: 0 !important;
            background: linear-gradient(90deg, rgba(66, 83, 229, 0) 0%, #4253e5 100%);
        }
    }
}
</style>
