<template>
    <div class="z100">
        <div class="wrapper_left" v-if="show">
            <div class="item">
                <sub-title title='建筑概况' />
                <div class="item-body building">
                    <div>
                        <img src="../../assets/images/home/<USER>">
                    </div>
                    <div>
                        <div class="building-text"><span>建筑面积:</span><span>34093.64</span>m²</div>
                        <div class="building-text"><span>占地面积:</span><span>40105</span>m²</div>
                        <div class="building-text"><span>建筑高度:</span><span>78.9</span>m</div>
                        <div class="building-text"><span>建筑层数:</span><span>14</span></div>
                    </div>
                </div>
            </div>
            <div class="item">
                <sub-title title='监控统计' />
                <div class="item-body device">
                    <div>
                        <div class="end">
                            <div class="text">设备总数</div>
                            <div style="color:#2FE5E7" class="num">{{ cam.all }}</div>
                        </div>
                        <div>
                            <div class="text">设备在线</div>
                            <div style="color:#F3F3F3" class="num">{{ cam.on }}</div>
                        </div>
                    </div>
                    <div>
                        <div class="end">
                            <div class="text">设备故障</div>
                            <div style="color:#E4A519" class="num">0</div>
                        </div>
                        <div>
                            <div class="text">设备离线</div>
                            <div style="color:#FC4444" class="num">{{ cam.off }}</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="item">
                <sub-title title='电能耗(kw/h)' />
                <div class="item-body energy">
                    <div>
                        <div class="energy-item">
                            <div class="text">今日</div>
                            <div class="num">{{ electricityData.currentDayValue }}</div>
                        </div>
                        <div class="energy-item">
                            <div class="text">昨日</div>
                            <div class="num">{{ electricityData.lastDayValue }}</div>
                        </div>
                    </div>
                    <div>
                        <div class="energy-item">
                            <div class="text">本月</div>
                            <div class="num">{{ electricityData.currentMonthValue }}</div>
                        </div>
                        <div class="energy-item">
                            <div class="text">本年</div>
                            <div class="num">{{ electricityData.currentYearValue }}</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="item">
                <sub-title title='能耗分项' />
                <div class="item-body energy_category">
                    <energy-sub style="flex:1" :echartData="category_data"></energy-sub>
                    <div class="energy_category-item center">
                        <div>
                            <div v-for="(item, i) in category_data">
                                <div class="center">
                                    <div :class="'num' + (i+1)" class="dot"></div>
                                    <div class="text">{{ item.name }}</div>
                                </div>

                                <div class="num " :class="'num' + (i+1)">{{ (item.value * 100 / category_all).toFixed(2) }}%</div>
                            </div>
                            <!-- <div>
                                <div class="center">
                                    <div style="background: linear-gradient(0deg, #FABB32, #E9A819);" class="dot"></div>
                                    <div class="text">{{ category_data[1].name }}</div>
                                </div>

                                <div class="num num2">{{ (category_data[1].value * 100 / category_all).toFixed(2) }}%</div>
                            </div>
                            <div>
                                <div class="center">
                                    <div style="background: linear-gradient(0deg, #71B0FF, #358FFF);" class="dot"></div>
                                    <div class="text">{{ category_data[2].name }}</div>
                                </div>

                                <div class="num num3">{{ (category_data[2].value * 100 / category_all).toFixed(2) }}%</div>
                            </div>
                            <div>
                                <div class="center">
                                    <div style="background: linear-gradient(-26deg, #F36447, #EF5B34);" class="dot"></div>
                                    <div class="text">{{ category_data[3].name }}</div>
                                </div>

                                <div class="num num4">{{ (category_data[3].value * 100 / category_all).toFixed(2) }}%</div>
                            </div>

                            <div>
                                <div class="center">
                                    <div style="background: linear-gradient(-26deg, #47f3cb, #34efe2);" class="dot"></div>
                                    <div class="text">{{ category_data[4].name }}</div>
                                </div>

                                <div class="num num5">{{ (category_data[4].value * 100 / category_all).toFixed(2) }}%</div>
                            </div> -->


                        </div>

                    </div>
                </div>

            </div>
        </div>
        <div class="wrapper_right" v-if="show">
            <div class="item">
                <sub-title1 title='照明系统' />
                <div class="item-body order">
                    <div>
                        <div class="total center">回路总数</div>
                        <div class="order-left">
                            <div class="center">
                                <div>{{ paifeng.count }}</div>
                                <div class="total">个</div>
                            </div>
                        </div>
                    </div>
                    <div class="order-right">
                        <div class="order-text"><img src="../../assets/images/home/<USER>" /><span
                                class="text">开启:</span><span class="num">{{ paifeng.on }}</span></div>
                        <div class="order-text"><img src="../../assets/images/home/<USER>" /><span
                                class="text">关闭:</span><span class="num">{{ paifeng.fault }}</span></div>
                    </div>
                </div>
            </div>
            <div class="item">
                <sub-title1 title='空调统计' />
                <div class="item-body su">
                    <div>
                        <div class="su-item center">
                            <div>
                                <img src="../../assets/images/home/<USER>" />
                            </div>
                            <div>
                                <div class="text">总数</div>
                                <div style="color:#2FE5E7" class="num">{{ kt.count }}</div>
                            </div>

                        </div>
                        <div class="su-item center">
                            <div>
                                <img src="../../assets/images/home/<USER>" />
                            </div>
                            <div>
                                <div class="text">运行中</div>
                                <div style="color:#F3F3F3" class="num">{{ kt.on }}</div>
                            </div>

                        </div>
                    </div>
                    <div>
                        <div class="su-item center">
                            <div>
                                <img src="../../assets/images/home/<USER>" />
                            </div>
                            <div>
                                <div class="text">停止中</div>
                                <div style="color:#F3F3F3" class="num">{{ kt.off }}</div>
                            </div>

                        </div>
                        <div class="su-item center">
                            <div>
                                <img src="../../assets/images/home/<USER>" />
                            </div>
                            <div>
                                <div class="text">故障</div>
                                <div style="color:#FC4444" class="num">+{{ kt.fault }}</div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
            <div class="item">
                <sub-title1 title='门禁系统' />
                <div class="item-body car ">

                    <div class="car-item center">
                        <div style="width: 6px;height: 6px;background: #15D715;">
                        </div>

                        <div class="text">门开</div>
                        <div style="color:#15D715" class="num">{{ door.on }}</div>

                    </div>
                    <div class="car-item center">
                        <div style="width: 6px;height: 6px;background: #D77117;">
                        </div>

                        <div class="text">门关</div>
                        <div style="color:#D77117" class="num">{{ door.off }}</div>

                    </div>

                </div>
            </div>
            <div class="item">
                <sub-title1 title='报警统计(本周)' />
                <Alarm :xAxisData="xAxisData" :echartData="echartData" />
            </div>
        </div>
    </div>
</template>

<script>
import {
    computed,
    defineComponent,
    getCurrentInstance,
    onMounted,
    reactive,
    toRefs,
    watch, inject
} from 'vue';
import energySub from '@/components/home/<USER>';
import Alarm from '@/components/echarts/weekAlarmEchart.vue'
import {
    useStore
} from 'vuex';
import {
    getCookie,
} from '@/utils/cookie';
import dayjs from 'dayjs'
export default defineComponent({
    name: "home",
    components: {
        energySub,
        Alarm
    },
    setup() {
        const {
            proxy
        } = getCurrentInstance()
        const emitter = inject('mitt');
        const store = useStore();
        const state = reactive({
            echartData: [10, 11, 12, 0, 8, 6, 2],
            xAxisData: ['星期一', '星期二', '星期三', '星期四', '星期五', '星期六', '星期天'],
            show: true,
            category_all: 0,
            category_data: [
                {
                    name: '控制动力',
                    value: 0
                },
                {
                    name: '公共照明',
                    value: 0
                },
                {
                    name: '常规动力',
                    value: 0
                },
                {
                    name: '应急照明',
                    value: 0
                },
                {
                    name: '普通照明',
                    value: 0
                }
            ],
            cam: {
                all: 0,
                on: 0,
                off: 0,
            },
            paifeng: {
                count: 0,
                on: 0,
                fault: 0,
                off: 0,
            },
            kt: {
                count: 0,
                on: 0,
                fault: 0,
                off: 0,
            },
            park: {
                on: 0,
                off: 0,
            },
            door: {
                on: 0,
                off: 0,
            },
            electricityData: {
                currentDayValue: 0,
                lastDayValue: 0,
                currentMonthValue: 0,
                lastMonthValue: 0,
                currentYearValue: 0,
            }
        })
        const projectId = computed(() => {
            return store.state.user.projectId || getCookie('projectId')
        })
        watch(projectId, (val) => {
            state.deviceCount = 0
            state.faultCount = 0
            if (val) {
                getStatisticAll()
            }

        })
        emitter.on('show', val => {
            state.show = val;
        })
        const getServerPage = () => {
            proxy.$api.getVideoServer({
                projectId: getCookie("gh_projectId"),
            }).then((res) => {
                state.servers = res.data;
                if (state.servers && state.servers.length > 0) {
                    state.servers.forEach((s) => {
                        GetSrcInfo(s.ip, s.port);
                    });
                }
            });
        };

        const GetSrcInfo = (ip, port) => {


            let root = `http://${ip}:${port}`;
            var url = root + "/api/v1/GetSrc";
            proxy.$axios
                .get(url)
                .then((result) => {
                    if (result.status == 200) {
                        var data = result.data;
                        state.cam.on = 0;
                        state.cam.off = 0;
                        state.cam.all = data.src.length;
                        for (var i = 0; i < data.src.length; i++) {
                            if (data.src[i]["bOnline"]) {
                                state.cam.on++;
                            } else {
                                state.cam.off++;
                            }
                        }
                    }
                })
                .catch((error) => {

                });
        };
        const getLight = () => {
            let all = 0, on = 0, off = 0;
            proxy.$api.getDeviceTypeStatus({
                projectId: getCookie("gh_projectId"),
                typeCode: ['Light8', 'light4']
            }).then(res => {
                res.data.forEach(d => {
                    //新风系统
                    all++;
                    if (d.dataVal == '1') {
                        on++;

                    } else if (d.dataVal == '0') {
                        off++;
                    }

                })


                state.paifeng.count = all;
                state.paifeng.on = on;
                state.paifeng.fault = off;

            });
        }


        const getDeviceStatus = (code) => {
            proxy.$api.getDeviceStatus({
                deviceCode: code
            }).then(res => {
                res.data.forEach(d => {
                    //给排水
                    if (code == 'BCPF') {
                        d.params.forEach(p => {
                            if (p.keyCode == 'runState') {
                                state.paifeng.count++;
                            }
                            if (p.keyCode == 'runState' && p.value == 1) {
                                state.paifeng.on++;
                                state.paifeng.count++;
                            } else if (p.keyCode == "runState" && p.value == 0) {
                                state.paifeng.off++;
                                state.paifeng.count++;
                            } else if (p.keyCode == "faultState" && p.value == 1) {
                                state.paifeng.fault++;
                            }
                        });

                    } else if (code == "AirConditioner") //电扶梯
                    {

                        d.params.forEach(p => {
                            if (p.keyCode == 'runState') {
                                state.kt.count++;
                            }
                            if (p.keyCode == 'runState' && p.value == 1) {
                                state.kt.on++;
                                // state.kt.count++;
                            } else if (p.keyCode == "runState" && p.value == 0) {
                                state.kt.off++;
                                // state.kt.count++;
                            } else if (p.keyCode == "faultState" && p.value == 1) {
                                state.kt.fault++;
                            }
                        });
                    } else if (code == "SafetyGate") {
                        d.params.forEach(p => {
                            if (p.keyCode == 'lockState' && p.value == 1) {
                                state.door.on++;
                            } else if (p.keyCode == "lockState" && p.value == 0) {
                                state.door.off++;
                            }
                        });
                    }

                });

            });
        }
        const getAlarmWeek = () => {
            state.echartData = [];
            proxy.$api.getAlarmWeek({
                projectId: getCookie("gh_projectId"),
            }).then((res) => {
                state.echartData = res.data.map(el => el.count);
            })
        }

        const getEnergyOver = (type, data) => {
            proxy.$api.getEnergyOverView({
                type: type
            }).then((res) => {
                res.data.forEach((element) => {
                    Object.keys(element).forEach((key) => {
                        if (key !== 'time') {
                            data[key] = element[key] ? element[key].toFixed(1) : element[key];
                        }
                    })
                })
            })
        }
        const energyFlow = () => {
            proxy.$api.energyFlow({
                bt: dayjs().startOf('month').format("YYYY-MM-DD HH:mm:ss"),
                et: dayjs().endOf('month').format("YYYY-MM-DD HH:mm:ss"),
                deviceType: 1,
                measurement: 'category'
            }).then((res) => {
                let data = res.data;
                if (data && data.length > 0) {
                    state.category_all = 0;
                    state.category_data = [];
                    for (let i = 0; i < data.length; i++) {
                        let element = data[i]
                        let obj = {
                            name: element.categoryname,
                            value: Number(element._aggregate.toFixed(1)),
                        }
                        if (obj.name) {
                            state.category_data.push(obj);
                            state.category_all += Number(obj.value);
                        }
                    }
                    console.log(state.category_data)
                }

            })
        }





        onMounted(() => {
            getServerPage();
            getDeviceStatus("BCPF");
            getDeviceStatus("SafetyGate");
            getDeviceStatus("AirConditioner");
            getAlarmWeek();
            getEnergyOver(1, state.electricityData)
            energyFlow();
            getLight()

        })

        return {
            ...toRefs(state),

            projectId
        }
    }
});
</script>

<style lang="scss" scoped>
.home {
    .home_wrapper {

        .wrapper_left,
        .wrapper_right {
            display: flex;
            flex-direction: column;
            width: 393px;
            z-index: 198;
            position: absolute;
            // background: rgba($color: #051a30, $alpha: 0.8);
        }

        .item {
            flex: 1;

            &-body {
                display: flex;
                justify-content: center;
                padding: 10px;
            }
        }

        .wrapper_left {
            background: linear-gradient(to left, rgba($color: #051a30, $alpha: 0), rgba($color: #051a30, $alpha: 0.8));

            .building {
                display: flex;

                &>div:last-of-type {
                    display: flex;
                    flex-direction: column;

                }

                &-text {
                    background: url("../../assets/images/home/<USER>") no-repeat bottom/100%;
                    margin-top: 18px;
                    display: flex;
                    align-items: center;
                    color: transparent;
                    background: linear-gradient(0deg, #97d3fb, #d7eefe);
                    -webkit-background-clip: text;
                    -webkit-text-fill-color: transparent;

                    span:first-of-type {
                        font-size: 16px;
                        font-family: "Alibaba-PuHuiTi";
                        font-weight: 400;
                        color: #C3D2E0;
                        margin-left: 35px;
                        margin-right: 18px;
                    }

                    span:last-of-type {
                        font-size: 24px;
                        font-family: "BEBAS";
                        font-weight: 400;
                        color: #FFFFFF;
                    }
                }

                &-text:first-of-type {
                    margin-top: 0;
                }

            }

            .device {
                display: flex;
                flex-direction: column;
                background: url("../../assets/images/home/<USER>") no-repeat center/100%;

                &>div {
                    display: flex;
                    justify-content: space-between;
                    padding: 0 50px;
                }

                &>div:first-child {
                    margin-bottom: 20px;
                }

                .text {
                    font-size: 16px;
                    font-family: "Alibaba-PuHuiTi";
                    font-weight: normal;
                    color: #c1f1ff;

                }

                .num {
                    font-size: 30px;
                    font-family: "BEBAS";
                    font-weight: 400;
                    color: #F3F3F3;

                }

                .end {
                    display: flex;
                    flex-direction: column;
                    align-items: flex-end;
                }
            }

            .energy {
                display: flex;
                flex-direction: column;

                height: calc(100% - 40px);

                &>div {
                    display: flex;
                    justify-content: space-between;
                    padding: 0 10px;
                }

                &>div:first-child {
                    margin-bottom: 30px;
                }

                .text {
                    font-size: 16px;
                    font-family: "Alibaba-PuHuiTi";
                    font-weight: normal;
                    color: #EAEBEC;
                    margin-right: 15px;
                    margin-left: 30px;
                }

                .num {
                    font-size: 32px;
                    font-family: "BEBAS";
                    font-weight: 400;
                    color: transparent;
                    background: linear-gradient(0deg, #97d3fb, #d7eefe);
                    -webkit-background-clip: text;
                    -webkit-text-fill-color: transparent;
                }

                &-item {
                    display: flex;
                    align-items: center;
                    // justify-content: center;
                    background: url("../../assets/images/home/<USER>") no-repeat bottom/100%;
                    flex: 1
                }
            }

            .energy_category {

                display: flex;
                height: calc(100% - 40px);
                align-items: center;

                .dot {
                    width: 6px;
                    height: 6px;
                }

                &-item {
                    flex: 1;
                    flex-direction: column;

                    &>div>div {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        width: 100%;
                        border-bottom: 1px solid rgba(255, 255, 255, 0.3);
                        margin-bottom: 14px;
                    }
                }

                .text {

                    font-size: 14px;
                    font-family: "Alibaba-PuHuiTi";
                    font-weight: normal;
                    color: #FFFFFF;
                    margin-left: 5px;
                }

                .num1 {
                    background: linear-gradient(0deg, #6CF8E7 0%, #4CEDD9 100%);
                }

                .num2 {
                    background: linear-gradient(0deg, #FABB32 0%, #E9A819 100%);
                }

                .num3 {
                    background: linear-gradient(0deg, #71B0FF 0%, #358FFF 100%);
                }

                .num4 {
                    background: linear-gradient(-26deg, #F36447 0%, #EF5B34 100%);
                }

                .num5 {
                    background: linear-gradient(-26deg, #47f3cb 0%, #34efe2 100%);
                }


                .num {

                    font-size: 20px;
                    font-family: "BEBAS";
                    font-weight: 400;
                    color: #FFFFFF;

                    -webkit-background-clip: text;
                    -webkit-text-fill-color: transparent;
                    display: flex;
                    align-self: flex-end;
                    margin-left: 30px;
                }

            }
        }

        .wrapper_right {
            background: linear-gradient(to right, rgba($color: #051a30, $alpha: 0), rgba($color: #051a30, $alpha: 0.8));

            .order {
                &>div:last-child {}

                display: flex;
                justify-content: center;
                align-items: center;

                .text {
                    font-size: 16px;
                    font-family: "Alibaba-PuHuiTi";
                    font-weight: normal;
                    color: #EAEBEC;
                    margin-right: 20px;
                }

                .num {

                    font-size: 26px;
                    font-family: "BEBAS";
                    font-weight: 400;
                    color: #F3F3F3;
                }

                &-left {
                    background: url("../../assets/images/home/<USER>") no-repeat center/100%;
                    width: 97px;
                    height: 118px;

                    font-size: 30px;
                    font-family: "BEBAS";
                    font-weight: 400;
                    color: #56E0FF;
                    display: flex;
                    justify-content: center;
                    align-items: flex-start;

                }

                &-right {
                    background: url("../../assets/images/home/<USER>") no-repeat center/100%;
                    width: 214px;
                    height: 140px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    flex-direction: column;
                }

                &-text {
                    margin: 20px 0;
                    display: flex;
                    align-items: center;
                    justify-content: flex-start;
                }

                .total {
                    font-size: 14px;
                    font-family: "Alibaba-PuHuiTi";
                    font-weight: normal;
                    color: #E9EAEB;

                }
            }

            .su {
                display: flex;
                flex-direction: column;
                white-space: nowrap;

                &>div {
                    display: flex;
                    justify-content: space-between;
                    padding: 0 10px;
                }

                &>div:first-child {
                    margin-bottom: 15px;
                }

                .text {
                    font-size: 16px;
                    font-family: "Alibaba-PuHuiTi";
                    font-weight: normal;
                    color: #EAEBEC;
                }

                .num {
                    font-size: 32px;
                    font-family: "BEBAS";
                    font-weight: 400;
                    color: #F3F3F3;
                }

                &-item {
                    flex: 1;

                    &>div:last-child {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        flex-direction: column;
                    }

                }
            }

            .car {

                white-space: nowrap;
                display: flex;
                align-items: center;
                justify-content: space-around;
                height: calc(100% - 40px);

                .text {
                    font-size: 16px;
                    font-family: "Alibaba-PuHuiTi";
                    font-weight: normal;
                    color: #EAEBEC;
                    margin: 0 10px;
                }

                .num {
                    font-size: 32px;
                    font-family: "BEBAS";
                    font-weight: 400;
                    color: #F3F3F3;
                }

                &-item {

                    display: flex;
                }
            }

        }

    }
}
</style>
