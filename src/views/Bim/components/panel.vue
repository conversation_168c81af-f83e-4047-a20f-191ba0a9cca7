<template>
    <el-dialog append-to-body :show-close="false" custom-class="panel_dialog" draggable v-model="panelVisibale"
        :width="width">
        <template #header>
            <div class="header">
                <div style="color:white">{{ title }}</div>
                <img @click="close" class="cursor" src="../img/panel2.png" />
            </div>
        </template>
        <el-tabs v-model="activeName" @tab-change="handleTabClick">

            <div class="history" v-if="!deviceCode.startsWith('jifang_')">

                <div class="list-item" v-for="(item, index) in sData" :key="index">
                    <div class="text">
                        {{ item.name }}
                    </div>
                    <div :style="{ color: getColor(panelData[item.id], item.config) }">
                        {{ getName(panelData[item.id], item.config) }}</div>
                </div>
                <div class="list-item" v-for="(item, index) in sInputData" :key="index">
                    <div class="list-item-icon">{{ item.name }}</div>
                    <div> {{ panelData[item.id] }}{{ getUnit(item.unit) }}</div>
                </div>
                <div class="list-item" v-for="(item, index) in sOutData" :key="index">
                    {{ item.name }}
                    <div style="margin-right: 5px;">{{ item.min }}</div><el-slider class="yangan" :marks="[item.min,item.max]" @change="changeSlider($event, item.variable)" :min="item.min" :max="item.max"
                        v-model="value2" >
                    </el-slider>
                    <div>{{ item.max }}</div>
                </div>

                <div class="list-item" v-if="numData.length > 0" v-for="item in numData">
                    <div> {{item.name }}</div>
                    <div class="btn_center">

                        <el-switch size="small" active-value="1"  inactive-value="0" v-model="panelData[item.id]" @change="writeValue($event, item.variable)" ></el-switch>

                        <!-- <div>
                            <i style="color:#12e312" class="iconfont icontingzhi2-copy"
                                @click="writeValue(item.variable, 1)"></i>
                        </div>
                        <div>
                            <i style="color:red;font-size: 20px;" class="iconfont iconbofang2"
                                @click="writeValue(item.variable, 0)"></i>
                        </div> -->
                    </div>
                </div>

                <div class="list-item" v-if="enums.length > 0" v-for="(item, i) in enums" :key="i">
                    <div> {{ item[0].standardName }}</div>
                    <div class="btn_center">
                        <el-dropdown @command="changeSeason">
                            <span class="el-dropdown-link center">
                                {{ getEnumName(item[0].id, item) }}
                                <el-icon class="el-icon--right">
                                    <arrow-down />
                                </el-icon>
                            </span>
                            <template #dropdown>
                                <el-dropdown-menu>
                                    <el-dropdown-item v-for="(em, mj) in item" :command="em" :key="mj">{{ em.name
                                    }}</el-dropdown-item>
                                </el-dropdown-menu>
                            </template>

                        </el-dropdown>
                    </div>
                </div>

            </div>

            <el-table v-else class="table" :data="jifangData" height="600" fit>
                <template #empty>
                    <no-data />
                </template>
                <el-table-column prop="devpropid" label="设备参数Id" align="center"></el-table-column>
                <el-table-column prop="propname" label="参数名称" align="center">
                </el-table-column>
                <el-table-column prop="curvalue" label="当前值" align="center">

                </el-table-column>
                <el-table-column prop="unit" label="单位" align="center">

                </el-table-column>
                <el-table-column prop="valuedescript" label="描述" align="center">

                </el-table-column>

            </el-table>

        </el-tabs>
    </el-dialog>
</template>

<script>
import {
    reactive,
    toRefs,
    defineComponent,
    getCurrentInstance,
    inject,
    nextTick,
    computed
} from "vue";
import historyChart from '@/components/echarts/historyChart.vue'
import bar from '@/components/energy/bar.vue'
import {
    getCookie
} from "@/utils/cookie"
import socket from '@/utils/socket'
import dayjs from 'dayjs'
import device from './device.vue'
import repair from './repair.vue'
import maintenance from './maintenance.vue'
import patrol from './patrol.vue'
import alarm from './alarm.vue'
import {
    useStore
} from 'vuex'
import calc from '@/utils/eval';
export default defineComponent({
    sockets: {
        live(res) {
            this.process(res)
        },
        onVarsChangedCallback(res) {
            this.process(res)
        },
    },
    components: {
        historyChart,
        bar,
        device,
        repair,
        maintenance,
        patrol,
        alarm
    },
    setup(props) {
        const {
            proxy
        } = getCurrentInstance();

        const state = reactive({
            panelVisibale: false,
            activeName: "real",
            historyData: {
                time: [],
                data: []
            }, //历史数据
            startEndDate: [],
            iconClickConfig: null, //点击图标时候得config
            chartData: {
                xAxis: [],
                data: []
            },
            units: null,
            config: null,
            title: null,
            sockets: null,
            panelData: {},
            sData: [], //状态数据
            sInputData: [], //模拟输入
            sOutData: [], //模拟输出
            numData: [], //数字输出 控制
            enums: [],
            std: [], //设备指标集合
            select_std: null,
            deviceCode: null,
            jifangData: [],
            width: "800px",
            interval: null,
        })
        state.sockets = inject('socket')
        const store = useStore();

        state.startEndDate.push(dayjs().format('YYYY-MM-DD 00:00:00'))
        state.startEndDate.push(dayjs().format('YYYY-MM-DD 23:59:59'))
        const show = (config) => {

            state.config = config;
            state.title = config.deviceName || config.name
            state.panelVisibale = true;
            state.deviceCode = config.deviceCode;
            if (config.deviceCode.startsWith("jifang_")) {
                state.width = "800px"
                getJiFangData();
                return;
            }
            state.width = "400px"
            openPanel(config.deviceId, config.deviceCode)
        }
        const openPanel = (deviceId, code) => {
            if (deviceId) {
                getStd(deviceId)
            }
        }
        const getHistory = () => {
            let deviceId = state.config.deviceId;
            if (state.config.deviceCode && state.config.deviceCode.startsWith("energy_")) {
                deviceId = state.config.deviceCode.split("_")[1];
            }
            proxy.$api.getHistoryEnergy({
                bt: dayjs(state.startEndDate[0]).format("YYYY-MM-DD HH:mm:ss"),
                et: dayjs(state.startEndDate[1]).format("YYYY-MM-DD HH:mm:ss"),
                deviceId: deviceId,
            }).then((res) => {
                state.historyData = {
                    time: [],
                    data: []
                };
                state.historyData.time = res.data.map((v, i) => {
                    return v.time
                })
                state.historyData.data = res.data.map((e, j) => {
                    return e.value
                })
                state.historyData.name = ""
            })
        }
        const getDeviceEnergyById = async () => {
            state.chartData = {
                xAxis: [],
                data: []
            }
            for (let i = 0; i < 24; i++) {
                state.chartData.xAxis.push(i + '时');
            }
            if (state.config.deviceCode?.startsWith("energy_")) {
                const {
                    data
                } = await proxy.$api.getDeviceEnergyById({
                    bt: dayjs().format('YYYY-MM-DD 00:00:00'),
                    et: dayjs().format('YYYY-MM-DD 23:59:59'),
                    id: state.config.deviceCode.split("_")[1],
                })
                state.chartData.data = data;
            }

        }
        const realTime = (data) => {
            proxy.$api.realTime(data).then(res => {

            });
        }
        const getStd = (deviceId) => {
            proxy.$api.getDeviceStandard({
                deviceId,
                projectId: getCookie("gh_projectId"),
            }).then((res) => {
                let RequestItems = [];
                state.sData = [];
                state.sInputData = [];
                state.sOutData = [];
                state.numData = [];
                state.enums = [];
                res.data.forEach((d, i) => {
                    d.standardParams.forEach((p, j) => {
                        if (p.dataType == 'num_input' && p.paramKey == 'Value') {
                            //状态 当前值
                            state.sData.push({
                                id: 's_d_' + i + '_' + j,
                                name: d.name,
                                config: p.config ? JSON.parse(p.config) : {},
                            })
                            panelSubscribe(d.variable, 's_d_' + i + '_' + j)
                        }
                        if (p.dataType == 'string_input' && p.paramKey == 'Value') {
                            //模拟输入
                            state.sInputData.push({
                                id: 's_d_' + i + '_' + j,
                                name: d.name,
                                unit: p.unit,
                            })
                            panelSubscribe(d.variable, 's_d_' + i + '_' + j)
                        }
                        if (p.dataType == 'string_out' && p.paramKey == 'Value') {
                            //模拟输出
                            state.sOutData.push({
                                name: d.name,
                                min: parseInt(p.min),
                                max: parseInt(p.max),
                                variable: d.variable,
                            })
                        }
                        if (p.dataType == 'num_out' && p.paramKey == 'Value') {

                            // state.sData.push({
                            //     id: 's_d_' + i + '_' + j,
                            //     name: d.name,
                            //     config: p.config ? JSON.parse(p.config) : {},
                            // })
                            // panelSubscribe(d.variable, 's_d_' + i + '_' + j)


                            //数字输出
                            state.numData.push({
                                id: 's_d_' + i + '_' + j,
                                name: d.name,
                                conditions: p.conditions,
                                variable: d.variable,
                            })
                            panelSubscribe(d.variable, 's_d_' + i + '_' + j)
                        }
                        if (p.dataType == "enum" && p.paramKey == "Value") {
                            if (p.config) {
                                let config = JSON.parse(p.config);
                                let enumItem = [];
                                config.forEach(c => {
                                    enumItem.push({
                                        id: 's_d_' + i + '_' + j,
                                        variable: d.variable,
                                        name: c.text,
                                        value: c.value,
                                        type: "enum",
                                        standardName: d.name,
                                    })
                                })
                                state.enums.push(enumItem)
                                panelSubscribe(d.variable, 's_d_' + i + '_' + j)
                            }
                        }
                    })

                    let variable = d.variable.split(":");

                    RequestItems.push({
                        id: null,
                        iosvrKey: variable[0],
                        chlKey: variable[1],
                        ctrlKey: variable[2],
                        varKey: variable[3],
                        propName: "Value",
                        realTime: true,
                    });

                    if (state.interval) {
                        clearInterval(state.interval);
                        state.interval = null;
                    }

                    state.interval = setInterval(() => {
                        realTime({requestItems:RequestItems});
                    }, 2000);

                })



            })
        }

        const handleTabClick = async (data) => {
            await nextTick()
            if (data == "energy") {
                getDeviceEnergyById();
            } else if (data == "history") {
                getHistory();
            } else if (data == 'device_history') {
                getDeviceParams();
            }
        }
        const close = () => {
            state.panelVisibale = false;
            state.panelData = {}
            state.sData = []
            state.sInputData = []
            state.sOutData = []
            state.numData = []
            state.enums = []
            state.jifangData = []
            socket.unsubscribe(state.sockets, 'panel', 'real');
            if (state.interval) {
                clearInterval(state.interval);
                state.interval = null;
            }
        }
        const writeValue = (val,variable) => {
            const id= getCookie("gh_projectId");
            console.log(id);
            socket.writeValue(state.sockets, variable,val?1:0,"panel","bim" ,getCookie("gh_projectId"),
                getCookie("gh_id"))
        }
        const changeSlider = (val, variable) => {
            socket.writeValue(state.sockets, variable, val, "panel","bim",getCookie("gh_projectId"),
                getCookie("gh_id"))
        }
        const panelSubscribe = (variable, id) => {
            if (variable) {
                let v = variable.split(':')
                let item = {
                    id: id,
                    iosvrKey: v[0],
                    chlKey: v[1],
                    ctrlKey: v[2],
                    varKey: v[3],
                    realTime: false,
                }
                socket.subscribe(state.sockets, 'real', 'panel', [item])
                state.panelData = Object.assign({}, state.panelData, {
                    [id]: 0,
                })
            }
        }
        const process = (res) => {

            if (res) {
                let data = JSON.parse(res)
                if (data.batchDefinitionId == 'real' && data.clientId == 'panel') {
                    data.data.forEach((d) => {
                        if (d.id.startsWith('s_')) {
                            state.panelData[d.id] = d.value
                        }
                    })
                }
            }
        }
        const getUnit = (id) => {
            let name = ''
            if (id) {
                for (let u of state.units) {
                    if (u.tagValue == id) {
                        name = u.tagName
                        break
                    }
                }
            }
            return name
        }
        const condition = (value, factor, v) => {
            if (!value) value = 0
            if (eval(value + factor + v)) return true
            return false
        }
        const getDicUtil = () => {
            proxy.$api.getDicUtil({
                dicCode: 'unit',
                projectId: getCookie("gh_projectId"),
            }).then((res) => {
                state.units = res.data
            })
        }
        const getDeviceParams = () => {
            proxy.$api.getDeviceStandard({
                deviceId: state.config.deviceId,
            }).then((res) => {
                state.std = res.data
                if (res.data.length > 0) {
                    state.select_std = res.data[0].variable
                    getDeviceHistory()
                }
            })
        }

        const getDeviceHistory = () => {
            proxy.$api.getHistoryData({
                bt: dayjs(state.startEndDate[0]).format("YYYY-MM-DD HH:mm:ss"),
                et: dayjs(state.startEndDate[1]).format("YYYY-MM-DD HH:mm:ss"),
                keyword: state.select_std,
            }).then((res) => {
                state.historyData = {
                    time: [],
                    data: []
                };
                state.historyData.time = res.data.histories.map((v, i) => {
                    return v.time
                })
                state.historyData.data = res.data.histories.map((e, j) => {
                    return e.value
                })
            })
        }

        const changeStd = (val) => {
            if (val) {
                getDeviceHistory(val);
            } else {
                state.historyData = {
                    time: [],
                    data: []
                };
            }
        }

        const getJiFangData = () => {
            proxy.$api.getJiFangData({
                code: state.config.deviceCode.split('_')[1],
            }).then((res) => {
                state.jifangData = res.data
            })
        }

        const getName = (value, config) => {
            let name = "";
            if (config && config.length && config.length > 0) {
                config.forEach(c => {
                    if (calc(value, c.factor, c.value)) {
                        name = c.text;
                    }
                })
            }
            return name;
        }
        const getColor = (value, config) => {
            let color = "";
            if (config && config.length && config.length > 0) {
                config.forEach(c => {
                    if (calc(value, c.factor, c.value)) {
                        color = c.color;
                    }
                })
            }
            return color;
        }
        const getEnumName = (id, item) => {
            let name = "";
            item.forEach(d => {
                if (state.panelData[id] == d.value) {
                    name = d.name;
                }
            })
            return name;
        }
        const changeSeason = (data) => {
            // socket.writeValue(
            //     this.$socket,
            //     data.variable,
            //     data.value,
            //     "ba",
            //     this.$socket.id
            // );

            socket.writeValue(state.sockets, data.variable, data.value,"panel",state.sockets.id ,getCookie("gh_projectId"),
                getCookie("gh_id"))

        }

        const open = () => {
            state.show = true
        }

        getDicUtil();

        return {
            ...toRefs(state),
            show,
            openPanel,
            handleTabClick,
            close,
            writeValue,
            condition,
            changeSlider,
            panelSubscribe,
            process,
            getUnit,
            getDeviceParams,
            changeStd,
            getColor,
            getName,
            getEnumName,
            changeSeason

        }
    }
})
</script>

<style lang="scss" scoped>
.panel_dialog {
    .header {
        background: url("../img/panel1.png");
        background-repeat: no-repeat;
        background-size: 100% 100%;
        height: 30px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
    }

    .history {
        height: 350px;
        width: 100%;
    }
}

.list-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #889cc3;
    padding: 0 20px;
    border-bottom: 1px solid #3a3b3d;
    margin-bottom: 15px;
}

.btn_center {
    display: flex;
    justify-content: center;
    align-items: center;

    &>div:first-child {
        margin-right: 10px;
    }

    i {
        font-size: 20px;
    }
}</style>
