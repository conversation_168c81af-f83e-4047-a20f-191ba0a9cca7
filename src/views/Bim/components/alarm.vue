<template>
<div>
    <el-form :inline="true" class="search_box form_inline" size="small">
        <el-form-item label="时间">
            <el-date-picker v-model="date" type="datetimerange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" @change="search">
            </el-date-picker>
        </el-form-item>
        <el-form-item>
            <el-button @click="search" class="searchBtn" size="small" type="text">查询</el-button>
        </el-form-item>
    </el-form>
    <div class="table">
        <el-table :height="400" :data="list"  style="width: 100%" fit>
            <template #empty>
                <noData />
            </template>
            <el-table-column prop="createTime" label="报警时间" align="center">
            </el-table-column>
            <el-table-column prop="alarmSource" label="报警类型" align="center">
                <template #default="scope">
                    <span>{{ getSourceName(scope.row.alarmSource) }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="alarmLevel" label="报警级别" align="center">
                <template #default="scope">
                    <span>{{ getLevelName(scope.row.alarmLevel) }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="alarmDesc" label="处理内容" align="center">
            </el-table-column>
            <el-table-column prop="deviceName" label="报警设备" align="center">
            </el-table-column>
            <el-table-column prop="updateTime" label="处理时间" align="center">
            </el-table-column>
        </el-table>
        <div class="center page">
            <el-pagination prev-icon="CaretLeft" next-icon="CaretRight"  :page-size="Pagination.size" :current-page="Pagination.page"  layout="total,prev, pager, next" @current-change="handleCurrentChange" :total="Pagination.total">
            </el-pagination>
        </div>
    </div>
</div>
</template>

<script>
import {
    defineComponent,
    getCurrentInstance,
    reactive,
    toRefs
} from "vue";
import dayjs from 'dayjs'
import {
    getCookie
} from "@/utils/cookie";
export default defineComponent({
    props:['deviceId'],
    setup(props) {
        const {
            proxy
        } = getCurrentInstance();
        const state = reactive({
            Pagination: {
                page: 1,
                size: 10,
                total: 0
            },
            date: [],
            list: null,
            levels: [],
            sources: []
        })
        state.date.push(dayjs(dayjs().format('YYYY-MM-DD 00:00:00')))
        state.date.push(dayjs(dayjs().format('YYYY-MM-DD 23:59:59')))
        const getHistroyRecordPage = () => {
            proxy.$api.getHistroyRecord({
                projectId: getCookie("gh_projectId"),
                status: true,
                deviceId: props.deviceId,
                page: state.Pagination.page,
                size: state.Pagination.size,
                bt: dayjs(state.date[0]).format('YYYY-MM-DD 00:00:00'),
                et: dayjs(state.date[1]).format('YYYY-MM-DD 23:59:59'),
            }).then((res) => {
                state.list = res.data
                state.Pagination.total = res.total
            })
        }
        const search = () => {
            state.Pagination.page = 1
            getHistroyRecordPage()
        }

        const getSources = () => {
            proxy.$api.getDicUtil({
                projectId: getCookie("gh_projectId"),
                dicCode: 'alarm_type'
            }).then(res => {
                state.sources = res.data
            })
        }
        const getLevels = () => {
            proxy.$api.getDicUtil({
                projectId: getCookie("gh_projectId"),
                dicCode: 'alarm_level'
            }).then(res => {
                state.levels = res.data
            })
        }
        const getSourceName = (data) => {
            let name = ''
            state.sources.forEach((d) => {
                if (d.tagValue == data) {
                    name = d.tagName
                    return false
                }
            })
            return name
        }
        const getLevelName = (data) => {
            let name = ''
            state.levels.forEach((d) => {
                if (d.tagValue == data) {
                    name = d.tagName
                    return false
                }
            })
            return name
        }
        const handleCurrentChange = (page) => {
            state.Pagination.page = page
            getHistroyRecordPage()
        }

        getSources()
        getLevels()
        getHistroyRecordPage();

        return {
            ...toRefs(state),
            search,
            getSourceName,
            getLevelName,
            handleCurrentChange,
        }
    }
})
</script>

<style scoped>
    
</style>
