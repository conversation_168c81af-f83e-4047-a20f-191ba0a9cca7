<template>
    <div class="electric-box layout_wrapper">
        <div class="wrapper">
            <el-form :inline="true" class="search_box form_inline" size="small">
                <el-form-item label="名称">
                    <el-input v-model="name" placeholder="请输入名称查询"></el-input>
                </el-form-item>
                <el-form-item label="楼层">
                    <el-input v-model="floor" placeholder="请输入楼层查询"></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button size="small" class="searchBtn" type="text" @click="search">查询</el-button>
                </el-form-item>
            </el-form>
            <div class="btn-group">
                <el-button icon="Plus" type="primary" size="mini" class="addBtn" @click="addSpace(null, 'add')">新增
                </el-button>
                <el-button icon="Minus" class="deleteBtn" type="primary" size="mini" @click="del">删除</el-button>
            </div>
            <div class="table">
                <el-table :height="tableHeight" :data="list" fit @select="select" @select-all="select">
                    <template #empty>
                        <no-data />
                    </template>
                    <el-table-column type="selection" width="55" align="center">
                    </el-table-column>
                    <el-table-column prop="name" label="空间名称" align="center">
                    </el-table-column>
                    <el-table-column prop="floor" label="楼层" align="center">
                    </el-table-column>
                    <el-table-column prop="deptName" label="部门" align="center">
                    </el-table-column>
                    <el-table-column prop="categoryName" label="分类名称" align="center">
                    </el-table-column>
                    <el-table-column prop="buildingSize" label="建筑面积(㎡)" align="center">
                    </el-table-column>
                    <el-table-column prop="useSize" label="使用面积(㎡)" align="center">
                    </el-table-column>
                    <el-table-column prop="rent" label="是否已租" align="center">
                        <template #default="scope">
                            {{ scope.row.rent == 1 ? '已租' : '未租' }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="remark" label="备注" align="center">
                    </el-table-column>
                    <el-table-column prop="createTime" label="创建时间" align="center">
                    </el-table-column>
                    <el-table-column label="操作" align="center">
                        <template #default="scope">
                            <el-button type="text" class="editBtn" @click="edit(scope.row, 'edit')">编辑</el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <div class="page center">
                <el-pagination prev-icon="CaretLeft" next-icon="CaretRight" :page-size="size" :current-page="page"
                    layout="total,prev, pager, next" @current-change="handleCurrentChange" :total="total">
                </el-pagination>
            </div>
        </div>

        <el-dialog align-center append-to-body   custom-class="custom_dialog" v-model="dialogData.visible" width="940px" title="空间管理">
            <el-form class="form" ref="form" :model="dialogData.space" :rules="dialogData.rule">
                <el-row type="flex" :gutter="30">
                    <el-col :span="12">
                        <el-form-item label="名称:" prop="name">
                            <el-input v-model="dialogData.space.name" placeholder="请输入名称"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="分类:" prop="categoryId">
                            <el-select clearable v-model="dialogData.space.categoryId">
                                <el-option v-for="item in categories" :key="item.id" :value="item.id"
                                    :label="item.tagName"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="用途:" prop="useType">
                            <el-select clearable v-model="dialogData.space.useType">
                                <el-option v-for="item in uses" :key="item.id" :value="item.id"
                                    :label="item.tagName"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="楼层:" prop="floor">
                            <el-input v-model="dialogData.space.floor" placeholder="请输入名称"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="部门:" prop="deptId">
                            <el-select clearable v-model="dialogData.space.deptId">
                                <el-option v-for="item in depts" :label="item.name" :key="item.id"
                                    :value="item.id"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="是否出租:">
                            <el-select clearable v-model="dialogData.space.rent">
                                <el-option label="已租" :value="1"></el-option>
                                <el-option label="未租" :value="0"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="构件id:">
                            <el-input v-model="dialogData.space.modelId" placeholder="请输入构件"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="bim文件编号:">
                            <el-input v-model="dialogData.space.bimFileId" placeholder="请输入名称"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="构件颜色:">
                            <el-input v-model="dialogData.space.color" placeholder="请输入名称"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="建筑面积:">
                            <el-input v-model="dialogData.space.buildingSize" placeholder="请输入名称"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="使用面积:">
                            <el-input v-model="dialogData.space.useSize" placeholder="请输入名称"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="空间体积:">
                            <el-input v-model="dialogData.space.spaceSize" placeholder="请输入名称"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="租金:">
                            <el-input v-model="dialogData.space.rentMoney" placeholder="请输入名称"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="客户:">
                            <el-input v-model="dialogData.space.merchant" placeholder="请输入名称"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="开始时间:">
                            <el-date-picker v-model="dialogData.space.beginTime" type="date" placeholder="开始时间"
                                :size="size" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="结束时间:">
                            <el-date-picker v-model="dialogData.space.endTime" type="date" placeholder="结束时间"
                                :size="size" />

                        </el-form-item>
                    </el-col>

                    <el-col :span="12">
                        <el-form-item label="备注:">
                            <el-input type="textarea" v-model="dialogData.space.remark" placeholder="请输入名称"></el-input>
                        </el-form-item>
                    </el-col>

                </el-row>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" class="saveBtn" size="mini" @click="saveSpace('form')">保存</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script>
import {
    ElMessage,
    ElMessageBox
} from 'element-plus'
import {
    getCookie
} from '@/utils/cookie'

import {
    onMounted,
    reactive,
    toRefs,
    ref,
    getCurrentInstance,
    computed,
    watch
} from 'vue'

import {
    useStore
} from 'vuex'
import dayjs from 'dayjs'
export default {
    name: "spacemanager",
    props: ['nav'],
    setup() {
        const title = ref(null)
        const form = ref(null)
        const {
            proxy
        } = getCurrentInstance()
        const store = useStore()
        const state = reactive({
            tableHeight: window.innerHeight * 0.60,
            page: 1,
            size: 10,
            total: 0,
            list: [],
            props: {
                label: 'name',
            },
            name: "",
            floor: "",
            data: null,
            items: [], //表格select
            dialogData: {
                visible: false,
                title: '',
                edit: false,
                space: {
                    name: '',
                    floor: null,
                    projectId: '',
                    rent: 0,
                    modelId: null,
                    bimFileId: null,
                    buildingSize: 0,
                    useSize: 0,
                    spaceSize: 0,
                    categoryId: "",
                    remark: null,
                    rentMoney: null,
                    color: null,
                    merchant: null,
                    deptId: null,
                    beginTime: null,
                    endTime: null,
                    useType: null

                },
                rule: {
                    name: [{
                        required: true,
                        message: '名称不能空',
                        trigger: 'blur'
                    }],
                    deptId: [{
                        required: true,
                        message: '请选择部门',
                        trigger: 'change'
                    }],
                    floor: [{
                        required: true,
                        message: '请输入楼层',
                        trigger: 'change'
                    }],
                    categoryId: [{
                        required: true,
                        message: '请选择分类',
                        trigger: 'change'
                    }],
                    useType: [{
                        required: true,
                        message: '请选择用途',
                        trigger: 'change'
                    }],

                },
            },
            categories: [],
            depts: [],
            uses: []
        })
        onMounted(() => {
            getDicUtil()
            getSpace()
            getDept();
            getSpaceUse();
            getSpaceType()
        })
        watch(projectId, (val) => {
            if (val) {
                getDicUtil()
                getSpace()
                getDept();
            }
        })
        const projectId = computed(() => {
            return store.state.user.projectId || getCookie('gh_projectId')
        })
        const getDicUtil = () => {
            proxy.$api.getDicUtil({
                dicCode: 'space_type',
                projectId: getCookie('gh_projectId'),
            }).then((res) => {
                state.categories = res.data
            })
        }
        const getDept = () => {
            proxy.$api.getDept({
                projectId: getCookie('gh_projectId'),
            }).then((res) => {
                state.depts = res.data
            })
        }
        const getSpace = () => {
            proxy.$api.getSpace({
                name: state.name,
                floor: state.floor,
                size: state.size,
                page: state.page,
                projectId: getCookie('gh_projectId'),
            }).then((res) => {
                state.list = res.data
                state.total = res.total
            })
        }
        const handleCurrentChange = (page) => {
            state.page = page
            getSpace()
        }
        const del = () => {
            if (state.items.length == 0) {
                ElMessage({
                    type: 'warning',
                    message: '请选择要删除的数据',
                })
                return
            }
            ElMessageBox.confirm('是否确认要删除该数据？', '提示', {
                confirmButtonClass: 'confirmBtn',
                cancelButtonClass: 'cancelBtn',
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            }).then(() => {
                proxy.$api.delSpace({
                    ids: state.items,
                }).then((res) => {
                    getSpace()
                    state.items = []
                })
            })
        }
        const edit = (row, type) => {
            if (type === 'edit') {
                state.dialogData.visible = true
                state.dialogData.edit = true;
                if (row.rent) {
                    row.rent = 1;
                } else {
                    row.rent = 0;
                }
                state.dialogData.space = Object.assign({}, row);
            }
        }

        const addSpace = (row, type) => {
            if (type === 'add') {
                state.dialogData.visible = true
                state.dialogData.title = "新增"
                state.dialogData.edit = false
                state.dialogData.space = {
                    name: '',
                    floor: null,
                    projectId: '',
                    rent: 0,
                    modelId: null,
                    bimFileId: null,
                    buildingSize: 0,
                    useSize: 0,
                    spaceSize: 0,
                    categoryId: "",
                    remark: null,
                    rentMoney: null,
                    color: null,
                    merchant: null,
                    deptId: null,
                    beginTime: null,
                    endTime: null
                }
            }
        }

        const select = (selection, row) => {
            state.items = []
            if (selection.length > 0) {
                selection.forEach((item) => {
                    state.items.push(item.id)
                })
            }
        }

        const search = () => {
            state.page = 1
            getSpace()
        }
        const saveSpace = () => {
            proxy.$refs['form'].validate(valid => {
                if (valid) {
                    state.dialogData.space.projectId = getCookie("gh_projectId")
                    if (state.dialogData.space.beginTime) {
                        state.dialogData.space.beginTime = dayjs(state.dialogData.space.beginTime).format("YYYY-MM-DD");
                    }
                    if (state.dialogData.space.endTime) {
                        state.dialogData.space.endTime = dayjs(state.dialogData.space.endTime).format("YYYY-MM-DD");
                    }
                    if (state.dialogData.edit) {
                        proxy.$api.editSpace(state.dialogData.space).then(res => {
                            state.dialogData.visible = false;
                            getSpace();
                        });

                    } else {
                        proxy.$api.addSpace(state.dialogData.space).then(res => {
                            state.dialogData.visible = false;
                            getSpace();
                        });
                    }
                }
            })

        }

        const getSpaceUse = () => {
            proxy.$api.getDicUtil({
                projectId: getCookie("gh_projectId"),
                dicCode: 'space_use',
            }).then((res) => {
                state.uses = res.data
            })
        }
        const getSpaceType = () => {
            proxy.$api.getDicUtil({
                projectId: getCookie("gh_projectId"),
                dicCode: 'space_type',
            }).then((res) => {
                state.categories = res.data
            })
        }

        return {
            ...toRefs(state),
            getSpace,
            title,
            saveSpace,
            search,
            handleCurrentChange,
            del,
            edit,
            addSpace,
            select,
            projectId,
            form,
            getDicUtil,
            getSpaceUse,
            getSpaceType
        }
    },
}
</script>

<style lang="scss" scoped>
.electric-box {
    .qrcode {
        background: #091822;
    }
}</style>
