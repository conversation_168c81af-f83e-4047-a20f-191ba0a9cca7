<template>
    <div class="bim-box">

        <div class="content">
            <div class="bimbox h100" id="gh-bim"></div>
        </div>

        <div class="bim" v-for="item in tags" :key="item.id" :id="'t' + item.id">
            <el-popover placement="top" :content="getName(item)" :disabled="!getName(item)" trigger="hover">
                <template #reference>
                    <div class="icon" v-if="item.icon" @contextmenu="showLink($event, item)">
                        <i v-if="!item.icon.startsWith('#')" :class="item.icon" :style="{ color: item.color }"></i>
                        <svg v-if="item.icon.startsWith('#')"
                            :class="[(alarm['p_' + item.objectId] && item.alarmConfig.type == 2 && alarm['p_' + item.objectId].value && alarm['p_' + item.objectId].value == alarm['p_' + item.objectId].real) ? 'alarm' : '']"
                            class="icon1" aria-hidden="true"
                            :style="{ fill: alarm['p_' + item.objectId] && item.alarmConfig.type == 3 && alarm['p_' + item.objectId].value && alarm['p_' + item.objectId].value == alarm['p_' + item.objectId].real ? item.alarmConfig.color : item.color }">
                            <use :xlink:href="item.icon"></use>
                        </svg>
                    </div>
                </template>
            </el-popover>

            <div class="line-label" v-if="item.data && item.data.length > 0">
                <div class="img">
                    <img src="./img/line.png" />
                </div>
                <div class="line-content">
                    <div class="item" v-for="(d, i) in item.data" :key="i">
                        <div class="item-text">{{ d.name }}</div>
                        <div class="item-value" :id="'t' + item.id + i" v-if="d.type == 1">
                            {{ realData['t' + item.id + i] }}
                        </div>
                        <!-- //图标显示 -->
                        <div class="item-value" v-if="d.type == 2">
                            <i class="cursor" v-show="iconCondition(realData['t' + item.id + i], t.factor, t.value)"
                                v-for="(t, j) in d.conditions" :key="j" :class="t.icon"></i>
                        </div>

                        <!-- //文本显示 -->
                        <div class="item-value" v-if="d.type == 3">
                            <span v-show="iconCondition(realData['t' + item.id + i], t.factor, t.value)"
                                v-for="(t, j) in d.conditions" :key="j">
                                {{ t.text }}
                            </span>
                        </div>
                        <div class="item-unit" v-if="d.unit">{{ getUnit(d.unit) }}</div>
                    </div>
                </div>
            </div>

        </div>

        <div ref="linkRef" class="link" @click="showLinkClick">
            附近监控
        </div>

        <panel ref="panelRef"></panel>

        <diagram ref="diagramRef"></diagram>

        <cctv :url="url" ref="camRef"></cctv>

        <!-- 快捷键菜单 -->

    </div>
</template>

<script>
import {
    defineComponent,
    reactive,
    toRefs,
    onMounted,
    watch,
    computed,
    inject,
    nextTick,
    onBeforeUnmount,
    getCurrentInstance,

} from 'vue'
import {
    useStore
} from 'vuex'
import {
    getCookie,
    setCookie
} from '@/utils/cookie'
import CountTo from '@/components/vueCountTo/vue-countTo'
import socket from '@/utils/socket'
import cctv from '@/components/cctv/src/main.vue'
import {
    home
} from './status'
import panel from './components/panel.vue'
import diagram from './components/diagram.vue'

export default defineComponent({
    sockets: {
        live(res) {
            this.process(res)
        },
        onVarsChangedCallback(res) {
            this.process(res)
        },
    },
    components: {
        CountTo,
         cctv,
        panel,
        diagram
    },
    setup(props) {
        const {
            proxy
        } = getCurrentInstance();
        const instance = {
            $viewer: null,
            drawableContainer: null,
        }
        const emitter = inject('mitt');
        //报警列表定位
        emitter.off('zoomToComponents');
        emitter.on('zoomToComponents', (data) => {
            if (data.deviceId) {
                proxy.$api.getObjectId({
                    projectId: getCookie("gh_projectId"),
                    deviceId: data.deviceId,
                    fileId: state.id
                }).then(res => {
                    if (res.data && res.data.objectId) {
                        zoomToComponents(res.data.objectId);
                    }
                });
            } else if (data.objectId) {
                zoomToComponents(data.objectId);
            }
        });

        const store = useStore();

        const state = reactive({
            camRef: null,
            panelRef: null,
            diagramRef: null,
            linkRef: null,
            id: '',
            drawableConfig: null,
            floors: [], //当前模型所有的楼层
            url: '',
            // makeOthersTranslucent: null,
            sockets: null,
            sData: [], //状态数据
            sInputData: [], //模拟输入
            sOutData: [], //模拟输出
            numData: [], //数字输出 控制
            tags: [],
            units: [],
            realData: {},
            alarm: {},
            isOpen: false,
            deviceId: '',
            floorName: null, //bim模型中楼层名称
            ids: [], //标签集合
            colors: [], //构件变色集合
            complete: false,
            model: "", //模型的url
            objectId: null, //构件定位
            list: [],
            isActive: 0,
            showLinkMenu: false,
            contextItem: null,
            showMap: new Set(),
            hideMap: new Set(),
        });

        const activeMenus = computed(() => {
            let menu = getCookie('funMenus');
            return store.state.menu.funMenus || (menu ? JSON.parse(menu) : '')
        });

        const isFloorExplosionActivated = computed(() => {
            return store.state.area.isFloorExplosionActivated
        });

        const areaId = computed(() => {
            // let area = getCookie('area');
            return store.state.area.area
        });

        const projectId = computed(() => {
            return store.state.user.projectId || getCookie('gh_projectId')
        })

        watch(activeMenus, (val, old) => {
            if (val.id == old.id) //菜单不变不加载
            {
                return;
            }
            clearTag();
            if (val.model && val.model != state.model) {
                state.ids = []
                refreshBim(val.model)
            } else if (val.model && val.model == state.model) {
                loadAllConfig()
                showMenuCondition();
            }


        })
        watch(areaId, val => {
            // let mode = getCookie("mode");
            // if (mode == 1) {
                if (val && val.id==-1) {
                    clearTag()
                    instance.$viewer.showAllComponents();
                    loadAllConfig()
                    state.floorName = null;
                    return;
                }
                else if (val && val.bimFloor) {
                    changeFloor(val.bimFloor)
                }
                showMenuCondition();
            // }

        })
        state.sockets = inject('socket')
        onMounted(() => {
            getDicUtil();
            if (activeMenus.value.model) {
                initBim(activeMenus.value.model)
            }
        })
        const getDicUtil = () => {
            proxy.$api.getDicUtil({
                dicCode: 'unit',
                projectId: getCookie("gh_projectId"),
            }).then((res) => {
                state.units = res.data
            })
        }
        const initBim = (fileUrl) => {
            if (fileUrl) {
                let path = fileUrl.split("/");
                if (path[3].length == 32) {
                    state.id = path[3]
                } else {
                    state.id = path[4];
                }
                state.model = fileUrl
                var BimfaceLoaderConfig = new BimfaceSDKLoaderConfig();
                BimfaceLoaderConfig.dataEnvType = BimfaceEnvOption.Local;
                BimfaceLoaderConfig.sdkPath = './jssdk';
                BimfaceLoaderConfig.path = `${fileUrl.substring(0, fileUrl.indexOf('index'))}viewToken.json`;
                try {
                    BimfaceSDKLoader.load(
                        BimfaceLoaderConfig,
                        successCallback,
                        failureCallback
                    )
                } catch (error) {

                }

            }
        }
        const successCallback = (viewMetaData) => {
            if (viewMetaData.viewType == '3DView') {
                let view = document.getElementById('gh-bim')
                let config = new Glodon.Bimface.Application.WebApplication3DConfig()
                config.domElement = view
                // config.enableExplosion = true; //楼层爆炸
                let app = new Glodon.Bimface.Application.WebApplication3D(config)
                instance.$viewer = app.getViewer()
                instance.$viewer.addModel(viewMetaData)
                let colorRed = new Glodon.Web.Graphics.Color(255, 255, 255, 0)
                instance.$viewer.setBackgroundColor(colorRed)
                instance.$viewer.addEventListener(
                    Glodon.Bimface.Viewer.Viewer3DEvent.MouseClicked,
                    (data) => {
                        console.log(data)
                    }
                )

                instance.$viewer.addEventListener(
                    Glodon.Bimface.Viewer.Viewer3DEvent.ViewAdded,
                    () => {
                        //加载过程中，中断加载还会调用此方法
                        if (instance.$viewer) {
                            instance.$viewer.render();
                            // state.makeOthersTranslucent =
                            //     Glodon.Bimface.Viewer.IsolateOption.MakeOthersTranslucent
                            state.drawableConfig = new Glodon.Bimface.Plugins.Drawable.DrawableContainerConfig();
                            state.drawableConfig.viewer = instance.$viewer
                            instance.drawableContainer = new Glodon.Bimface.Plugins.Drawable.DrawableContainer(
                                state.drawableConfig
                            )
                            // instance.$viewer.setCameraStatus(home)
                            instance.$viewer.render();
                            //加载完成标识
                            state.complete = true;
                            getModelConfig();
                            loadAllConfig();

                            //菜单相关配置，显示与隐藏
                            showMenuCondition();

                            //模型全局配置，显示与隐藏
                           // getModelCondition();

                        }

                    }
                );

            }
        }
        const showMenuCondition = () => {
            // if (state.hideMap.size > 0) {
            //     state.hideMap.forEach((k, v) => {
            //         instance.$viewer.showComponentsByObjectData(v);
            //     })
            //     state.hideMap.clear();
            // }
            // if (state.showMap.size > 0) {
            //     state.showMap.forEach((k, v) => {
            //         instance.$viewer.hideComponentsByObjectData(v);
            //     })
            //     state.showMap.clear();
            // }
            let hideMap = JSON.parse(sessionStorage.getItem('hideMap'));
            let showMap = JSON.parse(sessionStorage.getItem('showMap'));
            if (activeMenus.value.name && activeMenus.value.name != '首页' && activeMenus.value.name != '综合态势') {
                if (hideMap && hideMap.length > 0) {
                    hideMap.forEach(v => {
                        instance.$viewer.showComponentsByObjectData(v);
                    })
                }
                if (showMap && showMap.length > 0) {
                    showMap.forEach(v => {
                        instance.$viewer.hideComponentsByObjectData(v);
                    })
                }
            }

            let hide = localStorage.getItem('hideCondition');
            let show = localStorage.getItem('showCondition');
            if (hide && hide != 'null') {
                let hideCondition = JSON.parse(hide);
                if (hideCondition.length) {
                    instance.$viewer.hideComponentsByObjectData(hideCondition);
                    // state.hideMap.add(hideCondition);
                } else {
                    instance.$viewer.hideComponentsByObjectData([hideCondition]);
                    // state.hideMap.add([hideCondition]);
                }
                // sessionStorage.setItem('hideMap', JSON.stringify(state.hideMap))
            }
            if (show && show != 'null') {
                let showCondition = JSON.parse(show);
                if (instance.$viewer.showComponentsByObjectData) {
                    if (showCondition.length) {
                        instance.$viewer.showComponentsByObjectData(showCondition);
                        // state.showMap.add(showCondition);
                    } else {
                        instance.$viewer.showComponentsByObjectData([showCondition]);
                        // state.showMap.add([showCondition]);
                    }
                    // sessionStorage.setItem('showMap', JSON.stringify(state.showMap))
                }

            }
        }
        const failureCallback = (error) => {
            console.log(error)
        }
        const getModelConfig = () => {
            proxy.$api.getModelConfig({
                projectId: getCookie("gh_projectId"),
                fileId: state.id,
            }).then((res) => {
                res.data.forEach(d => {
                    if (d.ids && d.color && d.opacity) {
                        let color = new Glodon.Web.Graphics.Color(d.color, parseFloat(d.opacity));
                        instance.$viewer.overrideComponentsColorById(d.ids.split(","), color);
                        // 根据Id修改构件透明度
                        // instance.$viewer.overrideComponentsOpacityById(["307240"], 0.8);
                        instance.$viewer.render();
                    } else if (d.conditions && d.color && d.opacity) {
                        let color = new Glodon.Web.Graphics.Color(d.color, parseFloat(d.opacity));
                        let conditions = JSON.parse(d.conditions);
                        if (conditions.length) {
                            instance.$viewer.overrideComponentsColorByObjectData(JSON.parse(d.conditions), color);
                        } else {
                            instance.$viewer.overrideComponentsColorByObjectData([JSON.parse(d.conditions)], color);
                        }

                        instance.$viewer.render();
                    } else if (d.ids && !d.color && d.opacity) {
                        // 根据Id修改构件透明度
                        instance.$viewer.overrideComponentsOpacityById(d.ids.split(","), parseFloat(d.opacity));
                        instance.$viewer.render();
                    } else if (d.conditions && !d.color & d.opacity) {
                        instance.$viewer.overrideComponentsOpacityByObjectData([JSON.parse(d.conditions)], parseFloat(d.opacity));
                        instance.$viewer.render();

                    }
                })
            })
        }

        const getModelCondition = () => {
            proxy.$api.getModelCondition({
                projectId: getCookie("gh_projectId"),
                fileId: state.id,
            }).then((res) => {
                res.data.forEach(d => {
                    // showComponentsByObjectData
                    if (d.hideCondition) {
                        let hideCondition = JSON.parse(d.hideCondition);
                        if (hideCondition.length) {
                            instance.$viewer.hideComponentsByObjectData(JSON.parse(d.hideCondition));
                        } else {
                            instance.$viewer.hideComponentsByObjectData([JSON.parse(d.hideCondition)]);
                        }
                    }

                    if (d.showCondition) {
                        let showCondition = JSON.parse(d.showCondition);
                        if (instance.$viewer.showComponentsByObjectData) {
                            if (showCondition.length) {
                                instance.$viewer.showComponentsByObjectData(JSON.parse(d.showCondition));
                            } else {
                                instance.$viewer.showComponentsByObjectData([JSON.parse(d.showCondition)]);
                            }
                        }

                    }

                    // if (d.home) {
                    //     instance.$viewer.setCameraStatus(JSON.parse(d.home));
                    // }

                    if (d.transparentCondition) {

                    }

                    instance.$viewer.render();

                })
            })
        }

        const loadAllConfig = () => {
            if (state.isOpen) {
                proxy.$api.getDeviceConfig({
                    projectId: getCookie("gh_projectId"),
                    fileId: state.id,
                    deviceId: state.deviceId,
                }).then((res) => {
                    addCustom(res.data)
                    if (instance.$viewer) {
                        instance.$viewer.render()
                    }
                })
            } else {
                if (state.complete) {
                    let aid = areaId.value ? areaId.value.id : null;
                    if (aid == -1) {
                        aid = null;
                    }
                    //清空标签
                    state.tags = [];
                    state.ids = [];
                    state.colors = [];
                    proxy.$api.getBIMConfig({
                        projectId: getCookie("gh_projectId"),
                        fileId: state.id,
                        menuId: activeMenus.value.id,
                        areaId: aid,
                        floorShow: areaId.value?.name ? null : true
                    }).then((res) => {
                        addCustom(res.data)
                    })
                }

            }
        }
        const addCustom = (data) => {
            state.tags = data;
            socket.unsubscribe(state.sockets, 'bim', 'real')
            nextTick(() => {
                if (data && data.length > 0)
                    data.forEach((d, i) => {
                        if (d.data) {
                            d.data.forEach((c, j) => {
                                state.realData = Object.assign({}, state.realData, {
                                    ['t' + d.id + j]: 0,
                                })
                                if (c.variable) {
                                    var v = c.variable.split(':')
                                    let item = {
                                        id: 't' + d.id + j,
                                        iosvrKey: v[0],
                                        chlKey: v[1],
                                        ctrlKey: v[2],
                                        varKey: v[3],
                                        realTime: false,
                                    }
                                    socket.subscribe(state.sockets, 'real', 'bim', [item])
                                }
                            })
                        }
                        if (d.variable) {
                            let v = d.variable.split(':')
                            let item = {
                                id: 'p_' + d.objectId,
                                iosvrKey: v[0],
                                chlKey: v[1],
                                ctrlKey: v[2],
                                varKey: v[3],
                                realTime: false,
                            }
                            socket.subscribe(state.sockets, 'real', 'bim', [item])
                            if (d.alarmConfig.real) {
                                d.alarmConfig.real = -1;
                            }
                            state.alarm = Object.assign({}, state.alarm, {
                                ['p_' + d.objectId]: d.alarmConfig,
                            })
                        }
                        let config = new Glodon.Bimface.Plugins.Drawable.CustomItemConfig()
                        config.content = document.getElementById('t' + d.id)
                        config.viewer = instance.$viewer
                        config.worldPosition = JSON.parse(d.position)
                        let customItem = new Glodon.Bimface.Plugins.Drawable.CustomItem(
                            config
                        )
                        if (customItem.id) {
                            state.ids.push(customItem.id)
                        }
                        customItem.onClick(function (item) {
                            Analyevents(d)
                        })
                        instance.drawableContainer.addItem(customItem);
                        instance.$viewer.render()
                    })
                createClusterItem()
            })
        }
        let clusterContainer;
        const createClusterItem = () => {
            // 构造聚合标签容器配置项
            let clusterContainerConfig = new Glodon.Bimface.Plugins.Cluster.ClusterContainerConfig();
            clusterContainerConfig.viewer = instance.$viewer;
            // 构造聚合标签容器
            clusterContainer = new Glodon.Bimface.Plugins.Cluster.ClusterContainer(clusterContainerConfig);
            // 构造聚合标签配置项
            let clusterItemConfig = new Glodon.Bimface.Plugins.Cluster.ClusterItemConfig();
            clusterItemConfig.tags = instance.drawableContainer.getAllItems();
            // console.log(drawableContainer.getAllItems().length);
            clusterItemConfig.maxLevel = 8;
            clusterItemConfig.viewer = instance.$viewer;
            // 构造聚合标签对象
            let clusterItem = new Glodon.Bimface.Plugins.Cluster.ClusterItem(
                clusterItemConfig
            );
            // 定义聚合标签的点击事件
            clusterItem.onClick(function (data) {
                let boundingBox = data.boundingBox;
                instance.$viewer.zoomToBoundingBox(boundingBox, 5);
            });
            clusterContainer.addCluster(clusterItem);
            clusterContainer.update();
        }
        const Analyevents = (config, tag) => {

            state.iconClickConfig = config;
            //播放视频
            if (config.actionType == '1') {
                let servers = config.server.split('_')
                let cams = config.cam.split('_')
                // cams参数格式 ${d.camToken}_${d.strName}_${d.serverType}_${d.strSrcIpAddress}_${d.strUser}_${d.strPasswd}_${d.profileToken}
                //state.url = `${servers[0]}|${servers[2]}|${servers[3]}|${cams[0]}|${cams[1]}|${cams[1]}|${cams[2]}|${cams[3]}|${cams[4]}|${cams[5]}|${cams[6]}`
                state.camRef.opened({
                    server: servers[2], //流媒体ip --h5参数
                    port: servers[3], //流媒体port ---h5参数
                    token: cams[0], //zlm和h5平台通用
                    name: cams[1], //摄像机名称 --通用
                    serverType: cams[2], //平台类型 1 h5 2--zlm平台
                    ip: cams[3], //摄像机ip   zlm onvif
                    username: cams[4], //摄像机用户名 zlm onvif
                    password: cams[5], //摄像机密码 zlm onvif
                    profileToken: cams[6], //摄像机 onvif profileToken zlm onvif
                    ptzEnable: cams[7]
                });
            }
            //跳转页面显示模型
            if (config.actionType == '3' && config.modelFileId) {
                if (config.modelFileId) {
                    let d = config.modelFileId.split('_')
                    state.id = d[0]
                    state.deviceId = d[2]
                    state.tags = []
                    state.isOpen = true
                    if (instance.$viewer) {
                        if (state.complete) {
                            // instance.$viewer.destroy()
                        }
                        instance.$viewer = null
                    }
                    document.getElementById('gh-bim').innerHTML = ''
                    initBim(d[1])
                }
            }
            //跳转楼层
            if (config.actionType == 4) {
                if (config.openFloor) {
                    changeFloor(config.openFloor)
                }
            }

            //打开二维组态
            if (config.actionType == 5) {
                if (config.diagramId) {
                    let viewUrl = `${window.PROD_9008_API}/runview.html?diagramId=` + config.diagramId +
                        `&token=${getCookie("gh_token")}&projectId=${getCookie("gh_projectId")}&userName=${"gh_name"}&userId=${getCookie('gh_id')}`;
                    state.diagramRef.show(viewUrl, config.diagramName || '视图')
                }
            }

            //跳转面板
            if (config.actionType == 2) {
                state.panelRef.show(config);
            }
        }
        const changeFloor = (floor) => {
            if (state.complete) {
                clearTag();
                state.tags = []
                if (state.floorName == floor) {
                    state.floorName = ''
                    store.commit('area/SET_NAV_AREA', null)
                    instance.$viewer.showAllComponents()
                } else {
                    state.floorName = floor
                    instance.$viewer.hideAllComponents()
                    instance.$viewer.showComponentsByObjectData([{
                        levelName: floor,
                    },])
                }
                instance.$viewer.render()
                loadAllConfig()
            }
        }
        const clearTag = () => {
            state.ids.forEach((id) => {
                if (instance.drawableContainer) {
                    instance.drawableContainer.removeItemById(id)
                }
            })
            if (clusterContainer) {
                clusterContainer.getClusters().forEach(d => {
                    clusterContainer.removeClustersById(d.id);
                })
            }
            if (state.colors.length) {
                instance.$viewer.restoreComponentsColorById(state.colors)
                state.colors = [];
                instance.$viewer.render()
            }
        }

        const process = (res) => {
            if (res) {
                let data = JSON.parse(res)
                if (data.batchDefinitionId == 'real' && data.clientId == 'bim') {
                    data.data.forEach((d) => {
                        if (d.id.startsWith('p_')) {
                            state.alarm[d.id].real = d.value
                            processAnnotation(d.id, d.value) //alarm
                        } else if (d.id.startsWith('s_')) {
                            state.panelData[d.id] = d.value
                        } else {
                            state.realData[d.id] = d.value
                        }
                    })
                }
            }
        }
        //报警处理
        const processAnnotation = (id, value) => {
            let config = state.alarm[id]
            if (config) {
                //旋转
                if (config.type == 1) { }
                //闪烁
                if (config.type == 2 && value == config.value) {
                    if (instance.$viewer && instance.$viewer.addBlinkComponentsById) {
                        instance.$viewer.addBlinkComponentsById([id.split('_')[1]])
                        instance.$viewer.setBlinkColor(
                            new Glodon.Web.Graphics.Color(config.color, 0.8)
                        )
                        instance.$viewer.enableBlinkComponents(true)
                        instance.$viewer.setBlinkIntervalTime(500)
                        instance.$viewer.render()
                    }

                }
                //变色
                if (config.type == 3 && value == config.value) {
                    var color = new Glodon.Web.Graphics.Color(config.color, 0.8);
                    if (instance.$viewer) {
                        state.colors.push(id.split('_')[1]);
                        instance.$viewer.overrideComponentsColorById([id.split('_')[1]], color)
                    }
                    instance.$viewer.render()
                }
                //取消闪烁
                if (config.type == 2 && value != config.value) {
                    instance.$viewer.clearBlinkComponentsById([id.split('_')[1]])
                    instance.$viewer.render()
                }
                //取消变色
                if (config.type == 3 && value != config.value) {
                    instance.$viewer.restoreComponentsColorById([id.split('_')[1]])
                    instance.$viewer.render()
                }
            }
        }

        const iconCondition = (value, condition, v) => {
            if (!value) value = 0
            if (condition && eval(value + condition + v)) return true
            return false
        }
        const condition = (value, factor, v) => {
            if (!value) value = 0
            if (eval(value + factor + v)) return true
            return false
        }
        const getUnit = (id) => {
            let name = ''
            for (let u of state.units) {
                if (u.tagValue == id) {
                    name = u.tagName
                    break
                }
            }
            return name
        }

        const refreshBim = (value) => {
            let bimdiv = document.getElementById('gh-bim')
            bimdiv.innerHTML = ''
            if (instance.$viewer) {
                if (state.complete) {
                    // instance.$viewer.destroy()
                }

                instance.$viewer = null
            }
            initBim(value)
            state.model = value;
        }
        const zoomToComponents = (id) => {

            // if (state.objectId == id) {
            //     instance.$viewer.clearSelectedComponents();
            //     instance.$viewer.setCameraStatus(home)
            //     state.objectId = null;
            // } else {
                state.objectId = id;
                //加载完成才能定位
                if (instance.$viewer && state.complete) {
                    instance.$viewer.clearSelectedComponents();
                    instance.$viewer.setSelectedComponentsById([id]);
                    instance.$viewer.zoomToSelectedComponents();

                    instance.$viewer.addBlinkComponentsById([id])
                    instance.$viewer.setBlinkColor(
                        new Glodon.Web.Graphics.Color("#C10223", 1)
                    )
                    instance.$viewer.enableBlinkComponents(true)
                    instance.$viewer.setBlinkIntervalTime(500)

                    setTimeout(() => {
                        instance.$viewer.clearBlinkComponentsById([id])
                        instance.$viewer.render()
                    }, 3000)

                }
            // }

        }
        const getName = (item) => {
            if (item.cam) {
                return item.cam.split('_')[1]
            } else if (item.deviceName) {
                return item.deviceName
            }
            return "";
        }

        onBeforeUnmount(() => {
            state.complete = false;
        });
        const tabClick = (index, item) => {
            state.isActive = index;
            setCookie('component', item.component)
            if (item.component && !item.component.startsWith("pop_")) { //防止刷新页面 bim重新加载 pop页面不用设置模型
                //cookie大小限制
                setCookie('funMenus', JSON.stringify({
                    id: item.id,
                    name: item.name,
                    fullName: item.fullName,
                    component: item.component,
                    model: item.model
                }))

                store.commit('menu/SET_FUN_MENU', item)
            } else {
                let menu = getCookie('funMenus');
                if (menu) {

                    //更新上面二级菜单名称
                    let obj = JSON.parse(menu);
                    obj.fullName = item.fullName;
                    setCookie('funMenus', JSON.stringify(obj))
                    store.commit('menu/SET_FUN_MENU', obj)
                }
            }

        };

        const showLink = (event, item) => {
            console.log(event);
            state.contextItem = item;
            state.linkRef.style.left = event.pageX + 'px';
            state.linkRef.style.top = event.pageY + 'px';
            state.showLinkMenu = true;
        }

        const showLinkClick = () => {
            state.showLinkMenu = false;
            state.linkRef.style.left = '-10000px';
            console.log(state.contextItem)
            getLinkConfig()
        }
        const getLinkConfig = async () => {
            const {
                data
            } = await proxy.$api.getLinkConfig({
                fileId: state.id,
                areaId: state.contextItem.areaId
            });
            let length = state.tags.length;
            state.tags = [...state.tags, ...data];
            nextTick(() => {
                if (data && data.length > 0)
                    data.forEach((d, i) => {
                        let config = new Glodon.Bimface.Plugins.Drawable.CustomItemConfig()
                        config.content = document.getElementById('t' + (i + length))
                        config.viewer = instance.$viewer
                        config.worldPosition = JSON.parse(d.position)
                        let customItem = new Glodon.Bimface.Plugins.Drawable.CustomItem(
                            config
                        )
                        if (customItem.id) {
                            state.ids.push(customItem.id)
                        }
                        customItem.onClick(function (item) {
                            Analyevents(d)
                        })
                        instance.drawableContainer.addItem(customItem);
                        instance.$viewer.render()
                    })
                createClusterItem()
            })
        }

        return {
            ...toRefs(state),
            initBim,
            refreshBim,
            getName,
            successCallback,
            failureCallback,
            loadAllConfig,
            addCustom,
            Analyevents,
            process,
            processAnnotation,
            iconCondition,
            condition,
            getUnit,
            close,
            changeFloor,
            zoomToComponents,
            projectId,
            getDicUtil,
            areaId,
            tabClick,
            isFloorExplosionActivated,
            getModelConfig,
            activeMenus,
            showLink,
            showLinkClick
        }
    },
})
</script>

<style lang="scss" scoped>
.bim-box {
    height: 100%;
    width: 100%;

    .content {
        height: 100%;
        width: 100%;
        background: transparent;
        position: relative;
        color: #cdcdcd;
    }
}

.bimbox {
    position: relative;
}

.active {
    color: rgb(122, 177, 239);
    background: #0d2943;
}

.link {
    background-color: #03a9f44d;
    padding: 5px;
    white-space: nowrap;
    position: absolute;
    top: -10000px;
    cursor: pointer;
    z-index: 100;
    color: #fff;
    border-radius: 5px;

}

.bim {
    user-select: none;

    .icon {
        i {
            font-size: 30px;
            cursor: pointer;
        }

        position: relative;
        transform: translate(-21.5px, -100%);

    }

    .line-label {
        width: auto;
        display: flex;
        transform: translateY(-35px);

        .line-content {
            padding: 10px;
            background-color: #13232d;
            transform: translateY(-50%);
            border: 0.02rem solid rgb(38 210 229);
            border-radius: 5px;
            box-shadow: rgb(38 210 229) 0px 0px 15px inset;

            .item {
                display: flex;
                white-space: nowrap;
                font-size: 16px;

                .item-text {
                    margin-right: 20px;
                }

                .item-value {
                    color: #fac46e;
                    margin-right: 5px;
                }

                .item-unit {
                    color: #fac46e;
                }
            }
        }
    }
}

.alarm {
    font-size: 32px;
    animation: alarm .5s infinite alternate;
}

@keyframes alarm {
    to {
        fill: red;
    }
}
</style>
