<template>
<el-dialog align-center append-to-body   draggable @open="open" @closed="close" custom-class="addDiagram" v-model="show" width="940px" title="备件申请">

    <el-form class="form" :model="apply" :rules="rule" ref="form" label-width="100px" label-suffix="">
        <el-row type="flex" gutter="30">
            <el-col :span="12">
                <el-form-item label="名称" prop="name">
                    <el-input type="text" v-model="apply.name"></el-input>
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="所用设备" prop="deviceId">
                    <el-cascader ref="device" popper-class="cascader" v-model="apply.deviceId" :props="props" :options="devices"></el-cascader>
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="用途描述" prop="description">
                    <el-input type="textarea" rows="3" v-model="apply.description"></el-input>
                </el-form-item>
            </el-col>
        </el-row>
    </el-form>

    <panel title="备件添加"></panel>
    <div class="btn-group search_box">
        <div type="primary" icon="Plus" size="mini" class="searchBtn" @click="addSpare">新增</div>
    </div>

    <el-table class="table" :data="apply.spares" height="200px" fit>
        <template #empty>
            <no-data />
        </template>
        <el-table-column prop="name" label="备件名称" align="center">
        </el-table-column>
        <el-table-column prop="count" label="申请数量" align="center">
        </el-table-column>
        <el-table-column label="操作" align="center">
            <template #default="scope">
                <el-tag type="danger" class="cursor" @click="remove(scope.$index, scope.row)">移除</el-tag>
            </template>
        </el-table-column>
    </el-table>

    <div class="center search_box">
        <el-button type="primary" class="searchBtn" size="small" @click="save('form')">保存</el-button>
    </div>
    <el-dialog align-center append-to-body   draggable v-model="dialogSpareVisible" title="新增" custom-class="addDiagram border0" width="700px">
        <el-form ref="form1" :model="spare" :rules="rule" class="form">
            <el-row type="flex" :gutter="30">
                <el-col :span="12">
                    <el-form-item label="备件" prop="id">
                        <el-select filterable clearable @change="changeSpare" v-model="spare.id">
                            <el-option v-for="item in spares" :key="item.id" :label="item.name" :value="item.id"></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="库存">
                        <el-input disabled placeholder="库存" v-model="spare.count"></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-form-item label="申请数量" prop="applyCount">
                <el-input placeholder="申请数量" v-model.number="spare.applyCount"></el-input>
            </el-form-item>
        </el-form>
        <template #footer>
            <div class="dialog-footer search_box">
                <div type="primary" class="searchBtn" size="small" @click="saveSpare('form1')">确 定</div>
            </div>
        </template>
    </el-dialog>
</el-dialog>
</template>

<script>
import {
    getCookie
} from '@/utils/cookie'
import {
    reactive,
    toRefs
} from 'vue'
import {
    computed,
    getCurrentInstance,
    onMounted,
    watch
} from 'vue'
import {
    useRoute,
    useRouter
} from 'vue-router'
import {
    ElMessage
} from 'element-plus'
import {
    useStore
} from 'vuex'

export default {
    props: ['show', 'id'],
    setup(props, {
        emit,
    }) {
        const router = useRouter()
        const route = useRoute()
        const {
            proxy
        } = getCurrentInstance()
        const store = useStore()
        const state = reactive({
            spare: {
                id: '',
                name: '',
                count: 0,
                applyCount: 0,
            },
            dialogSpareVisible: false,
            props: {
                label: 'name',
                value: 'id',
            },
            spares: [],
            types: [],
            units: [],
            list: [],
            apply: {
                id: 0,
                name: '',
                deviceId: '',
                description: '',
                spares: [],
            },
            devices: [],
            rule: {
                id: [{
                    required: true,
                    message: '备件不能空',
                    trigger: 'change',
                }, ],
                deviceId: [{
                    required: true,
                    message: '所用设备不能空',
                    trigger: 'change',
                }, ],
                description: [{
                    required: true,
                    message: '备件用途不能空',
                    trigger: 'change',
                }, ],
                applyCount: [{
                    required: true,
                    trigger: 'change',
                    validator: (rule, value, callback) => {
                        if (!Number.isInteger(value)) {
                            callback(new Error('请输入数字值'))
                        } else {
                            if (value <= 0) {
                                callback(new Error('数字必须大于等于1'))
                            } else {
                                callback()
                            }
                        }
                    },
                }, ],
            },
        })

        const open = () => {
            if (props.id > 0) {
                getApplyList(props.id)
            }
            getDevicesList()
        }
        const projectId = computed(() => {
            return store.state.user.projectId || getCookie('gh_projectId')
        })
        watch(projectId, (val) => {
            if (val) {
                getApplyList()
                getDevicesList()
            }
        })
        const getApplyList = (id) => {
            proxy.$api.getApply({
                id,
                projectId: projectId.value
            }).then((res) => {
                if (res.data && res.data.length > 0) {
                    let spares = []
                    if (res.data[0].spares && res.data[0].spares.length > 0) {
                        res.data[0].spares.forEach((s) => {
                            spares.push({
                                spareId: s.id,
                                name: s.name,
                                count: s.count,
                            })
                        })
                    }
                    state.apply = res.data[0]
                    state.apply.spares = spares
                }
            })
        }
        const getDevicesList = () => {
            proxy.$api.getDevicesOfType({
                projectId: getCookie('gh_projectId'),
            }).then((res) => {
                state.devices = res.data
            })
        }
        const getSpareList = () => {
            proxy.$api.getSpare({
                projectId: getCookie('gh_projectId'),
            }).then((res) => {
                state.spares = res.data
            })
        }

        const addSpare = () => {
            state.spare = {
                id: '',
                name: '',
                count: 0,
                applyCount: 0,
            }
            state.dialogSpareVisible = true
            getSpareList()
        }

        const saveSpare = (formName) => {
            proxy.$refs[formName].validate((valid) => {
                if (valid) {
                    if (state.spare.applyCount > state.spare.count) {
                        ElMessage({
                            type: 'error',
                            message: '申请数量不能大于库存',
                        })
                        return
                    }
                    state.apply.spares.push({
                        spareId: state.spare.id,
                        count: state.spare.applyCount, //申请的数量
                        name: state.spare.name,
                    })
                    state.dialogSpareVisible = false
                }
            })
        }

        const remove = (index, row) => {
            state.apply.spares.splice(index, 1)
        }
        const save = (formName) => {
            proxy.$refs[formName].validate((validate) => {
                if (validate) {
                    if (state.apply.deviceId && state.apply.deviceId.length > 0) {
                        state.apply.deviceId = state.apply.deviceId[
                            state.apply.deviceId.length - 1
                        ]
                    }
                    state.apply.projectId = getCookie('gh_projectId')

                    proxy.$api.addApply(state.apply).then((res) => {
                        emit("update:show", false)
                        emit("getApplyPage")
                    })
                }
            })
        }
        const changeSpare = (value) => {
            if (value) {
                let spare = state.spares.find((s) => s.id == value)
                state.spare.id = spare.id;

                state.spare.count = spare.count
                state.spare.name = spare.name
                state.spare.applyCount = 0
            } else {
                state.spare = {
                    id: '',
                    name: '',
                    count: 0,
                    applyCount: 0,
                }
            }
        }

        const close = () => {
            emit("update:show", false)
        }

        return {
            ...toRefs(state),
            getApplyList,
            getDevicesList,
            getSpareList,
            addSpare,
            saveSpare,
            remove,
            save,
            changeSpare,
            projectId,
            open,close
        }
    }
}
</script>

<style lang="scss" scoped>
.electric-box {
    padding: 0 10px;

    .wrapper {
        display: flex;
        flex-direction: column;
        height: calc(100% - 180px);

        .info {
            .el-form {
                margin-top: 20px;
                width: 40%;
            }
        }

        .table {
            height: 40%;
        }
    }
}

.btn-group {
    margin-top: 20px;
}

.btn {
    text-align: center;
    margin: 15px 0;
}
</style>
