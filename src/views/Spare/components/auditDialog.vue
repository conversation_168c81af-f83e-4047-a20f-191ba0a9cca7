<template>
<el-dialog align-center append-to-body   draggable custom-class="addDiagram border0"  v-model="dialogData.visible" width="940px" :title="dialogData.title">
    <el-form class="form" ref="form" :model="dialogData.scrap">
        <el-row type="flex" gutter="30">
            <el-col :span="12">
                <el-form-item label="报废名称">
                    <el-input disabled type="text" v-model="dialogData.scrap.name"></el-input>
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="申请人">
                    <el-input disabled type="text" v-model="dialogData.scrap.username"></el-input>
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="设备名称">
                    <el-input disabled type="text" v-model="dialogData.scrap.deviceName"></el-input>
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="申请时间">
                    <el-input disabled type="text" v-model="dialogData.scrap.createTime"></el-input>
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="报废原因">
                    <el-input disabled type="textarea" v-model="dialogData.scrap.description"></el-input>
                </el-form-item>
            </el-col>
        </el-row>
    </el-form>
    <template #footer>
        <div class="dialog-footer search_box">
            <div type="primary" class="searchBtn" size="small" @click="saveAudit(1)">通过</div>
            <div type="primary" class="delBtn" size="small" @click="saveAudit(0)">不通过</div>
        </div>
    </template>
</el-dialog>
</template>

<script>
import {
    getCurrentInstance
} from 'vue';
export default {
    props: {
        dialogData: {
            visible: false,
            title: '',
            scrap: {
                name: '',
                username: '',
                deviceName: '',
                createTime: '',
                description: ''
            }
        }
    },
    setup(props) {
        const {
            proxy
        } = getCurrentInstance()

        const saveAudit = (status) => {
            proxy.$api.auditScrap({
                status,
                deviceId: props.dialogData.scrap.deviceId,
                id: props.dialogData.scrap.id,
            }).then((res) => {
                if (res.success) {
                    props.dialogData.visible = false
                    proxy.$emit('getScrapPage')
                    ElMessage({
                        type: 'success',
                        message: res.msg
                    })
                }
            })
        }
        return {
            saveAudit
        }
    }
};
</script>
