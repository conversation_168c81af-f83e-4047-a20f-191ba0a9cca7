<template>
  <el-dialog align-center append-to-body    draggable custom-class="addDiagram border0" v-model="dialogData.visible" width="940px" :title="dialogData.title">
    <el-form class="form" ref="form" :model="dialogData.scrap" :rules="dialogData.rule">
      <el-row type="flex" gutter="30">
        <el-col :span="12">
          <el-form-item label="报废名称" prop="name">
            <el-input type="text" v-model="dialogData.scrap.name"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="设备选择" prop="deviceId">
            <el-cascader popper-class="cascader" ref="device" v-model="dialogData.scrap.deviceId" :props="props"
              :options="devices"></el-cascader>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="报废原因" prop="description">
        <el-input type="textarea" rows="4" v-model="dialogData.scrap.description"></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer search_box">
        <div type="primary" size="small" class="searchBtn" @click="saveScrap('form')">确 定</div>
      </div>
    </template>
  </el-dialog>
</template>
<script>
import { ElMessage } from 'element-plus'
import { getCookie } from '@/utils/cookie'
import { computed, getCurrentInstance, onMounted, reactive, toRefs, watch } from 'vue';
import { useStore } from 'vuex';
export default {
  props: {
    dialogData: {
      visible: false,
      edit: false,
      title: '',
      scrap: {

      },
      rule: {
        description: [
          {
            required: true,
            message: '描述不能空',
            trigger: 'change',
          },
        ],
        name: [
          {
            required: true,
            message: '名称不能空',
            trigger: 'blur',
          },
        ],
        deviceId: [
          {
            required: true,
            message: '设备不能空',
            trigger: 'change',
          },
        ],
      },
    }
  },
  setup (props) {
    const { proxy } = getCurrentInstance()
    const store = useStore()
    const state = reactive({
      devices: [],
      props: {
        label: 'name',
        value: 'id'
      },
    })
    onMounted(() => {
      getDeviceTree()
    })
    const projectId = computed(() => {
      return store.state.user.projectId || getCookie('gh_projectId')
    })
    watch(projectId, (val) => {
      if (val) getDeviceTree()
    })
    const getDeviceTree = () => {
      proxy.$api.getDevicesOfType({
        projectId: getCookie('gh_projectId'),
      }).then((res) => {
        state.devices = res.data
      })
    }
    const saveScrap = (formName) => {
      proxy.$refs[formName].validate((validate) => {
        if (validate) {
          props.dialogData.scrap.projectId = getCookie('gh_projectId')
          if (props.dialogData.scrap.deviceId && props.dialogData.scrap.deviceId.length > 0) {
            props.dialogData.scrap.deviceId = props.dialogData.scrap.deviceId[
              props.dialogData.scrap.deviceId.length - 1
            ]
          }
          if (props.dialogData.edit) {
            proxy.$api.editScrap(props.dialogData.scrap).then(res => {
              if (res.success) {
                props.dialogData.visible = false
                proxy.$emit('getScrapPage')
                ElMessage({
                  type: 'success',
                  message: res.msg
                })
              }
            })
          } else {
            proxy.$api.addScrap(props.dialogData.scrap).then(res => {
              if (res.success) {
                props.dialogData.visible = false
                proxy.$emit('getScrapPage')
                ElMessage({
                  type: 'success',
                  message: res.msg
                })
              }
            })
          }
        }
      })
    }
    return {
      ...toRefs(state),
      getDeviceTree,
      saveScrap,
      projectId
    }
  }

};
</script>