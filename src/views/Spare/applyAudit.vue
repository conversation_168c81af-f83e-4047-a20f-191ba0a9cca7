<template>
<el-dialog append-to-body   draggable @open="open" custom-class="addDiagram" @closed="close" v-model="show" width="640px" title="备件审批">
    <el-form class="form" label-position="right" label-width="90px" ref="form">
        <el-form-item label="申请名称" prop="name">
            <el-input disabled type="text" size="small" v-model="apply.name"></el-input>
        </el-form-item>
        <el-form-item label="申请人" prop="name">
            <el-input disabled type="text" size="small" v-model="apply.username"></el-input>
        </el-form-item>
        <el-form-item label="所用设备" prop="name">
            <el-input disabled type="text" size="small" v-model="apply.deviceName"></el-input>
        </el-form-item>
        <el-form-item label="申请时间" prop="name">
            <el-input disabled type="text" size="small" v-model="apply.createTime"></el-input>
        </el-form-item>
        <el-form-item label="申请用途" prop="description">
            <el-input disabled type="textarea" v-model="apply.description"></el-input>
        </el-form-item>
    </el-form>

    <el-table class="table" height="100%" :data="apply.spares" fit>
        <template #empty>
            <no-data />
        </template>
        <el-table-column prop="name" label="备件名称" align="center">
        </el-table-column>
        <el-table-column prop="count" label="申请数量" align="center">
        </el-table-column>
    </el-table>

    <div class="center btn search_box">
        <el-button type="primary" class="searchBtn" size="small" @click="saveAudit(1)">通过</el-button>
        <el-button type="primary" class="delBtn" size="small" @click="saveAudit(0)">不通过</el-button>
    </div>
</el-dialog>
</template>

<script>
import {
    reactive,
    toRefs
} from 'vue'
import {
    getCurrentInstance,
    onMounted
} from 'vue'
import {
    useRouter
} from 'vue-router'
export default {
    props: ['show', 'data'],
    emits:['getApplyPage'],
    setup(props, {
        emit
    }) {
        const router = useRouter()
        const {
            proxy
        } = getCurrentInstance()
        const state = reactive({
            apply: {
                deviceId: '',
                description: '',
                name: '',
                spares: [],
            },
        })
        onMounted(() => {
            if (sessionStorage.getItem('apply')) {
                state.apply = JSON.parse(sessionStorage.getItem('apply'))
            }
        })
        const saveAudit = (status) => {
            proxy.$api.audit({
                status,
                id: state.apply.id,
            }).then((res) => {
                close();
                emit('getApplyPage')
            })
        }
        const open = () => {
            state.apply = props.data;
        }
         const close = () => {
           emit("update:show",false)
        }
        return {
            ...toRefs(state),
            saveAudit,
            open,close
        }
    },
}
</script>

<style lang="scss" scoped>


.btn {
    margin: 15px 0;
}
</style>
