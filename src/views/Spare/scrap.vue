<template>
<div class="h100">
    <el-form :inline="true" class="search_box " size="small">
        <el-form-item label="名称">
            <el-input v-model="keyword" placeholder="请输入名称查询"></el-input>
        </el-form-item>
        <el-form-item>
            <div size="small" class="searchBtn" type="text" @click="search">查询</div>
        </el-form-item>

        <div class="btn-group">
            <div type="primary" icon="Plus" size="mini" class="searchBtn" @click="scrap(null, 'add')">申请
            </div>
        </div>
    </el-form>

    <el-table class="table" :data="list" height="calc(100% - 90px)" fit>
        <template #empty>
            <no-data />
        </template>
        <el-table-column prop="name" label="名称" align="center">
        </el-table-column>
        <el-table-column prop="deviceName" label="设备名称" align="center">
        </el-table-column>
        <el-table-column prop="description" label="报废原因" align="center">
        </el-table-column>
        <el-table-column prop="createTime" label="申请时间" align="center">
        </el-table-column>
        <el-table-column prop="username" label="申请人" align="center">
        </el-table-column>
        <el-table-column prop="scrapStatus" label="状态" align="center">
            <template #default="scope">
                <span style="color: green" v-if="scope.row.scrapStatus == -1">待审批</span>
                <span style="color: #13d4d9" v-if="scope.row.scrapStatus == 1">通过</span>
                <span style="color: #b4061a" v-if="scope.row.scrapStatus == 0">不通过</span>
            </template>
        </el-table-column>
        <el-table-column label="操作" align="center">
            <template #default="scope">
                <el-button type="text" class="editBtn" @click="scrap(scope.row, 'edit')">编辑</el-button>
                <el-button type="text" class="editBtn" @click="audit(scope.row)">审批</el-button>
                <el-button type="text" class="del" @click="del(scope.row)">删除</el-button>
            </template>
        </el-table-column>
    </el-table>

    <div class="center page">
        <el-pagination prev-icon="CaretLeft" next-icon="CaretRight" :page-size="size" :current-page="page" layout="total,prev, pager, next" @current-change="handleCurrentChange" :total="total">
        </el-pagination>
    </div>
    <scrap-dialog v-if="dialogData.visible" :dialog-data="dialogData" @getScrapPage="getScrapPage" />
    <audit-dialog v-if="auditDialog.visible" :dialog-data="auditDialog" @getScrapPage="getScrapPage" />
</div>
</template>

<script>
import {
    getCookie
} from '@/utils/cookie'
import {
    reactive,
    toRefs
} from 'vue'
import {
    getCurrentInstance,
    onMounted,
    watch
} from 'vue'
import {
    ElMessage,
    ElMessageBox
} from 'element-plus'
import scrapDialog from './components/scrapDialog.vue'
import auditDialog from './components/auditDialog.vue'
import {
    useStore
} from 'vuex'

export default {
    name: 'scrap',
    components: {
        scrapDialog,
        auditDialog
    },
    setup() {
        const store = useStore()
        const {
            proxy
        } = getCurrentInstance()
        const state = reactive({
            page: 1,
            size: 10,
            total: 0,
            list: [],
            data: null,
            items: [], //表格select
            date: [],
            type: '',
            keyword: '',
            dialogData: {
                visible: false,
                edit: false,
                title: '报废申请',
                scrap: {
                    name: '',
                    deviceId: null,
                    description: ''
                },
                rule: {
                    description: [{
                        required: true,
                        message: '名称不能空',
                        trigger: 'change',
                    }, ],
                    name: [{
                        required: true,
                        message: '描述不能空',
                        trigger: 'change',
                    }, ],
                    deviceId: [{
                        required: true,
                        message: '设备不能空',
                        trigger: 'change',
                    }, ],
                },
            },
            auditDialog: {
                visible: false,
                title: '报废申请管理',
                scrap: {
                    name: '',
                    username: '',
                    deviceName: '',
                    createTime: '',
                    description: ''
                }
            },
            tableHeight: window.innerHeight * 0.60
        })

        onMounted(() => {
            getScrapPage()
        })
        const projectId = () => {
            return store.state.user.projectId || getCookie('gh_projectId')
        }
        watch(projectId, (val) => {
            if (val) {
                getScrapPage()
            }
        })
        const getScrapPage = () => {
            proxy.$api.getScrap({
                name: state.keyword,
                projectId: getCookie('gh_projectId'),
                size: state.size,
                page: state.page,
            }).then((res) => {
                state.list = res.data
                state.total = res.total
            })
        }
        const handleCurrentChange = (page) => {
            state.page = page
            getScrapPage()
        }
        // 删除
        const del = (row) => {
            if (row.scrapStatus == -1) {
                ElMessageBox.confirm('是否确认要删除该数据？', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    confirmButtonClass: 'confirmBtn',
                    cancelButtonClass: 'cancelBtn',
                    type: 'warning',
                }).then(() => {
                    proxy.$api.delScrap({
                        id: row.id,
                    }).then((res) => {
                        getScrapPage()
                    })
                })
            } else {
                ElMessage({
                    type: 'error',
                    message: '审批后不能删除',
                })
            }
        }
        const scrap = (row, type) => {
            if (type == 'add') {
                state.dialogData.visible = true
                state.dialogData.edit = false
                state.dialogData.scrap = {
                    name: '',
                    deviceId: null,
                    description: ''
                }
            } else if (type == 'edit') {
                if (row.scrapStatus != -1) {
                    ElMessage({
                        type: 'error',
                        message: '审批后不能修改',
                    })
                    return
                }
                state.dialogData.visible = true
                state.dialogData.edit = true
                let id = row.id
                proxy.$api.getScrap({
                    id,
                }).then((res) => {
                    if (res.data && res.data.length > 0) {
                        state.dialogData.scrap = res.data[0]
                    }
                })
            }
        }
        const audit = (row) => {
            if (row.scrapStatus != -1) {
                ElMessage({
                    type: 'error',
                    message: '不能重复审批',
                })
                return
            }
            state.auditDialog.visible = true
            state.auditDialog.scrap = row
        }
        const search = () => {
            state.page = 1
            getScrapPage()
        }

        return {
            ...toRefs(state),
            getScrapPage,
            handleCurrentChange,
            del,
            scrap,
            audit,
            search,
            projectId
        }
    },
}
</script>

<style lang="scss" scoped>

</style>
