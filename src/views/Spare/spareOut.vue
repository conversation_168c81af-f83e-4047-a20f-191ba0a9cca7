<template>
<div class="h100">
    <el-form :inline="true" class="search_box" size="small">
        <el-form-item label="名称">
            <el-input v-model="keyword" placeholder="支持 名称 查询"></el-input>
        </el-form-item>
        <el-form-item>
            <div size="small" class="searchBtn" type="text" @click="search">查询</div>
        </el-form-item>
        <div class="btn-group">
            <div type="primary" icon="Plus" size="mini" class="searchBtn" @click="addApply">新增</div>
        </div>
    </el-form>

    <el-table class="table" :data="list" height="calc(100% - 90px)" fit>
        <template #empty>
            <no-data />
        </template>
        <el-table-column prop="name" label="名称" align="center">
        </el-table-column>
        <el-table-column prop="deviceName" label="所用设备" align="center">
        </el-table-column>
        <el-table-column prop="description" label="申请用途" align="center">
        </el-table-column>
        <el-table-column prop="createTime" label="申请时间" align="center">
        </el-table-column>
        <el-table-column prop="username" label="申请人" align="center">
        </el-table-column>
        <el-table-column label="状态" align="center">
            <template #default="scope">
                <span style="color: green" v-if="scope.row.status == -1">待审批</span>
                <span style="color: green" v-if="scope.row.status == 1">通过</span>
                <span style="color: red" v-if="scope.row.status == 0">不通过</span>
            </template>
        </el-table-column>
        <el-table-column type="expand" label="备件">
            <template #default="props">
                <div v-for="(item, i) in props.row.spares" :key="i">
                    备件名称: {{ item.name }} <br />
                    申请数量: {{ item.count }}
                </div>
            </template>
        </el-table-column>
        <el-table-column label="操作" align="center">
            <template #default="scope">
                <el-button type="text" class="editBtn" @click="edit(scope.$index, scope.row)">编辑</el-button>
                <el-button type="text" class="editBtn" @click="audit(scope.$index, scope.row)">审批</el-button>
            </template>
        </el-table-column>
    </el-table>

    <div class="center page">
        <el-pagination prev-icon="CaretLeft" next-icon="CaretRight" :page-size="size" :current-page="page" layout="total,prev, pager, next" @current-change="handleCurrentChange" :total="total">
        </el-pagination>
    </div>

    <edit v-model:show="show" :id="id" @getApplyPage="getApplyPage"></edit>
    <audit v-model:show="showAudit" :data="auditData" @getApplyPage="getApplyPage"></audit>
</div>
</template>

<script>
import {
    getCookie
} from '@/utils/cookie'

import {
    computed,
    getCurrentInstance,
    onMounted,
    reactive,
    toRefs,
    watch
} from 'vue'
import {
    useRouter
} from 'vue-router'
import {
    ElMessage,
    ElMessageBox
} from 'element-plus'
import {
    useStore
} from 'vuex'
import edit from './spareOutEdit.vue'
import audit from './applyAudit.vue'
export default {
    name: 'spareout',
    components: {
        edit,audit
    },
    setup() {
        const router = useRouter();
        const {
            proxy
        } = getCurrentInstance();
        const store = useStore()
        const state = reactive({
            page: 1,
            size: 10,
            total: 0,
            list: [],
            data: null,
            items: [], //表格select
            date: [],
            type: '',
            keyword: '',
            show: false,
            showAudit:false,//审批
            id: 0,
            auditData:null,
        })
        onMounted(() => {
            getApplyPage();
        })
        const projectId = computed(() => {
            return store.state.user.projectId || getCookie('gh_projectId')
        })
        watch(projectId, (val) => {
            if (val) getApplyPage()
        })
        const getApplyPage = () => {
            proxy.$api.getApply({
                name: state.keyword,
                projectId: getCookie('gh_projectId'),
                size: state.size,
                page: state.page,
            }).then((res) => {
                state.list = res.data
                state.total = res.total
            })
        }
        const handleCurrentChange = (page) => {
            state.page = page
            getApplyPage()
        }
        const del = (index, row) => {
            if (row.status == -1) {
                ElMessageBox.confirm('是否确认要删除该数据？', '提示', {
                    confirmButtonClass: 'confirmBtn',
                    cancelButtonClass: 'cancelBtn',
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning',
                }).then(() => {
                    proxy.$api.delApply({
                        id: row.id,
                    }).then((res) => {
                        getApplyPage()
                    })
                })
            } else {
                ElMessage({
                    type: 'error',
                    message: '审批后不能删除',
                })
            }
        }
        const edit = (index, row) => {
            if (row.status != -1) {
                ElMessage({
                    type: 'error',
                    message: '审批后不能修改',
                })
                return
            }
           state.id=row.id;
           state.show=true;
        }
        const addApply = () => {
            state.show = true;
        }
        const audit = (index, row) => {
            if (row.status != -1) {
                ElMessage({
                    type: 'error',
                    message: '不能重复审批',
                })
                return
            }
            state.showAudit=true;
            state.auditData=row;
        }
        const search = () => {
            state.page = 1
            getApplyPage()
        }

        return {
            ...toRefs(state),
            search,
            audit,
            addApply,
            edit,
            del,
            getApplyPage,
            handleCurrentChange,
            projectId
        }
    },
}
</script>

<style lang="scss" scoped>
.electric-box {
    .wrapper {
        display: flex;
        flex-direction: column;
        height: calc(100% - 180px);
    }
}
</style>
