

const meetingData=[
    {
      "id": 2,
      "subject": "bb",
      "summary": "",
      "start_time": "2022-12-27 17:23",
      "end_time": "2022-12-27 23:59",
      "count_minutes": 396,
      "room_id": 1,
      "agenda": [],
      "files": [],
      "checkin": 0,
      "checkin_ahead_minute": "",
      "history": 1,
      "audit": 0,
      "status": "5",
      "detail": {
        "room": {
          "id": 1,
          "name": "平顶山运行指挥中心",
          "location_id": 1,
          "location_path": "1-",
          "seats": 15,
          "open_start_time": "",
          "open_end_time": "",
          "department": "[]",
          "vr_link": "",
          "images": [],
          "create_time": "2022-12-27 16:54:35",
          "update_time": null,
          "delete_time": null,
          "union_id": 0,
          "seat_id": 0,
          "lstop": 0,
          "audit_user": null,
          "open_start_date": "",
          "open_end_date": "",
          "forbidden_status": 0,
          "forbidden_type": 0,
          "forbidden_info": "[]",
          "assign_type": 0,
          "scene": "{\"open\":[],\"close\":[]}",
          "is_device_control": 0,
          "paperless_rid": 0,
          "paperless_ip": "",
          "location_name": "城运中心",
          "deviceType": []
        },
        "creator": {
          "user_id": "admin",
          "displayname": "admin",
          "avatar": "/meetingcloud/avatar/admin/64?v=0",
          "email": null,
          "department_name": "组织架构,行政部"
        },
        "clients": [],
        "scene": {
          "sceneConfigId": 0,
          "sceneOnTime": "2022-12-27 ",
          "sceneOffTime": "2022-12-27 ",
          "sceneName": ""
        },
        "distribute": [],
        "auditor": [],
        "leader": [
          {
            "user_id": "admin",
            "displayname": "admin",
            "avatar": "/meetingcloud/avatar/admin/64?v=0",
            "email": null,
            "department_name": "组织架构,行政部"
          }
        ],
        "attender": [
          {
            "user_id": "13600000001",
            "displayname": "jkj",
            "avatar": "/meetingcloud/avatar/13600000001/64?v=0",
            "email": null,
            "department_name": ""
          },
          {
            "user_id": "admin",
            "displayname": "admin",
            "avatar": "/meetingcloud/avatar/admin/64?v=0",
            "email": null,
            "department_name": "组织架构,行政部"
          }
        ],
        "cc": [],
        "external": []
      },
      "creator": "admin",
      "auditor": "",
      "audit_time": null,
      "create_time": "2022-12-27 17:18:17",
      "update_time": null,
      "delete_time": null,
      "sms_type": "2",
      "sms_time": "10",
      "end_sms_time": "10",
      "num": 15,
      "is_active": 0,
      "desc": "",
      "checkin_stop_minute": "",
      "meeting_type": 0,
      "video_conference": 0,
      "video_conference_id": "",
      "seat_type": 1,
      "has_seat": 0,
      "seat_img": "",
      "union_id": 0,
      "cc_type": 1,
      "meeting_cycle_id": 0,
      "cancelled_by": "",
      "paperless_mid": 0,
      "any_value(ma.attend_status)": "accepted",
      "order_field": 5,
      "room_is_delete": false,
      "attenderCount": 2,
      "weekday": "周二",
      "attend_status": "",
      "role": "leader"
    },
    {
      "id": 4,
      "subject": "指挥中心",
      "summary": "",
      "start_time": "2023-01-03 10:30",
      "end_time": "2023-01-03 14:30",
      "count_minutes": 240,
      "room_id": 1,
      "agenda": [],
      "files": [],
      "checkin": 0,
      "checkin_ahead_minute": "",
      "history": 1,
      "audit": 0,
      "status": "5",
      "detail": {
        "room": {
          "id": 1,
          "name": "平顶山运行指挥中心",
          "location_id": 1,
          "location_path": "1-",
          "seats": 15,
          "open_start_time": "",
          "open_end_time": "",
          "department": "[]",
          "vr_link": "",
          "images": [],
          "create_time": "2022-12-27 16:54:35",
          "update_time": "2022-12-28 10:10:07",
          "delete_time": null,
          "union_id": 0,
          "seat_id": 0,
          "lstop": 0,
          "audit_user": "admin",
          "open_start_date": "",
          "open_end_date": "",
          "forbidden_status": 0,
          "forbidden_type": 0,
          "forbidden_info": "[]",
          "assign_type": 0,
          "scene": "{\"open\":[],\"close\":[]}",
          "is_device_control": 0,
          "paperless_rid": 0,
          "paperless_ip": "",
          "location_name": "城运中心",
          "deviceType": []
        },
        "creator": {
          "user_id": "admin",
          "displayname": "admin",
          "avatar": "/meetingcloud/avatar/admin/64?v=0",
          "email": null,
          "department_name": "组织架构,行政部"
        },
        "clients": [],
        "scene": {
          "sceneConfigId": 0,
          "sceneOnTime": "2023-01-03 ",
          "sceneOffTime": "2023-01-03 ",
          "sceneName": ""
        },
        "distribute": [],
        "auditor": [],
        "leader": [
          {
            "user_id": "admin",
            "displayname": "admin",
            "avatar": "/meetingcloud/avatar/admin/64?v=0",
            "email": null,
            "department_name": "组织架构,行政部"
          }
        ],
        "attender": [
          {
            "user_id": "13600000001",
            "displayname": "jkj",
            "avatar": "/meetingcloud/avatar/13600000001/64?v=0",
            "email": null,
            "department_name": ""
          },
          {
            "user_id": "admin",
            "displayname": "admin",
            "avatar": "/meetingcloud/avatar/admin/64?v=0",
            "email": null,
            "department_name": "组织架构,行政部"
          }
        ],
        "cc": [],
        "external": [
          {
            "user_name": "张三",
            "mobile": "18812345678",
            "email": "<EMAIL>"
          },
          {
            "user_name": "王五",
            "mobile": "18256898888",
            "email": "<EMAIL>"
          }
        ]
      },
      "creator": "admin",
      "auditor": "",
      "audit_time": null,
      "create_time": "2023-01-03 10:15:57",
      "update_time": null,
      "delete_time": null,
      "sms_type": "2",
      "sms_time": "10",
      "end_sms_time": "10",
      "num": 80,
      "is_active": 0,
      "desc": "",
      "checkin_stop_minute": "",
      "meeting_type": 0,
      "video_conference": 0,
      "video_conference_id": "",
      "seat_type": 1,
      "has_seat": 0,
      "seat_img": "",
      "union_id": 0,
      "cc_type": 1,
      "meeting_cycle_id": 0,
      "cancelled_by": "",
      "paperless_mid": 0,
      "any_value(ma.attend_status)": "accepted",
      "order_field": 5,
      "room_is_delete": false,
      "attenderCount": 2,
      "weekday": "周二",
      "attend_status": "",
      "role": "leader"
    },
    {
      "id": 5,
      "subject": "欢迎领导莅临指导",
      "summary": "",
      "start_time": "2023-01-04 09:05",
      "end_time": "2023-01-04 23:59",
      "count_minutes": 894,
      "room_id": 6,
      "agenda": [],
      "files": [],
      "checkin": 0,
      "checkin_ahead_minute": "",
      "history": 1,
      "audit": 0,
      "status": "5",
      "detail": {
        "room": {
          "id": 6,
          "name": "悬浮会议室",
          "location_id": 1,
          "location_path": "1-",
          "seats": 100,
          "open_start_time": "",
          "open_end_time": "",
          "department": "[]",
          "vr_link": "",
          "images": [],
          "create_time": "2023-01-03 10:39:11",
          "update_time": null,
          "delete_time": null,
          "union_id": 0,
          "seat_id": 0,
          "lstop": 0,
          "audit_user": null,
          "open_start_date": "",
          "open_end_date": "",
          "forbidden_status": 0,
          "forbidden_type": 0,
          "forbidden_info": "[]",
          "assign_type": 0,
          "scene": "{\"open\":[],\"close\":[]}",
          "is_device_control": 0,
          "paperless_rid": 0,
          "paperless_ip": "",
          "location_name": "城运中心",
          "deviceType": []
        },
        "creator": {
          "user_id": "admin",
          "displayname": "admin",
          "avatar": "/meetingcloud/avatar/admin/64?v=0",
          "email": null,
          "department_name": "组织架构,行政部"
        },
        "clients": [],
        "scene": {
          "sceneConfigId": 0,
          "sceneOnTime": "2023-01-04 ",
          "sceneOffTime": "2023-01-04 ",
          "sceneName": ""
        },
        "distribute": [],
        "auditor": [],
        "leader": [
          {
            "user_id": "admin",
            "displayname": "admin",
            "avatar": "/meetingcloud/avatar/admin/64?v=0",
            "email": null,
            "department_name": "组织架构,行政部"
          }
        ],
        "attender": [
          {
            "user_id": "admin",
            "displayname": "admin",
            "avatar": "/meetingcloud/avatar/admin/64?v=0",
            "email": null,
            "department_name": "组织架构,行政部"
          }
        ],
        "cc": [],
        "external": []
      },
      "creator": "admin",
      "auditor": "",
      "audit_time": null,
      "create_time": "2023-01-04 09:02:06",
      "update_time": null,
      "delete_time": null,
      "sms_type": "2",
      "sms_time": "0",
      "end_sms_time": "10",
      "num": 0,
      "is_active": 0,
      "desc": "",
      "checkin_stop_minute": "",
      "meeting_type": 0,
      "video_conference": 0,
      "video_conference_id": "",
      "seat_type": 1,
      "has_seat": 0,
      "seat_img": "",
      "union_id": 0,
      "cc_type": 1,
      "meeting_cycle_id": 0,
      "cancelled_by": "",
      "paperless_mid": 0,
      "any_value(ma.attend_status)": "accepted",
      "order_field": 5,
      "room_is_delete": false,
      "attenderCount": 1,
      "weekday": "周三",
      "attend_status": "",
      "role": "leader"
    },
    {
      "id": 6,
      "subject": "欢迎领导莅临",
      "summary": "",
      "start_time": "2023-01-04 09:12",
      "end_time": "2023-01-04 23:59",
      "count_minutes": 887,
      "room_id": 1,
      "agenda": [],
      "files": [],
      "checkin": 1,
      "checkin_ahead_minute": "09:05",
      "history": 1,
      "audit": 0,
      "status": "5",
      "detail": {
        "room": {
          "id": 1,
          "name": "平顶山运行指挥中心",
          "location_id": 1,
          "location_path": "1-",
          "seats": 50,
          "open_start_time": "",
          "open_end_time": "",
          "department": "[]",
          "vr_link": "",
          "images": [],
          "create_time": "2023-01-03 10:29:10",
          "update_time": "2023-01-03 10:29:10",
          "delete_time": null,
          "union_id": 0,
          "seat_id": 0,
          "lstop": 0,
          "audit_user": "admin",
          "open_start_date": "",
          "open_end_date": "",
          "forbidden_status": 0,
          "forbidden_type": 0,
          "forbidden_info": "[]",
          "assign_type": 0,
          "scene": "{\"open\":[],\"close\":[]}",
          "is_device_control": 0,
          "paperless_rid": 0,
          "paperless_ip": "",
          "location_name": "城运中心",
          "deviceType": []
        },
        "creator": {
          "user_id": "admin",
          "displayname": "admin",
          "avatar": "/meetingcloud/avatar/admin/64?v=0",
          "email": null,
          "department_name": "组织架构,行政部"
        },
        "clients": [],
        "scene": {
          "sceneConfigId": 0,
          "sceneOnTime": "2023-01-04 ",
          "sceneOffTime": "2023-01-04 ",
          "sceneName": ""
        },
        "distribute": [],
        "auditor": [],
        "leader": [
          {
            "user_id": "13600000001",
            "displayname": "jkj",
            "avatar": "/meetingcloud/avatar/13600000001/64?v=0",
            "email": null,
            "department_name": ""
          }
        ],
        "attender": [
          {
            "user_id": "13600000001",
            "displayname": "jkj",
            "avatar": "/meetingcloud/avatar/13600000001/64?v=0",
            "email": null,
            "department_name": ""
          },
          {
            "user_id": "admin",
            "displayname": "admin",
            "avatar": "/meetingcloud/avatar/admin/64?v=0",
            "email": null,
            "department_name": "组织架构,行政部"
          }
        ],
        "cc": [],
        "external": []
      },
      "creator": "admin",
      "auditor": "",
      "audit_time": null,
      "create_time": "2023-01-04 09:10:30",
      "update_time": null,
      "delete_time": null,
      "sms_type": "2",
      "sms_time": "10",
      "end_sms_time": "10",
      "num": 10,
      "is_active": 0,
      "desc": "",
      "checkin_stop_minute": "09:18",
      "meeting_type": 0,
      "video_conference": 0,
      "video_conference_id": "",
      "seat_type": 1,
      "has_seat": 0,
      "seat_img": "",
      "union_id": 0,
      "cc_type": 1,
      "meeting_cycle_id": 0,
      "cancelled_by": "",
      "paperless_mid": 0,
      "any_value(ma.attend_status)": "accepted",
      "order_field": 5,
      "room_is_delete": false,
      "attenderCount": 2,
      "weekday": "周三",
      "attend_status": "accepted",
      "role": "attender"
    },
    {
      "id": 7,
      "subject": "城运中心测试会议",
      "summary": "",
      "start_time": "2023-01-06 19:00",
      "end_time": "2023-01-06 19:21",
      "count_minutes": 21,
      "room_id": 1,
      "agenda": [],
      "files": [],
      "checkin": 0,
      "checkin_ahead_minute": "",
      "history": 1,
      "audit": 0,
      "status": "5",
      "detail": {
        "room": {
          "id": 1,
          "name": "平顶山运行指挥中心",
          "location_id": 1,
          "location_path": "1-",
          "seats": 50,
          "open_start_time": "",
          "open_end_time": "",
          "department": "[]",
          "vr_link": "",
          "images": [],
          "create_time": "2023-01-03 10:29:10",
          "update_time": "2023-01-03 10:29:10",
          "delete_time": null,
          "union_id": 0,
          "seat_id": 0,
          "lstop": 0,
          "audit_user": "admin",
          "open_start_date": "",
          "open_end_date": "",
          "forbidden_status": 0,
          "forbidden_type": 0,
          "forbidden_info": "[]",
          "assign_type": 0,
          "scene": "{\"open\":[],\"close\":[]}",
          "is_device_control": 0,
          "paperless_rid": 0,
          "paperless_ip": "",
          "location_name": "城运中心",
          "deviceType": []
        },
        "creator": {
          "user_id": "admin",
          "displayname": "admin",
          "avatar": "/meetingcloud/avatar/admin/64?v=0",
          "email": null,
          "department_name": "组织架构,行政部"
        },
        "clients": [],
        "scene": {
          "sceneConfigId": 0,
          "sceneOnTime": "2023-01-06 ",
          "sceneOffTime": "2023-01-06 ",
          "sceneName": ""
        },
        "distribute": [],
        "auditor": [],
        "leader": [],
        "attender": [
          {
            "user_id": "admin",
            "displayname": "admin",
            "avatar": "/meetingcloud/avatar/admin/64?v=0",
            "email": null,
            "department_name": "组织架构,行政部"
          }
        ],
        "cc": [],
        "external": []
      },
      "creator": "admin",
      "auditor": "",
      "audit_time": null,
      "create_time": "2023-01-06 18:53:56",
      "update_time": "2023-01-06 19:21:44",
      "delete_time": null,
      "sms_type": "2",
      "sms_time": "10",
      "end_sms_time": "10",
      "num": 80,
      "is_active": 0,
      "desc": "",
      "checkin_stop_minute": "",
      "meeting_type": 0,
      "video_conference": 0,
      "video_conference_id": "",
      "seat_type": 1,
      "has_seat": 0,
      "seat_img": "",
      "union_id": 0,
      "cc_type": 1,
      "meeting_cycle_id": 0,
      "cancelled_by": "",
      "paperless_mid": 0,
      "any_value(ma.attend_status)": "accepted",
      "order_field": 5,
      "room_is_delete": false,
      "attenderCount": 1,
      "weekday": "周五",
      "attend_status": "accepted",
      "role": "attender"
    },
    {
      "id": 8,
      "subject": "11111",
      "summary": "",
      "start_time": "2023-01-06 19:27",
      "end_time": "2023-01-06 22:14",
      "count_minutes": 167,
      "room_id": 1,
      "agenda": [],
      "files": [],
      "checkin": 0,
      "checkin_ahead_minute": "",
      "history": 1,
      "audit": 0,
      "status": "5",
      "detail": {
        "room": {
          "id": 1,
          "name": "平顶山运行指挥中心",
          "location_id": 1,
          "location_path": "1-",
          "seats": 50,
          "open_start_time": "",
          "open_end_time": "",
          "department": "[]",
          "vr_link": "",
          "images": [],
          "create_time": "2023-01-03 10:29:10",
          "update_time": "2023-01-03 10:29:10",
          "delete_time": null,
          "union_id": 0,
          "seat_id": 0,
          "lstop": 0,
          "audit_user": "admin",
          "open_start_date": "",
          "open_end_date": "",
          "forbidden_status": 0,
          "forbidden_type": 0,
          "forbidden_info": "[]",
          "assign_type": 0,
          "scene": "{\"open\":[],\"close\":[]}",
          "is_device_control": 0,
          "paperless_rid": 0,
          "paperless_ip": "",
          "location_name": "城运中心",
          "deviceType": []
        },
        "creator": {
          "user_id": "admin",
          "displayname": "admin",
          "avatar": "/meetingcloud/avatar/admin/64?v=0",
          "email": null,
          "department_name": "组织架构,行政部"
        },
        "clients": [],
        "scene": {
          "sceneConfigId": 0,
          "sceneOnTime": "2023-01-06 ",
          "sceneOffTime": "2023-01-06 ",
          "sceneName": ""
        },
        "distribute": [],
        "auditor": [],
        "leader": [],
        "attender": [
          {
            "user_id": "admin",
            "displayname": "admin",
            "avatar": "/meetingcloud/avatar/admin/64?v=0",
            "email": null,
            "department_name": "组织架构,行政部"
          }
        ],
        "cc": [],
        "external": []
      },
      "creator": "admin",
      "auditor": "",
      "audit_time": null,
      "create_time": "2023-01-06 19:26:08",
      "update_time": null,
      "delete_time": null,
      "sms_type": "2",
      "sms_time": "10",
      "end_sms_time": "10",
      "num": 0,
      "is_active": 0,
      "desc": "",
      "checkin_stop_minute": "",
      "meeting_type": 0,
      "video_conference": 0,
      "video_conference_id": "",
      "seat_type": 1,
      "has_seat": 0,
      "seat_img": "",
      "union_id": 0,
      "cc_type": 1,
      "meeting_cycle_id": 0,
      "cancelled_by": "",
      "paperless_mid": 0,
      "any_value(ma.attend_status)": "accepted",
      "order_field": 5,
      "room_is_delete": false,
      "attenderCount": 1,
      "weekday": "周五",
      "attend_status": "accepted",
      "role": "attender"
    },
    {
      "id": 9,
      "subject": "城市运行123",
      "summary": "12113231433321",
      "start_time": "2023-01-07 09:25",
      "end_time": "2023-01-07 09:45",
      "count_minutes": 20,
      "room_id": 2,
      "agenda": [],
      "files": [
        "/9/1673054532982启动仪式.jpg"
      ],
      "checkin": 0,
      "checkin_ahead_minute": "",
      "history": 1,
      "audit": 0,
      "status": "5",
      "detail": {
        "room": {
          "id": 2,
          "name": "决策室",
          "location_id": 1,
          "location_path": "1-",
          "seats": 24,
          "open_start_time": "",
          "open_end_time": "",
          "department": "[]",
          "vr_link": "",
          "images": [],
          "create_time": "2022-12-29 17:50:09",
          "update_time": null,
          "delete_time": null,
          "union_id": 0,
          "seat_id": 0,
          "lstop": 0,
          "audit_user": null,
          "open_start_date": "",
          "open_end_date": "",
          "forbidden_status": 0,
          "forbidden_type": 0,
          "forbidden_info": "[]",
          "assign_type": 0,
          "scene": "{\"open\":[],\"close\":[]}",
          "is_device_control": 0,
          "paperless_rid": 0,
          "paperless_ip": "",
          "location_name": "城运中心",
          "deviceType": []
        },
        "creator": {
          "user_id": "admin",
          "displayname": "admin",
          "avatar": "/meetingcloud/avatar/admin/64?v=0",
          "email": null,
          "department_name": "组织架构,行政部"
        },
        "clients": [],
        "scene": {
          "sceneConfigId": 0,
          "sceneOnTime": "2023-01-07 ",
          "sceneOffTime": "2023-01-07 ",
          "sceneName": ""
        },
        "distribute": [],
        "auditor": [],
        "leader": [
          {
            "user_id": "19927381728",
            "displayname": "张三",
            "avatar": "/meetingcloud/avatar/19927381728/64?v=0",
            "email": null,
            "department_name": ""
          }
        ],
        "attender": [
          {
            "user_id": "13600000001",
            "displayname": "jkj",
            "avatar": "/meetingcloud/avatar/13600000001/64?v=0",
            "email": null,
            "department_name": ""
          },
          {
            "user_id": "admin",
            "displayname": "admin",
            "avatar": "/meetingcloud/avatar/admin/64?v=0",
            "email": null,
            "department_name": "组织架构,行政部"
          }
        ],
        "cc": [],
        "external": []
      },
      "creator": "admin",
      "auditor": "",
      "audit_time": null,
      "create_time": "2023-01-07 09:23:08",
      "update_time": "2023-01-07 09:45:23",
      "delete_time": null,
      "sms_type": "2",
      "sms_time": "10",
      "end_sms_time": "10",
      "num": 5,
      "is_active": 0,
      "desc": "",
      "checkin_stop_minute": "",
      "meeting_type": 0,
      "video_conference": 0,
      "video_conference_id": "",
      "seat_type": 1,
      "has_seat": 0,
      "seat_img": "",
      "union_id": 0,
      "cc_type": 1,
      "meeting_cycle_id": 0,
      "cancelled_by": "",
      "paperless_mid": 0,
      "any_value(ma.attend_status)": "accepted",
      "order_field": 5,
      "room_is_delete": true,
      "attenderCount": 2,
      "weekday": "周六",
      "attend_status": "accepted",
      "role": "attender"
    },
    {
      "id": 11,
      "subject": "123456789",
      "summary": "",
      "start_time": "2023-01-08 16:30",
      "end_time": "2023-01-08 18:29",
      "count_minutes": 119,
      "room_id": 1,
      "agenda": [],
      "files": [],
      "checkin": 0,
      "checkin_ahead_minute": "",
      "history": 1,
      "audit": 0,
      "status": "5",
      "detail": {
        "room": {
          "id": 1,
          "name": "平顶山运行指挥中心",
          "location_id": 1,
          "location_path": "1-",
          "seats": 50,
          "open_start_time": "",
          "open_end_time": "",
          "department": "[]",
          "vr_link": "",
          "images": [],
          "create_time": "2023-01-03 10:29:10",
          "update_time": "2023-01-03 10:29:10",
          "delete_time": null,
          "union_id": 0,
          "seat_id": 0,
          "lstop": 0,
          "audit_user": "admin",
          "open_start_date": "",
          "open_end_date": "",
          "forbidden_status": 0,
          "forbidden_type": 0,
          "forbidden_info": "[]",
          "assign_type": 0,
          "scene": "{\"open\":[],\"close\":[]}",
          "is_device_control": 0,
          "paperless_rid": 0,
          "paperless_ip": "",
          "location_name": "城运中心",
          "deviceType": []
        },
        "creator": {
          "user_id": "admin",
          "displayname": "admin",
          "avatar": "/meetingcloud/avatar/admin/64?v=0",
          "email": null,
          "department_name": "组织架构,行政部"
        },
        "clients": [],
        "scene": {
          "sceneConfigId": 0,
          "sceneOnTime": "2023-01-08 ",
          "sceneOffTime": "2023-01-08 ",
          "sceneName": ""
        },
        "distribute": [],
        "auditor": [],
        "leader": [
          {
            "user_id": "19922837162",
            "displayname": "李四",
            "avatar": "/meetingcloud/avatar/19922837162/64?v=0",
            "email": null,
            "department_name": ""
          }
        ],
        "attender": [
          {
            "user_id": "19927381728",
            "displayname": "张三",
            "avatar": "/meetingcloud/avatar/19927381728/64?v=0",
            "email": null,
            "department_name": ""
          },
          {
            "user_id": "admin",
            "displayname": "admin",
            "avatar": "/meetingcloud/avatar/admin/64?v=0",
            "email": null,
            "department_name": "组织架构,行政部"
          }
        ],
        "cc": [],
        "external": []
      },
      "creator": "admin",
      "auditor": "",
      "audit_time": null,
      "create_time": "2023-01-08 16:28:31",
      "update_time": null,
      "delete_time": null,
      "sms_type": "2",
      "sms_time": "10",
      "end_sms_time": "10",
      "num": 5,
      "is_active": 0,
      "desc": "",
      "checkin_stop_minute": "",
      "meeting_type": 0,
      "video_conference": 0,
      "video_conference_id": "",
      "seat_type": 1,
      "has_seat": 0,
      "seat_img": "",
      "union_id": 0,
      "cc_type": 1,
      "meeting_cycle_id": 0,
      "cancelled_by": "",
      "paperless_mid": 0,
      "any_value(ma.attend_status)": "accepted",
      "order_field": 5,
      "room_is_delete": false,
      "attenderCount": 2,
      "weekday": "周日",
      "attend_status": "accepted",
      "role": "attender"
    }
  ]

const roomData=[{
    "id": 1,
    "name": "平顶山市市域社会治理现代化指挥中心",
    "name_alias": "平顶山市市域社会治理\n现代化指挥中心\t",
    "location_name": "城运中心",
    "deviceType": [],
    "usage": {
        "appointTime": [],

    }
},
{
    "id": 3,
    "name": "决策室东门",
    "scene": {
        "open": [],
        "close": []
    },
    "name_alias": "决策室东门",
    "location_name": "城运中心",
    "deviceType": [],
    "usage": {
        "appointTime": [],

    }
},
{
    "id": 4,
    "name": "交警研判室",
    "name_alias": "交警研判室",
    "location_name": "城运中心",
    "deviceType": [],
    "usage": {
        "appointTime": [],

    }
},
{
    "id": 6,
    "name": "202会议室",
    "location_id": 1,
    "location_path": "1-",
    "seats": 20,

    "scene": {
        "open": [],
        "close": []
    },

    "name_alias": "202会议室",
    "location_name": "城运中心",
    "deviceType": [],
    "usage": {
        "appointTime": [{
            "start_time": "00:00",
            "end_time": "23:59"
        }],

    }
},
{
    "id": 8,
    "name": "多功能厅南门",
    "location_id": 1,

    "name_alias": "多功能厅南门",
    "location_name": "城运中心",
    "deviceType": [],
    "usage": {
        "appointTime": [],

    }
},
{
    "id": 9,
    "name": "悬浮会议室",
    "location_id": 1,
    "location_path": "1-",

    "is_device_control": 0,
    "paperless_rid": 0,
    "paperless_ip": "",
    "name_alias": "悬浮会议室",
    "location_name": "城运中心",
    "deviceType": [],
    "usage": {
        "appointTime": [],

    }
},
{
    "id": 10,
    "name": "决策室",

    "name_alias": "决策室",
    "location_name": "城运中心",
    "deviceType": [],
    "usage": {
        "appointTime": [],

    }
},
{
    "id": 11,
    "name": "201会议室",

    "name_alias": "201会议室",
    "location_name": "城运中心",
    "deviceType": [],
    "usage": {
        "appointTime": [],

    }
}
]


export {meetingData,roomData};