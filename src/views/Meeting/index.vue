<template>
    <div class="z100">
        <div class="left">
            <div class="header flex-start">
                <img src="../../assets/images/common/head.png">
                <div> 会议室列表</div>
            </div>
            <div class="input">
                <el-input v-model="keyword" @change="search" :prefix-icon="Search" placeholder="按会议室搜索"></el-input>
            </div>
            <div class="device">

                <el-scrollbar>
                    <div class="list space-between" v-for="item in room" :key="item.id">
                        <div class="center cursor">
                            <i class="iconfont iconhome"></i>
                            <div class="name">{{ item.name }}</div>
                        </div>
                        <div class="center state">
                            <div style="color:green" v-if="item.usage.appointTime.length">
                                {{ item.usage.appointTime[0].start_time }}-{{ item.usage.appointTime[0].end_time }}</div>
                        </div>
                        <div class="position cursor">
                            <img src="../../assets/images/common/position.png" />
                        </div>
                    </div>
                </el-scrollbar>

            </div>

            <div class="page center">
                <el-pagination prev-icon="CaretLeft" next-icon="CaretRight" :page-size="size" :current-page="page"
                    layout="total,prev, pager, next" @current-change="handleCurrentChange" :total="total">
                </el-pagination>
            </div>

        </div>

        <pop :show="activeMenus.popName ? true : false" :title="activeMenus.name || ''">
            <Transition name="fade" mode="out-in">
            <component :is="activeMenus.popName"></component>
            </Transition>
        </pop>


        <div class="right">
            <div class="item" style="flex:1">
                <sub-title1 title='运行统计' />
                <div class="item-body order">
                    <div>
                        <div class="total center">会议室总数</div>
                        <div class="order-left">
                            <div class="center">
                                <div>{{ on + off }}</div>
                            </div>
                        </div>
                    </div>
                    <div class="order-right">
                        <div class="order-text">
                            <div class="dot" style="background:#1AAC1A"></div><span class="text">预约数量:</span><span
                                class="num">{{ on }}</span>
                        </div>
                        <div class="order-text">
                            <div class="dot" style="background:#C47F13"></div><span class="text">空闲数量:</span><span
                                class="num">{{ off }}</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="item" style="flex:2">
                <sub-title1 title='审批列表' />
                <div class="item-body kong">
                    <el-scrollbar v-if="audit.length>0">
                        <div class="list space-between" v-for="item in audit" :key="item.id">
                            <div class="name">
                                <div class="flex">
                                    <div>{{ item.subject }}</div>
                                    <div class="hour">{{ item.count_minutes }}分</div>
                                    <div>{{ item.detail.creator.department_name }}</div>
                                </div>
                                <div class="time">{{ item.start_time }}</div>
                            </div>

                            <div class="btn space-between">
                                <div class="center cursor" @click="agreeMeeting(item)">
                                    <div class="center">通过</div>
                                </div>

                                <div class="center cursor" @click="unAgreeMeeting(item)">
                                    <div class="center">拒绝</div>
                                </div>
                            </div>
                        </div>
                    </el-scrollbar>
                    <noData v-else></noData>
                </div>
            </div>
            <div class="item" style="flex:2">
                <sub-title1 title='预约记录' />
                <div class="item-body event">
                    <el-scrollbar v-if="meeting.length>0">
                        <div class="list space-between" v-for="i in meeting" :key="i.id">
                            <div class="name">
                                <div >
                                    <span style="color:#0CCA0F" class="iconfont  iconguangbo-dingshi"></span>
                                </div>
                                <div>{{ i.detail.room.name }}</div>
                                <div class="hour">{{ i.count_minutes }}/min</div>
                                <!-- <div>{{ i.detail.creator.department_name }}</div> -->
                            </div>
                            <div class="time">
                                {{ i.start_time }}
                            </div>
                            <div class="bar"></div>
                        </div>
                    </el-scrollbar>
                    <noData v-else></noData>
                </div>
            </div>
        </div>

        <el-dialog align-center append-to-body   custom-class="addDiagram border0" v-model="auditVisible" width="540px" title="会议审批">
            <el-form class="form" ref="form">
                <el-form-item label="拒绝原因：" prop="description">
                    <el-input type="textarea" placeholder="请输入内容" v-model="desc"></el-input>
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer search_box">
                    <div type="primary" :disabled="loading" size="mini" class="searchBtn" @click="auditMeeting('form')">确 定
                    </div>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script>
import {
    defineComponent,
    getCurrentInstance,
    reactive,
    toRefs,
    onMounted,
} from 'vue';
import {useStore} from 'vuex';
import { ElMessageBox, dayjs} from 'element-plus'
import pop from '@/components/pop'
export default defineComponent({
    name: "meeting",
    components: {
        pop,
    },
    setup() {
        const {
            proxy
        } = getCurrentInstance()
        const store = useStore();
        const state = reactive({
            keyword: "",
            room: [], //设备列表
            meeting: [],
            popName: '',
            auditVisible: false,
            desc: null,
            on: 0,
            off: 0,
            audit: [
            ],
            activeMenus: {
                popName: ''
            }
        })

        onMounted(() => {
            getRoom();
            getMeeting();
            getAuditMeeting();

        });

        const getRoom = async () => {
            let {
                data
            } = await proxy.$api.getRoom()
            state.on = 0;
            state.off = 0;
            if (data) {
                state.room = data;
                data.forEach(d => {
                    if (d.usage.appointTime.length > 0) {
                        state.on++;
                    } else {
                        state.off++;
                    }
                })
            }

        };
        //所有会议
        const getMeeting = async () => {
            let {
                data
            } = await proxy.$api.getMeeting({
                status: 100,
                type:0,
                page: 1,
                size:10,
                bt:dayjs().startOf('month').format('YYYY-MM-DD'),
                et:dayjs().endOf('month').format('YYYY-MM-DD')
            })
            if (data) {
                state.meeting =data;

            }

        };

        //待审核会议
        const getAuditMeeting = async () => {
            let {
                data
            } = await proxy.$api.getMeeting({
                status: 0
            })
            if (data) {
                state.auditMeeting = data;

            }

        };

        const handleCurrentChange = (page) => {
            state.page = page;
            getDeviceList();
        }

        const search = () => {
            state.page = 1;
            getDeviceList();
        }

        const agreeMeeting = (item) => {
            ElMessageBox.confirm('是否确认要同意该会议预约？', '提示', {
                confirmButtonClass: 'confirmBtn',
                cancelButtonClass: 'cancelBtn',
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            }).then(async () => {
                const {
                    success
                } = await proxy.$api.auditMeeting({
                    id: state.meeting.id,
                    desc: "",
                    pass: 1
                });
            })
        };

        const unAgreeMeeting = (item) => {
            state.auditVisible = true;
            state.meeting = item;

        }

        const auditMeeting = async () => {
            const {
                success
            } = await proxy.$api.auditMeeting({
                id: state.meeting.id,
                desc: state.desc,
                pass: 0
            });
            if (success) {
                state.auditVisible = false;
            }
        }

        return {
            ...toRefs(state),
            handleCurrentChange,
            search,
            agreeMeeting,
            unAgreeMeeting,
            auditMeeting,
        }
    }
});
</script>

<style lang="scss" scoped>
.kong {
    .list {
        padding: 0 20px;
    }

    .name {
        display: flex;
        flex-direction: column;
        align-items: flex-start;

        .hour {
            margin: 0 10px
        }
    }
}

.event {
    .name {
        display: flex;
        // flex-direction: column;
        width: auto;
        .hour {
            margin: 0 10px;
            white-space: nowrap;
            width: 52px;
        }

        &>div:nth-child(2) {
            width: 90px;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
        }
    }

    .time {
        width: 120px;

    }


}
</style>
