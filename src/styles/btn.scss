@import './variables.scss';

@mixin colorBtn($bgColor, $borderColor) {
  background: $bgColor;
  border: 1px solid $borderColor;

  &:hover,
  &:focus {
    background: $bgColor;
    border: 1px solid $borderColor;
  }
}





.editBtn {
  color: #889cc3 !important;
  font-family: 'Alibaba-PuHuiTi',
    'PingFang SC';
  font-weight: 400 !important;
  min-height: 0 !important;
  padding: 0 !important;
}


.btn {
  .el-button {
    margin: 0 5px;
  }
}

.btn,
.account,
.form,
.panel {
  .saveBtn {
    @include colorBtn($addBtnBgColor, $themeColor)
  }

  .deleteBtn {
    @include colorBtn($delBtnBgColor, $delBtnBorderColor)
  }

  .resetBtn {
    @include colorBtn($addBtnBgColor, $themeColor)
  }



  .backBtn {
    color: #fff;

    &:hover,
    &:focus {
      color: #fff;
    }
  }
}

.login_btn {
  width: 100%;
  color: rgba(255, 255, 255, 0.3) !important;
  background-color: rgba(61, 233, 250, 0.3) !important;
  border: none !important;

  &:hover {
    background-color: rgba(61, 233, 250, 0.1);
  }
}

.closeBtn {
  color: rgba(255, 255, 255, 0.5);
  font-size: 18px;
  cursor: pointer;
}

.confirmBtn {
  background-color: rgba(61, 233, 250, 0.5) !important;
  border: 1px solid $themeColor !important;
  font-size: 14px;
  font-family: 'PingFangSC-Medium',
    'PingFang SC';
  font-weight: 500;
  color: #FFFFFF !important;
}


.cancelBtn {
  background: none !important;
  border: none !important;
  font-size: 14px;
  font-family: 'Alibaba-PuHuiTi',
    'PingFang SC';
  font-weight: 400;
  color: #FFFFFF !important;
}

.custom_dialog,
.table {
  .saveBtn {
    @include colorBtn($addBtnBgColor, $themeColor)
  }

  .deleteBtn {
    @include colorBtn($delBtnBgColor, $delBtnBorderColor)
  }

  .del {
    color: #b4061a;
    font-size: 14px;
    font-family: 'Alibaba-PuHuiTi',
      'PingFang SC';
    font-weight: 400;
    margin-left: 20px !important;

    &:hover,
    &:focus {
      color: #b4061a;
    }
  }
}


.btn-group,
.form_inline {
  .addBtn {
    @include colorBtn($addBtnBgColor, $themeColor)
  }

  .deleteBtn {
    @include colorBtn($delBtnBgColor, $delBtnBorderColor)
  }

  .amendBtn {
    @include colorBtn($amendBtnBgColor, $amendBtnBorderColor)
  }

  .settingBtn,
  .settingBtn:hover,
  .settingBtn:focus {
    font-size: 12px;
    font-family: 'Alibaba-PuHuiTi',
      'PingFang SC';
    font-weight: 400;
    color: #778897;
  }
}