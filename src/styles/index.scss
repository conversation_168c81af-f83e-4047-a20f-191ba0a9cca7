@import './variables.scss';
@import './mixin.scss';
@import './element-ui.scss';
@import './btn.scss';

html {
  height: 100%;
  box-sizing: border-box;

  // -moz-user-select: none;
  // /*火狐*/
  // -webkit-user-select: none;
  // /*webkit浏览器*/
  // -ms-user-select: none;
  // /*IE10*/
  // -khtml-user-select: none;
  // /*早期浏览器*/
  // user-select: none;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}


body {
  height: 100%;
  font-family: 'Alibaba-PuHuiTi';
  padding: 0;
  margin: 0;
  font-size: 14px;
  overflow: hidden;
}

#app {
  height: 100%;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}


.center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex-start {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.space-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}


.layout_bg {
  background: url('../assets/images/home.jpg') no-repeat center fixed;
  width: 100%;
  height: 100%;
  background-size: cover;
}

.home_bg {
  background: url('../assets/images/home_bg.png') no-repeat center fixed;
  width: 100%;
  height: 100%;
  background-size: cover;
}

.locat {
  color: $themeColor;
}

.play {
  color: #02e287;
}

.p1 {
  padding: 15px;
}




.layout_wrapper {
  position: relative;
  height: calc(100% - 100px);
  top: 38px;
}



.auto {
  .el-checkbox__input.is-checked+.el-checkbox__label {
    font-weight: 400;
    color: #c7dfff;
  }
}

.BMap_bubble_pop {
  border: 1px solid #323a3e !important;
  background-color: #091822 !important;
  padding: 8px !important;
  border-radius: 4px !important;

  img {
    display: none;
  }
}

.BMap_bubble_title {
  font-size: 16px;
  font-weight: 500;
  color: $whiteColor !important;
}

.BMap_cpyCtrl {
  display: none;
}

.BMapLabel {
  background-color: rgba(255, 255, 255, 0) !important;
  border: none !important;
}

.anchorBL {
  display: none;
}

.bread {
  position: absolute;
  z-index: 9;
  top: 64px;
}

.top_tabs {
  position: relative;
  width: 75px;
  text-align: center;
  font-size: 14px;
  font-family: 'Alibaba-PuHuiTi';
  font-weight: 400;
  color: rgba(255, 255, 255, 0.5);
  cursor: pointer;

  .active {
    position: absolute;
    width: 100%;
    height: 18px;
    left: 0;
    right: 0;
    bottom: 0px;
    background: url('../assets/images/btn_bak.png') no-repeat;
    background-size: contain;
  }
}

.form-item-btn {
  margin-right: 32px !important;
}



.sum {
  line-height: 40px;
  margin-top: 8px;
  text-align: center;
  color: #fff;

  .iconfont {
    font-size: 22px;
  }
}






.date-picker {
  background-color: #282D34 !important;
}

.el-date-table td.in-range div {
  background-color: #091822 !important;
}



.alarm_list {
  position: absolute;
  padding: 8px;
  width: 377px;
  right: 8px;
  background-color: rgba(9, 22, 30, 0.8);
  // border: 1px solid #2b2e32;

  .alarm_scrollbar {
    height: calc(100% - 145px);

    .list {
      position: relative;
      background: rgba(47, 54, 60, 0.3);
      border: 1px solid #2f363c;
      color: $whiteColor;
      padding: 24px 15px 24px 15px;
      margin-bottom: 8px;
      cursor: pointer;

      .alarm_name {
        display: flex;
        justify-content: space-between;
        font-size: 16px;
        font-family: "Alibaba-PuHuiTi";
        font-weight: 500;
        color: $whiteColor;
        margin-bottom: 6px;
      }

      .msg,
      .date {
        font-size: 14px;
        font-family: "Alibaba-PuHuiTi";
        font-weight: 400;
        color: #9ca4b7;
      }

      .type {
        position: absolute;
        top: 0;
        font-size: 12px;
        border-radius: 0px 0px 8px 0px;
        left: 0;
        padding: 1px 5px;
      }

      .pt {
        background: #417edf;
      }

      .yz {
        background: #e3731b;
      }

      .jj {
        background: #b4061a;
      }
    }
  }

  .alarm_btn {
    display: flex;
    flex-wrap: wrap;
    width: 100%;

    .btn {
      background: rgba(40,107,148,0.9);
      font-size: 12px;
      font-family: "Alibaba-PuHuiTi";
      font-weight: 400;
      color: #9ca4b7;
      width: calc(20% - 8px);
      margin: 5px 4px;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      height: 64px;
      cursor: pointer;

      i {
        color: $whiteColor;
        font-size: 18px;
      }

      &:last-child {
        margin-right: 0;
      }

      .label {
        margin-top: 8px;
      }

      &:last-child {
        margin-right: 0;
      }
    }
  }
}

.form_inline {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  height: 40px;
  padding: 5px 0;
  // background: rgba(68, 114, 141, 0.1);
  box-shadow: 0px 1px 0px 0px rgba(199, 223, 255, 0.5);

  .el-form-item--small.el-form-item {
    margin-bottom: 0px;
    vertical-align: middle !important;
  }
}

.noData {
  color: $whiteColor;
  font-size: 14px;
  display: flex;
  height: 100%;
  justify-content: center;
  align-items: center;
}

.custom-tree-node {
  font-size: 14px;
  font-family: 'Alibaba-PuHuiTi',
    'PingFang SC';
  font-weight: 400;
  color: #C7DFFF;

  .uploadBtn {
    color: #778897;
  }

  .uploadBtn:hover {
    color: $themeColor;
  }
}

.card-body {
  font-size: 16px;
  font-family: "Alibaba-PuHuiTi";
  font-weight: 400;
  padding: 10px 0;
  height: calc(100% - 49px);

  .card-data {
    display: flex;
    margin-bottom: 15px;
    padding-left: 15px;

    .card-data-title {
      color: #889cc3;
      margin-right: 10px;
    }

    .card-data-val {
      color: $whiteColor;
    }

    .val {
      font-size: 16px;
      font-family: "Alibaba-PuHuiTi";
      font-weight: 400;
      color: #1bc0ed;
    }
  }

  .link-data {
    display: flex;
    justify-content: space-between;
    padding: 0 8px 0 0;
    font-size: 16px;
    font-family: "Alibaba-PuHuiTi";
    font-weight: 400;
    color: $whiteColor;

    .iconfont {
      color: #02e287;
    }
  }

  .progress-data {
    display: flex;
    font-size: 16px;
    font-family: "Alibaba-PuHuiTi";
    font-weight: 400;
    color: $whiteColor;


  }

  .el-timeline {
    display: flex;
    flex-direction: column;
    height: 100%;
    padding-left: 10px;

    .el-timeline-item {
      flex: 1;
    }
  }
}

.el-picker-panel__icon-btn {
  color: $whiteColor !important;
}

.el-input.is-disabled .el-input__inner {
  background-color: transparent !important;
}


//广播页面播放
.play_wrapper {
  .table {
    .el-checkbox {
      display: none;
    }
  }

}

//个人中心和报警分析
.content_tab {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 10px;
  height: 40px;
  margin-bottom: 9px;
  background: rgba(68, 114, 141, 0.1);
  box-shadow: 0px 1px 0px 0px rgba(199, 223, 255, 0.5);


  .left_tab {
    height: 100%;

    .btn {
      font-size: 14px;
      display: inline-block;
      cursor: pointer;
      height: 40px;
      color: $whiteColor;
      padding: 0 16px;
      line-height: 40px;
      background: transparent;
      border: none;
      color: rgba(255, 255, 255, 0.5);

      &.active {
        color: #c7dfff;
        background: linear-gradient(180deg,
            rgba(199, 223, 255, 0) 0%,
            rgba(199, 223, 255, 0.3) 100%);
        box-shadow: 0px 1px 0px 0px #c7dfff;
      }
    }
  }
}

.btn-group {
  display: flex;
  align-items: center;
  margin-bottom: 5px;

  .iconfont {
    margin: 0 5px;
  }

  .el-button--mini {
    padding: 2px 8px;
  }
}

.table {
  font-size: 14px;
  font-family: 'Alibaba-PuHuiTi';
  font-weight: 400;
  .onLine {
    color: #27edbb;
  }

  .offLine {
    color: #b8bcbf;
  }

  .gzhf {
    color: #e3731b;
  }

  .gz {
    color: #ff0000;
  }

  .cq {
    color: #4253e5;
  }

}

.content {
  .page {
    // margin-top: 10px;
    text-align: right;
  }
}

.tree_search,
.select_date,
.container_header {
  .el-input__inner {
    // background-color: rgba(97, 109, 148, 0.2) !important;
    font-size: 14px;
    font-family: 'Alibaba-PuHuiTi';
    font-weight: 400;
    color: $whiteColor;
  }
}

.tree_search .el-input__inner {
  border: none;
}


.tree {
  .custom-tree-node {
    display: flex;
    width: 100%;
    color: $whiteColor;
    justify-content: space-between;
    padding-right: 15px;

    .iconfont {
      color: $themeColor;
      margin-right: 5px;
    }

    .icon {
      width: 10px;
      height: 10px;
      display: inline-block;
      border-radius: 50%;
    }

    .online {
      background: linear-gradient(205deg, #07ef35 0%, #017206 100%);
    }

    .offline {
      background: linear-gradient(205deg, #ef2a07 0%, #b2061b 100%);
    }
  }
}

.downLoad {
  height: 28px;
  min-height: 28px !important;
  background: rgba(61, 233, 250, 1) !important;
  border-radius: 2px !important;
  padding: 0 20px !important;
  border: none !important;
  color: #05090D !important;
  margin: 3px 0 !important;
}

.form-inline {
  font-family: 'Alibaba-PuHuiTi',
    'PingFang SC';
  font-weight: 400;
  color: $whiteColor;
}



.host_box,
.loginForm {
  .el-input__wrapper {
    font-family: 'Alibaba-PuHuiTi';
    font-size: 14px;
    font-weight: 400;
    color: $whiteColor !important;
    background-color: rgba(47, 54, 60, 0.3) !important;
    border: 1px solid #2F363C;
    box-shadow: none;
  }
}



.iconfont {
  line-height: 25px;
  cursor: pointer;
}

.list-item {
  .el-slider__button {
    width: 10px;
    height: 10px;
    border: 1px solid #13d4d9;
  }
}

.bf-panel {
  z-index: 103 !important;
  top: 64px !important;
  right: 400px !important;
}


.bf-panel,
.bf-setting .bf-setting-li,
.bf-panel .bf-title,
.bf-tree-header,
.bf-walk-panel {
  background-color: rgba(9, 22, 30, 0.55) !important;
}

.bf-house {
  display: none;
}

.clearfix::after {
  content: "";
  clear: both;
  display: block;
  height: 0px;
}

.select_panel,
.picker {
  background: none !important;
  border: none !important;
}


.flex {
  display: flex;
}


.h100 {
  height: 100%;
}

iframe {
  height: 100%;
  width: 100%;
  border: 0;
}

div {

  /*定义滚动条高宽及背景 高宽分别对应横竖滚动条的尺寸*/
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
    // background-color: #1c1b1b96;
  }

  /*定义滚动条轨道 内阴影+圆角*/
  ::-webkit-scrollbar-track {
    // -webkit-box-shadow: inset 0 0 6px #1c1b1b96;
    border-radius: 6px;
    // background-color: #1c1b1b96;
  }

  /*定义滑块 内阴影+圆角*/
  ::-webkit-scrollbar-thumb {
    border-radius: 5px;
    -webkit-box-shadow: inset 0 0 6px rgba(144, 147, 153, 0.3);
    background-color: rgba(144, 147, 153, 0.3);
  }
}

.cursor {
  cursor: pointer;
}

.map_wrapper {
  position: relative;
  width: 100%;
  height: 100%;

  .allmap {
    width: 100%;
    height: 100%;
    overflow: hidden;
    margin: 0;
  }

  .panel {
    background-color: #091822 !important;
    position: absolute;
    z-index: 100;
    padding: 10px;
    border: 1px solid #323a3e;

    .header {
      color: #889cc3;
      font-size: 16px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-bottom: 1px solid #889cc3;
      padding: 5px 0;
      margin-bottom: 10px;
    }

    el-poppe .panel_list {
      display: flex;
      justify-content: space-between;
      align-items: center;
      color: #889cc3;

      .slider {
        width: 100px;
      }

      .standardName {
        padding: 0 5px;
      }
    }
  }
}

//map中 2D 3D切换面板
.device-type {
  position: absolute;
  bottom: 70px;
  right: 390px;
  width: 100px;
  z-index: 100;
  background-color: #091822;

  .el-card {
    background-color: #091922 !important;
    border: 1px solid #323a3e;
  }
}

.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}



//工单中心
.el-tabs__content {
  height: calc(100% - 55px);
}

//bimface
.bim-box {
  &.hide_left {
    .bf-tree-toolbar {
      left: 10px !important;
      transition: left 0.5s;
    }
  }

  .bf-toolbar.bf-toolbar-bottom {
    border: none;
    background: rgba(0, 0, 0, 0.2);
    z-index: 99999;
  }

  .bf-toolbar.bf-toolbar-bottom,
  .bf-walk-panel {
    display: none;
    flex-direction: column;
    right: 130px;
    left: auto !important;
    top: 50%;
    transform: translateY(-50%);
    bottom: auto !important;
    opacity: 1;
    transition: right 0.5s;
  }


  .bf-tree-toolbar {
    display: none;
  }

  .bf-tips {
    z-index: 105;
  }
}

.w100 {
  width: 100%;
}


.purchase {
  .el-form-item {
    margin-bottom: 0 !important;
  }
}

.h_center {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}








//设备详情
.panel {
  .el-input__wrapper {
    background-color: unset !important;
    box-shadow: unset !important;
    transition: none !important;
    padding-left: 10px !important;
    width: 100px;

  }

  .el-input__inner {
    color: rgba(255, 255, 255, 0.9);
    font-family: Microsoft YaHei;
    font-weight: bold;
    // font-size: 18px;
  }
}











//bim页面svg图标
.icon1 {
  width: 48px;
  height: 48px;
  cursor: pointer;
}


:root {
  --el-menu-active-color: white !important;
  --el-disabled-bg-color: transparent !important;
  --el-disabled-border-color: transparent !important;
}


.el-popover.el-popper {
  min-width: 0 !important;
  width: unset !important;
  --el-popover-padding: 5px !important;
  --el-bg-color-overlay: #03a9f44d !important;
  --el-text-color-regular: white
}



.left,
.right {
  padding-top: 70px;
  z-index: 100;
  height: 100%;
  position: absolute;
}



.left {
  width: 387px;
  left: 0;
  // background: linear-gradient(90deg, rgba(0, 8, 18, 0.8) 0%, rgba(0, 14, 30, 0.6) 72.19%, rgba(0, 15, 32, 0.3) 83.65%, rgba(0, 12, 26, 0.1) 100%);


  background: linear-gradient(to left, rgba($color: #051a30, $alpha: 0), rgba($color: #051a30, $alpha: 0.8));

  .header {
    font-size: 16px;
    font-family: "DOUYU";
    font-weight: 400;
    color: #E6F4FF;
  }

  .input {
    margin-bottom: 12px;
  }

  .device {
    height: calc(100% - 130px);

    .list {
      background: rgba(16, 52, 87, 0.25);
      font-size: 14px;
      font-family: "Alibaba-PuHuiTi";
      font-weight: normal;
      color: #FFFFFF;
      margin-bottom: 8px;
      border-left: 1px solid #4274A3;
      height: 52px;

      .name {
        width: 150px;
        overflow: hidden;

      }

      .iconfont {
        background: linear-gradient(180deg, #FFFFFF 0%, #8ED6FF 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        margin: 0 10px;
      }

      .state {
        div {
          margin-right: 18px;
          white-space:nowrap;
        }

        &>:last-child {
          margin-right: unset;
        }
      }
    }

  }


  //element plus
  .el-input {
    --el-input-bg-color: #021424 !important;
    --el-input-icon-color: #7A9BBD !important;
    --el-input-placeholder-color: #7A9BBD !important;
    --el-input-hover-border-color: #3E5B7B !important;
    --el-border-color: #3E5B7B !important;
    --el-color-primary: #3E5B7B !important;
  }

  .el-input__inner {
    color: #7A9BBD;
    font-size: 16px;
    font-family: "Alibaba-PuHuiTi";
    font-weight: normal;
  }

  .el-pagination {
    --el-fill-color-blank: transparent !important;
    --el-pagination-text-color: #5C7992 !important;
    --el-color-primary: #ffffff !important;
  }
}

.right {
  right: 0;
  width: 393px;
  // background: linear-gradient(270deg, rgba(0, 8, 18, 0.8) 0%, rgba(0, 14, 30, 0.6) 72.19%, rgba(0, 15, 32, 0.3) 83.65%, rgba(0, 12, 26, 0.1) 100%);
  // background: rgba($color: #051a30, $alpha: 0.8);
  background: linear-gradient(to right, rgba($color: #051a30, $alpha: 0), rgba($color: #051a30, $alpha: 0.8));


  box-sizing: border-box;
  display: flex;
  flex-direction: column;

  .item {
    height: calc(100% / 3);

    &-body {
      display: flex;
      justify-content: center;
      padding: 10px;
    }
  }

  .order {
    display: flex;
    justify-content: center;
    align-items: center;

    .text {
      font-size: 16px;
      font-family: "Alibaba-PuHuiTi";
      font-weight: normal;
      color: #EAEBEC;
      margin-right: 20px;
    }

    .num {

      font-size: 26px;
      font-family: "BEBAS";
      font-weight: 400;
      color: #F3F3F3;
      width: 100px;
    }

    &-left {
      background: url("../assets/images/common/d6.png") no-repeat center/100%;
      width: 155px;
      height: 120px;

      display: flex;
      justify-content: center;
      align-items: flex-start;

      .center {

        font-size: 32px;
        font-family: "BEBAS";
        font-weight: 400;
        color: #FFFFFF;
        background: linear-gradient(0deg, #FFFFFF 0%, #1196FC 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }

    }

    &-right {
      width: 214px;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
    }

    &-text {
      margin: 10px 0;
      display: flex;
      align-items: center;
      justify-content: flex-start;

      .dot {
        width: 5px;
        height: 5px;
        margin-right: 7px;
      }
    }

    .total {
      font-size: 14px;
      font-family: "Alibaba-PuHuiTi";
      font-weight: normal;
      color: #E9EAEB;

    }
  }

  .kong {
    font-family: "Alibaba-PuHuiTi";
    font-weight: normal;
    color: #E6F0F6;
    flex-direction: column;
    height: calc(100% - 40px);

    .name {
      margin-right: 7px;
      display: flex;
      align-items: center;
      white-space: nowrap;
    }

    .btn {
      width: 85px;
      height: 44px;
      background: url('../assets/images/common/d5.png') no-repeat;
      background-size: 100%;
      margin-left: 7px;

      &>div {
        flex: 1
      }

      &>div>div {
        height: 32px;
        width: 32px;
        background: url("../assets/images/common/d2.png") no-repeat;
        background-size: 100%;
      }
    }

    .list {
      width: 100%;
      background: rgba(16, 52, 87, 0.25);
      margin-bottom: 6px;
    }

    .dot {
      height: 1px;
      border-bottom: 1px dashed #6E94BA;
      flex: 1
    }
  }

  .event {
    font-family: "Alibaba-PuHuiTi";
    font-weight: normal;
    color: #E6F0F6;

    flex-direction: column;

    height: calc(100% - 40px);

    .name {
      display: flex;
      align-items: center;
      font-size: 14px;
      font-family: "Alibaba-PuHuiTi";
      font-weight: normal;
      color: #F0F9FF;
      width: 120px;

      .iconfont {
        margin-right: 5px;
      }
    }

    .time {

      font-size: 18px;
      font-family: "BEBAS";
      font-weight: 400;
      color: #C3D2E0;
    }

    .list {
      width: 100%;
      background: rgba(16, 52, 87, 0.25);
      margin-bottom: 6px;
      height: 40px;
      position: relative;
      padding: 0 10px;
      display: flex;
      align-items: center;
      justify-content: space-between;

    }

    .bar {
      width: 7px;
      height: 1px;
      background: #466582;
      position: absolute;
      top: 0;
      right: 0;
    }

  }

  .month_repair {
    display: flex;
    height: 48px;
    box-shadow: 0px 1px 0px 0px rgba(240, 240, 240, 0.2);

    .list {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      font-family: "Alibaba-PuHuiTi";
      font-weight: 400;
      background: transparent !important;

      .icon {
        width: 8px;
        height: 8px;
        margin-right: 6px;
      }

      .value {
        font-size: 25px;
        margin-left: 10px;
        font-family: "DINAlternate-Bold";
        font-weight: bold;
        color: #ffffff;
      }
    }
  }

  .sub_echart {
    height: calc(100% - 48px);
  }

}



//设备详情弹窗
.diagram {
  .el-pagination {
    --el-fill-color-blank: transparent !important;
    --el-pagination-text-color: #5C7992 !important;
    --el-color-primary: #ffffff !important;
  }
  .page {
    background-color: rgba(16, 52, 87, 0.6) !important;
    /* 背景透明 */
  }

}


//首页
.wrapper_left,
.wrapper_right {
  padding-top: 70px;
  height: 100%
}
.wrapper_right {
  right: 0;
}

.wrapper_left {
  left: 0;
}






//显示与隐藏动画

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease !important;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0 !important;
}

.z100 {
  z-index: 100;
}

.el-popper.is-customized {
  padding: 6px 12px;
  background: linear-gradient(90deg, rgb(159, 229, 151), rgb(204, 229, 129));
}


//菜单 swiper style

.swiper-slide {
  // width: 74px !important;
}

.swiper-button-next {
  background: url("../assets/images/menu/next.png") no-repeat center;
  right: -23px !important;

}

.swiper-button-prev {
  background: url("../assets/images/menu/prev.png") no-repeat center;
  left: -23px !important;
}





//设置弹窗页面二级弹窗
.addDiagram {

  .el-dialog__body,
  .el-dialog__footer {
    opacity: 1 !important;
  }

  .el-input__wrapper,
  .el-textarea__inner {
    background-color: #0C1B26 !important;
  }

  .el-input,
  .el-textarea {
    --el-input-border-color: #283F52 !important;
    --el-input-hover-border-color: #283F52 !important;
    --el-border-color-hover: #283F52 !important;

  }

  .el-select {
    --el-select-border-color-hover: #283F52 !important;
    width: 100%;
  }

  .el-input__inner {
    border: 0 !important;
    background-color: unset !important;
  }


  .dialog-footer {
    display: flex;
    justify-content: flex-end;
  }


}

.border0 {
  .el-dialog__body {
    border-bottom: 0px !important;
  }

}


input {
  text-align: unset !important;
}

.el-pager li{
  color: white !important;
}

// .swiper-wrapper{
//   justify-content: center;
// }


.energy_container{
  background:linear-gradient(270deg,#000812,rgba(0,14,30,.8) 72.19%,rgba(0,15,32,.5) 83.65%,rgba(0,12,26,0)) ;
}