.el-textarea .el-input__count {
        background: transparent !important;
}

.project_name .el-dropdown {
        font-size: 16px;
        font-family: 'Alibaba-PuHuiTi';
        font-weight: 400;
        color: #778897;
}

.lxxj {
        .el-form-item__label {
                float: none;
        }
}


.content {
        .page {
                .el-pagination__total {
                        font-size: 14px;
                        font-family: 'Alibaba-PuHuiTi',
                                'PingFang SC';
                        font-weight: 400;
                        color: #fff;
                }

                .el-pagination.is-background .btn-next,
                .el-pagination.is-background .btn-prev,
                .el-pagination.is-background .el-pager li {
                        background-color: rgba(255, 255, 255, 0);
                        border: 1px solid #34373a;
                        color: #fff;
                }

                .el-pagination.is-background .el-pager li:not(.disabled).active {
                        background-color: transparent;
                        color: rgba(61, 233, 250, 0.5);
                }
        }





        .el-collapse {
                border-bottom: none;
                border-top: none;
        }

        .el-collapse-item__header {
                border-bottom: none;
                line-height: 20px;
                background-color: transparent;
        }

        .el-collapse-item__wrap {
                background-color: transparent;
                border-bottom: none;
        }

        .el-collapse-item__arrow {
                color: #fff;
        }
}

.el-step__head.is-success {
        color: #3de9fa !important;
        border-color: #3de9fa !important;

        .el-step__icon {
                background: #3de9fa;
                color: #05090D !important;
        }
}

.el-step__head.is-process,
.el-step__head.is-wait {
        border-color: #616D94 !important;
        color: #9CA4B7 !important;

        .el-step__icon {
                background: rgba(97, 109, 148, 0.5);
                font-size: 24px;
                font-family: 'PingFangSC-Medium',
                        'PingFang SC';
                font-weight: 500;
        }
}

.el-step__title.is-process {
        color: #9CA4B7 !important;
}

.el-step__line {
        width: 86%;
        left: 50px !important;
        top: 20px !important;
        background-color: rgba(255, 255, 255, 0.4) !important;
}

.el-step__title {
        font-size: 12px !important;
}

.el-step__icon {
        width: 40px !important;
        height: 40px !important;
}

.el-steps {
        justify-content: center;
}


.el-step__title.is-success {
        color: #3de9fa !important;
        font-family: 'Alibaba-PuHuiTi',
                'PingFang SC';
        font-weight: 400;
}

.layout-header {
        .el-badge__content--danger {
                background-color: #B4061A;
                border: none;
        }
}

.el-form-item__label {
        color: #c7dfff;
}

.plan_container {
        .fc-theme-standard td {
                border: 1px solid rgba(240, 240, 240, 0.2) !important;
        }

        .fc-theme-standard th,
        .fc-theme-standard .fc-scrollgrid {
                border: none !important
        }

        .fc-col-header {
                height: 48px !important;
                line-height: 48px !important;
                background: rgba(255, 255, 255, 0.03);
                box-shadow: 0px 1px 0px 0px rgba(240, 240, 240, 0.2);
        }

        .fc .fc-button {
                padding: 0;
        }

        .fc .fc-button-primary,
        .fc .fc-button-primary:focus,
        .fc .fc-button:focus {
                background-color: rgba(0, 0, 0, 0) !important;
                border-color: rgba(0, 0, 0, 0) !important;
                box-shadow: none !important;
        }

        .fc-toolbar-chunk {
                display: flex;

                div {
                        display: flex;
                        width: 350px;
                        justify-content: space-between;
                }
        }

        .fc .fc-toolbar-title {
                font-size: 18px;
                font-weight: 500;
        }
}

.scrollbar {
        .el-scrollbar__view {
                height: 100%;
                overflow-x: hidden;
        }
}

.el-message-box {
        background-color: #091822 !important;
        border: 1px solid rgba(228, 240, 255, 0.1) !important;
}

.el-message-box__title {
        font-size: 16px !important;
        font-family: 'PingFangSC-Medium',
                'PingFang SC';
        font-weight: 500;
        color: #fff !important;
}

.el-date-editor .el-range-separator {
        color: #fff !important;
}

.el-message-box__message p {
        font-size: 14px;
        font-family: 'Alibaba-PuHuiTi',
                'PingFang SC';
        font-weight: 400;
        color: #fff;
}

.pageForm {
        .el-form-item {
                display: inline-block;
                width: 35%;

                .el-form-item__content .el-textarea {
                        width: 86%;
                }
        }
}

.form {
        .el-form-item__label {
                color: #fff;
        }

        .el-cascader {
                display: block !important;
        }

        .el-input__inner,
        .el-textarea__inner,
        .el-input.is-disabled .el-input__inner,
        .el-textarea.is-disabled .el-textarea__inner {
                background-color: rgba(47, 54, 60, 0.3);
                color: #fff;
                border: 1px solid #2F363C;
        }

        .el-input.is-disabled .el-input__inner {
                background-color: rgba(47, 54, 60, 0.3);
        }

        .el-upload--picture-card {
                background-color: transparent;
                width: 100px;
                height: 100px;
                line-height: 100px;
        }
}

.search_sub {
        .el-form-item--small.el-form-item {
                margin-bottom: 0px;
                vertical-align: middle !important;
        }

        .el-date-editor .el-range-separator {
                color: #fff;
        }

        .el-range-editor--small .el-range-input {
                background-color: transparent;
        }

        .el-input__inner {
                background-color: rgba(68, 114, 141, 0.1);
        }

        .el-date-editor {
                --el-input-bg-color: rgba(68, 114, 141, 0.1);
        }


}

.el-form {
        .el-date-editor {
                --el-input-bg-color: transparent !important;
        }

}

.el-picker-panel {
        .el-input {
                --el-input-bg-color: transparent !important;
                --el-input-border-color: transparent
        }

        .el-button {
                --el-button-bg-color: rgba(61, 233, 250, .5);
                --el-button-border-color: #3de9fa;
                --el-button-text-color: white
        }
}

.cascader {
        background: rgba(47, 54, 60, 0.8) !important;
        border: 1px solid #2F363C !important;

        .el-cascader-node__label {
                color: #fff;
        }

        .el-cascader-menu {
                border-right: 1px solid #2F363C;
        }

        .el-radio__inner {
                background-color: rgba(47, 54, 60, 0.3) !important;
                border: 1px solid #889CC3;
        }

        .el-cascader-node:not(.is-disabled):focus,
        .el-cascader-node:not(.is-disabled):hover {
                background: transparent;
        }
}



.loginForm {
        .el-input__inner:hover {
                border-color: #2F363C;
        }

        .el-input {
                --el-input-focus-border-color: #2f363c00;
                --el-input-hover-border-color: #2f363c00;
        }

        input:-webkit-autofill,
        input:-webkit-autofill:hover,
        input:-webkit-autofill:focus,
        textarea:-webkit-autofill,
        select:-webkit-autofill {
                -webkit-text-fill-color: #808080;
                transition: background-color 100000s ease-out 0.5s;
        }

        .el-form-item.is-error .el-input__inner {
                border-color: rgba(228, 240, 255, 0.1);
        }
}

.table {
        .el-input__inner {
                background: rgba(47, 54, 60, 0.3) !important;
                border: 1px solid #2f363c !important;
        }

        .el-table__fixed-right::before,
        .el-table__fixed::before {
                background-color: #34373a !important;
        }

        .el-radio__input.is-checked .el-radio__inner {
                border-color: #13D4D9;
                background: #13D4D9;
        }

        .el-radio__input.is-checked .el-radio__inner::after {
                background-color: #13D4D9;
        }

        .el-radio__inner {
                background-color: rgba(255, 255, 255, .2);
        }

        .el-checkbox__inner {
                background-color: rgba(255, 255, 255, 0) !important;
        }
}

.el-tabs__item {
        color: rgba(255, 255, 255, .5) !important;
}

.el-tabs__nav-wrap::after {
        height: 1px !important;
        background-color: #7289a0 !important;
}

.el-tabs__header {
        background: rgba(68, 114, 141, 0.1);
        // box-shadow: 0px 1px 0px 0px rgb(199 223 255 / 50%);
}

.el-table__body tr.hover-row>td,
.el-table__body tr.current-row>td {
        background-color: rgba(255, 255, 255, 0) !important;
}

.el-tabs__item.is-active {
        color: #c7dfff !important;
        background: linear-gradient(180deg,
                        rgba(199, 223, 255, 0) 0%,
                        rgba(199, 223, 255, 0.3) 100%);
        box-shadow: 0px 1px 0px 0px #c7dfff;
}

.el-tabs__active-bar {
        height: 0 !important;
}

.el-tabs--bottom .el-tabs__item.is-bottom:last-child,
.el-tabs--bottom .el-tabs__item.is-top:last-child,
.el-tabs--top .el-tabs__item.is-bottom:last-child,
.el-tabs--top .el-tabs__item.is-top:last-child {
        padding-right: 20px !important;
}

.el-tabs--bottom .el-tabs__item.is-bottom:nth-child(2),
.el-tabs--bottom .el-tabs__item.is-top:nth-child(2),
.el-tabs--top .el-tabs__item.is-bottom:nth-child(2),
.el-tabs--top .el-tabs__item.is-top:nth-child(2) {
        padding-left: 20px !important;
}

.card-body {
        .el-timeline-item__tail {
                left: 5px !important;
                top: 13px;
                // height: 76% !important;
                border-left: 1px solid rgba(255, 255, 255, 0.3) !important;
        }

        .el-timeline-item__node {
                background-color: rgba(47, 54, 60, 0.3) !important;
                border: 1px solid #889CC3;
        }

        .el-textarea__inner {
                font-size: 12px;
                font-family: 'Alibaba-PuHuiTi',
                        'PingFang SC';
                font-weight: 400;
                color: #fff;
        }
}


.el-timeline-item__tail {
        left: 5px !important;
        top: 5px;
        height: 80% !important;
        border-left: 1px solid #505356 !important;
}



.el-radio-button__inner {
        background: rgba(47, 54, 60, 0.3) !important;
        border: 1px solid #4a5966 !important;
}

.el-radio-button__orig-radio:checked+.el-radio-button__inner {
        background: rgba(27, 192, 237, 0.3) !important;
        border: 1px solid #1bc0ed !important;
}



.tree {
        .el-tree {
                background: rgba(255, 255, 255, 0) !important;
        }

        .el-checkbox__inner,
        .el-input__inner {
                background-color: rgba(255, 255, 255, 0) !important;
        }
}

.el-tree-node__content {
        font-size: 16px;
        color: #fff;
}

.el-tree-node__content:hover {
        background-color: rgba(255, 255, 255, 0) !important;
}

.el-tree-node:focus>.el-tree-node__content {
        background-color: rgba(255, 255, 255, 0) !important;
        color: #13d4d9;
}

.el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content {
        background-color: #66b1ff87;
}

.el-month-table td.in-range div,
.el-month-table td.in-range div:hover {
        background-color: #091822 !important;
}

.form-inline {
        .el-date-editor .el-range__icon {
                margin-left: 0;
        }

        .el-range-editor.el-input__inner {
                padding: 0;
        }

        .el-input__inner {
                height: 32px;
        }

        .el-date-editor .el-range-separator,
        .el-form-item__label {
                color: #fff;
        }

        .el-form-item,
        .el-form-item--small.el-form-item,
        .el-form-item--mini.el-form-item {
                margin-bottom: 0px;
        }
}

.el-range-editor .el-range-input {
        font-size: 14px;
        color: #6A8FBD !important;
}

.el-input.is-disabled .el-input__inner {
        font-size: 14px !important;
        border: 1px solid #2F363C !important;
}

.search_box {
        display: flex;
        align-items: center;

        .el-form-item__label {
                align-items: center;
                font-size: 14px;
        }


        .el-form-item {
                margin-bottom: 5px !important;
        }


        .el-date-editor .el-range-separator,
        .el-date-editor .el-range-input,
        .el-form-item__label {
                color: $whiteColor;
        }

        .el-form--inline .el-form-item {
                vertical-align: middle;
        }

        .searchBtn {
                background: #1B6DC2;
                border-radius: 4px;
                padding: 5px 15px;
                box-shadow: 0px 0px 3px 3px #2CBDEB inset;
                display: flex;
                justify-content: center;
                align-items: center;
                font-size: 12px;
                font-family: "Alibaba-PuHuiTi";
                font-weight: 500;
                font-style: italic;
                color: #FFFFFF;
                cursor: pointer;
        }

        .delBtn {
                background: #CC2E2E;
                border-radius: 4px;
                padding: 5px 15px;
                box-shadow: 0px 0px 3px 3px #FF9696 inset;
                display: flex;
                justify-content: center;
                align-items: center;
                font-size: 12px;
                font-family: "Alibaba-PuHuiTi";
                font-weight: 500;
                font-style: italic;
                color: #FFFFFF;
                cursor: pointer;
                margin-left: 15px;
        }

        .updateBtn {
                background: #3de9fa;
                border-radius: 4px;
                padding: 5px 15px;
                box-shadow: 0px 0px 3px 3px #3de9fa inset;
                display: flex;
                justify-content: center;
                align-items: center;
                font-size: 12px;
                font-family: "Alibaba-PuHuiTi";
                font-weight: 500;
                font-style: italic;
                color: #FFFFFF;
                cursor: pointer;
                margin-left: 15px;
        }

        .el-input__wrapper {
                font-family: "Alibaba-PuHuiTi";
                font-size: 14px;
                font-weight: 400;
                color: $whiteColor !important;
                background-color: #1A395F !important;
                box-shadow: none;
        }

        .el-date-editor {
                --el-input-border-color: #829CBD !important;
                --el-input-hover-border: #829CBD !important;
                --el-input-focus-border: #829CBD !important;
                --el-input-focus-border-color: #829CBD !important;
                --el-input-hover-border-color: #829CBD !important;
        }


        .el-form-item__content {
                line-height: unset !important;
        }


}

.control {
        .el-radio__inner {
                background: rgba(255, 255, 255, 0.5);
                border: 1px solid #fff;
        }

        .el-radio__input.is-checked .el-radio__inner {
                border-color: #fff;
                background: #13d4d9;
        }
}

//个人中心设置
.info {
        .el-form-item__label {
                color: rgba(199, 223, 255, 1);
                font-size: 14px;
        }

        .el-input__inner {
                background: rgba(47, 54, 60, 0.3);
                border: 1px solid #2f363c;
        }

        .el-radio-group {
                width: 100%;
        }

        .el-form-item.is-error .el-input__inner,
        .el-form-item.is-error .el-input__inner:focus,
        .el-form-item.is-error .el-textarea__inner,
        .el-form-item.is-error .el-textarea__inner:focus,
        .el-message-box__input div.invalid>input,
        .el-message-box__input div.invalid>input:focus {
                border-color: unset;
        }
}

.el-cascader-menu {
        min-width: 150px !important;
}

.navCascader .el-cascader-menu__wrap {
        height: 100% !important;
}


.select_panel {





        .el-cascader-menu {
                border-right: none;
        }

        .el-cascader-node__postfix,
        .el-cascader-node__prefix {
                display: none;
        }
}






.el-cascader__dropdown.el-popper .el-popper__arrow::before {
        border-bottom-color: rgba(255, 255, 255, 0) !important;
        border-right-color: rgba(255, 255, 255, 0) !important;
}

.el-popper__arrow {
        display: none !important;
}

.el-date-range-picker__time-header {
        border-bottom: 1px solid #2F363C !important;
}

.el-popper,
.el-dropdown__popper.el-popper {
        border: none !important;
}

.el-date-picker__header--bordered {
        border-bottom: 1px solid #505459 !important;
}

.el-picker-panel__footer {
        border-top: 1px solid #2F363C !important;
        background-color: transparent !important;

        .el-button--default,
        .el-button.is-plain:hover,
        .el-button.is-disabled.is-plain,
        .el-button.is-disabled.is-plain:focus,
        .el-button.is-disabled.is-plain:hover {
                background: rgba(61, 233, 250, 0.5);
                border-color: #3de9fa;
                color: #fff;
        }

        .el-button--text {
                color: #fff;
        }
}

.el-date-range-picker__content.is-left {
        border-right: 1px solid #2F363C !important;
}

.el-date-table th {
        border-bottom: 1px solid #505459 !important;
}

.el-picker-panel {
        background: #282D34 !important;
        border: none;

        .el-input__inner {
                font-family: 'Alibaba-PuHuiTi',
                        'PingFang SC';
                font-weight: 400;
                color: #fff;
                background-color: rgba(47, 54, 60, 0.3);
                border: 1px solid #2F363C;
                font-size: 14px;
        }
}

.el-month-table td .cell {
        color: #fff !important;
}

.el-dropdown-menu,
.el-menu {
        background-color: #091822 !important;
        color: #fff;
}

.el-dropdown-menu__item:hover {
        background-color: #282D34 !important;
        color: #fff;
}

.alarm_container {
        .el-table__body tr.current-row>td {
                background: #0e1d2f !important;
                color: #3e83d4;
        }
}

.work {
        .el-checkbox__inner {
                background: transparent;
                border: 1px solid;
        }
}

.progress-data {
        .el-textarea__inner {
                background: rgba(47, 54, 60, 0.3);
                border: 1px solid #2f363c;
        }
}




.el-dialog__body {
        padding: 10px !important;

}

.cctv-dialog {


        .el-dialog__footer {
                font-size: 12px;
                background: #091822 !important;
        }



        .el-dialog__title {
                color: #fff;
        }
}

.nav_container {
        .el-input__inner {
                background-color: transparent;
                border: none;
                color: #fff;
                text-align: center;
        }
}

.left_search .el-input__inner {
        background-color: rgba(97, 109, 148, 0.2);
        border: none;
        font-size: 14px;
        font-family: 'Alibaba-PuHuiTi', 'PingFang SC';
        font-weight: 400;
        color: #fff;
}

.el-input-group__append,
.el-input-group__prepend {
        background-color: rgba(47, 54, 60, 0.3) !important;
        border: 1px solid #2F363C !important;
}

.el-page-header {
        margin-bottom: 10px;
}

.el-page-header,
.el-page-header__content {
        color: #fff !important;
}






//------------el-table-------------------
//所有表格头部
.el-table__cell {

        background: rgba(68, 114, 141, 0.1) !important;
        color: #fff !important;
        font-size: '14px';
        font-family: "Alibaba-PuHuiTi";
        font-weight: 500;

}

.el-table__inner-wrapper {
        height: 100% !important;
}


.el-table {
        --el-table-row-hover-bg-color: transparent !important;
        --el-table-header-bg-color: transparent !important;
        --el-table-border-color: #1E4870 !important;

        thead {
                font-size: 14px !important;
                font-family: "Alibaba-PuHuiTi" !important;
                font-weight: bold !important;
                color: #FFFFFF !important;

                tr {
                        background: #103457 !important;
                }

        }

        tbody {
                tr {
                        background: transparent !important;
                }
        }

}





.el-table__body-wrapper {
        background-color: rgba(16, 52, 87, 0.6) !important;
        /* 背景透明 */
}

.el-table,
.el-table__expanded-cell {
        background-color: rgba(255, 255, 255, 0) !important;
        /* 背景透明 */
        color: $whiteColor;
}

.el-table td,
.el-table th.is-leaf,
.el-table th.is-leaf {
        border-bottom: 1px solid #34373a;
}

.el-table .el-table th.is-leaf {
        border-bottom: 1px solid #465661 !important;
}

.el-table--border::after,
.el-table--group::after {
        background: #465661 !important;
}

.el-table::before {
        background: none;
}

/*鼠标移入某行时的背景色*/
.el-table--enable-row-hover .el-table__body tr:hover>td {
        background-color: rgba(255, 255, 255, 0);
}



//------------el-select-------------------

.el-popper.is-pure {
        background-color: #0A1B29 !important;
}

// .el-select-dropdown__item.hover,
// .el-select-dropdown__item:hover {
//         background-color: #282D34 !important;
// }

// .el-select-dropdown__item,.el-select-dropdown__item.selected {
//         background-color: #282D34 !important;
// }

.el-select-dropdown__item.hover,
.el-select-dropdown__item:hover {
        background-color: #264458 !important;
}

//------------el-slider-------------------
.el-slider__bar {
        background: linear-gradient(-90deg, #1E79F2, #1CD3DE);

}

.el-slider__runway {
        background: #2D435E !important;

}

.el-switch__label.is-active,
.el-switch__label {
        font-size: 16px;
        font-family: "Alibaba-PuHuiTi";
        font-weight: bold;
        font-style: italic;
        color: #FFFFFF;
}

//------------el-dailog-------------------
.el-dialog {
        --el-dialog-bg-color: transparent !important;
}




.el-dialog__header {
        // height: 54px !important;
        // padding: 0 !important;
        // display: flex;
        // align-items: center;
        margin-right: 0 !important;


}

.el-dialog__headerbtn .el-dialog__close {
        color: white !important;
}

.el-dialog__headerbtn {
        top: 10px !important;
}

.el-dialog__title {
        font-size: 20px !important;
        font-family: "Alibaba-PuHuiTi" !important;
        font-weight: bold !important;
        font-style: italic !important;
        color: #FFFFFF !important;
        margin-left: 10px;
        margin-top: 10px;
}

.el-dialog__body,
.el-dialog__footer {


        .el-pagination {
                --el-fill-color-blank: transparent !important;
                --el-pagination-text-color: #5C7992 !important;
                --el-color-primary: #ffffff !important;
        }


}


//------------el-pagination-------------------
.el-pagination {
        --el-fill-color-blank: transparent !important;
        --el-pagination-text-color: #5C7992 !important;
        --el-color-primary: #ffffff !important;
}

//------------el-cascader-------------------
.el-cascader {
        width: 100%;
}


//-----------------el-dialog-------------------
.el-dialog {
        background-image: url('../assets/images/dialog/bg.png') !important;
        background-repeat: no-repeat !important;
        background-size: 100% 100% !important;
}
.el-dialog__header{
        background-image: url('../assets/images/dialog/bg_title.png') !important;
        background-repeat: no-repeat !important;
        background-size: 100% 100% !important;
        background-position-y: 5px;
}


.el-badge__content.is-fixed{
        right: unset !important;
}