
const state = {
  funMenus: null, //底部系统菜单
  index: -2,//个人中心快捷菜单触发,底部菜单
  mode:0,
  dialog:{
    component:""
  },//设置弹窗页面
}

const mutations = {
  SET_MODE: (state: { mode: any; }, mode: any) => {
    state.mode = mode;
  },

  SET_MENU_INDEX: (state: { index: any; }, index: any) => {
    state.index = index;
  },

  SET_FUN_MENU: (state: { funMenus: any; }, funMenus: any) => {
    state.funMenus = funMenus;
  },
  SET_DIALOG: (state: { dialog: any; }, dialog: any) => {
    state.dialog = dialog;
  },
  USER_SIGNOUT: (state: { funMenus: null; }) => {
    state = {
      funMenus: null,
    }
  }
}
const actions = {

}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}

