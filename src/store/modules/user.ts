import { login, logout, refreshToken } from '../../api/user'
import { getCookie, removeToken, setCookie } from '@/utils/cookie'
import { ActionTree, useStore } from 'vuex';
import { getDefaultProject } from '../../api/project'


const getDefaultState = () => {
  return {
    token: getCookie("gh_token"),
    projectId: 0,
    name: '',
    avatar: ''
  }
}


const state = getDefaultState()
const store = useStore()
const mutations = {
  RESET_STATE: (state: any) => {
    Object.assign(state, getDefaultState())
  },
  SET_TOKEN: (state: any, token: any) => {
    state.token = token
  },
  SET_NAME: (state: any, name: any) => {
    state.name = name
  },
  SET_PROJECT_ID: (state: any, projectId: any) => {
    state.projectId = projectId
  }
}

const actions: ActionTree<any, any> = {
  // 登录
  async login({ commit }, userInfo: any) {
    const { username, password, params, code } = userInfo

    return new Promise((resolve, reject) => {
      login({ username: username.trim(), password: password, params: params.captchaVerification, code }).then((response: any) => {
        const data = response.data
        commit('SET_TOKEN', data.access_token);
        setCookie("gh_token", data.access_token);
        setCookie("gh_id", data.userId);
        setCookie("gh_name", data.name);
        setCookie("_refreshToken", data.refresh_token);

        getDefaultProject().then((res: any) => {
          if (res.data) {
            setCookie("gh_projectId", res.data);
            commit('SET_PROJECT_ID', res.data)
          }
          resolve(response);
        });
      }).catch((error: any) => {
        reject(error)
      })
    })
  },


  // user logout
  logout({ commit, state }): any {
    return new Promise((resolve, reject) => {
      logout(state.token).then((response: any) => {
        removeToken(state.token) // must remove  token  first
        commit('RESET_STATE')
        resolve(response)
      }).catch((error: any) => {
        reject(error)
      })
    })
  },

  refreshToken({ commit }) {
    return new Promise((resolve, reject) => {
      let token = getCookie('_refreshToken');
      if (token) {
        refreshToken({ refreshTokenValue: token }).then((response: any) => {
          const { data } = response
          commit('SET_TOKEN', data.access_token)
          setCookie("gh_token", data.access_token)
          setCookie("_refreshToken", data.refresh_token);
          resolve(response);
        }).catch((error: any) => {
          reject(error);
        });
      }
    });

  },

  // remove token
  resetToken({ commit }) {
    return new Promise<void>(resolve => {
      // removeToken() // must remove  token  first
      commit('RESET_STATE')
      resolve()
    })
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}

