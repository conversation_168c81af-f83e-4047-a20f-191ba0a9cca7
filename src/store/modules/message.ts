import Vue from 'vue'
const state = {
  mesData: [],
  connected: false,
  count: 0,
  alarms: [],
  isClose: false,
  h_alarm:[],
  r_alarm:[],
}
const mutations = {
  SET_MESSAGE_DATA: (state: { mesData: any; }, data: any) => {
    state.mesData = data;
  },
  DELETE_STATE: (state: { mesData: never[]; }) => {
    state = {
      mesData: [],
    }
  },
  SET_CONNECTED(state: { connected: any; }, data: any) {
    state.connected = data;
  },
  SET_ALARM_COUNT(state: { count: any,alarms:any }, count: any) {
    if(count==0)
    {
      state.alarms=[];
    }
    state.count = count;
  },
  SET_ALARM_H(state: { h_alarm: any,r_alarm:any }, alarms: any) {
    state.r_alarm=[];
    state.h_alarm=alarms;
  },
  SET_ALARM_R(state: { r_alarm: any[]; }, alarm: any) {
    if(alarm.length==0)
    {
         state.r_alarm=[];
         return;
    }
    state.r_alarm.unshift(alarm);
  },
  // 报警声音是否关闭
  SET_CLOSE_ALARM(state: { isClose: any; }, isClose: any) {
    state.isClose = isClose
  }
}
const actions = {

}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}