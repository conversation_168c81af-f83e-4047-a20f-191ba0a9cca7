
/* eslint-disable */
declare module '*.vue' {
  import type { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}
declare module 'vue-monoplasty-slide-verify';
declare module 'vue-seamless-scroll';
declare module 'crypto-js';
declare module 'vue-drag-resize'



  interface Window {
     DEV_SOCKET_API?: string;
     PROD_SOCKET_API?:string;
     DEV_BASE_API?:string;
     PROD_BASE_API?:string;
     DEV_9008_API?:string;
     PROD_9008_API?:string;
     username?:string;
     password?:string;
  }






//bimface
declare var BimfaceSDKLoaderConfig: any
declare var BimfaceEnvOption: any
declare var BimfaceSDKLoader: any
declare var DrawableContainerConfig: any
declare var Glodon: any


//雷达扫描 mapv
declare namespace mapvgl {
  class View {
    constructor(public option: any)
    getAllLayers: any;
    removeLayer: any;
    addLayer: any;
    removeAllLayers: any;

  }
  class FanLayer {
    constructor(public option: any) { }
  }
}

/// <reference path="./types/bmapgl/index.d.ts" />












