import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'
import '@/utils/rem'
import '@/styles/index.scss'
import '@/styles/font.scss'  // 外部字体文件
import './permission'
import VueSocketIO from 'vue-socket.io'
//symbol图标使用js
import '@/assets/iconfont/iconfont.js'
import '@/assets/iconfont/iconfont.css'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import locale from 'element-plus/lib/locale/lang/zh-cn'

import { SlideVerify } from 'vue-monoplasty-slide-verify'
import SubTitle from '@/components/SubTitle.vue'
import SubTitle1 from '@/components/SubTitle1.vue'
import SubTitle2 from '@/components/SubTitle2.vue'
import LayoutHeader from '@/views/Layout/LayoutHeader.vue'

import Line from '@/components/line.vue'
import Menu from '@/components/Menu.vue'
import Floor from '@/components/Floor.vue'
import Panel from '@/components/device/Panel.vue'

import mitt from 'mitt'//代替eventbus
import BIM from "@/views/Bim/index.vue";
import noData from '@/components/noData.vue'
import diagramNoData from '@/components/diagramNoData.vue'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import axios from 'axios'
//socket连接
const vSocket = new VueSocketIO({
    debug: true,
    // 服务器端地址
    connection: process.env.NODE_ENV == "development" ? window.DEV_SOCKET_API : window.PROD_SOCKET_API,
    vuex: {
        actionPrefix: 'SOCKET_',
        mutationPrefix: 'SOCKET_',

    },
    // options:{path:'/abc'}

});

//api接口对象
const api: any = {};
const apiContext = require.context('./api/', true, /.ts$/);
apiContext.keys().forEach(component => {
    const object = apiContext(component)
    for (const key in object) {
        if (object.hasOwnProperty(key)) {
            const element = object[key]
            api[key] = element
        }
    }
});

let app = createApp(App);



// 全局 property
app.config.globalProperties.$api = api;
app.provide('mitt', mitt());
app.config.globalProperties.$axios = axios
app
    .component('SubTitle', SubTitle)
    .component("SubTitle1", SubTitle1)
    .component("SubTitle2", SubTitle2)
    .component('LayoutHeader', LayoutHeader)
    .component('Line', Line)
    .component('GMenu', Menu)
    .component('Floor', Floor)
    .component('Panel', Panel)
    .component('bim', BIM)
    .component('noData', noData)
    .component('diagramNoData', diagramNoData)
    .use(SlideVerify)
    .use(vSocket)
    .use(ElementPlus, { locale })
    .use(store).use(router).mount('#app');
    
const Components = require.context('./views/', true, /\.vue$/);
Components.keys()
    .map(Components)
    .forEach((item: any) => {
        if (item.default.name)
            app.component(item.default.name, item.default)
    })

    

    for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
        app.component(key, component)
      }





