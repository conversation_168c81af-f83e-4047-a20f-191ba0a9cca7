import request from '@/utils/request'
import qs from 'qs'
// 能耗总览 设备今日 昨日 本月 上个月 本年 用能
export function getEnergyOverView(params: any) {
    return request({
        url: '/energy-service/energy-search',
        method: 'get',
        params
    })
}
// 能源审计-用能指标 设备时间段用量统计
export function periodUsageStatistic(params: any) {
    return request({
        url: '/energy-service/energy-search/audit-device',
        method: 'get',
        params
    })
}
// 能源审计 部门 区域 分项 能耗 某一项 本期 同比 环比 全年
export function suboptionUsageStatistic(params: any) {
    return request({
        url: '/energy-service/energy-search/audit-device',
        method: 'get',
        params
    })
}
// 能源流向&能源审计 部门 区域 分项 能耗 时间范围内使用统计
export function energyFlow(params: any) {
    return request({
        url: '/energy-service/energy-search/flow',
        method: 'get',
        params
    })
}

export function energyFlowExcel(params: any) {
    return request({
        url: '/energy-service/energy-search/flow-excel',
        method: 'get',
        params,
        responseType: 'blob'
    })
}

// 统计报表   历史总用量
export function historyOverviewAll(params: any) {
    return request({
        url: '/energy-service/energy-search/overview-all',
        method: 'get',
        params
    })
}
// 统计报表-整体趋势 部门 区域 分项 按时间查询 相加显示(返回时间)
export function overviewTrend(params: any) {
    return request({
        url: '/energy-service/energy-search/overview-trend',
        method: 'get',
        params
    })
}
// 能耗报表-简报-能耗费用 今日 昨日 7天 30天 一年
export function reportCharge(params: any) {
    return request({
        url: '/energy-service/energy-search/report-charge',
        method: 'get',
        params
    })
}
// 能耗报表-简报-统计指标
export function reportStandard(params: any) {
    return request({
        url: '/energy-service/energy-search/report-standard',
        method: 'get',
        params
    })
}
// 能耗统计 区域能耗 水电气今日 昨日 本月 上月 本年 统计
export function energyStartStatistic(params: any) {
    return request({
        url: '/energy-service/energy-search/stat-area',
        method: 'get',
        params
    })
}
//能耗统计 分项能耗 水电气今日 昨日 本月 上月 本年 统计
export function energyCategoryStatistic(params: any) {
    return request({
        url: '/energy-service/energy-search/stat-category',
        method: 'get',
        params
    })
}
// 大数据用能分析 部门 区域 分项 能耗 按时间查询 相加显示(返回时间)
export function energyAnalysis(params: any) {
    return request({
        url: '/energy-service/energy-search/stat-chart',
        method: 'get',
        params
    })
}
//能耗统计 部门能耗 水电气今日 昨日 本月 上月 本年 统计
export function deptEnergyStatistic(params: any) {
    return request({
        url: '/energy-service/energy-search/stat-dept',
        method: 'get',
        params
    })
}
export function featureEnergyStatistic(params: any) {
    return request({
        url: '/energy-service/energy-search/stat-feature',
        method: 'get',
        params
    })
}
// 时间范围能所有设备峰平谷用量

export function timeLimitAllPeriod(params: any) {
    return request({
        url: '/energy-service/period',
        method: 'get',
        params
    })
}
// 今日昨日本月上月本年峰平谷用电金额
export function periodCharge(params: any) {
    return request({
        url: '/energy-service/period/charge',
        method: 'get',
        params
    })
}
// 获取区域里面的分项
export function getAreaCategory(params: any) {
    return request({
        url: '/energy-service/energy-search/area-category',
        method: 'get',
        params
    })
}
export function getReadMeterRecord(params: any) {
    return request({
        url: '/energy-service/energy-search/log',
        method: 'get',
        params
    })
}

// 能耗分析  同比环比
export function overviewChain(params: any) {
    return request({
        url: '/energy-service/energy-search/overview-Chain',
        method: 'get',
        params,
    })
}
// 获取部门
export function getDeptTree(params: any) {
    return request({
        url: '/basicinfo-service/dept/tree',
        method: 'get',
        params
    })
}
// 查询能耗分项树
export function getCategory(params: any) {
    return request({
        url: '/energy-service/category/tree',
        method: 'get',
        params
    })
}

export function getFeature(params: any) {
    return request({
        url: '/energy-service/feature/tree',
        method: 'get',
        params
    })
}
//能源审计 部门 区域 分项 能耗 某一项 本期 同比 环比 全年
export function dosageChain(params: any) {
    return request({
        url: '/energy-service/energy-search/audit-energy',
        method: 'get', params,
    })
}


//能耗总览 单位面积总和能耗
export function unitAreaEnergyAll(params: any) {
    return request({
        url: '/energy-service/energy-search/all',
        method: 'get',
        params,
    })
}
//下载抄表数据

export function getDevice(params: any) {
    return request({
        url: '/energy-service/device',
        method: 'get',
        params,
    })
}
//单表 大数据用能分析 部门 区域 分项 能耗 按时间查询 相加显示(返回时间)
export function singleEnergyAnalysis(params: any) {
    return request({
        url: '/energy-service/energy-search/single-chart',
        method: 'get',
        params
    })
}
// 查询电表--树

export function getEnergyDeviceTree(params: any) {
    return request({
        url: '/energy-service/device/tree',
        method: 'get',
        params
    })
}
// 首页7日能耗
export function getEnergySeven(params: any) {
    return request({
        url: '/energy-service/energy-search/seven',
        method: 'get',
        params
    })
}



export function getHistoryEnergy(params: any) {
    return request({
      url: '/energy-service/history',
      method: 'get',
      params,
    })
  }


  export function getDeviceEnergyById(params: any) {
    return request({
      url: '/energy-service/energy-search/device-id',
      method: 'get',
      params,
    })
  }

  export function energyDay(params:any){
    return request({
        url:'/energy-service/energy-search/day',
        method: 'get',
        params
    })
}

