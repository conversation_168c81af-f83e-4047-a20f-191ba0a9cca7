import request from '@/utils/request'

// 查询任务列表
export function getTask() {
        return request({
                url: '/itc-service/task',
                method: 'get',
        })
}

// 任务远程控制
export function postTaskCmd(data: any) {
        return request({
                url: '/acs-service/itc/task/cmd',
                method: 'post',
                data
        })
}
// 创建点播任务
export function postMusicTask(data: any) {
        return request({
                url: '/acs-service/itc/task/musicTask',
                method: 'post',
                data
        })
}
// 任务统计
export function getTaskStatistics(params: any) {
        return request({
                url: '/itc-service/task/statistics',
                method: 'get',
                params
        })
}
// 停止任务
export function getTaskStop(params: any) {
        return request({
                url: '/itc-service/task/stop',
                method: 'post',
                params
        })
}
// 创建tts任务
export function getTaskTTSTask(params: any) {
        return request({
                url: '/itc-service/task/ttsTask',
                method: 'post',
                params
        })
}