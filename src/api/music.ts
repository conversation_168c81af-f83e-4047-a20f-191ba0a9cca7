import request from '@/utils/request'
// 获取远程文件列表
export function getMusic() {
        return request({
                url: '/itc-service/music',
                method: 'post'
        })
}

// 文件上传
export function uploadFile(id: any, data: any) {
        return request({
                url: `/itc-service/upload/${id}`,
                method: 'post',
                data
        })
}
// 创建媒体库
export function addMusic(params: any) {
        return request({
                url: `/itc-service/music/add`,
                method: 'post',
                params
        })
}