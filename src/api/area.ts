import request from '@/utils/request'
import qs from 'qs'
export function getProjectArea(params: any) {
  return request({
    url: '/basicinfo-service/area/tree',
    method: 'get',
    params,
    paramsSerializer: (params) => {
      return qs.stringify(params, { arrayFormat: 'repeat' });
    }
  })
}

export function getAreaConfig(params: any) {
  return request({
    url: '/basicinfo-service/area-config',
    method: 'get',
    params,
  })
}


export function getMenuDiagram(params: any) {
  return request({
    url: '/basicinfo-service/area-config/area',
    method: 'get',
    params,
  })
}




