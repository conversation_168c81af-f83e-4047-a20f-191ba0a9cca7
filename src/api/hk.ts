import request from '@/utils/request'

// 查询报警主机
export function getAlarmDevice(params: any) {
  return request({
    url: '/hk-service/alarm/device',
    method: 'get',
    params
  })
}


// 查询子系统
export function getAlarmSub(params: any) {
  return request({
    url: '/hk-service/alarm/sub',
    method: 'get',
    params
  })
}

// 查询子系统状态
export function getAlarmSubStatus(params: any) {
  return request({
    url: '/hk-service/alarm/sub-status',
    method: 'get',
    params
  })
}

// 查询子系统防区
export function getDefence(params: any) {
  return request({
    url: '/hk-service/alarm/defence',
    method: 'get',
    params
  })
}

// 查询子系统防区
export function cmd(data: any) {
  return request({
    url: '/hk-service/alarm/defence',
    method: 'post',
    data
  })
}


export function getHKDoor(data: any) {
  return request({
    url: '/acs-service/dashi/door',
    method: 'post',
    data
  })
}
export function getHKController(params: any) {
  return request({
    url: '/acs-service/hk/device',
    method: 'get',
    params
  })
}

export function getDaShiRecord(data: any) {
  return request({
    url: '/acs-service/dashi/record',
    method: 'post',
    data
  })
}

export function getDaShiEvent(data: any) {
  return request({
    url: '/acs-service/dashi/event',
    method: 'post',
    data
  })
}
export function getHkTerminal(params: any) {
  return request({
    url: '/acs-service/hk/terminal',
    method: 'get',
    params
  })
}

export function hkTerminalCmd(data: any) {
  return request({
    url: '/acs-service/hk/cmd',
    method: 'post',
    data
  })
}


export function getHkProgram(params: any) {
  return request({
    url: '/acs-service/hk/program',
    method: 'get',
    params
  })
}
export function crossRecords(params: any) {
  return request({
    url: '/acs-service/hk/crossRecords',
    method: 'get',
    params
  })
}


export function vehicleList(params: any) {
  return request({
    url: '/acs-service/hk/vehicleList',
    method: 'get',
    params
  })
}












