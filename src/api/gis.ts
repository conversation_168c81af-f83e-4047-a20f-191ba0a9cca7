import request from '@/utils/request'
export function getConfig(params:any){
  return request({
    url: '/bim-service/gis',
    method: 'get',
    params
  })
}


export function addConfig(data:any) {
  return request({
    url: '/bim-service/gis',
    method: 'post',
    data,
  })
}


export function deleteConfig(params:any) {
  return request({
    url: '/bim-service/gis',
    method: 'delete',
    params,
  })
}


export function getPosition(params:any){
  return request({
    url: '/bim-service/gis/position',
    method: 'get',
    params
  })
}

export function getCamPosition(params:any){
  return request({
    url: '/bim-service/gis/cam',
    method: 'get',
    params
  })
}
