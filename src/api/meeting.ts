
import request from '@/utils/request'
import qs from 'qs'

export function getRoom(params: any) {
  return request({
    url: '/acs-service/meeting/room',
    method: 'get',
    params,
    paramsSerializer: (params) => {
      return qs.stringify(params, { arrayFormat: 'repeat' });
    }
  })
}

export function getMeeting(params: any) {
  return request({
    url: '/acs-service/meeting/list',
    method: 'get',
    params,
  })
}


export function auditMeeting(data: any) {
  return request({
    url: '/acs-service/meeting/audit',
    method: 'post',
    data,
  })
}



