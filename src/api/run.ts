import request from '@/utils/request'
import qs from 'qs'
// 查询当前模式
export function getRunModel(params: any) {
  return request({
    url: '/scene-service/running',
    method: 'get',
    params
  })
}
// 更新模式
export function updateRunModel(data: any) {
  return request({
    url: '/scene-service/running',
    method: 'put',
    data
  })
}
// 一次查询三种模式下的配置
export function getRunConfig(params: any) {
  return request({
    url: '/scene-service/running/config',
    method: 'get',
    params
  })
}
// 取消系统或子系统模式配置
export function unCheckRun(params: any) {
  return request({
    url: '/scene-service/running/uncheck',
    method: 'put',
    params
  })
}


// 查询手动记录
export function getRunManualLog(params: any) {
  return request({
    url: '/rtdb-service/runLog',
    method: 'get',
    params
  })
}


export function updateStrategyEnable(data: any) {
  return request({
    url: '/scene-service/strategy/state',
    method: 'post',
    data
  })
}



