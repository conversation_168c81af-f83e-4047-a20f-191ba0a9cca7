import request from '@/utils/request'
import qs from 'qs'

//-------------备品备件管理-----------
//查询报废
export function getScrap(params: any) {
  return request({
    url: '/maintenance-service/scrap',
    method: 'get',
    params
  })
}

//添加报废
export function addScrap(data: any) {
  return request({
    url: '/maintenance-service/scrap',
    method: 'post',
    data,
  })
}

//编辑报废

export function editScrap(data: any) {
  return request({
    url: '/maintenance-service/scrap/edit',
    method: 'put',
    data
  })
}

export function auditScrap(data: any) {
  return request({
    url: '/maintenance-service/scrap',
    method: 'put',
    data
  })
}


//删除报废
export function delScrap(params: any) {
  return request({
    url: '/maintenance-service/scrap',
    method: 'delete',
    params
  })
}