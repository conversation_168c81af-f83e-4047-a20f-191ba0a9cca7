import request from '@/utils/request'
// 查询终端信息
export function getTerminal(params: any) {
        return request({
                url: '/acs-service/itc/terminal',
                method: 'get',
                params
        })
}

// 终端种类统计
export function getTerminalStatistic(params: any) {
        return request({
                url: '/itc-service/terminal/statistic',
                method: 'get',
                params
        })
}
// 终端种类统计
export function getTerminalStatus(params: any) {
        return request({
                url: '/itc-service/terminal/status',
                method: 'get',
                params
        })
}
// 终端日志
export function getTerminalRecord(params: any) {
        return request({
                url: '/acs-service/itc/terminal/record',
                method: 'get',
                params
        })
}


export function getTaskRecord(params: any) {
        return request({
                url: '/acs-service/itc/terminal/task',
                method: 'get',
                params
        })
}

export function getITCMusic(data: any) {
        return request({
                url: '/acs-service/itc/music',
                method: 'post',
                data
        })
}

export function getITCTask(params: any) {
        return request({
                url: '/acs-service/itc/task',
                method: 'get',
                params
        })
}