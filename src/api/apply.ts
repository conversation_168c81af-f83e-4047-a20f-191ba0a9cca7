import request from '@/utils/request'


//-------------备品申请管理-----------

export function getApply(params: any) {
  return request({
    url: '/maintenance-service/apply',
    method: 'get',
    params
  })
}


export function addApply(data: any) {
  return request({
    url: '/maintenance-service/apply',
    method: 'post',
    data,
  })
}



export function audit(data: any) {
  return request({
    url: '/maintenance-service/apply',
    method: 'put',
    data
  })
}


export function delApply(params: any) {
  return request({
    url: '/maintenance-service/apply',
    method: 'delete',
    params,
  })
}