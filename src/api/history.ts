import request from '@/utils/request'
import qs from 'qs'

export function getAnalyze(params: any) {
  return request({
    url: '/scene-service/history/analyze',
    method: 'get',
    params,                                                                                                                                                                                                                                                 
    paramsSerializer: (params) => {
      return qs.stringify(params, { arrayFormat: 'repeat' });
    }
  })
}


export function getHistoryData(params: any) {
  return request({
    url: '/scene-service/history/no-page',
    method: 'get',
    params,
  })
}

export function getHistoryDown(params: any) {
  return request({
    url: '/scene-service/history/down',
    method: 'get',
    params,
    responseType: 'blob'
  })
}


export function getEvents(params: any) {
  return request({
    url: '/scene-service/event',
    method: 'get',
    params,
  })
}