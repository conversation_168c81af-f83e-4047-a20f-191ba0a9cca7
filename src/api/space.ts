import request from '@/utils/request'
import qs from 'qs'
export function getSpace(params: any) {
  return request({
    url: '/bim-service/bim-space',
    method: 'get',
    params
  })
}

export function getSpaceType(params: any) {
  return request({
    url: '/bim-service/bim-space/type',
    method: 'get',
    params
  })
}

export function getSpaceUse(params: any) {
  return request({
    url: '/bim-service/bim-space/use',
    method: 'get',
    params
  })
}


export function addSpace(data: any) {
  return request({
    url: '/bim-service/bim-space',
    method: 'post',
    data,
  })
}



export function editSpace(data: any) {
  return request({
    url: '/bim-service/bim-space',
    method: 'put',
    data
  })
}


export function delSpace(params: any) {
  return request({
    url: '/bim-service/bim-space',
    method: 'delete',
    params,
    paramsSerializer: (params) => {
      return qs.stringify(params, { arrayFormat: 'repeat' });
    }
  })
}




export function getCustomer(params: any) {
  return request({
    url: '/bim-service/customer',
    method: 'get',
    params
  })
}


export function addCustomer(data: any) {
  return request({
    url: '/bim-service/customer',
    method: 'post',
    data,
  })
}



export function editCustomer(data: any) {
  return request({
    url: '/bim-service/customer',
    method: 'put',
    data
  })
}


export function delCustomer(params: any) {
  return request({
    url: '/bim-service/customer',
    method: 'delete',
    params,
    paramsSerializer: (params) => {
      return qs.stringify(params, { arrayFormat: 'repeat' });
    }
  })
}





