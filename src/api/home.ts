import request from "@/utils/request";
import qs from "qs";
export function getSystemCount(params: any) {
  return request({
    url: "/basicinfo-service/menu/tree",
    method: "get",
    params,
    paramsSerializer: (params) => {
      return qs.stringify(params, { arrayFormat: "repeat" });
    },
  });
}

export function getDeviceCount(params:any) {
    return request({
      url: '/resource-service/report/count',
      method: 'get',
      params,
    
    })
  }

  export function getTwl() {
    return request({
      url: '/resource-service/report/twl',
      method: 'get',
    })
  }
  
  export function getDeviceStatus(params:any) {
    return request({
      url: '/resource-service/device-standard-param/val',
      method: 'get',
      params,
    
    })
  }

