import request from '@/utils/request'

// 查询终端
export function getInfoTerminal(params: any) {
  return request({
    url: '/info-service/terminal/shizhan-terminal',
    method: 'get',
    params
  })
}



export function getSigTerminal(params:any){
  return request({
      url: '/acs-service/sigital/terminal',
      method: 'get',
      params
  })
}


export function cmd(data:any){
  return request({
      url: '/acs-service/sigital/cmd',
      method: 'post',
      data
  })
}

export function setVol(data:any){
  return request({
      url: '/acs-service/sigital/vol',
      method: 'post',
      data
  })
}


