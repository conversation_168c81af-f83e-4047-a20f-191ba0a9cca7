import request from '@/utils/request'
export function getAreaVideo(params: any) {
  return request({
    url: '/video-service/camera/area',
    method: 'get',
    params
  })
}


export function getVideo(params: any) {
  return request({
    url: '/video-service/camera',
    method: 'get',
    params
  })
}


export function getVideoServer(params: any) {
  return request({
    url: '/video-service/server',
    method: 'get',
    params
  })
}


export function getSession(params: any) {
  return request({
    url: '/video-service/server/session',
    method: 'get',
    params
  })
}


export function ptz(data: any) {
  return request({
    url: '/video-service/ptz',
    method: 'post',
    data
  })
}

export function movePtz(data: any) {
  return request({
    url: '/video-service/move',
    method: 'post',
    data
  })
}

export function stopMovePtz(data: any) {
  return request({
    url: '/video-service/stop',
    method: 'post',
    data
  })
}




export function getVideoLine(params:any) {
  return request({
    url: '/video-service/line',
    method: 'get',
    params,
  })
}
export function getLineEvent(params:any) {
  return request({
    url: '/video-service/event',
    method: 'get',
    params,
  })
}
export function saveLineEvent(data: any) {
  return request({
    url: '/video-service/event',
    method: 'post',
    data
  })
}