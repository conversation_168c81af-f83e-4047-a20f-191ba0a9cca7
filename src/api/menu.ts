import request from "@/utils/request";
import qs from "qs";
export function getMenuList(params: any) {
  return request({
    url: "/basicinfo-service/menu/tree",
    method: "get",
    params,
    paramsSerializer: (params) => {
      return qs.stringify(params, { arrayFormat: "repeat" });
    },
  });
}

export function getMenus(params: any) {
  return request({
    url: "/basicinfo-service/menu",
    method: "get",
    params,
    paramsSerializer: (params) => {
      return qs.stringify(params, { arrayFormat: "repeat" });
    },
  });
}

export function getshortcut(params: any) {
  return request({
    url: "/basicinfo-service/menu/short",
    method: "get",
    params,
    paramsSerializer: (params) => {
      return qs.stringify(params, { arrayFormat: "repeat" });
    },
  });
}


export function getUserMenu(params: any) {
  return request({
    url: "/basicinfo-service/user-menu",
    method: "get",
    params
  });
}

export function addUserMenu(data: any) {
  return request({
    url: "/basicinfo-service/user-menu",
    method: "post",
    data
  });
}

export function delUserMenu(data: any) {
  return request({
    url: "/basicinfo-service/user-menu",
    method: "delete",
    data
  });
}
