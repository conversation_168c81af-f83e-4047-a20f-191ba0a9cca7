import request from '@/utils/request'
import qs from 'qs'

//-------------备品备件管理-----------
//查询备品备件
export function getSpare(params: any) {
  return request({
    url: '/maintenance-service/spare',
    method: 'get',
    params
  })
}

//添加备品备件
export function addSpare(data: any) {
  return request({
    url: '/maintenance-service/spare',
    method: 'post',
    data,
  })
}

//编辑备品备件

export function editSpare(data: any) {
  return request({
    url: '/maintenance-service/spare',
    method: 'put',
    data
  })
}

//删除备品备件
export function delSpare(params: any) {
  return request({
    url: '/maintenance-service/spare',
    method: 'delete',
    params,
    paramsSerializer: (params) => {
      return qs.stringify(params, { arrayFormat: 'repeat' });
    }
  })
}