import request from '@/utils/request'
// 查询终端信息
export function getIPCastTerminal(params: any) {
        return request({
                url: '/acs-service/ipcast/terminal',
                method: 'get',
                params
        })
}


export function getIPCastSession(params: any) {
        return request({
                url: '/acs-service/ipcast/session',
                method: 'get',
                params
        })
}



export function getIPCastFile(params: any) {
        return request({
                url: '/acs-service/ipcast/file',
                method: 'get',
                params
        })
}


export function ipcastCtrl(data: any) {
        return request({
                url: '/acs-service/ipcast/ctrl',
                method: 'post',
                data
        })
}

export function ipcastPlay(data: any) {
        return request({
                url: '/acs-service/ipcast/play',
                method: 'post',
                data
        })
}



export function getTerminalById(params: any) {
        return request({
                url: '/acs-service/ipcast/id',
                method: 'get',
                params
        })
}
