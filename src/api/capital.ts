import request from '@/utils/request'
import qs from 'qs'
//----------库房------------------------------

//查询库房
export function getWareHouse(data: any) {
  return request({
    url: '/asset-service/basedata/warehouse/getListByCondition',
    method: 'post',
    data
  })
}

//添加库房
export function addWareHouse(data: any) {
  return request({
    url: '/maintenance-service/patrol-point',
    method: 'post',
    data,
  })
}

//编辑库房

export function editWareHouse(data: any) {
  return request({
    url: '/maintenance-service/patrol-point',
    method: 'put',
    data
  })
}

//删除库房
export function delWareHouse(params: any) {
  return request({
    url: '/maintenance-service/patrol-point',
    method: 'delete',
    params,
  })
}



//----------资产分类------------------------------

//查询资产分类
export function getAssetsTypeList(params: any) {
  return request({
    url: '/asset-service/assets-type',
    method: 'get',
    params
  })
}

//添加资产分类
export function addAssetsType(data: any) {
  return request({
    url: '/asset-service/assets-type',
    method: 'post',
    data,
  })
}

//编辑资产分类

export function editAssetsType(data: any) {
  return request({
    url: '/asset-service/assets-type',
    method: 'put',
    data
  })
}

//删除资产分类
export function delAssetsType(params: any) {
  return request({
    url: '/asset-service/assets-type',
    method: 'delete',
    params,
    paramsSerializer: (params) => {
      return qs.stringify(params, { arrayFormat: 'repeat' });
    }
  })
}




//--------资产品牌--------------------------------

//查询资产品牌
export function getBrandList(data: any) {
  return request({
    url: '/asset-service/brand',
    method: 'get',
    data
  })
}

//添加资产品牌
export function addBrand(data: any) {
  return request({
    url: '/asset-service/brand',
    method: 'post',
    data,
  })
}

//编辑资产品牌

export function editBrand(data: any) {
  return request({
    url: '/asset-service/brand',
    method: 'put',
    data
  })
}

//删除资产品牌
export function delBrand(params: any) {
  return request({
    url: '/asset-service/brand',
    method: 'delete',
    params,
    paramsSerializer: (params) => {
      return qs.stringify(params, { arrayFormat: 'repeat' });
    }
  })
}


//--------盘点任务--------------------------------

//盘点任务
export function getCheckTaskList(data: any) {
  return request({
    url: '/asset-service/assets/checkInstance/getListByCondition',
    method: 'post',
    data
  })
}


export function getTaskInfo(params: any) {
  return request({
    url: '/asset-service/assets/checkInstance/getInfo',
    method: 'post',
    params
  })
}


//--------资产采购--------------------------------

//查询采购
export function getPurchase(params: any) {
  return request({
    url: '/asset-service/purchase',
    method: 'get',
    params
  })
}

export function getPurchaseDetail(params: any) {
  return request({
    url: '/asset-service/purchase/detail',
    method: 'get',
    params
  })
}


//--------资产报废--------------------------------

//查询报废
export function getAssetsScrap(data: any) {
  return request({
    url: '/asset-service/assets/scrap/getScrapPageList',
    method: 'post',
    data
  })
}

export function getScrapDetail(params: any) {
  return request({
    url: '/asset-service/assets/scrap/detail',
    method: 'post',
    params
  })
}
export function postScrap(data: any) {
  return request({
    url: '/asset-service/assets/scrap/scrapDispose',
    method: 'post',
    data
  })
}


//--------资产报修--------------------------------

//查询报废
export function getAssetsRepair(data: any) {
  return request({
    url: '/asset-service/assets/repair/getRepairPageList',
    method: 'post',
    data
  })
}

export function getRepairDetail(params: any) {
  return request({
    url: '/asset-service/assets/repair/detail',
    method: 'post',
    params
  })
}


//--------资产盘点--------------------------------

//查询报废
// export function getCheckRecord(data: any) {
//   return request({
//     url: '/asset-service/assets/checkRecord/getListByCondition',
//     method: 'post',
//     data
//   })
// }

export function getCheckTaskDetail(params: any) {
  return request({
    url: '/asset-service/checkTask/detail',
    method: 'get',
    params
  })
}

export function getCheckInfoList(params: any) {
  return request({
    url: '/asset-service/checkTask/info',
    method: 'get',
    params
  })
}

//--------资产预警--------------------------------
export function warningUpdate(data: any) {
  return request({
    url: '/asset-service/assets/warning/update',
    method: 'post',
    data
  })
}

//--------资产领取--------------------------------

//获取领用记录
export function getReciveList(params: any) {
  return request({
    url: '/asset-service/receive',
    method: 'get',
    params
  })
}

export function addReceive(data: any) {
  return request({
    url: '/asset-service/receive',
    method: 'post',
    data
  })
}


export function saveScrap(data: any) {
  return request({
    url: '/asset-service/scrap',
    method: 'post',
    data
  })
}

export function auditAssetsScrap(data: any) {
  return request({
    url: '/asset-service/scrap/audit',
    method: 'post',
    data
  })
}
export function getScraps(params: any) {
  return request({
    url: '/asset-service/scrap',
    method: 'get',
    params
  })
}

export function updateReceive(data: any) {
  return request({
    url: '/asset-service/receive',
    method: 'put',
    data
  })
}

export function auditPurchase(data: any) {
  return request({
    url: '/asset-service/purchase/audit',
    method: 'post',
    data
  })
}


export function getReceiveDetail(params: any) {
  return request({
    url: '/asset-service/receive/detail',
    method: 'get',
    params
  })
}

export function getReciveDetail(params: any) {
  return request({
    url: '/asset-service/assets/receive/detail',
    method: 'post',
    params
  })
}


//--------资产调拨--------------------------------

//获取调拨记录
export function getAllocate(data: any) {
  return request({
    url: '/asset-service/assets/allocate/getListByCondition',
    method: 'post',
    data
  })
}

export function getAllocateDetail(params: any) {
  return request({
    url: '/asset-service/assets/allocate/detail',
    method: 'post',
    params
  })
}

//获取调拨单位
export function findWhse(data: any) {
  return request({
    url: '/asset-service/basedata/warehouse/findWhseOrSteList',
    method: 'post',
    data
  })
}

//--------资产入库--------------------------------
export function getInRecord(params: any) {
  return request({
    url: '/asset-service/info',
    method: 'get',
    params
  })
}

export function getInfoDetail(params: any) {
  return request({
    url: '/asset-service/info/detail',
    method: 'get',
    params
  })
}

export function infoAdd(data: any) {
  return request({
    url: '/asset-service/info',
    method: 'post',
    data
  })
}

export function infoEdit(data: any) {
  return request({
    url: '/asset-service/info',
    method: 'put',
    data
  })
}



//--------历史合同--------------------------------

export function getContractHistory(data: any) {
  return request({
    url: '/asset-service/contract/his/getGroupList',
    method: 'post',
    data
  })
}


export function getContractReal(data: any) {
  return request({
    url: '/asset-service/real/contract/getGroupList',
    method: 'post',
    data
  })
}


export function getCheckContract(data: any) {
  return request({
    url: '/asset-service/contract/check/getGroupList',
    method: 'post',
    data
  })
}



export function getContractDetail(params: any) {
  return request({
    url: '/asset-service/contract/check/detail',
    method: 'post',
    params
  })
}

export function updateContractDetail(data: any) {
  return request({
    url: '/asset-service/contract/check/updateContractCheck',
    method: 'post',
    data
  })
}

//新签合同
export function addContractDetail(data: any) {
  return request({
    url: '/asset-service/contract/check/commitContractCheck',
    method: 'post',
    data
  })
}

//-----getDepartmentByOperator 获取部门
export function getDepartmentByOperator(params: any) {
  return request({
    url: '/asset-service/sys/department/getTree',
    method: 'post',
    params
  })
}

//获取甲方公司
export function contractFirstParty(params: any) {
  return request({
    url: '/asset-service/basedata/contractFirstParty/getListByCondition',
    method: 'post',
    params
  })
}

export function getCustomer(params: any) {
  return request({
    url: '/asset-service/account/customer/getListByCondition',
    method: 'post',
    params
  })
}

export function getProvince(data: any) {
  return request({
    url: '/asset-service/basedata/provinceCity/getTree',
    method: 'post',
    data
  })
}

export function addHouse(data: any) {
  return request({
    url: '/asset-service/basedata/warehouse/add',
    method: 'post',
    data
  })
}

export function updateHouse(data: any) {
  return request({
    url: '/asset-service/basedata/warehouse/update',
    method: 'post',
    data
  })
}
export function delHouse(params: any) {
  return request({
    url: '/asset-service/basedata/warehouse/delete',
    method: 'post',
    params
  })
}




export function updateCheckRecord(data: any) {
  return request({
    url: '/asset-service/assets/checkRecord/update',
    method: 'post',
    data
  })
}

export function delCheckTask(params: any) {
  return request({
    url: '/asset-service/checkTask',
    method: 'delete',
    params,
    paramsSerializer: (params) => {
      return qs.stringify(params, { arrayFormat: 'repeat' });
    }
  })
}


export function addCheckTask(data: any) {
  return request({
    url: '/asset-service/checkTask',
    method: 'post',
    data
  })
}

export function addCheckRecord(data: any) {
  return request({
    url: '/asset-service/checkRecord',
    method: 'post',
    data
  })
}

export function getCheckTask(params: any) {
  return request({
    url: '/asset-service/checkTask',
    method: 'get',
    params
  })
}

export function getCheckRecord(params: any) {
  return request({
    url: '/asset-service/checkRecord',
    method: 'get',
    params
  })
}

export function updateCheck(data: any) {
  return request({
    url: '/asset-service/assets/check/update',
    method: 'post',
    data
  })
}

export function delCheck(params: any) {
  return request({
    url: '/asset-service/assets/check/delete',
    method: 'post',
    params
  })
}
export function addPurchase(data: any) {
  return request({
    url: '/asset-service/purchase',
    method: 'post',
    data
  })
}