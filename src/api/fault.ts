import request from '@/utils/request'
import qs from 'qs'


//查询保修
export function getFault(params: any) {
  return request({
    url: '/maintenance-service/fault',
    method: 'get',
    params
  })
}



export function addFault(data: any) {
  return request({
    url: '/maintenance-service/fault',
    method: 'post',
    data
  })
}
export function updateFault(data: any) {
  return request({
    url: '/maintenance-service/fault',
    method: 'put',
    data
  })
}