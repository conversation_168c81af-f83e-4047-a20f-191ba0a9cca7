import request from '@/utils/request'

// 查询手动记录
export function getEvent(params: any) {
  return request({
    url: '/hk-service/door/record',
    method: 'get',
    params
  })
}


// 查询门禁
export function getDoor(params: any) {
  return request({
    url: '/acs-service/dahua/door',
    method: 'get',
    params
  })
}

// 查询门禁记录
export function getDoorLog(params:any){
  return request({
      url: '/acs-service/dahua/record',
      method: 'get',
      params
  })
}

// 查询门禁记录
export function getDoorLogCount(params:any){
  return request({
      url: '/acs-service/dahua/count',
      method: 'get',
      params
  })
}

