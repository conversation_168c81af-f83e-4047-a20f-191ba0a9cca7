<template>
  <div class="no_data">
    <div class="icon"> <img :src="noData" alt /></div>
    {{ msg }}
  </div>
</template>
<script>
export default {
  data () {
    return {
      noData: require('../assets/images/noData.png'),
      msg: '暂无数据',
    }
  },
}
</script>
<style lang="scss" scoped>
.no_data {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-family: "Alibaba-PuHuiTi";
  font-weight: 400;
  color: #889cc3;
  height: 100%;

  .icon {
    width: 41px;
    height: 32px;
    margin-right: 10px;

    img {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
