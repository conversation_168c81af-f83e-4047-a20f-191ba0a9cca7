<template>
  <div class="wrapper">
    <el-scrollbar>
      <div v-for="item in list" :key="item.id" class="list">
        <host :host="item" />
      </div>
    </el-scrollbar>
  </div>
</template>
<script lang="ts">
import host from "@/components/host.vue";
import { reactive, toRefs } from "vue";
export default {
  components: {
    host,
  },
  setup() {
    const state = reactive({
      list: [
        {
          id: 1,
          name: "报警主机01",
        },
        // {
        //   id: 2,
        //   name: "报警主机02",
        // },
        // {
        //   id: 3,
        //   name: "报警主机03",
        // },
        // {
        //   id: 4,
        //   name: "报警主机04",
        // },
        // {
        //   id: 5,
        //   name: "报警主机05",
        // },
      ],
    });

    return {
      ...toRefs(state),
    };
  },
};
</script>
<style lang="scss" scoped>
.wrapper {
  height: 100%;
  color: #fff;
  .list {
    height: 28%;
  }
}
</style>