<template>
<div>
    <panel :title="host.name">
        <div class="top_tabs" v-for="(item, i) in list" :key="i" @click="change(item.name)">
            {{ item.name }}
            <div :class="{ active: isActive == item.name }"></div>
        </div>
    </panel>
    <div class="host_box host_box">
        <el-select v-model="subVal" placeholder="请选择"  size="small" v-if="isActive == '子系统'">
            <el-option v-for="item in options" :key="item.indexCode" :label="item.name" :value="item.indexCode">
            </el-option>
        </el-select>

        <el-select v-model="fqVal" placeholder="请选择"  size="small" v-if="isActive == '防区'">
            <el-option v-for="item in option1" :key="item.indexCode" :label="item.name" :value="item.indexCode">
            </el-option>
        </el-select>

        <div class="status">
            {{ isActive }}状态：<span class="cur">{{ hostActive }}</span>
        </div>

        <div class="btn_list">
            <span :class="{ hh: hostActive == '布防' }" @click="clickBtn('布防',1)">布防</span>
            <span :class="{ hh: hostActive == '撤防' }" @click="clickBtn('撤防',0)">撤防</span>
            <!-- <span :class="{ hh: hostActive == '旁路' }" @click="clickBtn('旁路')">旁路</span>
            <span :class="{ hh: hostActive == '强制布防' }" @click="clickBtn('强制布防')" v-if="isActive == '子系统'">强制布防</span>
            <span :class="{ hh: hostActive == '强制布防' }" @click="clickBtn('强制布防')" v-if="isActive == '防区'">旁路恢复</span> -->
        </div>
    </div>
</div>
</template>

<script>
import {
    reactive,
    toRefs
} from "vue";
import {
    getCurrentInstance,
    onMounted
} from "vue";

import {
    ElMessage
} from "element-plus";

export default {
    props: ["host"],
    setup() {
        const state = reactive({
            options: [{
                    indexCode: "选项1",
                    name: "子系统1",
                },
                {
                    indexCode: "选项2",
                    name: "子系统2",
                },
            ],
            option1: [{
                    indexCode: "选项1",
                    name: "防区1",
                },
                {
                    indexCode: "选项2",
                    name: "防区2",
                },
            ],
            fqVal: "",
            subVal: "",
            isActive: "子系统",
            list: [{
                    name: "子系统",
                },
                {
                    name: "防区",
                },
            ],
            hostActive: "布防",
            device: null
        });
        const change = (data) => {
            state.isActive = data;
        };
        const clickBtn = (val, status) => {
            if (val == '子系统' && subVal) {
                let cmd = {
                    "subSystemList": [{
                        "subSystemIndexCode": state.subVal,
                        "status": status
                    }]
                }
                issue(cmd);
            }
        };
        const {
            proxy
        } = getCurrentInstance();

        onMounted(() => {
            getAlarmDevice();
        });
        const getSub = () => {
            proxy.$api.getAlarmSub().then((res) => {
                state.options = JSON.parse(res.data)
            });
        };
        const issue = (data) => {
            proxy.$api.cmd(data).then((res) => {
                ElMessage({
                    type: "success",
                    message: "操作成功",
                });
            });
        };

        const getDefence = () => {
            proxy.$api.getAlarmSub({
                deviceIndexCode: state.device
            }).then((res) => {
                state.option1 = JSON.parse(res.data);
            });
        };

        const getAlarmDevice = () => {
            proxy.$api.getAlarmSub().then((res) => {
                state.device = JSON.parse(res.data)[0].indexCode;
                getDefence();
                getSub();
            });
        };

        return {
            ...toRefs(state),
            change,
            clickBtn,
        };
    },
};
</script>

<style lang="scss" scoped>
.host_box {
    text-align: center;
    padding: 22px 0;
    font-size: 14px;
    font-family: "Alibaba-PuHuiTi";
    font-weight: 400;
    color: #889cc3;

    .btn_list {
        flex: 1;
        display: flex;
        width: 100%;

        .hh {
            background: rgba(27, 192, 237, 0.3);
            border: 1px solid #1bc0ed;
            color: #fff;
        }

        span {
            flex: 1;
            height: 30px;
            line-height: 30px;
            background: rgba(97, 109, 148, 0.2);
            border: 1px solid #616d94;
            font-size: 14px;
            margin: 0 2px;
            font-family: "Alibaba-PuHuiTi";
            font-weight: 400;
            color: rgba(255, 255, 255, 0.5);
            text-align: center;
            cursor: pointer;
        }
    }

    .status {
        padding: 11px 0 8px 0;

        .cur {
            font-size: 14px;
            font-family: "Alibaba-PuHuiTi";
            font-weight: 400;
            color: #1bc0ed;
        }
    }
}
</style>
