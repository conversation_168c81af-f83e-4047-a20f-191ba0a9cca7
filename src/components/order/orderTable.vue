<template>
    <!-- 维保  巡检提交工单  保修工单使用 -->
    <div class="h100 order">
        <div class="btn-group search_box">
            <div type="primary" icon="Plus" size="mini" class="searchBtn" @click="addOrder">发起工单</div>
        </div>
        <el-table height="calc(100% - 60px)" :data="list" fit>
            <template #empty>
                <no-data />
            </template>
            <el-table-column prop="createTime" label="创建时间" align="center">
            </el-table-column>
            <el-table-column prop="name" label="名称" align="center">
            </el-table-column>
            <el-table-column prop="level" label="工单等级" align="center">
                <template #default="scope">
                    <span v-if="scope.row.level == 1">一般</span>
                    <span v-if="scope.row.level == 2">紧急</span>
                    <span v-if="scope.row.level == 3">严重</span>
                </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" align="center">
                <template #default="scope">
                    <span style="color: green">{{ getStatus(scope.row.status) }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="staffType" label="所属专业" align="center">
                <template #default="scope">
                    <span>{{ getStaffTypeName(scope.row.staffType) }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="status" label="工单类型" align="center">
                <template #default="scope">
                    <span style="color: green" v-if="scope.row.type == 1">报修工单</span>
                    <span style="color: #E3731B" v-if="scope.row.type == 2">巡检报修</span>
                    <span style="color: #13D4D9" v-if="scope.row.type == 3">维保工单</span>
                </template>
            </el-table-column>
            <el-table-column label="操作" align="center">
                <template #default="scope">
                    <el-button type="text" class="editBtn" @click="details(scope.row)">详情</el-button>
                </template>
            </el-table-column>
        </el-table>
        <div class="center page">
            <el-pagination prev-icon="CaretLeft" next-icon="CaretRight" :page-size="size" :current-page="page"
                layout="total,prev, pager, next" @current-change="handleCurrentChange" :total="total">
            </el-pagination>
        </div>
        <add-order-dialog v-if="orderDialog.visible" :dialogData="orderDialog" @getOrderPage="getOrderPage" />

        <el-dialog align-center append-to-body draggable custom-class="addDiagram " v-model="showDetail" width="940px" title="工单详情">
            <detail v-model:showDetail="showDetail" :order="order"></detail>
        </el-dialog>
    </div>
</template>

<script>
import addOrderDialog from '@/views/Repair/components/addOrderDialog.vue'
import detail from '@/views/Order/orderDetail.vue'
import {
    getCookie,
    setCookie
} from '@/utils/cookie'
import {
    reactive,
    toRefs
} from 'vue'
import {
    computed,
    getCurrentInstance,
    onMounted,
    watch
} from 'vue'
import {
    useRouter
} from 'vue-router'
import {
    useStore
} from 'vuex'
export default {
    props: ['status', 'type'],
    components: {
        addOrderDialog,
        detail
    },
    setup(props) {
        const {
            proxy
        } = getCurrentInstance()
        const store = useStore()
        const router = useRouter()

        const state = reactive({
            page: 1,
            size: 10,
            total: 0,
            list: [],
            types: [],
            state: [{
                value: -1,
                name: '待派单',
            },
            {
                value: 1,
                name: '待接单',
            },
            {
                value: 2,
                name: '挂起',
            },
            {
                value: 3,
                name: '正常关闭',
            },
            {
                value: 4,
                name: '异常关闭',
            },
            {
                value: 5,
                name: '退回',
            },
            {
                value: 6,
                name: '完成',
            },
            {
                value: 8,
                name: '恢复工单',
            },
            {
                value: 9,
                name: '处理中',
            },
            ],
            orderDialog: {
                visible: false,
                title: '详情',
                order: {
                    paths: []
                },
                rule: {
                    name: [{
                        required: true,
                        message: '工单主题不能空',
                        trigger: 'blur'
                    }],
                    description: [{
                        required: true,
                        message: '描述不能空',
                        trigger: 'blur'
                    }]
                }
            },
            showDetail: false,
            order: null
        })

        const projectId = computed(() => {
            return store.state.user.projectId || getCookie('gh_projectId')
        })
        watch(projectId, (val) => {
            if (val) {
                getDicUtilList()
                getOrderPage()
            }
        })
        watch(props, (status, preStatus) => {
            if (status) getOrderPage()
        })
        onMounted(() => {
            getDicUtilList()
            getOrderPage()
        })

        const getDicUtilList = () => {
            proxy.$api.getDicUtil({
                dicCode: 'profession_type',
                projectId: getCookie("gh_projectId"),
            }).then(res => {
                state.types = res.data
            })
        }
        const handleCurrentChange = (page) => {
            state.page = page
            getOrderPage()
        }
        const getOrderPage = () => {
            proxy.$api.getOrder({
                page: state.page,
                size: state.size,
                projectId: getCookie("gh_projectId"),
                status: props.status,
            }).then((res) => {
                state.list = res.data
                state.total = res.total
            })
        }
        const getStaffTypeName = (type) => {
            let name = ''
            state.types.forEach((t) => {
                if (t.tagValue == type) {
                    name = t.tagName
                }
            })
            return name
        }
        const getStatus = (val) => {
            let name = ''
            state.state.forEach((t) => {
                if (t.value == val) {
                    name = t.name
                }
            })
            return name
        }
        const details = (row) => {
            state.showDetail = true;
            state.order = row;
        }

        const addOrder = () => {
            state.orderDialog.visible = true
            state.orderDialog.title = '新增工单'
        }
        return {
            ...toRefs(state),
            projectId,
            getOrderPage,
            handleCurrentChange,
            details,
            getStatus,
            getStaffTypeName,
            getDicUtilList,
            addOrder
        }
    },

}
</script>

<style scoped>
.order {
    padding: 0 15px;
}
</style>
