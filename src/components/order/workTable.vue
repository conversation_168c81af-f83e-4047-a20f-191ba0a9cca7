<template>
  <div class="work-table">
    <div class="btn-group">
      <el-button type="primary" icon="Plus" size="mini" class="addBtn" @click="addOrder">新增</el-button>
    </div>
    <div class="table">
      <el-table :data="list" :height="tableHeight" fit>
        <template #empty>
          <no-data />
        </template>
        <el-table-column prop="startTime" label="创建时间" align="center">
        </el-table-column>
        <el-table-column prop="name" label="名称" align="center">
        </el-table-column>
        <el-table-column prop="level" label="工单等级" align="center">
          <template #default="scope">
            <span v-if="scope.row.level == 1">一般</span>
            <span v-if="scope.row.level == 2">紧急</span>
            <span v-if="scope.row.level == 3">严重</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" align="center">
          <template #default="scope">
            <span style="color: #13d4d9">{{ getStatus(scope.row.status) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="staffType" label="所属专业" align="center">
          <template #default="scope">
            <span>{{ getStaffTypeName(scope.row.staffType) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center">
          <template #default="scope">
            <el-button type="text" class="editBtn" @click="details(scope.row)">详情11</el-button>
            <el-button type="text" class="editBtn" @click="send(scope.row)">派单</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="center page">
        <el-pagination prev-icon="CaretLeft" next-icon="CaretRight" :page-size="size" :current-page="page"
          layout="total,prev, pager, next" @current-change="handleCurrentChange" :total="total">
        </el-pagination>
      </div>
      <send-dialog v-if="dialogData.visible" :dialog-data="dialogData" />
      <add-order-dialog v-if="orderDialog.visible" :dialogData="orderDialog" @getPatrolTaskList="getPatrolTaskList" />
    </div>
  </div>
</template>

<script>
import {
  getCookie
} from '@/utils/cookie'
import {
  computed,
  getCurrentInstance,
  onMounted,
  reactive,
  toRefs,
  watch
} from 'vue'
import {
  useRouter
} from 'vue-router'
import sendDialog from '@/views/patrol/components/sendDialog.vue'
import addOrderDialog from '@/views/Repair/components/addOrderDialog.vue'
import { useStore } from 'vuex'

export default {
  name: 'order-table',
  props: ['status'],
  components: {
    sendDialog,
    addOrderDialog
  },
  setup(props) {
    const router = useRouter()
    const store = useStore()
    const { proxy } = getCurrentInstance()
    const state = reactive({
      tableHeight: window.innerHeight * 0.60,
      page: 1,
      size: 10,
      total: 0,
      list: [],
      types: [],
      state: [{
        value: -1,
        name: '待派单',
      },
      {
        value: 1,
        name: '待接单',
      },
      {
        value: 2,
        name: '挂起',
      },
      {
        value: 3,
        name: '正常关闭',
      },
      {
        value: 4,
        name: '异常关闭',
      },
      {
        value: 5,
        name: '退回',
      },
      {
        value: 6,
        name: '完成',
      },
      {
        value: 8,
        name: '恢复工单',
      },
      {
        value: 9,
        name: '处理中',
      },
      ],
      dialogData: {
        visible: false,
        title: '派单',
        send: {
          staffId: ''
        },
        rule: {
          userId: [{
            required: true,
            message: '接收人不能空',
            trigger: 'change'
          }]
        }
      },
      orderDialog: {
        visible: false,
        title: '详情',
        order: {
          paths: []
        },
        rule: {
          name: [{
            required: true,
            message: '名称不能空',
            trigger: 'blur'
          }],
          description: [{
            required: true,
            message: '描述不能空',
            trigger: 'blur'
          }]
        }
      }
    })

    onMounted(() => {
      getDicUtilList()
      getPatrolTaskList()
    })
    const projectId = computed(() => {
      return store.state.user.projectId || getCookie('gh_projectId')
    })
    watch(projectId, (val) => {
      if (val) {
        getDicUtilList()
        getPatrolTaskList()
      }
    })
    watch(props, (status, preStatus) => {
      if (status) getPatrolTaskList()
    })
    const getDicUtilList = () => {
      proxy.$api.getDicUtil({
        dicCode: 'profession_type',
        projectId: getCookie("gh_projectId"),
      }).then((res) => {
        state.types = res.data
      })
    }
    const handleCurrentChange = (page) => {
      state.page = page
      getPatrolTaskList()
    }
    const getPatrolTaskList = () => {
      proxy.$api.getPatrolTask({
        page: state.page,
        size: state.size,
        projectId: getCookie("gh_projectId"),
        status: props.status,
      }).then((res) => {
        state.list = res.data
        state.total = res.total
      })
    }
    const getStaffTypeName = (type) => {
      let name = ''
      state.types.forEach((t) => {
        if (t.tagValue == type) {
          name = t.tagName
        }
      })
      return name
    }
    const getStatus = (val) => {
      let name = ''
      state.state.forEach((t) => {
        if (t.value == val) {
          name = t.name
        }
      })
      return name
    }
    const details = (row) => {
      sessionStorage.setItem('order', JSON.stringify(row));
      router.push({
        path: '/order/detail'
      })
    }
    // 派单
    const send = (row) => {
      state.dialogData.visible = true
      state.dialogData.send = Object.assign({}, row)
    }
    const addOrder = () => {
      state.orderDialog.visible = true
      state.orderDialog.title = '新增工单'
    }
    return {
      ...toRefs(state),
      projectId,
      getStatus,
      details,
      getStaffTypeName,
      getPatrolTaskList,
      handleCurrentChange,
      getDicUtilList,
      send,
      addOrder
    }
  },
}
</script>

<style></style>
