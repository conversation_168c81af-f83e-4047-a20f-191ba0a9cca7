<template>
    <div>
        <div class="menu">
            <swiper :modules="modules" :navigation="{ nextEl: '.swiper-button-next', prevEl: '.swiper-button-prev' }" :grab-cursor="true" :slides-per-view="9" :space-between="10" :centerInsufficientSlides="true">
                <swiper-slide @click="clickMenu(item)" v-for="item in menuList" :key="item.id">
                    <div class="slide">
                        <img :src="activeName == item.name ? require('' + '../assets/images/menu/' + item.imgName + '_选中.png') : require('' + '../assets/images/menu/' + item.imgName + '.png')" />
                        <div class="slide-text">{{ item.name }}</div>
                    </div>
                </swiper-slide>
            </swiper>
            <div class="swiper-button-prev"></div>
            <div class="swiper-button-next"></div>
    
        </div>
    
        <Transition name="fade" mode="out-in">
            <Floor :mode="mode" v-show="activeMenus.showFloor"></Floor>
        </Transition>
        <Transition name="fade" mode="out-in">
            <div class="tabs" v-if="list.length > 0">
                <div v-for="(item, index) in list" :key="index" :class="[secondName == item.name ? 'second_active' : '']" class="list" @click="clickMenu(item)">
                    {{ item.name }}
                </div>
            </div>
        </Transition>
    </div>
    </template>
    
    <script>
    import {
        useRouter
    } from 'vue-router'
    import {
        useStore
    } from 'vuex'
    import {
        getCookie,
        removeToken,
        setCookie
    } from '@/utils/cookie'
    
    import {
        defineComponent,
        reactive,
        toRefs,
        onMounted,
        getCurrentInstance,
        computed
    } from 'vue'
    import {
        Swiper,
        SwiperSlide
    } from "swiper/vue/swiper-vue";
    
    import "swiper/swiper-bundle.css";
    import {
        Navigation
    } from 'swiper';
    
    export default defineComponent({
        components: {
            Swiper,
            SwiperSlide,
        },
        props:['mode'],
        setup() {
            const {
                proxy
            } = getCurrentInstance()
            const router = useRouter()
            const store = useStore()
            const state = reactive({
                menuList: [],
                list: [], //二级菜单
                activeName: '首页',
                secondName: ''
    
            })
            const activeMenus = computed(() => {
                let menu = getCookie("funMenus");
                return store.state.menu.funMenus ?
                    store.state.menu.funMenus : menu ? JSON.parse(menu) : {
                        name: '首页',
                        component: 'home',
                        showFloor: false,
                    };
            });
    
            onMounted(() => {
                let secondMenu = sessionStorage.getItem("second")
                if (secondMenu) {
                    state.list = JSON.parse(secondMenu);
                }
                let firstMenuName = sessionStorage.getItem("firstMenuName")
                if (firstMenuName) {
                    state.activeName = firstMenuName;
                }
    
                let secondMenuName = sessionStorage.getItem("secondMenuName")
                if (secondMenuName) {
                    state.secondName = secondMenuName;
                }
    
                // getProjectMenu()
    
            });
    
            const getProjectMenu = () => {
                if (getCookie("gh_projectId") != 0) {
                    proxy.$api.getMenuList({
                        configFlag: false,
                        projectId: getCookie("gh_projectId"),
                        menuType: [1, 2],
                        isPcMenu: true,
                        enable: true,
                    }).then((res) => {
                        state.menuList = res.data;
                        if (!getCookie("funMenus")) {
                            clickMenu(res.data[0]);
                        }
                        let hide = [];
                        let show = [];
                        res.data.forEach(d => {
                            if (d.hideCondition) {
                                hide.push(JSON.parse(d.hideCondition));
                            } else if (d.showCondition) {
                                show.push(JSON.parse(d.showCondition));
                            }
                        })
                        if (hide.length > 0) {
                            sessionStorage.setItem('hideMap', JSON.stringify(hide));
                        }
                        if (show.length > 0) {
                            sessionStorage.setItem('showMap', JSON.stringify(show));
                        }
    
                    })
                }
            }
            getProjectMenu()
            const clickMenu = (item) => {
                if (item) {
                    //配置页面 弹窗
                    if (item.component && item.component.startsWith("dialog_")) {
                        store.commit('menu/SET_DIALOG', {
                            name: item.name,
                            component: item.component.split('_')[1]
                        })
                        return;
                    }
    
                    //重置二级菜单
                    if (item.menuType == 1) {
                        state.list = [];
                        sessionStorage.removeItem("second")
                        //一级菜单名称
                        sessionStorage.setItem("firstMenuName", item.name)
                    }
                    //点击的是一级菜单
                    if (item.menuType == 1 && item.children && item.children.length > 0) {
                        state.list = item.children;
                        //记录二级菜单,刷新使用
                        sessionStorage.setItem("second", JSON.stringify(item.children))
                    }
    
                    if (item.menuType == 1 && state.activeName !== item.name) {
                        state.activeName = item.name
                    }
                    if (item.menuType == 2) {
                        state.secondName = item.name;
                        sessionStorage.setItem("secondMenuName", item.name)
                    }
    
                    //一级菜单没有绑定组件，默认显示子集的第一个
                    if (item.menuType == 1 && !item.component && item.children.length > 0) {
                        item = item.children[0];
    
                        state.secondName = item.name;
                        sessionStorage.setItem("secondMenuName", item.name)
    
                    }
    
                    if (item.component) {
                        if ((item.menuType == 1 || item.menuType == 2) && item.component.includes("_") && item.component.split("_").length == 2) { //主要是各个子系统第一个
                            item.secondComponent = item.component.split("_")[1];
                            item.component = item.component.split("_")[0];
                        } else if ((item.menuType == 1 || item.menuType == 2) && item.component.includes("_") && item.component.split("_").length == 3) //子系统弹窗
                        {
                            let components = item.component.split("_");
                            item.component = components[0]
                            item.secondComponent = components[1]
                            item.popName = components[2]
                        }
                        removeToken("funMenus")
                        setCookie('funMenus', JSON.stringify({
                            id: item.id,
                            name: item.name,
                            component: item.component,
                            secondComponent: item.secondComponent,
                            code: item.code, //菜单编码
                            // hideCondition: item.hideCondition,
                            // showCondition: item.showCondition,
                            diagramId: item.diagramId,
                            popName: item.popName,
                            model: item.model,
                            vr: item.vr,
                            ar: item.ar,
                            showFloor: item.showFloor,
                        }))
                        localStorage.removeItem("hideCondition")
                        if (item.hideCondition) {
                            localStorage.setItem("hideCondition", item.hideCondition)
                        }
                        localStorage.removeItem("showCondition")
                        if (item.showCondition) {
                            localStorage.setItem("showCondition", item.showCondition)
                        }
                        store.commit('menu/SET_FUN_MENU', item)
                    }
    
                    router.push({
                        path: '/overview'
                    })
    
                }
    
            }
    
            return {
                ...toRefs(state),
                clickMenu,
                getProjectMenu,
                activeMenus,
                // projectId,
                modules: [Navigation],
            }
        },
    })
    </script>
    
    <style lang="scss" scoped>
    .menu {
        position: fixed;
        width: 1000px;
        left: 50%;
        bottom: 10px;
        transform: translateX(-50%);
        z-index: 101;
        // background: linear-gradient(270deg,#0008124f,rgb(0 14 30 / 21%) 72.19%,rgb(0 15 32 / 24%) 83.65%,rgba(0,12,26,0));
        .slide {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            // width: 97px;
    
            &-text {
                // margin-top: 5px;
                font-size: 14px;
                font-family: "Alibaba-PuHuiTi";
                font-weight: normal;
                color: #B9DDFD;
                // display: none;
                height: 28px;
                line-height: 28px;
            }
    
            img {
                width: 60px;
                height: 79px;
            }
    
        }
    
    }
    
    .tabs {
        position: absolute;
        // height: 22px;
        width: 1000px;
        bottom: 140px;
        left: 0;
        right: 0;
        margin: 0 auto;
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 101;
    
        .active {
            background-color: rgba(61, 233, 250, 0.5) !important;
        }
    
        .list {
            position: relative;
            width: 66px;
            margin: 0 10px;
            text-align: center;
            color: #fff;
            font-size: 14px;
            // border-left: 1px solid #44728d;
            // border-right: 1px solid #44728d;
            background-color: rgba(68, 114, 141, 0.6);
            cursor: pointer;
            white-space: nowrap;
            background: #0B2239;
            // background: url('../assets/images/menu/second.png');
            // background-repeat: no-repeat;
            // background-size: 100% 100%;
            padding: 5px 0;
    
            box-shadow: 0 0 3px 3px #1D71D8 inset;
    
        }
    }
    
    .second_active {
        background: #432B09 !important;
        box-shadow: 0 0 3px 3px #F9BA5D inset !important;
    }
    
    // img:hover+.slide-text {
    //     display: block;
    // }
    </style>
    