<template>
<div class="diagram_pop" v-if="show">
    <div class="diagram" >
        <div class="header">
            <img src="./img/head.png" />
            <div class="title">{{title}}</div>
        </div>
        <div class="content">
            <slot></slot>
        </div>
    </div>
</div>
</template>

<script>
import {
    defineComponent,
    reactive,
    toRefs
} from "vue";
export default defineComponent({
    props: ['show', 'title'],

    setup(props, {
        emit
    }) {
        const state = reactive({

        })
        const close = () => {
            emit('update:show', false)
        }
        return {
            ...toRefs(state),
            close,

        }
    },
})
</script>

<style lang="scss" scoped>
div {
    box-sizing: border-box;
}

.diagram_pop{
    height: 100%;
    width: 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 9;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
}

.diagram {
    height: calc(100% - 240px);
    width: 1100px;
    margin-top: 70px;
    padding: 5px;
    // z-index: 100;
    // position: absolute;
    // left: 50%;
    // transform: translateX(-50%);

    .header {
        height: 41px;
        width: 100%;
        padding-bottom: 10px;
        display: flex;
        align-items: center;
        justify-content: flex-start;

        .title {
            height: 100%;
            display: flex;
            align-items: center;
            font-size: 18px;
            font-family: "BEBAS";
            font-weight: normal;
            font-style: italic;
            color: #B4C4EC;
            margin-left: 10px;
        }

        .close {
            height: 28px;
            width: 28px;
            background: url("./img/close.png") no-repeat;
        }
    }

    .content {
        // padding: 10px;
        height: calc(100% - 42px);
        overflow-y: auto;
        overflow-x: hidden;
    }
}
</style>
