import calc from '@/utils/eval';
<template>
<Transition name="fade" mode="out-in" appear>
    <div class="diagram" v-if="show">
        <div class="header">
            <div class="title">{{ name }}</div>
            <div class="close cursor" @click="close">
            </div>
        </div>
        <div class="content">
            <slot></slot>
        </div>
    </div>
</Transition>
</template>

<script>
import {
    defineComponent,
    reactive,
    toRefs
} from "vue";
export default defineComponent({
    props: ['show','name'],

    setup(props,{emit}) {
        const state = reactive({

        })
        const close=()=>{
            emit('update:show',false)
        }
        return {
            ...toRefs(state),
            close,

        }
    },
})
</script>

<style lang="scss" scoped>
div {
    box-sizing: border-box;
}

.diagram {
    height: calc(100% - 100px - 139px);
    width: 1100px;
    // background: rgba(30, 39, 61, 0.8);
    background: rgba(6,26,42,0.9);
    margin-top: 100px;
    padding: 5px;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);

    .header {
        background: url("./img/head.png") no-repeat;
        height: 41px;
        width: 100%;
        background-size: 100% 100%;
        padding-bottom: 10px;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .title {
            height: 100%;
            display: flex;
            align-items: center;
            font-size: 18px;
            font-family: "BEBAS";
            font-weight: normal;
            font-style: italic;
            color: #7A9BBD;
            margin-left: 40px;
        }

        .close{
            height: 28px;
            width: 28px;
            background: url("./img/close.png") no-repeat;
        }
    }

    .content{
        padding: 10px;
        height: calc(100% - 42px);
        overflow-y: auto;
        overflow-x: hidden;
    }
}
</style>
