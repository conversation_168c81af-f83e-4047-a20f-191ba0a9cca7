<template>
  <div class="san_chart" ref="sanChart"></div>
</template>

<script>
import { onMounted, ref, inject, nextTick, toRefs, watch } from 'vue';
export default {
  props: ["nodes", "links","unitName"],

  setup (props) {
    const sanChart = ref(null)
    let echarts = inject('ec') //引入

    watch(props, (newVal, oldVal) => {
      if (newVal)
        initChart()
    })
    onMounted(() => {
      nextTick(() => {
        initChart()
      })
    })
    const initChart = () => {
      let option = {
        tooltip: {
          trigger: "item",
          backgroundColor: 'rgba(47, 54, 60, 0.8)',
          borderColor: '#2F363C',
          textStyle: {
            color: '#fff'
          },
          triggerOn: "mousemove",
          formatter: (params) => params.data.value+" "+props.unitName
        },
        series: {
          type: "sankey",
          layout: "none",
          top: 50,
          left: "3%",
          right: "12%",
          nodeGap: 14,
          layoutIterations: 0, // 自动优化列表，尽量减少线的交叉，为0就是按照数据排列
          data: props.nodes, // 节点
          links: props.links, // 节点之间的连线
          draggable: false,
          focusNodeAdjacency: "allEdges", // 鼠标划上时高亮的节点和连线，allEdges表示鼠标划到节点上点亮节点上的连线及连线对应的节点

          levels: [{
            depth: 0,
            itemStyle: {
              color: "#F27E7E"
            },
            lineStyle: {
              color: "source",
              opacity: 0.2
            }
          },
          {
            depth: 1,
            lineStyle: {
              color: "source",
              opacity: 0.2
            }
          },
          {
            depth: 2,
            lineStyle: {
              color: "source",
              opacity: 0.2
            }
          },
          {
            depth: 3,
            label: {
              fontSize: 12
            }
          }
          ],
          label: {
            fontSize: 14,
            color: "#fff"
          },
          itemStyle: {
            normal: {
              borderWidth: 0
            }
          }
        }
      };
      var myChart = echarts.init(sanChart.value)
      myChart.setOption(option); //展示
      myChart.resize(); //刷新画布
      window.addEventListener('resize', () => {
        myChart.resize(); //刷新画布
      })
    }
    return {
      sanChart,
      initChart
    }
  }
}
</script>

<style>
.san_chart {
  width: 100%;
  height: 100%;
}
</style>
