<template>
    <div class="audit_container">
        <div class="audit_container_header">
            <card title="用能指标"/>
        </div>
        <div class="audit_container_body">
            <div class="body_content">
                <div class="content_text" v-for="(item,i) in resultData" :key="i">
                    <div class="Light"></div>
                    <div class="explana">{{item.text}}</div>
                </div>
                <div class="content_result">
                    <div><i class="iconfont iconxiaolian"></i></div>
                    <div class="result_btn">占比合理，继续保持！</div>
                </div>
            </div>
            <div class="right_content">
                <div class="item">
                    <div class="item_header">您的能耗：128</div>
                    <div class="item_chart">
                        <el-progress :percentage="128"></el-progress>
                    </div>
                </div>
                <div class="item">
                    <div class="item_label">
                        <div class="Lheight"></div>
                        <span>公共建筑用能指南（单位：度/平方米.年）</span>
                    </div>
                </div>
                <div class="item">
                    <div class="item_header">平均值：128</div>
                    <div class="item_chart">
                        <el-progress :percentage="100"></el-progress>
                    </div>
                </div>
                <div class="item">
                    <div class="item_header">先进指：128</div>
                    <div class="item_chart">
                        <el-progress :percentage="128"></el-progress>
                    </div>
                </div>
                <div class="item">
                    <div class="item_header">合理值：128</div>
                    <div class="item_chart">
                        <el-progress :percentage="60"></el-progress>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import card from "@/components/energy/card" 
import pieChart from "@/components/energy/pieChart"
export default {
    components:{
        card,
        pieChart
    },
    data(){
        return{
            resultData:[
                {
                    text:'本审计周期您的单位面积耗电为128度/平方米.年，低于先进值。高于平均值'
                }
            ]
        }
    }
}
</script>

<style lang="scss" scoped>
.audit_container{
    width: 100%;
    height: 100%;
    &_header{
        width: 100%;
        height: 40px;
    }
    &_body{
        width: 100%;
        height: calc(100% - 40px);
        display: flex;
        .body_content{
            width: 50%;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            align-items: center;
            .content_text{
                width: 50%;
                padding: 40px 0;
                display: flex;
                justify-content: flex-start;
                align-items: flex-start;
                .Light{
                    width: 10px;
                    height: 10px;
                    border-radius: 5px;
                    background: #3E83D4;
                    margin-top: 5px;
                    margin-right: 10px;
                }
                .explana{
                    word-wrap:break-word;
                    margin-left: 10px;
                }
            }
            .chart_title{
                
                justify-content: flex-start;
            }
            .content_result{
                width: 50%;
                height: 50%;
                display: flex;
                flex-direction: column;
                align-items: center;
                .result_btn{
                    margin-top: 20px;
                    border: solid 1px #32EC7C;
                    padding: 4px 30px;
                    color: #32EC7C;
                }
                i{
                    font-size: 120px;
                    color: #32EC7C;
                }
            }
            
        }
        .right_content{
            width: 50%;
            height: 100%;
            padding: 40px;
            .item{
                margin-bottom: 24px;
                width: 70%;
                .item_chart{
                    margin-top: 24px;
                }
            }
        }
    }
}
    
</style>
<style lang="scss">
.audit_container .item{
    .el-progress{
        .el-progress__text{
            display: none !important;
        }
        .el-progress-bar__outer{
            height: 8px !important;
        }
        .el-progress-bar__inner{
            background: #32EC7C;
        }
    }
    
} 
</style>