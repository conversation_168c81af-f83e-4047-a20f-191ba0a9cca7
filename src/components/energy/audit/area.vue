<template>
    <div class="audit_container">
        <div class="audit_container_header">
            <card title="区域能耗"/>
        </div>
        <div class="audit_container_body">
            <div class="body_content">
                <div class="explan_text">
                    <div class="content_text" v-for="(item,i) in resultData" :key="i">
                        <div class="Light"></div>
                        <div class="explana" v-html="item.text"></div>
                    </div>
                </div>
                <div class="content_result">
                    <div class="abnormal"><i class="iconfont iconiconfront-"></i></div>
                    <div class="result_btn improve">有待改进，一般！</div>
                    <!-- <div>
                        <el-button>能源诊断</el-button>
                    </div> -->
                </div>
            </div>
            <div class="right_content">
                <div class="item">
                    <div class="item_label">
                        <div class="Lheight"></div>
                        <span>区域能耗排行</span>
                    </div>
                </div>
                <div class="item">
                    <div class="item_header">01.园区食堂</div>
                    <div class="item_chart">
                        <el-progress :percentage="35"></el-progress>
                    </div>
                </div>
                <div class="item">
                    <div class="item_header">02.节能办公室</div>
                    <div class="item_chart">
                        <el-progress :percentage="25"></el-progress>
                    </div>
                </div>
                <div class="item">
                    <div class="item_header">03.食堂</div>
                    <div class="item_chart">
                        <el-progress :percentage="15"></el-progress>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import card from "@/components/energy/card" 
import pieChart from "@/components/energy/pieChart"
export default {
    components:{
        card,
        pieChart
    },
    data(){
        return{
            resultData:[
                {
                    text:"<span>用量最多的区域为：园区食堂，占总能耗的35%，本区域能耗同比增长10%，环比增加5%，比全年平均值增加10%</span>"
                },
                {
                    text:"<span>建议进一步</span><a href='javascript:void(0)'>分析诊断</a>"
                }
            ]
        }
    }
}
</script>

<style lang="scss" scoped>
.audit_container{
    width: 100%;
    height: 100%;
    &_header{
        width: 100%;
        height: 40px;
    }
    &_body{
        width: 100%;
        height: calc(100% - 40px);
        display: flex;
        .body_content{
            width: 50%;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            align-items: center;
            padding: 40px 0;
            .explan_text{
                width: 50%;
                height: 50%;
            }
            .content_text{
                width: 100%;
                display: flex;
                justify-content: flex-start;
                align-items: flex-start;
                margin-bottom: 20px;
                .Light{
                    width: 10px;
                    height: 10px;
                    border-radius: 5px;
                    background: #3E83D4;
                    margin-top: 5px;
                    flex-shrink:0
                }
                .explana{
                    word-wrap:break-word;
                    margin-left: 10px;
                }
            }
            .chart_title{
                
                justify-content: flex-start;
            }
            .content_result{
                width: 50%;
                height: 50%;
                display: flex;
                flex-direction: column;
                align-items: center;
                .result_btn{
                    margin-top: 20px;
                    border: solid 1px #32EC7C;
                    padding: 4px 30px;
                    color: #32EC7C;
                    &.improve{
                        border: solid 1px gold;
                        color: gold;
                    }
                }
                i{
                    font-size: 140px;
                    font-weight: 600;
                    color: #32EC7C;
                }
                .abnormal i{
                    color: gold;;
                }
                .el-button{
                    padding: 16px 40px;
                    background: #3E83D4;
                    border-radius: 30px;
                    border: none;
                    color: #fff;
                }
            }
            
        }
        .right_content{
            width: 50%;
            height: 100%;
            padding: 40px;
            .item{
                margin-bottom: 24px;
                width: 70%;
                .item_chart{
                    margin-top: 24px;
                }
            }
        }
    }
}
    
</style>
<style lang="scss">
.audit_container .item{
    .el-progress{
        .el-progress__text{
            display: none !important;
        }
        .el-progress-bar__outer{
            height: 8px !important;
        }
        .el-progress-bar__inner{
            background: #32EC7C;
        }
    }
    
}
    
</style>