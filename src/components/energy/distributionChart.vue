<template>
  <div ref="distribChart" class="chart" />
</template>
<script>
import { onMounted, ref, inject, nextTick } from 'vue'
export default {
  setup () {
    const distribChart = ref(null)
    let echarts = inject('ec')


    onMounted(() => {
      nextTick(() => {
        initChart()
      })
    })
    const initChart = () => {
      let option = {
        grid: {
          left: '5%',
          right: '10%',
          top: '20%',
          bottom: '4%',
          containLabel: true
        },
        tooltip: {
          show: true,
          trigger: 'item'
        },
        legend: {
          show: true,
          x: 'center',
          top: '8',
          icon: 'stack',
          itemWidth: 8,
          itemHeight: 8,
          textStyle: {
            color: '#1bb4f6',
            fontSize: 14
          },
          data: ['峰时段用量', '平时段用量', '谷时段用量']
        },
        xAxis: [{
          type: 'category',
          boundaryGap: false,
          axisLabel: {
            color: '#778897'
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: '#262F36'
            }
          },
          axisTick: {
            show: false,
          },
          splitLine: {
            show: false,
            lineStyle: {
              color: '#195384'
            }
          },
          data: ['10', '12', '14', '16', '18']
        }],
        yAxis: [{
          type: 'value',
          name: '',
          axisLabel: {
            formatter: '{value}',
            textStyle: {
              color: '#778897'
            }
          },
          axisLine: {
            lineStyle: {
              color: '#27b4c2'
            }
          },
          axisTick: {
            show: false,
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#273035'
            }
          }
        },

        ],
        series: [{
          name: '峰时段用量',
          type: 'line',
          symbol: 'circle',
          symbolSize: 8,
          itemStyle: {
            normal: {
              color: '#FFEB6D',
              lineStyle: {
                color: "#FFEB6D",
                width: 1
              },
            }
          },
          label: {
            normal: {
              show: true,
              position: 'top'
            }
          },
          markPoint: {
            itemStyle: {
              normal: {
                color: 'red'
              }
            }
          },
          data: [0, 0, 0, 0, 0]
        },
        {
          name: '平时段用量',
          type: 'line',

          symbol: 'circle',
          symbolSize: 8,
          label: {
            normal: {
              show: true,
              position: 'top'
            }
          },
          itemStyle: {
            normal: {
              color: '#13D4D9',
              lineStyle: {
                color: "#13D4D9",
                width: 1
              },
            }
          },
          data: [0, 0, 0, 0, 0]
        },
        {
          name: '谷时段用量',
          type: 'line',

          symbol: 'circle',
          symbolSize: 8,
          label: {
            normal: {
              show: true,
              position: 'top'
            }
          },
          itemStyle: {
            normal: {
              color: '#4253E5',
              lineStyle: {
                color: "#4253E5",
                width: 1
              },
            }
          },
          data: [0, 0, 0, 0, 0]
        },

        ]
      }
      var myChart = echarts.init(distribChart.value)
      myChart.setOption(option); // 展示
      myChart.resize(); // 刷新画布
      window.addEventListener("resize", () => {
        myChart.resize(); // 刷新画布
      });
    }
    return {
      distribChart,
      initChart
    }
  }
}
</script>
<style scoped>
.chart {
  width: 100%;
  height: 100%;
}
</style>