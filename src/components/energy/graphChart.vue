<template>
  <div ref="graphChart" class="graph-chart" />
</template>

<script>
import { watch, ref, inject, onMounted, nextTick } from 'vue'
export default {
  props: {
    chartData: {
      type: Object,
      default: () => {
        return {}
      }
    },
    unitName: {
      type: String,
      default: ''
    },
    name1: {
      type: String,
      default: ''
    },
    name2: {
      type: String,
      default: ''
    }
  },
  setup (props) {
    const graphChart = ref(null)
    let echarts = inject('ec') //引入

    watch(props, (newVal, oldVal) => {
      if (newVal) initChart()
    })
    onMounted(() => {
      nextTick(() => {
        initChart()
      })
    })
    const initChart = () => {
      var myChart = echarts.init(graphChart.value)
      const option = {
        title: {
          top: '8',
          left: 'center',
          text: props.chartData.title,
          textStyle: {
            align: 'center',
            color: '#FFFFFF',
            fontWeight: 400,
            fontSize: 22
          },
        
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          },
          backgroundColor: 'rgba(47, 54, 60, 0.8)',
          borderColor: '#2F363C',
          textStyle: {
            fontSize: 12,
            lineHeight: 24,
            color: '#fff'
          },
          formatter:function(params) {
            let tooltipContent = params[0].name + '<br/>';  // 第一个系列的类别
            params.forEach(function(item) {
                tooltipContent += item.seriesName + ': ' + item.value +props.unitName+ '<br/>';  // 每个系列的数据值
            });
            return tooltipContent;
        }
        },
        legend: {
          right: '10',
          y: '10px',
          textStyle: {
            color: '#f2f2f2',
            fontSize: 13
          },
          icon: 'rect',
          itemWidth: 14,
          itemHeight: 4,
          data: [props.name1, props.name2]
        },
        dataZoom: [{
          type: 'slider',
          show: true,
          height: 20,
          left: '2%',
          right: '2%',
          bottom: '2%',
          start: 0,
          end: 50,
          textStyle: {
            color: '#d4ffff',
            fontSize: 11
          }
        }, {
          type: 'inside'
        }

        ],
        grid: {
          right: '5%',
          bottom: '10%',
          left: '2%',
          top: '80px',
          containLabel: true
        },
        xAxis: [{
          type: 'category',
          data: props.chartData.xData,
          axisLine: {
            lineStyle: {
              color: 'rgba(255,255,255,0.5)'
            }
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            show: true,
            textStyle: {
              color: '#FFF',
              fontSize: 12
            }
          }
        }],
        yAxis: [{
          type: 'value',
          name: '单位(' + props.chartData.unit + ')',
          nameTextStyle: {
            color: 'rgba(255,255,255,1)'
          },
          position: 'left',
          axisLine: {
            lineStyle: {
              color: '#0B4CA9'
            }
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(255,255,255,0.3)'
            }
          },
          axisLabel: {
            color: 'rgba(255,255,255,0.8)',
            fontSize: 12
          }
        }],
        series: [{
          name: props.name1,
          type: 'line',
          yAxisIndex: 0,
          symbolSize: 12,
          itemStyle: {
            normal: {
              color: '#27EDBB'
            }
          },
          data: props.chartData.thisPeriod
        },
        {
          name: props.name2,
          type: 'line',
          yAxisIndex: 0,
          symbolSize: 12,
          itemStyle: {
            normal: {
              color: '#4253E5'
            }
          },
          data: props.chartData.firstHalf
        }

        ]
      }
      myChart.setOption(option); //展示
      myChart.resize(); //刷新画布
      window.addEventListener('resize', () => {
        myChart.resize(); //刷新画布
      })
    }

    return {
      graphChart,
      initChart
    }
  }
}
</script>

<style scoped>
.graph-chart {
  width: 100%;
  height: 100%;
}
</style>
