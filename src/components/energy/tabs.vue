<template>
  <div class="top_tabs" v-for="(item, i) in typeData" :key="i" @click="handleCheck(item.id)">
    {{ item.name }}
    <div :class="{ active: checkType == item.id }"></div>
  </div>
</template>

<script>
import { reactive, toRefs } from 'vue'
import { getCurrentInstance } from 'vue'
export default {
  props: ['typeData'],
  setup (props) {
    const { proxy } = getCurrentInstance()
    const state = reactive({
      checkType: props.typeData[0].id
    })
    const handleCheck = (data) => {
      state.checkType = data
      proxy.$emit('clickDeviceType', data)
    }
    return {
      ...toRefs(state),
      handleCheck
    }
  }
}
</script>
