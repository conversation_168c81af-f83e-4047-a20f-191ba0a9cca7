<template>
  <div class="pie_chart" ref="myEcharts"></div>
</template>

<script>
import {
  ref,
  inject,
  reactive,
  toRefs,
  onMounted,
  watch
} from 'vue'
export default {
  props: ['yData','text'],

  setup (props) {
    const myEcharts = ref(null)
    let echarts = inject('ec') //引入
    const state = reactive({
      myChart: null,
    })
    watch(props, (newVal, oldVal) => {
      if (newVal) initChart()
    })
    onMounted(() => {
      initChart()
    })

    const initChart = () => {
      let colorList = [
        '#1AE6E6',
        '#EEEE11',
        '#2292DD',
        '#F79709',
        '#9E87FF',
        '#DD4822',
        '#764DB3',
        '#EE11EE',
        '#EE11EE',
      ]
      let option = {
        title: {
          text:props.text?props.text: '能耗',
          x: 'center',
          y: 'center',
          textStyle: {
            color: '#fff',
          },
        },
        tooltip: {
          trigger: 'item',
          backgroundColor: 'rgba(47, 54, 60, 0.8)',
          borderColor: '#2F363C',
          textStyle: {
            color: '#fff'
          },
          formatter: function (parms) {
            var str =
              parms.marker +
              '' +
              parms.data.name +
              '</br>' +
              '用量：' +
              parms.data.value +
              '</br>' +
              '占比：' +
              parms.percent +
              '%'
            return str
          },
        },
        series: [{
          type: 'pie',
          center: ['50%', '50%'],
          radius: ['45%', '60%'],
          clockwise: true,
          avoidLabelOverlap: true,
          hoverOffset: 15,
          itemStyle: {
            normal: {
              color: function (params) {
                return colorList[params.dataIndex]
              },
            },
          },
          label: {
            show: true,
            textStyle: {
              color: '#fff'
            },
            position: 'outside',
            formatter: '{b}\n{d}%'
          },
          labelLine: {
            normal: {
              show: false,
            },
          },
          data: props.yData
        },],
      }
      const myChart = echarts.init(myEcharts.value)
      myChart.setOption(option) //展示
      myChart.resize() //刷新画布
      window.addEventListener('resize', () => {
        myChart.resize() //刷新画布
      })
    }

    return {
      ...toRefs(state),
      myEcharts,
      initChart
    }
  },
}
</script>

<style lang="scss" scoped>
.pie_chart {
  width: 100%;
  height: 100%;
}
</style>
