<template>
  <div class="zt">
    <img :src="noData" alt />
    <p class="msg">暂无数据～</p>
  </div>
</template>
<script>
export default {
  data () {
    return {
      noData: require('../assets/images/zt.png')
    }
  },
}
</script>
<style lang="scss" scoped>
.zt {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 8;
  height: 100%;
  // background: rgba($color: #051a30  , $alpha: 0.8);
  .msg {
    font-size: 14px;
    font-family: "Alibaba-PuHuiTi";
    font-weight: 400;
    color: #778897;
  }
}
</style>
