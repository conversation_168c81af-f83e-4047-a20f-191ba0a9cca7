<template>
    <div class="floor">
        <el-scrollbar>

            <div v-for="item in list" :class="[item.id == areaId ? 'active' : '']" :key="item.id" @click="clickFloor(item)"
                class="num center cursor">

                <span> {{ item.name }}</span>
            </div>
            <div class="num center cursor" @click="clickFloor({ id: -1, name: '' })">

                <i class="iconfont iconnenghao"></i>

            </div>

        </el-scrollbar>
    </div>
</template>
    
<script>

import {
    useStore
} from 'vuex'
import {
    getCookie,
    setCookie
} from '@/utils/cookie'

import {
    defineComponent,
    reactive,
    toRefs,
    onMounted,
    computed,
    watch,
    getCurrentInstance,
    inject
} from 'vue'

export default defineComponent({

    setup() {
        const {
            proxy
        } = getCurrentInstance()
        const store = useStore()
        const state = reactive({
            list: [],
            areaId: null,
        })
        onMounted(() => {

        });

        // const projectId = computed(() => {
        //     return store.state.user.projectId || getCookie("gh_projectId");
        // });

        // watch(projectId, (val) => {
        //     if (val) {
        //         getProjectMenu()
        //     }
        // })

        onMounted(() => {
            getArea()
        })

        const getArea = () => {
            proxy.$api.getProjectArea({
                projectId: getCookie("gh_projectId"),
            }).then((res) => {
                if (res.data && res.data.length > 0) {
                    if (res.data[0] && res.data[0].children.length > 0) {
                        setCookie('area', res.data[0].children[0].id)
                        state.list = res.data[0].children;
                    }
                }

            })
        }

        const clickFloor = (item) => {
            state.areaId = item.id;
            store.commit('area/SET_NAV_AREA', item)
        }

        return {
            ...toRefs(state),

            // projectId,
            clickFloor

        }
    },
})
</script>
    
<style lang="scss" scoped>
.floor {
    display: flex;
    flex-direction: column;
    position: absolute;
    right: 393px;
    top: 50%;
    transform: translateY(-50%);
    height: 900px;
    font-size: 20px;
    font-family: "BEBAS";
    font-weight: 400;
    color: #FFFFFF;
    z-index: 9;


    .num {
        background: url("../assets/images/floor/floor.png") no-repeat center/100%;
        height: 50px;
        width: 50px;
        margin-bottom: 5px;
    }

    .active {

        font-size: 20px;
        font-family: "BEBAS";
        font-weight: 400;
        color: rgba(255, 255, 255, 0);
        background: url("../assets/images/floor/floor_active.png") no-repeat center/100%;

        span {
            background: linear-gradient(0deg, #1D88EC 0%, #2FAAF0 49.755859375%, #2AF3F5 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

    }

    :deep(.el-scrollbar__wrap) {
        display: flex;
        justify-content: center;
        align-items: center;
    }

}
</style>
    