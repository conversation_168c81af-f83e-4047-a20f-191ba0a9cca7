<template>
<div class="energySub">
    <div class="box">
        <subEchart :echartData="echartData" :colors="colors"></subEchart>
    </div>
</div>
</template>

<script lang="ts">
import {
    toRefs,
    reactive,
    defineComponent
} from "vue";
import subEchart from "@/components/echarts/subEchart.vue";
export default defineComponent({
    components: {
        subEchart,
    },
    props:['echartData'],
    setup() {
        const state = reactive({
            colors: [ "#4CEDD9","#EAA818", "#358FFF", "#F36447","#47f3cb"],
            // echartData: [{
            //         name: "照明",
            //         value: "4000",
            //     },
            //     {
            //         name: "空调插座",
            //         value: "3564",
            //     },
            //     {
            //         name: "特殊用电",
            //         value: "3564",
            //     },
            //     {
            //         name: "动力",
            //         value: "2548",
            //     },
            //     {
            //         name: "其他",
            //         value: "2548",
            //     }
            // ],
        });

        return {
            ...toRefs(state),
        };
    },
});
</script>

<style lang="scss" scoped>
.energySub {
    width: 100%;
    height: 100%;

    .box {
        height: 100%;
    }
}
</style>
