<template>
<!-- 运行记录 -->
<div class="run">
    <el-form :inline="true" class="search_box form_inline" size="small">
        <el-form-item label="时间">
            <el-date-picker v-model="date" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
        </el-form-item>
        <el-form-item>
            <el-button class="searchBtn" type="text" @click="getRunManualLogList">查询</el-button>
        </el-form-item>
    </el-form>

    <el-table :data="list" height="calc(100% - 100px)" fit>
        <template #empty>
            <no-data />
        </template>
        <el-table-column prop="logTime" label="时间" align="center"></el-table-column>
        <el-table-column prop="deviceName" label="设备" align="center"></el-table-column>
        <el-table-column prop="standardName" label="指标" align="center"></el-table-column>
        <el-table-column label="操作内容" align="center">
            <template #default="scope">
                <span>数值从{{ scope.row.oldValue }}改变为{{ scope.row.newValue }}</span>
            </template>
        </el-table-column>
        <el-table-column prop="username" label="操作者" align="center"></el-table-column>
    </el-table>

    <div class="page center">

        <el-pagination prev-icon="CaretLeft" next-icon="CaretRight" :page-size="size" :current-page="page" layout="total,prev, pager, next" @current-change="handleCurrentChange" :total="total">
        </el-pagination>
    </div>
</div>
</template>

<script>
import dayjs from 'dayjs'
import {
    reactive,
    toRefs,
    onMounted,
    getCurrentInstance,
    computed,
    watch
} from 'vue'
import {
    getCookie
} from '@/utils/cookie'
import {
    useStore
} from 'vuex'

export default {

    props: ['deviceId'],
    components: {
     
    },
    setup(props) {
        const {
            proxy
        } = getCurrentInstance()
        const store = useStore()
        const state = reactive({
            tableHeight: window.innerHeight * 0.60,
            date: [],
            userId: '',
            page: 1,
            size: 10,
            total: 0,
            status: '',
            list: [],
            sources: [],
            levels: [],
        })
        onMounted(() => {
            state.date.push(dayjs().format('YYYY-MM-DD 00:00:00'))
            state.date.push(dayjs().format('YYYY-MM-DD 23:59:59'))
            getRunManualLogList()
        })
        const projectId = computed(() => {
            return store.state.user.projectId || getCookie('gh_projectId')
        })
        watch(projectId, (val) => {
            if (val) {
                getRunManualLogList()
            }
        })
        watch(()=>props.deviceId,(val)=>{
            if(val){
                getRunManualLogList()
            }
        })
        const getRunManualLogList = () => {
            proxy.$api.getDeviceLog({
                bt: dayjs(state.date[0]).format("YYYY-MM-DD 00:00:00"),
                et: dayjs(state.date[1]).format("YYYY-MM-DD 23:59:59"),
                page: state.page,
                size: 10,
                deviceId: props.deviceId,
            }).then((res) => {
                state.total = res.total
                state.list = res.data
            })
        }
        const handleCurrentChange = (page) => {
            state.page = page
            getRunManualLogList()
        }
        const getSourceName = (data) => {
            let name = ''
            state.sources.forEach((d) => {
                if (d.tagValue == data.alarmSource) {
                    name = d.tagName
                    return false
                }
            })
            return name
        }
        const getLevelName = (data) => {
            let name = ''
            state.levels.forEach((d) => {
                if (d.tagValue == data.alarmSource) {
                    name = d.tagName
                    return false
                }
            })
            return name
        }

        return {
            ...toRefs(state),
            getRunManualLogList,
            handleCurrentChange,
            getSourceName,
            getLevelName,
            projectId
        }
    },

}
</script>


