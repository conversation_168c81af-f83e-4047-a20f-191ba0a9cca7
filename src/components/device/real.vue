<template>
<!-- 实时工况 -->
<div class="real_content">
    <div class="box-list">
        <el-scrollbar>
            <div v-for="(item, i) in data" :key="i">
                <div class="box-list-item cursor" @click="changeBox(item, i)" v-if=" item.standardParams[0] && item.standardParams[0].dataType == 'string_input'">
                    <div class="icon" :class="[activeBox==item.id?'active':'']"></div>
                    <div class="temperature">
                        <div class="box-list-title">{{ item.name }}</div>
                        <div class="item">
                            <div>{{ item.standardParams[0].name }}</div>
                            <div class="box-list-num">
                                {{ values['n' + i] }}{{ getUnitName(item.standardParams[0].unit) }}
                            </div>
                        </div>
                    </div>
                    <div class="arrow">
                        <img src="./img/arrow.png"/>
                    </div>
                    <div class="bar top_bar"></div>
                    <div class="bar bottom_bar"></div>
                </div>
            </div>
        </el-scrollbar>
    </div>
    <div class="chart_right">
        <panel :title="realParams.title+'-实时数据'" />
        <realChart :realData="realData" :chartParams="realParams" />
    </div>
</div>
</template>

<script>
import {
    getCookie
} from '@/utils/cookie' // get token from cookie
import socket from '@/utils/socket'
import dayjs from 'dayjs'
import realChart from '@/components/echarts/realChart'

import {
    reactive,
    toRefs,
    onMounted,
    onUnmounted,
    inject,
    getCurrentInstance,
    computed,
    watch
} from 'vue'
import {
    useStore
} from 'vuex'
import Panel from './Panel.vue'
export default {

    props: ['deviceId'],
    components: {
        realChart,
        Panel

    },
    sockets: {
        live(res) {
            this.process(res)
        },
        onVarsChangedCallback(res) {
            this.process(res)
        },
    },
    setup(props) {
        const {
            proxy
        } = getCurrentInstance()
        const store = useStore()
        const state = reactive({
            data: [],
            values: {},
            checked: false,
            chartsData: {},
            interval: null,
            activeBox: '',
            realData: {
                time: [],
                data: [],
            },
            timer: null,
            realParams: {
                unit: '',
                title: '',
            },
            swiperOption: {
                slidesPerView: 8,
                spaceBetween: 0,
                freeMode: true,
                navigation: {
                    nextEl: '.next',
                    prevEl: '.prev',
                },
            },
            sockets: null,
            unit: []
        })
        state.sockets = inject('socket')
        onUnmounted(() => {
            if (state.interval) {
                clearInterval(state.interval)
            }
        })
        const projectId = computed(() => {
            return store.state.user.projectId || getCookie('gh_projectId')
        })
        watch(projectId, (val) => {
            if (val) {
                getUnit()
            }
        })
        watch(()=>props.deviceId,(val)=>{
            if(val){
                getDeviceParams()
            }
        })
        onMounted(() => {

            getUnit()
            getDeviceParams()
        })
        const getDeviceParams = () => {
            proxy.$api.getDeviceStandard({
                deviceId: props.deviceId,
            }).then((res) => {
                state.data = res.data
                state.activeBox = state.data[0].standardParams[0].id
                subscribe(res.data)
            })
        }
        const subscribe = (data) => {
            data.forEach((d, i) => {
                state.values = Object.assign({}, state.values, {
                    ['n' + i]: 0
                })
                state.values = Object.assign({}, state.values, {
                    time: getnewDate(),
                })
                state.chartsData[d.name] = 'n' + i
                if (d.variable) {
                    let v = d.variable.split(':')
                    let item = {
                        id: 'n' + i,
                        iosvrKey: v[0],
                        chlKey: v[1],
                        ctrlKey: v[2],
                        varKey: v[3],
                        realTime: false,
                    }
                    socket.subscribe(state.sockets, 'real', 'device', [item])
                }
            })
            getrealData(data[0], 0)
        }
        const getrealData = (item, i) => {
            if (state.timer) {
                window.clearInterval(state.timer)
            }
            state.realParams = {
                title: item.name,
                unit: item.standardParams[0].unit,
            }

            if (state.realData.time.length == 10) {
                state.realData.time.shift()
                state.realData.data.shift()
            }
            let data = parseInt(state.values['n' + i])
            state.realData.time.push(getnewDate())
            state.realData.data.push(data)

            state.timer = setInterval(() => {
                if (state.realData.time.length == 10) {
                    state.realData.time.shift()
                    state.realData.data.shift()

                    let data = parseInt(state.values['n' + i])
                    state.realData.time.push(getnewDate())
                    state.realData.data.push(data)
                } else {
                    let data = parseInt(state.values['n' + i])
                    state.realData.time.push(getnewDate())
                    state.realData.data.push(data)
                }
            }, 5000)
        }
        const getnewDate = () => {
            let time = dayjs().format('HH:mm:ss')
            return time
        }
        const process = (res) => {
            if (res) {
                let data = JSON.parse(res)
                if (data.batchDefinitionId == 'real' && data.clientId == 'device') {
                    data.data.forEach((d) => {
                        state.values[d.id] = d.value
                        state.values.time = getnewDate()
                    })
                }
            }
        }
        // 点击box
        const changeBox = (item, i) => {
            state.activeBox = item.standardParams[0].id
            state.realData = {
                time: [],
                data: [],
            }
            getrealData(item, i)
        }
        const getUnit = () => {
            proxy.$api.getDicUtil({
                dicCode: 'unit',
                projectId: getCookie("gh_projectId"),
            }).then((res) => {
                state.unit = res.data
            })
        }
        const getUnitName = (val) => {
            let name = ''
            state.unit.forEach((u) => {
                if (u.tagValue == val) {
                    name = u.tagName
                }
            })
            return name
        }

        return {
            ...toRefs(state),
            projectId,
            getDeviceParams,
            subscribe,
            getrealData,
            getnewDate,
            process,
            changeBox,
            getUnit,
            getUnitName
        }
    },
}
</script>

<style lang="scss" scoped>
.real_content {
    display: flex;
    flex-direction: row;
    height: calc(100% - 56px);

    .box-list {
        width: 286px;
        height: calc(100% - 20px);

        .box-list-item {
            background-color: #061527;
            border: 1px solid #15457F;
            margin-bottom: 12px;
            display: flex;
            position: relative;

            .icon {
                width: 85px;
                height: 92px;
                background: url("./img/t.png") no-repeat;
                margin-right: 20px;
            }

            .temperature {
                display: flex;
                flex-direction: column;

                .box-list-title {
                    padding: 14px 8px 0 8px;
                    font-size: 16px;
                    font-family: "Alibaba-PuHuiTi";
                    font-weight: 500;
                    color: #7B96C7;
                }

                .item {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    font-size: 16px;
                    font-family: "Alibaba-PuHuiTi";
                    font-weight: 400;
                    color: #889cc3;
                    padding: 12px 8px;

                    .box-list-num {
                        font-size: 19px;
                        font-family: "DINAlternate-Bold";
                        font-weight: bold;
                        color: #2D87E6;
                        margin-left: 20px;
                        width: 27px;
                    }
                }

            }

            .bar {
                width: 29px;
                height: 1px;
                background: #1FA6CD;
            }

            .bottom_bar {
                position: absolute;
                bottom: 0;
                right: 15px;
            }

            .top_bar {
                position: absolute;
                top: 0;
                left: 0;
            }

            .arrow{
                height: 94px;
                flex:1;
                display: flex;
                justify-content: center;
                align-items: center;
            }
        }
    }

    .active {
        background: url("./img/t_active.png") no-repeat !important;
    }

    .chart_right {
        flex: 1;
        margin-left: 20px;

        .real_chart {
            height: calc(100% - 49px);
        }
    }
}
</style>
