<template>
    <div class="cmd">

        <div class="list" v-for="item in data.state" :key="item.id">
            <div class="icon">
                <i class="iconfont iconyoujiantou "></i>
            </div>
            <div class="name">{{ item.name }}</div>
            <div class="dot"></div>
            <div class="num">
                <div>{{ getName(realData[item.id], item.config) }}</div>
                <i :class="item.icon"></i>
            </div>
        </div>

        <div class="list" v-for="item in data.num_out" :key="item.id">
            <div class="icon">
                <i class="iconfont iconyoujiantou "></i>
            </div>
            <div class="name">{{ item.standardName }}</div>
            <div class="dot"></div>
            <div class="num">
                <el-switch @change="changeSwitch($event, item.variable)" :active-value="1" v-model="realData[item.id]"
                    :inactive-value="0"></el-switch>
            </div>
        </div>

        <div class="list" v-for="item in data.string_input" :key="item.id">
            <div class="icon">
                <i class="iconfont iconyoujiantou "></i>
            </div>
            <div class="name">{{ item.standardName }}</div>
            <div class="dot"></div>
            <div class="num">
                <div>{{ realData[item.id] }}{{ getUnitName(item.unit) }}</div>
                <i :class="item.icon"></i>
            </div>
        </div>

        <div class="list" v-for="item in data.string_out" :key="item.id">
            <div class="icon">
                <i class="iconfont iconyoujiantou "></i>
            </div>
            <div class="name">{{ item.standardName }}</div>
            <div class="slider ">
                <div>{{ item.min }}</div>
                <el-slider @change="changeSlider($event, item)" v-model="realData[item.id]" :max="item.max" :min="item.min"
                    :show-tooltip="true" />
                <div>{{ item.max }}</div>
            </div>

        </div>

        <div class="list" v-for="item in data.enum" :key="item.id">
      
            <div class="icon">
                <i class="iconfont iconyoujiantou "></i>
            </div>
            <div class="name">{{ item.name }}</div>

            <div class="search_box slider">
                <el-select style="width: 80%;" v-model="realData[item.id]" @change="changeEnum($event, item.variable)">
                    <el-option v-for="(e, i) in  item.enum" :key="i" :label="e.name" :value="e.value"></el-option>
                </el-select>
            </div>

        </div>

    </div>
</template>

<script>
import {
    defineComponent,
    onMounted,
    onUnmounted,
    reactive,
    toRefs,
    inject,
    getCurrentInstance,
    nextTick, watch
} from "vue";
import socket from "@/utils/socket";
import {
    getCookie
} from "@/utils/cookie";
import calc from '@/utils/eval';
export default defineComponent({
    props: ['deviceId'],
    sockets: {
        live(res) {
            this.subscribeData(res);
        },
        onVarsChangedCallback(res) {
            this.subscribeData(res);
        },
    },
    setup(props) {
        const state = reactive({
            data: {
                name: '',
                state: [],
                num_out: [],
                string_input: [],
                string_out: [],
                enum: []
            },
            sockets: null,
            realData: {},
            unit: []
        });
        const {
            proxy
        } = getCurrentInstance();
        state.sockets = inject("socket");

        onMounted(() => {
            socket.unsubscribe(state.sockets, "panel", "real");
            if (props.deviceId) {
                getDeviceList(props.deviceId)
            }
            getUnit()
        });
        watch(() => props.deviceId, (val) => {
            if (val) {
                getDeviceList(val)
            }
        })
        const getUnit = () => {
            proxy.$api.getDicUtil({
                dicCode: 'unit',
                projectId: getCookie("gh_projectId"),
            }).then((res) => {
                state.unit = res.data
            })
        }
        const getUnitName = (val) => {
            let name = ''
            state.unit.forEach((u) => {
                if (u.tagValue == val) {
                    name = u.tagName
                }
            })
            return name
        }
        //解析订阅指标
        const deviceStd = (d) => {
            let ws = [];
            state.data = {
                name: '',
                state: [],
                num_out: [],
                string_input: [],
                string_out: [],
                enum: []
            };

            if (d.deviceStandards) {
                let data = {};
                data.name = d.name;
                data.id = d.id
                data.state = [];
                data.num_out = [];
                data.string_input = [];
                data.string_out = [];
                data.enum = [];
                //设备指标          
                d.deviceStandards.forEach((s, j) => {
                    //当前指标的参数集合
                    if (s.deviceParams) {
                        s.deviceParams.forEach((p, k) => {
                            //只解析当前值
                            if (p.paramKey == "Value") {
                                //状态输入  
                                if (p.dataType == "num_input") {
                                    data.state.push({
                                        name: s.name,
                                        id: "s_" + j + '_' + k,
                                        config: p.config ? JSON.parse(p.config) : null
                                    });
                                    let v = s.variable.split(":");
                                    let item = {
                                        id: "s_" + j + '_' + k,
                                        iosvrKey: v[0],
                                        chlKey: v[1],
                                        ctrlKey: v[2],
                                        varKey: v[3],
                                        realTime: false,
                                    };
                                    state.realData = Object.assign({}, state.realData, {
                                        ["s_" + j + '_' + k]: 0,
                                    });
                                    ws.push(item);
                                } else if (p.dataType == "num_out") {
                                    let item = {};
                                    item.standardName = s.name;
                                    item.value = p.paramValue;
                                    item.icon = p.icon || 'iconfont iconjidian01';
                                    item.variable = s.variable;
                                    item.id = "s_" + j + '_' + k;
                                    item.config = p.config ? JSON.parse(p.config) : null
                                    //数据订阅
                                    let v = s.variable.split(":");
                                    let item1 = {
                                        id: "s_" + j + '_' + k,
                                        iosvrKey: v[0],
                                        chlKey: v[1],
                                        ctrlKey: v[2],
                                        varKey: v[3],
                                        realTime: false,
                                    };
                                    state.realData = Object.assign({}, state.realData, {
                                        ["s_" + j + '_' + k]: 0,
                                    });
                                    ws.push(item1);
                                    data.num_out.push(item);
                                } else if (p.dataType == "string_input") {
                                    let item = {};
                                    item.standardName = s.name;
                                    item.value = p.paramValue;
                                    item.icon = p.icon || 'iconfont iconjidian01';
                                    item.unit = p.unit;
                                    item.id = "s_" + j + '_' + k;
                                    item.config = p.config ? JSON.parse(p.config) : {}
                                    //数据订阅
                                    let v = s.variable.split(":");
                                    let item1 = {
                                        id: "s_" + j + '_' + k,
                                        iosvrKey: v[0],
                                        chlKey: v[1],
                                        ctrlKey: v[2],
                                        varKey: v[3],
                                        realTime: false,
                                    };
                                    state.realData = Object.assign({}, state.realData, {
                                        ["s_" + j + '_' + k]: 0,
                                    });
                                    ws.push(item1);
                                    data.string_input.push(item);
                                } else if (p.dataType == "string_out") {
                                    let item = {};
                                    item.standardName = s.name;
                                    item.variable = s.variable;

                                    item.min = p.min || 0;
                                    item.max = p.max || 100;

                                    item.icon = p.icon || 'iconfont iconjidian01';

                                    item.id = "s_" + j + '_' + k;

                                    //数据订阅
                                    let v = s.variable.split(":");

                                    let item1 = {
                                        id: "s_" + j + '_' + k,
                                        iosvrKey: v[0],
                                        chlKey: v[1],
                                        ctrlKey: v[2],
                                        varKey: v[3],
                                        realTime: false,
                                    };
                                    state.realData = Object.assign({}, state.realData, {
                                        ["s_" + j + '_' + k]: 0,
                                    });
                                    ws.push(item1);
                                    data.string_out.push(item);
                                } else if (p.dataType == "enum") {



                                    if (p.config) {
                                        let config = JSON.parse(p.config);
                                        let e = [];
                                        config.forEach(c => {
                                            e.push({
                                                name: c.text,
                                                value: Number(c.value),
                                                // variable: s.variable
                                            })
                                        })
                                        data.enum.push({
                                            id: "s_" + j + '_' + k,
                                            name: s.name,
                                            enum: e,
                                            variable: s.variable
                                        })
                                        let v = s.variable.split(":");
                                        let item1 = {
                                            id: "s_" + j + '_' + k,
                                            iosvrKey: v[0],
                                            chlKey: v[1],
                                            ctrlKey: v[2],
                                            varKey: v[3],
                                            realTime: false,
                                        };
                                        state.realData = Object.assign({}, state.realData, {
                                            ["s_" + j + '_' + k]: 0,
                                        });
                                        ws.push(item1);
                                    }


                                }
                            }
                        });
                    }
                });
                state.data = data;
            }

            if (ws.length > 0) {
                nextTick(() => {
                    socket.subscribe(state.sockets, "real", "panel", ws);
                });
            }
        };

        const getDeviceList = async (id) => {
            socket.unsubscribe(state.sockets, "panel", "real");
            let {
                data
            } = await proxy.$api.getDevicesStdById({
                deviceId: id,
                projectId: getCookie("gh_projectId")
            })
            if (data && data.length > 0) {
                deviceStd(data[0]);
            }

        };
        const subscribeData = (res) => {
            if (res) {
                let data = JSON.parse(res);
                if (data.batchDefinitionId == "real" && data.clientId == "panel") {
                    data.data.forEach((d) => {
                        state.realData[d.id] = Number(d.value);
                    });
                }
            }
        };
        const getName = (value, config) => {
            let name = "";
            if (config && config.length && config.length > 0) {
                config.forEach(c => {
                    if (calc(value, c.factor, c.value)) {
                        name = c.text;
                    }
                })
            }
            return name;
        }
        const getColor = (value, config) => {
            let color = "";
            if (config && config.length && config.length > 0) {
                config.forEach(c => {
                    if (calc(value, c.factor, c.value)) {
                        color = c.color;
                    }
                })
            }
            return color;
        }

        const changeSwitch = (value, variable) => {
            socket.writeValue(
                state.sockets,
                variable,
                value,
                "ba",
                state.sockets.id,
                getCookie("gh_projectId"),
                getCookie("gh_id")
            );
        };

        const changeSlider = (val, data) => {
            socket.writeValue(
                state.sockets,
                data.variable,
                val,
                "ba",
                state.sockets.id,
                getCookie("gh_projectId"),
                getCookie("gh_id")
            );
        };
        const changeEnum = (val, variable) => {
            socket.writeValue(
                state.sockets,
                variable,
                val,
                "ba",
                state.sockets.id,
                getCookie("gh_projectId"),
                getCookie("gh_id")
            );
        };

        onUnmounted(() => {
            socket.unsubscribe(state.sockets, "panel", "real");
        })
        return {
            ...toRefs(state),
            subscribeData,
            getName,
            getColor,
            changeSwitch,
            changeSlider,
            getUnitName,
            changeEnum
        }
    }
})
</script>

<style lang="scss" scoped>
.cmd {
    color: white;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-direction: column;

    .list {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 10px;
        background: #051526;
        height: 45px;
        width: 100%;
        border-left: 3px solid #1F82F5;
        padding-right: 20px;
        box-sizing: border-box;

        .icon {
            color: #1F82F5;
        }

        .num {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            margin-left: 10px;

            div {
                margin-right: 10px;
            }
        }

        .name {
            margin-right: 10px;
            // width: 100px;
        }

        .dot {
            height: 1px;
            border-bottom: 1px dashed #233E5A;
            flex: 1;
        }

        .slider {
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: center;
        }
    }
}
</style>
