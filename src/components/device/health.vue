<template>
  <div class="health_content">
    <div class="top">
      <scoreEchart />
      <healthDegree :seriesData="seriesData" :radarData="radarData"></healthDegree>
    </div>
    <div class="bottom">
      <panel title="健康数据采集" />
      <div class="info">
        <div v-for="(item, i) in list" :key="i" class="list">
          <div class="label" :style="item.sty">{{ item.label }}</div>
          <div class="value">{{ item.value }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import healthDegree from "@/components/echarts/healthDegreeEchart.vue";
import scoreEchart from "@/components/echarts/scoreEchart.vue";
import { reactive, toRefs } from "vue";
import {
  defineComponent,
  getCurrentInstance,
  onMounted,
} from "vue";

export default defineComponent({
  components: {
    healthDegree,
    scoreEchart,
  },
  props: ["deviceId"],
  setup(props) {
    const state = reactive({
      seriesData: [70, 90, 80, 85, 70],
      radarData: [
        {
          name: "使用时长",
          max: 100,
          value: 70,
        },
        {
          name: "启停次数",
          max: 100,
          value: 90,
        },
        {
          name: "在线率",
          max: 100,
          value: 80,
        },
        {
          name: "保养次数",
          max: 100,
          value: 85,
        },
        {
          name: "故障次数",
          max: 100,
          value: 70,
        },
      ],
      list: [
        {
          label: "累计运行时长",
          value: 497,
          sty: "background:linear-gradient(180deg, #FFFFFF 0%, #27EDBB 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent;",
        },
        {
          label: "累计启停次数",
          value: "97",
          sty: "background:linear-gradient(180deg, #FFFFFF 0%, #27A6ED 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent;",
        },
        {
          label: "累计故障次数",
          value: "34",
          sty: "background:linear-gradient(180deg, #FFFFFF 0%, #27EDBB 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent;",
        },
        {
          label: "累计保养次数",
          value: "149",
          sty: "background:linear-gradient(180deg, #FFFFFF 0%, #27A6ED 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent;",
        },
        {
          label: "累计在线时长",
          value: "100",
          sty: "background:linear-gradient(180deg, #FFFFFF 0%, #27EDBB 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent;",
        },
      ],
    });
    const { proxy }: any = getCurrentInstance();

    const getMaintenanceTime = async () => {
      // 获取维保次数
      let { data } = await proxy.$api.getMaintenanceTime(props.deviceId);
      state.list[3].value = data;
    };
    const getDeviceHealthData = async () => {
      let { data } = await proxy.$api.getDeviceHealthData(props.deviceId);
      state.list[1].value = data.onCount;
      state.list[0].value = data.onTime / 24;
      state.list[2].value = data.onFault;
    };

    onMounted(() => {
      getMaintenanceTime();
    });

    return {
      ...toRefs(state),
      getMaintenanceTime,
    };
  },
});
</script>

<style lang="scss" scoped>
.health_content {
  display: flex;
  flex-direction: column;
  padding: 0 10px;
  height: 100%;

  .top {
    display: flex;
    height: 60%;
  }

  .bottom {
    .info {
      display: flex;
      margin: 20px 0;

      .list {
        flex: 1;
        margin: 0 15px;
        padding: 33px 0;
        text-align: center;
        border: 1px solid #363b3d;

        .label {
          font-size: 18px;
          font-family: "PingFangSC-Medium", "PingFang SC";
          font-weight: 500;
          color: #ffffff;
          line-height: 25px;
          margin-bottom: 15px;
        }

        .value {
          width: 80%;
          height: 50px;
          line-height: 50px;
          margin: 0 auto;
          font-size: 25px;
          font-family: "DINAlternate-Bold", "DINAlternate";
          font-weight: bold;
          background-color: rgba(255, 255, 255, 0.05);
          color: #ffffff;
        }
      }
    }
  }
}
</style>
