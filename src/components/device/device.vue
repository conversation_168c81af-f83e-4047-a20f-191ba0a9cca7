<template>
<div class="device">
    <el-row type="flex" :gutter="10" justify="space-between">
        <el-col :xs="15" :sm="15" :md="15" :lg="15" :xl="15">
            <div class="info">
                <panel title="基础信息"></panel>
                <el-row class="item">
                    <el-col :xs="24" :sm="24" :md="8" :lg="8" :xl="8">
                        <span class="item-label">设备名称：</span>
                        <span class="item-value">{{ device.name }}</span>
                    </el-col>
                    <el-col :xs="24" :sm="24" :md="8" :lg="8" :xl="8">
                        <span class="item-label">设备型号: </span>
                        <span class="item-value">{{ device.model }}</span>
                    </el-col>
                    <el-col :xs="24" :sm="24" :md="8" :lg="8" :xl="8">
                        <span class="item-label">设备编号: </span>
                        <span class="item-value">{{ device.code }}</span>
                    </el-col>
                </el-row>
                <el-row class="item">
                    <el-col :xs="24" :sm="24" :md="8" :lg="8" :xl="8">
                        <span class="item-label">生产厂家: </span>
                        <span class="item-value">{{ device.factory }}</span>
                    </el-col>
                    <el-col :xs="24" :sm="24" :md="8" :lg="8" :xl="8">
                        <span class="item-label">设备类型: </span>
                        <span class="item-value">{{ device.deviceTypeName }}</span>
                    </el-col>
                    <el-col :xs="24" :sm="24" :md="8" :lg="8" :xl="8">
                        <span class="item-label">所属空间: </span>
                        <span class="item-value">{{ device.areaName }}</span>
                    </el-col>
                </el-row>
                <el-row class="item">
                    <el-col :xs="24" :sm="24" :md="8" :lg="8" :xl="8">
                        <span class="item-label">所属部门: </span>
                        <span class="item-value">{{ device.departmentName }}</span>
                    </el-col>
                    <el-col :xs="24" :sm="24" :md="8" :lg="8" :xl="8">
                        <span class="item-label">维保周期: </span>
                        <span class="item-value">{{ device.period }}</span>
                    </el-col>
                </el-row>
            </div>
            <div class="position">
                <panel title="安装信息"></panel>
                <el-row class="item">
                    <el-col :xs="24" :sm="24" :md="8" :lg="8" :xl="8">
                        <span class="item-label">安装时间: </span>
                        <span class="item-value">{{ device.installTime }}</span>
                    </el-col>
                    <el-col :xs="24" :sm="24" :md="8" :lg="8" :xl="8">
                        <span class="item-label">安装位置: </span>
                        <span class="item-value">{{ device.position }}</span>
                    </el-col>
                </el-row>
            </div>
            <div class="position">
                <panel title="BIM信息" />
                <el-row class="item">

                    <span class="item-label">模型名称：</span>
                    <span class="item-value">{{modelName}}</span>

                </el-row>
                <el-row class="item">
                    <span class="item-label"> 构件名称：</span>
                    <span class="item-value">{{name}}</span>
                </el-row>
            </div>
            <div class="position">
                <panel title="GIS信息" />
                <el-row class="item">
                    <el-col :xs="24" :sm="24" :md="8" :lg="8" :xl="8">
                        <span class="item-label">经度：</span>
                        <span class="item-value">{{points&&points.length>0?points[0].lat:''}}</span>
                    </el-col>
                    <el-col :xs="24" :sm="24" :md="8" :lg="8" :xl="8">
                        <span class="item-label">纬度：</span>
                        <span class="item-value">{{points&&points.length>0?points[0].lng:''}}</span>
                    </el-col>
                </el-row>
            </div>
            <div class="position">
                <panel title='安装手册'></panel>
                <div class="book-list">
                    <div class="book-list-item" v-if="device.manualPath">
                        <div>
                            <i class="iconfont iconshuomingshu"></i>
                        </div>
                        <div class="center">
                            说明书
                            <el-link type="primary" :href="device.manualPath">下载</el-link>
                        </div>
                    </div>
                    <div class="book-list-item" v-if="device.cadPath">
                        <div>
                            <i class="iconfont iconicon-book"></i>
                        </div>
                        <div class="center">
                            图纸
                            <el-link type="primary" :href="device.cadPath">下载</el-link>
                        </div>
                    </div>
                </div>
            </div>
        </el-col>
        <el-col :xs="9" :sm="9" :md="9" :lg="9" :xl="9">
            <div class="img_box">
                <img v-if="device.imgPath" :src="device.imgPath" style="object-fit: contain" />
            </div>
            <div class="qrCode">
                <div ref="qrCodeDiv" id="qrCode"></div>
                <div class="title">设备二维码</div>
            </div>
        </el-col>
    </el-row>
</div>
</template>

<script>
import QRCode from 'qrcodejs2';
import {
    getCookie
} from '@/utils/cookie' // get token from cookie
import {
    defineComponent,
    reactive,
    ref,
    toRefs,
    onMounted,
    watch,
    getCurrentInstance,
    computed
} from 'vue'
import Panel from './Panel.vue';
import {
    useStore
} from 'vuex';
export default defineComponent({
    components: {
        Panel
    },
    name: 'device',
    props: ['deviceId'],
    setup(props) {
        const {
            proxy
        } = getCurrentInstance()
        const store = useStore()
        const qrCodeDiv = ref(null)
        const state = reactive({
            device: {},
            points: [],
            modelName: '',
            name: ''
        })
        watch(props, (newVal, oldVal) => {
            if (newVal) {
                getDevice()
                getGisBim()
            }
        })
        watch(projectId, (val) => {
            if (val) {
                getDevice()
                getGisBim()
            }
        })
        onMounted(() => {
            if (props.deviceId) {
                getDevice()
                getGisBim()
            }
        })
        const projectId = computed(() => {
            return store.state.user.projectId || getCookie('gh_projectId')
        })
        const getDevice = () => {
            proxy.$api.getDevices({
                projectId: getCookie("gh_projectId"),
                id: props.deviceId,
            }).then((res) => {
                state.device = res.data.length > 0 ? res.data[0] : null
                document.getElementById('qrCode').innerHTML = ''
                new QRCode(qrCodeDiv.value, {
                    text: state.device.id,
                    width: 212,
                    height: 212,
                    colorDark: "#fff", //二维码颜色
                    colorLight: "#000", //二维码背景色
                });
            })
        }
        const getGisBim = () => {
            proxy.$api.GisBim({
                projectId: getCookie("gh_projectId"),
                deviceId: props.deviceId,
            }).then(res => {
                const data = res.data
                state.points = eval('(' + data.points[0] + ')');

                if (data.gis.length > 0) {
                    const modelName = [];
                    const name = [];
                    data.gis.forEach(element => {
                        if (element.modelName) {
                            modelName.push(element.modelName)
                            state.modelName = modelName.join('，')
                        }
                        if (element.name) {
                            name.push(element.name)
                            state.name = name.join('，')
                        }
                    });
                }
            })
        }

        return {
            ...toRefs(state),
            qrCodeDiv,
            getDevice,
            getGisBim,
            projectId
        }
    },
})
</script>

<style lang="scss" scoped>
.device {
    .item {
        .item-value {
            font-size: 14px;
            font-family: "Alibaba-PuHuiTi";
            font-weight: 400;
            color: #ffffffad;
        }

        .item-label {
            font-size: 16px;
            font-family: "Alibaba-PuHuiTi";
            font-weight: 400;
            color: #7A9BBD;
        }
    }



    .position {
        margin: 25px 0;

        .book-list {
            display: flex;
            padding: 10px;

            i {
                font-size: 24px;
            }

            .book-list-item {
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                margin-right: 15px;

                .iconfont {
                    color: #fff;
                }

                .center {
                    font-size: 16px;
                    font-family: "Alibaba-PuHuiTi";
                    font-weight: 400;
                    color: #889cc3;
                }
            }
        }
    }

    .img_box {
        width: 350px;
        height: 350px;
        margin: 0 auto;
        background: rgba(47, 54, 60, 0.3);
        border: solid 1px #4a5966;

        img {
            height: 100%;
            width: 100%;
        }
    }

    .qrCode {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-top: 20px;

        .title {
            text-align: center;
            font-size: 16px;
            font-family: "Alibaba-PuHuiTi";
            font-weight: 400;
            color: #ffffff;
            margin-top: 10px;
        }
    }

    .info{
        margin-top:15px
    }
}
</style>
