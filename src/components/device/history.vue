<template>
    <div class="history_content">
        <div class="box-list">
            <el-scrollbar>
                <div v-for="(item, i) in data" :key="i" class="box-list-item" @click="changeBox(item, i)">
                    <div class="icon" :class="{ active: activeBox == item.standardParams[0].id }"></div>
                    <div class="header">
                        <div class="box-list-title">{{ item.name }}</div>

                    </div>
                    <div class="item">
                        <div>平均值</div>
                        <div class="box-list-num">
                            {{ values[item.variable] ? values[item.variable].mean : 0 }}
                        </div>
                    </div>
                    <!-- <div class="item">
                    <div>最大值</div>
                    <div class="box-list-num">
                        {{ values[item.variable].max }}
                    </div>
                </div>
                <div class="item">
                    <div>最小值</div>
                    <div class="box-list-num">
                        {{ values[item.variable].min }}
                    </div>
                </div> -->

                    <div class="arrow">
                        <img src="./img/arrow.png" />
                    </div>
                    <div class="bar top_bar"></div>
                    <div class="bar bottom_bar"></div>
                </div>

            </el-scrollbar>
        </div>
        <div class="chart">
            <!-- <panel :title="item?.name"> -->
            <el-form :inline="true" class="search_box form_inline" size="small">
                <el-form-item label="时间选择">
                    <el-date-picker :popper-class="search_picker" v-model="startEndDate" type="daterange"
                        range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                        @change="changeDate"></el-date-picker>
                </el-form-item>
            </el-form>
            <!-- </panel> -->
            <!-- <el-form :inline="true" class="search_sub" size="small" style="margin-right:15px">
            <el-button type="primary" size="small" @click="down">导出</el-button>
        </el-form> -->

            <historyChart :historyData="historyData" />
        </div>
    </div>
</template>

<script>
import dayjs from 'dayjs'
import historyChart from '@/components/echarts/historyChart'
import {
    reactive,
    toRefs,
    onMounted,
    getCurrentInstance, watch
} from 'vue'
import {
    ElMessage
} from 'element-plus'
import Panel from './Panel.vue'
export default {
    components: {
        historyChart,
        Panel
    },
    props: ['deviceId'],
    setup(props) {
        const {
            proxy
        } = getCurrentInstance()
        const state = reactive({
            data: [],
            values: {},
            checked: false,
            chartsData: {},
            interval: null,
            chart: null,
            activeBox: '',
            historyData: {
                data: [],
                time: [],
                name: '',
            },
            realParams: {
                title: '',
                unit: '',
            },
            startEndDate: [],
            item: null,
            downData: []
        })
        watch(() => props.deviceId, (val) => {
            if (val) {
                getDeviceParams()
            }
        })
        onMounted(() => {
            state.startEndDate.push(dayjs().format('YYYY-MM-DD 00:00:00'))
            state.startEndDate.push(dayjs().format('YYYY-MM-DD 23:59:59'))
            if (props.deviceId) {
                getDeviceParams()
            }
        })

        const getDeviceParams = () => {
            proxy.$api.getDeviceStandard({
                deviceId: props.deviceId,
            }).then((res) => {
                // let data = [];

                // if (res.data && res.data.length > 0) {
                //     res.data.forEach((d) => {
                //         if (d.standardParams && d.standardParams.length > 0) {
                //             for (let i = 0; i < d.standardParams.length; i++) {
                //                 if (d.standardParams[i].dataType == 'string_input') {
                //                     data.push(d)
                //                     break
                //                 }
                //             }
                //         }
                //     })
                // }

                // state.data = data
                state.data = res.data
                analyze(state.data)
                changeBox(res.data[0])
            })
        }
        const analyze = (data) => {
            if (data) {
                let key = []
                data.forEach((d) => {
                    if (d.variable) {
                        key.push(d.variable)
                        state.values = Object.assign({}, state.values, {
                            [d.variable]: {
                                mean: 0,
                                max: 0,
                                min: 0,
                            },
                        })
                    }
                })
                if (key.length > 0) {
                    proxy.$api.getAnalyze({
                        keyword: key,
                        bt: dayjs(state.startEndDate[0]).format("YYYY-MM-DD 00:00:00"),
                        et: dayjs(state.startEndDate[1]).format("YYYY-MM-DD 23:59:59"),
                    }).then((res) => {
                        res.data.forEach((d) => {
                            state.values[d.variable].max = d.max
                            state.values[d.variable].min = d.min
                            state.values[d.variable].mean = d.mean
                        })
                    })
                }
            }
        }
        const changeBox = (item) => {
            if (item) {
                state.item = item
                state.activeBox = item.standardParams[0].id
                getHistory(item.variable, item.name)
            }

        }
        const getHistory = (variable, name) => {
            proxy.$api.getHistoryData({
                bt: dayjs(state.startEndDate[0]).format("YYYY-MM-DD HH:mm:ss"),
                et: dayjs(state.startEndDate[1]).format("YYYY-MM-DD HH:mm:ss"),
                variable: variable,
            }).then((res) => {
                state.downData = res.data;
                state.historyData.time = res.data.map((v, i) => {
                    return v.ts
                })
                state.historyData.data = res.data.map((e, j) => {
                    return e._value
                })
                state.historyData.name = name
            })
        }

        const down = () => {
            if (!state.item) {
                ElMessage.warning("无历史指标数据");
                return;
            }
            const params = {
                bt: dayjs(state.startEndDate[0]).format("YYYY-MM-DD HH:mm:ss"),
                et: dayjs(state.startEndDate[1]).format("YYYY-MM-DD 23:59:59"),
                keyword: state.item.variable,
            }
            proxy.$api.getHistoryDown(params).then(res => {
                const link = document.createElement('a')
                const blob = new Blob([res], {
                    type: 'application/vnd.ms-excel'
                })
                link.style.display = 'none'
                link.href = URL.createObjectURL(blob)
                link.setAttribute('download', `历史数据.xlsx`)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link)
            })
        }
        const changeDate = (value) => {
            if (state.item && state.item.variable) {
                getHistory(state.item.variable, state.item.name)
            }
        }

        return {
            ...toRefs(state),
            getDeviceParams,
            analyze,
            changeBox,
            getHistory,
            changeDate,
            down
        }
    },
}
</script>

<style lang="scss" scoped>
.history_content {
    display: flex;
    flex-direction: row;

    .box-list {
        width: 224px;
        height: calc(100% - 20px);
        width: 286px;

        .box-list-item {
            align-items: center;
            border: 1px solid #15457F;
            margin-bottom: 12px;
            display: flex;
            position: relative;

            .icon {
                width: 85px;
                height: 92px;
                background: url("./img/t.png") no-repeat;
                margin-right: 20px;
            }

            .item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                flex-direction: column;
                font-size: 12px;
                font-family: "Alibaba-PuHuiTi";
                font-weight: 400;
                color: #7B96C7;
                padding: 6px 8px;
            }

            .box-list-num {
                font-size: 19px;
                font-family: "DINAlternate-Bold";
                font-weight: bold;
                color: #2D87E6;
            }

            .header {
                display: flex;
                align-items: center;

                .box-list-title {
                    font-size: 16px;
                    font-family: "Alibaba-PuHuiTi";
                    font-weight: 500;
                    color: #7B96C7;
                }
            }
        }

        .bar {
            width: 29px;
            height: 1px;
            background: #1FA6CD;
        }

        .bottom_bar {
            position: absolute;
            bottom: 0;
            right: 15px;
        }

        .top_bar {
            position: absolute;
            top: 0;
            left: 0;
        }

        .arrow {
            height: 94px;
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: center;
        }

    }

    .chart {
        flex: 1;
        margin-left: 15px;

        .history_chart {
            height: calc(100% - 49px);
        }

        .search_box {
            text-align: right;
            padding-right: 20px;
        }

        .search_sub {
            margin-left: 20px;
            padding-bottom: 10px;
        }
    }

    .active {
        background: url("./img/t_active.png") no-repeat !important;
    }
}
</style>
