<template>
  <div class="analysis_content">
    <div class="top">
      <sub-title title="用能统计" />
      <div class="box">
        <div class="item">
          <div class="name day">本日</div>
          <span class="num">215,510</span>
        </div>
        <div class="item">
          <div class="name week">本周</div>
          <span class="num">8,899</span>
        </div>
        <div class="item">
          <div class="name month">本月</div>
          <span class="num">954,705</span>
        </div>
        <div class="item">
          <div class="name year">本年</div>
          <span class="num">18888,899</span>
        </div>
      </div>
    </div>
    <div class="chart">
      <historyChart :historyData="historyData" />
    </div>
  </div>
</template>
<script lang="ts">
import { reactive, toRefs } from "vue";
import historyChart from "@/components/echarts/historyChart.vue";
export default {
  components: {
    historyChart,
  },
  setup() {
    const state = reactive({
      historyData: {
        data: [
          100, 1900, 200, 600, 8700, 100, 900, 200, 600, 870, 8700, 100, 900,
          200, 600, 870, 1200, 700, 300, 500,
        ],
        time: [
          "2012-8-1",
          "2012-8-2",
          "2012-8-3",
          "2012-8-4",
          "2012-8-5",
          "2012-8-6",
          "2012-8-7",
          "2012-8-8",
          "2012-8-9",
          "2012-8-10",
          "2012-8-11",
          "2012-8-12",
          "2012-8-13",
          "2012-8-14",
          "2012-8-15",
          "2012-8-16",
          "2012-8-17",
          "2012-8-18",
          "2012-8-19",
          "2012-8-20",
        ],
        name: "",
      },
    });
    return {
      ...toRefs(state),
    };
  },
};
</script>
<style lang="scss" scoped>
.analysis_content {
  display: flex;
  flex-direction: column;
  padding: 0 10px;
  height: calc(100% - 56px);
  .top {
    .box {
      display: flex;
      .item {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        border: 1px solid #363b3d;
        padding: 30px 0;
        margin: 10px;

        .name {
          font-size: 18px;
          font-family: "PingFangSC-Medium", "PingFang SC";
          font-weight: 500;
          margin-bottom: 20px;
        }
        .day {
          background: linear-gradient(180deg, #ffffff 0%, #ed7327 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
        .week {
          background: linear-gradient(180deg, #ffffff 0%, #e5950d 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
        .month {
          background: linear-gradient(180deg, #ffffff 0%, #27edbb 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
        .year {
          background: linear-gradient(180deg, #ffffff 0%, #27a6ed 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
        .num {
          width: 78%;
          height: 50px;
          line-height: 50px;
          background: rgba(255, 255, 255, 0.05);
          text-align: center;
          font-size: 25px;
          font-family: "DINAlternate-Bold", "DINAlternate";
          font-weight: bold;
          color: #3de9fa;
        }
      }
    }
  }
  .chart {
    flex: 1;
    margin-top: 20px;
  }
}
</style>