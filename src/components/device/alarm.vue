<template>
<!-- 报警记录 -->
<div class="content">
    <el-form :inline="true" class="search_box form_inline" size="small">
        <el-form-item label="处理状态">
            <el-select placeholder="请选择" v-model="status">
                <el-option label="已处理" :value="true"></el-option>
                <el-option label="未处理" :value="false"></el-option>
            </el-select>
        </el-form-item>
        <el-form-item label="报警时间">
            <el-date-picker v-model="date" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
        </el-form-item>
        <el-form-item>
            <el-button class="searchBtn" type="text" @click="getHistroyRecordList">
                查询
            </el-button>
        </el-form-item>
    </el-form>

    <el-table :data="list" height="calc(100% - 100px)" fit>
        <template #empty>
            <no-data />
        </template>
        <el-table-column prop="alarmDesc" label="报警描述" align="center"></el-table-column>
        <el-table-column prop="alarmLevel" label="报警级别" align="center">
            <template #default="scope">
                <span>{{ getLevelName(scope.row) }}</span>
            </template>
        </el-table-column>
        <el-table-column prop="alarmSource" label="报警源" align="center">
            <template #default="scope">
                <span>{{ getSourceName(scope.row) }}</span>
            </template>
        </el-table-column>
        <el-table-column prop="alarmVariable" label="报警变量" align="center"></el-table-column>
        <el-table-column prop="deviceName" label="报警设备" align="center"></el-table-column>
        <el-table-column prop="standardName" label="报警指标" align="center"></el-table-column>
        <el-table-column prop="createTime" label="创建时间" align="center"></el-table-column>
    </el-table>

    <div class="page center">
        <el-pagination prev-icon="CaretLeft" next-icon="CaretRight" :page-size="size" :current-page="page" layout="total,prev, pager, next" @current-change="handleCurrentChange" :total="total">
        </el-pagination>
    </div>
</div>
</template>

<script>
import dayjs from 'dayjs'
import {
    getCookie
} from '@/utils/cookie'
import {
    reactive,
    toRefs,
    onMounted,
    getCurrentInstance,
    computed,
    watch
} from 'vue'
import {
    useStore
} from 'vuex'

export default {
    name: 'electric-alarm',
    props: ['deviceId'],
    components: {

    },
    setup(props) {
        const {
            proxy
        } = getCurrentInstance()
        const store = useStore()
        const state = reactive({
            list: [],
            page: 1,
            size: 10,
            total: 0,
            status: true,
            date: [],
            sources: [],
            levels: [],
            tableHeight: window.innerHeight * 0.60,
      
        })
        const projectId = computed(() => {
            return store.state.user.projectId || getCookie('gh_projectId')
        })
        watch(projectId, (val) => {
            if (val) {
                getSourcesPage()
                getLevelPage()
            }

        })
        onMounted(() => {
            getSourcesPage()
            getLevelPage()
            state.date.push(dayjs().format('YYYY-MM-DD 00:00:00'))
            state.date.push(dayjs().format('YYYY-MM-DD 23:59:59'))
        })
        const getSourcesPage = () => {
            proxy.$api.getDicUtil({
                projectId: getCookie("gh_projectId"),
                dicCode: 'alarm_type',
            }).then((res) => {
                state.sources = res.data
            })
        }
        const getLevelPage = () => {
            proxy.$api.getDicUtil({
                projectId: getCookie("gh_projectId"),
                dicCode: 'alarm_level',
            }).then((res) => {
                state.levels = res.data
            })
        }
        const getHistroyRecordList = () => {
            proxy.$api.getHistroyRecord({
                page: state.page,
                size: state.size,
                bt: typeof state.date[0] == 'string' ?
                    state.date[0] : dayjs(state.date[0]).format('YYYY-MM-DD 00:00:00'),
                et: typeof state.date[1] == 'string' ?
                    state.date[1] : dayjs(state.date[1]).format('YYYY-MM-DD 23:59:59'),
                status: state.status,
                deviceId: props.deviceId,
            }).then((res) => {
                state.total = res.total
                state.list = res.data
            })
        }
        const handleCurrentChange = (page) => {
            state.page = page
            getHistroyRecordList()
        }
        const getSourceName = (data) => {
            let name = ''
            state.sources.forEach((d) => {
                if (d.tagValue == data.alarmSource) {
                    name = d.tagName
                    return false
                }
            })
            return name
        }
        const getLevelName = (data) => {
            let name = ''
            state.levels.forEach((d) => {
                if (d.tagValue == data.alarmLevel) {
                    name = d.tagName
                    return false
                }
            })
            return name
        }
        return {
            ...toRefs(state),
            projectId,
            getHistroyRecordList,
            handleCurrentChange,
            getSourceName,
            getLevelName,
            getSourcesPage,
            getLevelPage
        }
    },
}
</script>

<style lang="scss" scoped>
.content {
    padding: 0 10px;
    height: calc(100% - 56px);
}
</style>
