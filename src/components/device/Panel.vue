<template>
<div class="panel">
    <div class="title">{{ title }}</div>
    <slot />
</div>
</template>

<script>
import {
    defineComponent
} from 'vue'
export default defineComponent({
    props: {
        title: {
            type: String,
            default: () => {
                return '智慧控制'
            },
        },
    },
})
</script>

<style lang="scss" scoped>
.panel {
    display: flex;
    align-items: center;
    height: 36px;
    line-height: 36px;
    background: url("./img/title.png") no-repeat;
    background-size: 100% 100%;

    width: 100%;

    .title {
        padding-left: 30px;
        padding-bottom: 15px;
        font-size: 16px;
        font-family: "BEBAS";
        font-weight: normal;
        font-style: italic;
        color: white;

    }
}
</style>
