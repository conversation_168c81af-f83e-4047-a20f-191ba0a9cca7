<template>
  <div class="content">
    <el-form :inline="true" class="search_box form_inline" size="small">
      <el-form-item label="操作人">
        <el-select placeholder="请选择" v-model="userId">
          <el-option v-for="item in users" :key="item.id" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="时间">
        <el-date-picker v-model="date" type="datetimerange" range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期"></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button class="searchBtn" type="text" @click="search">查询</el-button>
      </el-form-item>
    </el-form>
    <div class="table">
      <el-table :data="list" :height="tableHeight"  fit>
        <template #empty>
          <no-data />
        </template>
        <el-table-column prop="logTime" label="时间" align="center"></el-table-column>
        <el-table-column prop="standardName" label="设备指标" align="center"></el-table-column>
        <el-table-column prop="content" label="记录内容" align="center">
          <template #default="scope">
            <span>
              数值从{{ scope.row.oldValue }}改变为{{ scope.row.newValue }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="username" label="操作者" align="center"></el-table-column>
      </el-table>
    </div>
    <div class="page">
      <el-pagination prev-icon="CaretLeft" next-icon="CaretRight" :page-size="size" :current-page="page" layout="total,prev, pager, next" @current-change="handleCurrentChange" :total="total">
      </el-pagination>
    </div>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import {
  getCookie
} from '@/utils/cookie'
import {
  reactive,
  toRefs,
  onMounted,
  getCurrentInstance,
  computed,
  watch
} from 'vue'
import { useStore } from 'vuex'

export default {

  props: ['deviceId'],
  setup () {
    const { proxy } = getCurrentInstance()
    const store = useStore()
    const state = reactive({
      tableHeight: window.innerHeight * 0.60,
      list: [],
      page: 1,
      size: 10,
      total: 0,
      userId: '',
      date: [],
      users: [],
    })
    const projectId = computed(() => {
      return store.state.user.projectId || getCookie('gh_projectId')
    })
    watch(projectId, (val) => {
      if (val) {
        getUserList()
        getRunManualLogList()
      }
    })
    onMounted(() => {
      state.date.push(dayjs().format('YYYY-MM-DD 00:00:00'))
      state.date.push(dayjs().format('YYYY-MM-DD 23:59:59'))
      getUserList()
      getRunManualLogList()
    })
    const getUserList = () => {
      proxy.$api.getUser({
        projectId: getCookie('gh_projectId'),
        status: 1,
      }).then((res) => {
        state.users = res.data
      })
    }
    const getRunManualLogList = () => {
      proxy.$api.getRunManualLog({
        projectId: getCookie('gh_projectId'),
        type: 2,
        bt: dayjs(state.date[0]).format('YYYY-MM-DD HH:mm:ss'),
        et: dayjs(state.date[1]).format('YYYY-MM-DD HH:mm:ss'),
        tag: 1,
        page: state.page,
        size: 10,
      }).then((res) => {
        state.list = res.data
        state.total = res.total
      })
    }
    const search = () => {
      state.page = 1
      getRunManualLogList()
    }
    const handleCurrentChange = (page) => {
      state.page = page
      getRunManualLogList()
    }
    return {
      ...toRefs(state),
      getUserList,
      getRunManualLogList,
      search,
      handleCurrentChange,
      projectId
    }
  },
}
</script>

<style lang="scss" scoped>
.content {
  padding: 0 10px;
  height: calc(100% - 56px);
}
</style>
