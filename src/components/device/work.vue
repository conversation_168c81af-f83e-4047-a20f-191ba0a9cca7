<template>
<!-- 智控记录 -->
<div class="run">
    <el-form :inline="true" class="search_box form_inline" size="small">
        <el-form-item label="处理时间">
            <el-date-picker v-model="date" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
        </el-form-item>
        <el-form-item>
            <el-button class="searchBtn" type="text" @click="getProcessLogList">
                查询
            </el-button>
        </el-form-item>
    </el-form>
    <el-table :data="list" height="calc(100% - 100px)" fit>
        <template #empty>
            <no-data />
        </template>
        <el-table-column prop="deviceName" label="维保设备" align="center"></el-table-column>
        <el-table-column prop="createTime" label="维保时间" align="center"></el-table-column>
        <el-table-column prop="creatorName" label="维保人员" align="center"></el-table-column>
        <el-table-column prop="description" label="维保内容" align="center"></el-table-column>
    </el-table>
    <div class="page center">
        <el-pagination prev-icon="CaretLeft" next-icon="CaretRight" :page-size="size" :current-page="page" layout="total,prev, pager, next" @current-change="handleCurrentChange" :total="total">
        </el-pagination>
    </div>
</div>
</template>

<script>
import dayjs from 'dayjs'
import {
    getCookie
} from '@/utils/cookie'
import {
    reactive,
    toRefs,
    onMounted,
    getCurrentInstance,
    computed,
    watch
} from 'vue'
import {
    useStore
} from 'vuex'

export default {

    props: ['deviceId'],

    setup(props) {
        const {
            proxy
        } = getCurrentInstance()
        const store = useStore()
        const state = reactive({
            tableHeight: window.innerHeight * 0.60,
            list: [],
            page: 1,
            size: 10,
            total: 0,
            status: '',
            date: [],
        })
        onMounted(() => {
            state.date.push(dayjs().format('YYYY-MM-DD 00:00:00'))
            state.date.push(dayjs().format('YYYY-MM-DD 23:59:59'))
            getProcessLogList()
        })
        const projectId = computed(() => {
            return store.state.user.projectId || getCookie('gh_projectId')
        })
        watch(projectId, (val) => {
            if (val) {
                getProcessLogList()
            }
        })
        const getProcessLogList = () => {
            proxy.$api.getProcessLog({
                projectId: getCookie("gh_projectId"),
                page: state.page,
                size: state.size,
                deviceId: props.deviceId,
                type: 3,
                bt: typeof state.date[0] == 'string' ?
                    state.date[0] : dayjs(state.date[0]).format('YYYY-MM-DD HH:mm:ss'),
                et: typeof state.date[1] == 'string' ?
                    state.date[1] : dayjs(state.date[1]).format('YYYY-MM-DD HH:mm:ss'),
            }).then((res) => {
                state.list = res.data
                state.total = res.total
            })
        }

        const handleCurrentChange = (page) => {
            state.page = page
            getProcessLogList()
        }
        return {
            ...toRefs(state),
            getProcessLogList,
            handleCurrentChange,
            projectId
        }
    }
}
</script>
