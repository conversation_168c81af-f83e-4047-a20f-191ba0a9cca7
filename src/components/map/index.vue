<template>
    <div class="map_wrapper">
        <div class="allmap" ref="allmap"></div>
        <div ref="panel" v-if="data" class="panel">
            <div class="header">
                <div>{{ data.name }}</div>
                <div class="cursor" @click="hidePanel">
                    <i class="iconfont iconchahao"></i>
                </div>
            </div>
            <div v-for="t in data.input" :key="t.id" class="panel_list">
                <span class="standardName">{{ t.standardName }}</span>
                <div class="flex">{{ realData[t.id] }} {{ getUnitName(t.unit) }}</div>
            </div>
            <div v-for="t in data.string_out" :key="t.id" class="panel_list">
                <span class="standardName">{{ t.standardName }}</span>
                <el-slider v-model="realData[t.id]" @change="changeSlider($event, t)" class="slider" :min="parseInt(t.min)"
                    :max="parseInt(t.max)"></el-slider>
            </div>
            <div v-for="t in data.num_out" :key="t.id" class="panel_list">
                <span class="standardName">{{ t.standardName }}</span>
                <div class="flex">
                    <div>
                        <i class="iconfont icontingzhi2-copy" @click="changeSwitch(t.variable, 1)"></i>
                    </div>
                    <div>
                        <i class="iconfont iconbofang2" @click="changeSwitch(t.variable, 0)"></i>
                    </div>
                </div>
            </div>
            <div v-for="(e, index) in data.enums" :key="index" class="panel_list">
                <div class="standardName">{{ e[0].standardName }}</div>
                <div>
                    <el-dropdown trigger="click" @command="changeSeason">
                        <span class="el-dropdown-link">
                            {{ getName(e) }}
                            <i class="el-icon-arrow-down el-icon--right"></i>
                        </span>
                        <template #dropdown>
                            <el-dropdown-menu>
                                <el-dropdown-item v-for="(em, mj) in e" :command="em" :key="mj">{{ em.name
                                }}</el-dropdown-item>
                            </el-dropdown-menu>
                        </template>
                    </el-dropdown>
                </div>
            </div>
        </div>
        <!-- 自定义标签 -->
        <div :ref="item.configId" :id="item.configId" v-for="item in markers" :key="item.configId"
            style="position: absolute" @click.prevent="clickMarker(item)">
            <span style="font-size: 32px" :id="item.configId + '_span'" :style="{ color: item.iconColor }"
                :class="item.icon"></span>
        </div>

        <el-cctv ref="camRef" :url="url" :visiable="visiable" @closeView="closeView"></el-cctv>
        <div class="device-type">
            <el-card class="box-card">
                <div class="cursor" @click="changeType(1)">2D平面</div>
                <div class="cursor earth" @click="changeType(2)">卫星面</div>
            </el-card>
        </div>
    </div>
</template>

<script lang="ts">
import {
    getCookie
} from '@/utils/cookie';
import socket from '@/utils/socket'
import CustomMarker from './CustomMakerTS'
import GisConfig from '@/model/GISConfig'
import {
    StringInput,
    NumOut,
    StringOut,
    SubData,
    LiveDataResponse,
    Subenum,
    SubSendData
} from '@/model/TS_Type'
import {
    defineComponent,
    onMounted,
    reactive,
    ref,
    toRefs,
    nextTick,
    inject,
    onBeforeUnmount,
    computed,
    watch,
    getCurrentInstance
} from 'vue';

import cctv from '@/components/cctv/src/main.vue'

import {
    Emitter
} from 'mitt'
import {
    useStore
} from 'vuex';

export default defineComponent({
    components: {
        'el-cctv': cctv,
    },
    sockets: {
        live(res: string) {
            this.subscribeData(res)
        },
        onVarsChangedCallback(res: string) {
            this.subscribeData(res)
        },
    },

    setup(props) {
        const {
            proxy
        }: any = getCurrentInstance()
        const emitter = inject('mitt') as Emitter
        const allmap = ref<HTMLElement>();
        const panel = ref<HTMLElement>();
        const store = useStore()
        const coordinate = computed(() => {
            return store.state.area.coordinate
        })
        watch(coordinate, (val) => {
            if (val) {
                initMap()
            }
        })
        let map = {} as BMapGL.Map;
        let mapv = {} as mapvgl.View;
        const state = reactive({
            panelData: {},
            opts: {
                width: 220,
                title: '设备信息',
                enableMessage: true
            },
            configMap: new Map(),
            unit: [] as {
                tagValue: string,
                tagName: string
            }[],
            markers: [] as GisConfig[],
            url: '',
            visiable: false,
            realData: [] as {
                [prop: string]: any
            },
            sockets: null,
            data: {} as SubData, //面板数据
            polygons: new Map(),
            interval: null as any,
            types: new Map(),
            isIndeterminate: true,
            checkAll: false,
            subs: [] as SubSendData[],
            lays: [],
            camRef: {} as typeof cctv,
        });
        state.sockets = inject('socket') as any;
        onMounted(() => {
            if (allmap.value) {
                map = new BMapGL.Map(allmap.value);
            }
            map.setMapStyleV2({
                styleId: '9ccb9db8a42508808ddbdf60b4ed9ae2'
            });
            map.addEventListener('tilesloaded', function () {
                getGisConfig(true, true);
                handleCheckAllChange(true);
            });
            map.enableScrollWheelZoom(); // 开启鼠标滚轮缩放
            map.disableDoubleClickZoom();

            initMap();
            getUnit();

            state.interval = setInterval(() => {
                if (state.polygons.size > 0) {
                    state.polygons.forEach(d => {
                        let config = state.configMap.get(d._config.id);
                        map.removeOverlay(d);
                        if (config.dataType != 'polyline') {
                            if (config.alarmColor == d._config.fillColor) {
                                d._config.fillColor = config.fillColor;
                            } else {
                                d._config.fillColor = config.alarmColor;
                            }
                        } else {
                            if (config.alarmColor == d._config.strokeColor) {
                                d._config.strokeColor = config.borderColor;
                            } else {
                                d._config.strokeColor = config.alarmColor;
                            }
                        }

                        map.addOverlay(d);
                    })
                }
            }, 500)
        });
        const menu = computed(() => {
            let menu = getCookie('activeMenus');
            return (store.state as any).menu.activeMenus || (menu ? JSON.parse(menu) : '')
        });

        const projectId = computed(() => {
            return store.state.user.projectId || getCookie('gh_projectId')
        })
        watch(projectId, (val) => {
            if (val) getUnit()
        })

        watch(menu, (val) => {
            state.types.forEach((d, k) => {
                if (val.deviceType.includes(d.id)) {
                    d.checked = true;
                    handleCheckedChange(true, d.id);
                } else {
                    d.checked = false;
                    handleCheckedChange(false, d.id);
                }
            })
        })

        emitter.off('changeCenter', () => { });
        emitter.on('changeCenter', (points) => {
            if (points) {
                let center = JSON.parse(points);
                if (center && center.length > 0) {
                    //@ts-ignore
                    map.panTo(new BMapGL.Point(center[0].lng, center[0].lat), 1000);
                    //    map.setZoom(15);
                }
            }
        });

        const initMap = () => {
            if (map) {

                // @ts-ignore
                //  map.centerAndZoom('上海市', 11); // 初始化地图,设置中心点坐标和地图级别
                if (coordinate.value) {
                    let center = coordinate.value.split(',');
                    map.centerAndZoom(new BMapGL.Point(center[0], center[1]), 11); // 初始化地图,设置中心点坐标和地图级别
                }
            }

        };

        const getUnitName = (val: string) => {
            let name = '';
            state.unit.forEach((u) => {
                if (u.tagValue == val) {
                    name = u.tagName;
                }
            });
            return name;
        };
        const getUnit = () => {
            proxy.$api.getDicUtil({
                dicCode: 'unit',
                projectId: getCookie("gh_projectId")
            }).then((res: {
                data: {
                    tagValue: string; tagName: string;
                }[];
            }) => {
                state.unit = res.data;
            });
        };
        const getGisConfig = (all: boolean, reload: boolean) => {
            proxy.$api.getConfig({
                projectId: getCookie("gh_projectId")
            }).then((res: any) => {
                state.subs = [] as SubSendData[];
                let gisData = res.data as GisConfig[];
                gisData.forEach(d => {
                    state.configMap.set(d.configId, d);
                    if (all) {
                        resolve(d)
                    } else {
                        if (d.deviceType) {
                            if (menu.value && menu.value.deviceType && menu.value.deviceType.includes(d.deviceType)) {
                                resolve(d)
                            }
                        }

                    }

                    if (reload) {
                        if (d.deviceType) {
                            state.types.set('t_' + d.deviceType, {
                                name: d.deviceTypeName,
                                id: d.deviceType,
                                checked: (menu.value && menu.value.deviceType && menu.value.deviceType.includes(d.deviceType)) ? true : false,
                            });
                        }
                    }

                })
                // resolve(res.data, all);
            });
        };
        const resolve = (d: GisConfig) => {
            // data.forEach(d => {
            if (d.alarmStd && d.conditionName && d.conditionValue) {
                for (let i = 0; i < d.stds.length; i++) {
                    if (d.stds[i].stdId == d.alarmStd) {
                        if (d.stds[i].variable) {
                            let v = d.stds[i].variable.split(':');
                            let sub = {
                                id: d.configId,
                                iosvrKey: v[0],
                                chlKey: v[1],
                                ctrlKey: v[2],
                                varKey: v[3],
                                realTime: false,
                            };
                            state.subs.push(sub);
                            socket.subscribe(state.sockets, 'real', 'gisalarm', [sub]);
                            break;
                        }
                    }
                }
            }
            if (d.drawType == 'marker') {
                state.markers.push(d);
                nextTick(() => {
                    let marker = new CustomMarker(JSON.parse(d.points)[0], this, 'marker', d.configId);
                    //@ts-ignore
                    map.addOverlay(marker);

                });
            } else if (d.drawType == "circle") {
                var circle = new BMapGL.Circle(JSON.parse(d.points)[0], d.radius, {
                    id: d.configId,
                    strokeColor: d.borderColor, // 边线颜色
                    fillColor: d.fillColor, // 填充颜色。当参数为空时，圆形没有填充颜色
                    strokeWeight: 2, // 边线宽度，以像素为单位
                    strokeOpacity: 1, // 边线透明度，取值范围0-1
                    fillOpacity: 0.2 // 填充透明度，取值范围0-1
                });
                // state.configMap.set(d.configId, d);
                map.addOverlay(circle);
            } else if (d.drawType == "polygon" || d.drawType == "polyline" || d.drawType == "rectangle") {
                let points = JSON.parse(d.points) as {
                    lng: number,
                    lat: number
                }[];
                let GisPoints = [] as BMapGL.Point[];
                points.forEach(p => {
                    GisPoints.push(new BMapGL.Point(p.lng, p.lat));
                });
                let polygon;
                if (d.drawType == "polygon" || d.drawType == "rectangle") {
                    polygon = new BMapGL.Polygon(GisPoints, {
                        id: d.configId,
                        strokeColor: d.borderColor, // 边线颜色
                        fillColor: d.fillColor, // 填充颜色。当参数为空时，圆形没有填充颜色
                        strokeWeight: 2, // 边线宽度，以像素为单位
                        strokeOpacity: 1, // 边线透明度，取值范围0-1
                        fillOpacity: 0.2 // 填充透明度，取值范围0-1
                    });
                } else if (d.drawType == "polyline") {
                    polygon = new BMapGL.Polyline(GisPoints, {
                        id: d.configId,
                        strokeColor: d.borderColor, // 边线颜色
                        // fillColor: d.fillColor, // 填充颜色。当参数为空时，圆形没有填充颜色
                        strokeWeight: 2, // 边线宽度，以像素为单位
                        strokeOpacity: 1, // 边线透明度，取值范围0-1
                        // fillOpacity: 0.2 // 填充透明度，取值范围0-1
                    });
                }
                // state.configMap.set(d.configId, d);
                //@ts-ignore
                map.addOverlay(polygon);
            }

            // });
        };

        const clickMarker = (item: GisConfig) => {
            if (item.actionType == 1) {
                if (item.server && item.cam) {
                    // state.visiable = true
                    // let servers = item.server.split('_')
                    // let cams = item.cam.split('_')
                    // state.url = `${servers[0]}|${servers[2]}|${servers[3]}|${cams[0]}|${cams[1]}|${cams[1]}`

                    let servers = item.server.split('_')
                    let cams = item.cam.split('_')
                    // cams参数格式 ${d.camToken}_${d.strName}_${d.serverType}_${d.strSrcIpAddress}_${d.strUser}_${d.strPasswd}_${d.profileToken}
                    //state.url = `${servers[0]}|${servers[2]}|${servers[3]}|${cams[0]}|${cams[1]}|${cams[1]}|${cams[2]}|${cams[3]}|${cams[4]}|${cams[5]}|${cams[6]}`
                    state.camRef.opened({
                        server: servers[2], //流媒体ip --h5参数
                        port: servers[3], //流媒体port ---h5参数
                        token: cams[0], //zlm和h5平台通用
                        name: cams[1], //摄像机名称 --通用
                        serverType: cams[2], //平台类型 1 h5 2--zlm平台
                        ip: cams[3], //摄像机ip   zlm onvif
                        username: cams[4], //摄像机用户名 zlm onvif
                        password: cams[5], //摄像机密码 zlm onvif
                        profileToken: cams[6], //摄像机 onvif profileToken zlm onvif
                        ptzEnable: cams[7]
                    });
                }
            } else if (item.actionType == 2) {
                socket.unsubscribe(state.sockets, 'gis', 'real')
                let ws = [] as SubSendData[];
                // state.list = [];
                state.data = {} as SubData
                state.data.name = item.deviceName;
                state.data.state = [] //状态
                state.data.enums = [] //枚举
                state.data.string_out = [] //模拟输出
                state.data.input = [] //模拟字输入
                state.data.num_out = [] as NumOut[] //数字输出
                item.stds.forEach((d, i) => {
                    if (d.variable) {
                        if (d.dataType == 'num_input') {
                            state.data.state.push({
                                name: d.stdName,
                                id: 's' + i,
                            })
                            let v = d.variable.split(':')
                            let item = {
                                id: 's' + i,
                                iosvrKey: v[0],
                                chlKey: v[1],
                                ctrlKey: v[2],
                                varKey: v[3],
                                realTime: false,
                            }
                            state.realData = Object.assign({}, state.realData, {
                                ['s' + i]: 0,
                            })
                            ws.push(item)
                        } else if (d.dataType == 'enum') {
                            if (d.paramValue) {
                                let enums = d.paramValue.split(';')
                                let e = [] as Subenum[]
                                enums.forEach((d1) => {
                                    e.push({
                                        id: 's_' + i,
                                        variable: d.variable,
                                        name: d1.split('=')[0],
                                        value: d1.split('=')[1],
                                        type: 'enum',
                                        icon: d.icon,
                                        standardName: d.stdName,
                                    })
                                })
                                let v = d.variable.split(':')
                                let item1 = {
                                    id: 's_' + i,
                                    iosvrKey: v[0],
                                    chlKey: v[1],
                                    ctrlKey: v[2],
                                    varKey: v[3],
                                    realTime: false,
                                }
                                state.realData = Object.assign({}, state.realData, {
                                    ['s_' + i]: 0,
                                })
                                ws.push(item1)

                                state.data.enums.push(e)
                            }
                        } else if (d.dataType == 'string_input') {
                            let item1 = {} as StringInput;
                            item1.standardName = d.stdName;
                            item1.value = d.paramValue;
                            item1.icon = d.icon;
                            item1.unit = d.unit;
                            item1.id = 's' + i;
                            //数据订阅
                            let v = d.variable.split(':')
                            let item2 = {
                                id: 's' + i,
                                iosvrKey: v[0],
                                chlKey: v[1],
                                ctrlKey: v[2],
                                varKey: v[3],
                                realTime: false,
                            }
                            state.realData = Object.assign({}, state.realData, {
                                ['s' + i]: 0,
                            })
                            ws.push(item2)
                            state.data.input.push(item1)
                        } else if (d.dataType == 'num_out') {
                            let item = {} as NumOut
                            item.standardName = d.stdName
                            item.value = d.paramValue;
                            item.icon = d.icon;
                            item.variable = d.variable;
                            item.id = 's' + i;
                            //数据订阅
                            let v = d.variable.split(':');
                            let item1 = {
                                id: 's' + i,
                                iosvrKey: v[0],
                                chlKey: v[1],
                                ctrlKey: v[2],
                                varKey: v[3],
                                realTime: false,
                            }
                            state.realData = Object.assign({}, state.realData, {
                                ['s' + i]: 0,
                            })
                            ws.push(item1)
                            state.data.num_out.push(item)
                        } else if (d.dataType == 'string_out') {
                            let item = {} as StringOut
                            item.standardName = d.stdName
                            item.variable = d.variable

                            item.min = d.min || 0
                            item.max = d.max || 100

                            item.icon = d.icon

                            item.id = 's' + i;

                            //数据订阅
                            let v = d.variable.split(':')

                            let item1 = {
                                id: 's' + i,
                                iosvrKey: v[0],
                                chlKey: v[1],
                                ctrlKey: v[2],
                                varKey: v[3],
                                realTime: false,
                            }
                            state.realData = Object.assign({}, state.realData, {
                                ['s' + i]: 0,
                            })
                            ws.push(item1)
                            state.data.string_out.push(item)
                        }
                    }
                });
                if (ws.length > 0) {
                    nextTick(() => {
                        socket.subscribe(state.sockets, 'real', 'gis', ws)
                    })
                };
                nextTick(() => {
                    var pixel = map.pointToOverlayPixel(JSON.parse(item.points)[0]);
                    if (panel.value !== undefined) {
                        panel.value.style.left = pixel.x - (panel.value.clientWidth) / 2 + 'px';;
                        panel.value.style.top = pixel.y - panel.value.clientHeight - 50 + 'px';
                    }

                });

            } else if (item.actionType == 3) {
                emitter.emit("changeMode", {
                    mode: 1,
                    model: item.model
                })
            } else if (item.actionType == 4) {
                emitter.emit("changeMode", {
                    mode: 0,
                    url: item.vr
                })
            }

        };
        const closeView = () => {
            state.visiable = false
        };
        const subscribeData = (res: string) => {
            if (res) {
                let data = JSON.parse(res) as LiveDataResponse
                if (data.batchDefinitionId == 'real' && data.clientId == 'gis') {
                    data.data.forEach((d) => {
                        state.realData[d.id] = Number(d.value)
                    })
                } else if (data.batchDefinitionId == 'real' && data.clientId == 'gisalarm') {
                    data.data.forEach((d) => {
                        analyAlarm(d.id, d.value);
                    })
                }
            }
        };
        const analyAlarm = (id: string, value: (number | string)) => {
            let config = state.configMap.get(id);
            if (config) {
                if (config.conditionName && config.conditionValue) {
                    var condition;
                    if (config.conditionName == 'between') {
                        condition = config.conditionValue + "<=" + value + " && " + config.conditionValue + "<=" + value
                    } else {
                        condition = value + config.conditionName + config.conditionValue;
                    }
                    let success = eval(condition);
                    if (config.drawType == 'marker') {
                        let span = document.getElementById(id + '_span');
                        if (span) {

                            if (mapv.getAllLayers) {
                                state.lays = mapv.getAllLayers();
                            } else {
                                mapv = new mapvgl.View({
                                    map: map
                                });
                            }
                            if (success) {
                                if (!span.className.includes('marker-active')) {
                                    span.className = span.className + ' marker-active';
                                }

                                var data = [{
                                    geometry: {
                                        type: 'Point',
                                        coordinates: [JSON.parse(config.points)[0].lng, JSON.parse(config.points)[0].lat]
                                    },
                                    properties: {
                                        count: 100
                                    },
                                    id: config.configId
                                }];

                                var fanLayer = new mapvgl.FanLayer({
                                    color: '#c90e0e',
                                    data: data,
                                    size: function () {
                                        return 10000;
                                    }
                                });
                                let exist = false;
                                for (let i = 0; i < state.lays.length; i++) {
                                    //@ts-ignore
                                    if (state.lays[i].data && state.lays[i].data[0].id == config.configId) {
                                        exist = true;
                                        break;
                                    }
                                }
                                if (!exist) {
                                    mapv.addLayer(fanLayer);
                                }

                            } else {
                                if (span.className.includes('marker-active')) {
                                    span.className = span.className.replaceAll("marker-active", '')
                                }
                                // mapv.removeAllLayers();
                                for (let i = 0; i < mapv.getAllLayers().length; i++) {
                                    // let lays= mapv.getAllThreeLayers();
                                    // lays.forEach(l=>{
                                    //     mapv.removeLayer(l);
                                    // })
                                    let lay = mapv.getAllLayers()[i];

                                    if (lay.data && lay.data[0].id == config.configId) {
                                        mapv.removeLayer(lay);
                                        mapv.removeLayer(lay.threeLayer);
                                        // break;
                                    }
                                }
                            }
                        }
                    } else if (config.drawType == "polygon" || config.drawType == "rectangle" || config.drawType == "circle" || config.drawType == "polygon") {
                        let lays = map.getOverlays();
                        let overlay;
                        for (let i = 0; i < lays.length; i++) {
                            if (lays[i]._config.id == id) {
                                overlay = lays[i];
                                break;
                            }
                        }
                        if (success) {
                            state.polygons.set(id, overlay);
                        } else {
                            state.polygons.delete(id);
                        }
                    }

                }
            }
        }
        const getName = (e: Subenum[]) => {
            let name = '季节'
            if (e && e.length > 0) {
                let val = state.realData[e[0].id]
                for (let i = 0; i < e.length; i++) {
                    if (e[i].value == val) {
                        name = e[i].name
                        break
                    }
                }
            }
            return name
        };
        const changeSeason = (data: Subenum) => {
            socket.writeValue(
                state.sockets,
                data.variable,
                data.value,
                'ba',
                (state.sockets as any).id,
                getCookie("gh_projectId"),
                getCookie("gh_id")
            )
        };
        const hidePanel = () => {
            socket.unsubscribe(state.sockets, 'gis', 'real');
            if (panel.value) {
                panel.value.style.left = -1000000 + 'px';
            }

        };
        const changeSlider = (val: (string | number), data: StringOut) => {
            socket.writeValue(
                state.sockets,
                data.variable,
                val,
                'ba',
                (state.sockets as any).id,
                getCookie("gh_projectId"),
                getCookie("gh_id")
            )
        };
        const changeSwitch = (variable: string, value: (string | number)) => {
            socket.writeValue(state.sockets, variable, value, 'ba', (state.sockets as any).id, getCookie("gh_projectId"),
                getCookie("gh_id"))
        };
        const handleCheckAllChange = (val: boolean) => {
            state.isIndeterminate = false;
            state.types.forEach((v, k) => {
                v.checked = val;
            });
            map.clearOverlays();
            socket.unsubscribe(state.sockets, 'gis', 'real');
            socket.unsubscribe(state.sockets, 'gisalarm', 'real');
            state.polygons = new Map();
            if (mapv && mapv.removeAllLayers) {
                mapv.removeAllLayers();
            }
            if (val) {
                getGisConfig(true, false);
            }

        };
        const handleCheckedChange = (value: (string | number | boolean), id: string) => {
            let count = [];
            state.types.forEach((d, k) => {
                if (d.checked) {
                    count.push(d);
                }
            })
            if (count.length == 0) { //全取消
                state.isIndeterminate = false;
                state.checkAll = false;
                handleCheckAllChange(false);
            } else if (count.length < state.types.size) {
                state.isIndeterminate = true;
                state.checkAll = false;
                map.getOverlays().forEach(overlay => {
                    let config;
                    if (overlay.type == 'marker') {
                        config = state.configMap.get(overlay.id);
                    } else {
                        config = state.configMap.get(overlay._config.id);
                    }
                    if (config.deviceType == id) {
                        if (!value) //取消选中
                        {
                            map.removeOverlay(overlay);
                            if (overlay._config && overlay._config.id) {
                                state.polygons.delete(overlay._config.id);
                            }
                            socket.unsubscribe(state.sockets, 'gisalarm', 'real');
                            state.subs.forEach(s => {
                                //排除取消的overlay
                                let config1;
                                if (overlay.type == 'marker') {
                                    config1 = state.configMap.get(overlay.id);
                                } else {
                                    config1 = state.configMap.get(overlay._config.id);
                                }
                                if (config1.deviceType != id) {
                                    socket.subscribe(state.sockets, 'real', 'gisalarm', [s]);
                                }
                            })
                        } else //选中
                        {
                            state.configMap.forEach((d, k) => {
                                if (d.deviceType == id) {
                                    resolve(d);
                                }
                            })
                        }
                    }
                });

            } else if (count.length == state.types.size) //全选中
            {
                //会走上面全选事件
                state.isIndeterminate = false;
                state.checkAll = true;
                handleCheckAllChange(true);
            }

        }
        onBeforeUnmount(() => {
            socket.unsubscribe(state.sockets, 'gis', 'real');
            socket.unsubscribe(state.sockets, 'gisalarm', 'real');
            if (state.interval) {
                clearInterval(state.interval);
            }
            // if (mapv) {
            //     mapv.destroy()
            // }
        });
        const changeType = (val: number) => {
            if (val == 2) {
                map.setMapType(BMAP_EARTH_MAP);
            } else if (val == 1) {
                map.setMapType(BMAP_NORMAL_MAP);
            }

        }

        return {
            ...toRefs(state),
            allmap,
            panel,
            projectId,
            getUnit,
            getUnitName,
            getGisConfig,
            closeView,
            clickMarker,
            subscribeData,
            getName,
            changeSeason,
            hidePanel,
            changeSlider,
            changeSwitch,
            analyAlarm,
            handleCheckAllChange,
            handleCheckedChange,
            changeType
        };
    }
});
</script>

<style lang="scss" scoped>
.map_wrapper {
    height: 100%;
    width: 100%;
    position: absolute;
    left: 0;
    top: 0;
    z-index: 7;
}

.marker-active {
    animation: alarm 1s infinite;
}

.earth {
    margin-top: 15px;
}

.panel {
    background-color: #091822 !important;
    position: absolute;
    z-index: 100;
    padding: 10px;
    border: 1px solid #323a3e;

    .header {
        color: #889cc3;
        font-size: 16px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-bottom: 1px solid #889cc3;
        padding: 5px 0;
        margin-bottom: 10px;
    }

    .panel_list {
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: #889cc3;

        .slider {
            width: 100px;
        }

        .standardName {
            padding: 0 5px;
        }
    }
}

@keyframes alarm {
    from {
        color: red;
    }

    to {
        color: yellow;
    }
}</style>
