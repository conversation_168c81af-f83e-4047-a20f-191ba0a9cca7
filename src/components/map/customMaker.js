class CustomMaker extends BMapGL.Overlay {
  constructor(point, that, type, id) {
    super();
    this._point = point;
    this.that = that;
    this.type = type;
    this.id = id;

  }
  initialize(map) {
    this._map = map;
    map.getPanes().labelPane.appendChild(document.getElementById(this.id));
    this._div = document.getElementById(this.id);
    return document.getElementById(this.id);
  }
  draw() {
    var map = this._map;
    var pixel = map.pointToOverlayPixel(this._point);
    this._div.style.left = pixel.x - 16 + 'px';;
    this._div.style.top = pixel.y - 45 + 'px';
  }
}


export default CustomMaker
