<template>
<div class="video_con">
    <video :id="videoid" v-if="token" />
    <div v-else class="msg">
        <img src="./img/video.png" alt />
        <span>暂无视频~</span>
    </div>
</div>
</template>

<script>
import {
    defineComponent,
    onMounted,
    onUnmounted,
    reactive,
    toRefs,
    nextTick,
    watch,
    inject
} from 'vue'
import {
    H5sPlayerWS
} from '@/lib/h5splayer'
export default defineComponent({
    props: {
        ip: {
            type: String,
            default: () => {
                return ''
            },
        },
        token: {
            type: String,
            default: () => {
                return ''
            },
        },
    },
    setup(props) {
        const emitter = inject('mitt');
        const state = reactive({
            player: null,
            videoid: null,
        })
        watch(() => props.token, val => {
            state.videoid = 'video_' + val;
            playVideo();
        });
        state.videoid = 'video_' + props.token;

        function playVideo() {
            const conf = {
                hlsver: 'v1',
                host: props.ip,
                protocol: window.location.protocol,
                rootpath: '/',
                session: '',
                streamprofile: 'main',
                token: props.token,
                videoid: state.videoid,
            }
            nextTick(() => {
                state.player = new H5sPlayerWS(conf)
                state.player.connect()
            })
        }
        onMounted(() => {
            if (props.token) {
                playVideo()
            }
            emitter.off('stopVideo');
            emitter.on('stopVideo', () => {
                if (state.player) {
                    state.player.disconnect()
                    state.player = null
                }
            });
        })
        onUnmounted(() => {
            if (state.player) {
                state.player.disconnect()
                state.player = null
            }
        })

        return {
            ...toRefs(state),
            playVideo,
        }
    },
})
</script>

<style lang="scss" scoped>
.video_con {
    justify-content: center;
    align-items: center;
    display: flex;
    color: #778897;
    height: 100%;

    .msg {
        display: flex;
        justify-content: center;
        align-items: center;

        img {
            margin-right: 10px;
        }
    }

    video {
        width: 100%;
        height: 100%;
        object-fit: fill;
    }
}
</style>
