<template>
    <el-dialog align-center append-to-body custom-class="cctv-dialog" draggable :title="title" @close="Close" v-model="cvisiable"
        width="853px">
        <div>
            <video v-if="type == 1" @dblclick="full" ref="fullRef" class="h5video" :id="token" autoplay></video>
            <div style="height:480px" v-if="type == 2" ref="fullRef" class="h5video" :id="token" autoplay></div>
        </div>
        <div class="ptz" :style="{ display: ptzEnable ? 'flex' : 'none' }">
            <div class="ptzbtn">
                <div class="up">
                    <div>
                        <el-button @mousedown="PtzAction('up')" @mouseup="PtzAction('stop')" type="primary" size="mini"
                            circle>
                            <i class="iconfont iconshangjiantou"></i>
                        </el-button>
                    </div>
                </div>
                <div class="mid">
                    <div>
                        <el-button @mousedown="PtzAction('left')" @mouseup="PtzAction('stop')" type="primary" size="mini"
                            circle>
                            <i class="iconfont iconzuojiantou"></i>
                        </el-button>
                    </div>
                    <div>
                        <el-button @mousedown="PtzAction('right')" @mouseup="PtzAction('stop')" type="primary" size="mini"
                            circle>
                            <i class="iconfont iconyoujiantou"></i>
                        </el-button>
                    </div>
                </div>
                <div class="down">
                    <div>
                        <el-button @mousedown="PtzAction('down')" @mouseup="PtzAction('stop')" type="primary" size="mini"
                            circle>
                            <i class="iconfont iconxiajiantou"></i>
                        </el-button>
                    </div>
                </div>
            </div>
            <div class="ptzzoom">
                <div>
                    <el-button @mousedown="PtzAction('zoomin')" @mouseup="PtzAction('stop')" type="primary" size="mini"
                        circle>
                        <i class="iconfont iconjian"></i>
                    </el-button>
                </div>
                <div>
                    <el-button @mousedown="PtzAction('zoomout')" @mouseup="PtzAction('stop')" type="primary" size="mini"
                        circle>
                        <i class="iconfont icontianjia"></i>
                    </el-button>
                </div>
            </div>
        </div>
    </el-dialog>
</template>

<script>
import '@/lib/h5splayer'
import {
    H5sPlayerCreate
} from '@/lib/h5splayerhelper'
import {
    ref
} from '@vue/reactivity'
import CryptoJS from 'crypto-js/crypto-js'

import {
    defineComponent,
    getCurrentInstance,
    reactive,
    watch,
    toRefs,
    nextTick
} from 'vue'

export default defineComponent({
    name: 'VariableDiglog',

    props: ['url', 'visiable'],

    setup(props, context) {
        const {
            proxy
        } = getCurrentInstance()
        const fullRef = ref();
        const state = reactive({
            title: 'cam',
            conf: null,
            cvisiable: false,
            token: null,
            jessibuca: null,
            useWCS: false,
            useMSE: false,
            useOffscreen: false,
            type: 1,
            data: null,
            ptzEnable: false,
            session: null,
        });
        watch(() => props.visiable, (val) => {
            state.cvisiable = val;
        });
        const Close = () => {
            state.cvisiable = false;
            state.type = 1;
            if (state.conf) {
                state.conf.disconnect();
                state.conf = null;
            }
            if (state.jessibuca) {
                state.jessibuca.pause();
                state.jessibuca.destroy();
                state.jessibuca = null;
            }
            state.data = null;
        };
        const opened = async (data) => {
            state.cvisiable = true;
            state.data = data;
            if (data) {
                //   server:,//流媒体ip --h5参数
                //   port:,//流媒体port ---h5参数
                //   token:,//zlm和h5平台通用
                //   name:,//摄像机名称 --通用
                //   serverType:,//平台类型 1 h5 2--zlm平台
                //   ip:,//摄像机ip   zlm onvif
                //   username:,//摄像机用户名 zlm onvif
                //   password:,//摄像机密码 zlm onvif
                //   profileToken:,//摄像机 onvif profileToken zlm onvif
                //   ptzEnable:  云台
                state.session = data.session;
                state.token = data.token;
                state.title = data.name; //摄像机名称
                if (data.serverType == 2) { //zlm平台
                    state.type = data.serverType
                } else {
                    state.type = 1;
                }
                await nextTick();
                if (state.type == 2) {
                    state.jessibuca = new window.Jessibuca(
                        Object.assign({
                            container: document.getElementById(state.token),
                            videoBuffer: 0.2, // 缓存时长
                            isResize: false,
                            useWCS: state.useWCS,
                            useMSE: state.useMSE,
                            text: "",
                            // background: "bg.jpg",
                            loadingText: "加载中...",
                            // hasAudio:false,
                            debug: true,
                            supportDblclickFullscreen: true,
                            showBandwidth: false, // 显示网速
                            forceNoOffscreen: state.useOffscreen,
                            isNotMute: true,
                            timeout: 10
                        },)
                    );
                    state.jessibuca.play(`http://${data.server}:${data.port}/live/${data.token}.live.flv?key=${CryptoJS.HmacMD5(data.token, "zlm").toString()}`);
                } else {
                    // if (!state.session)
                    //     await getSession()

                    let conf = {
                        videoid: state.token,
                        protocol: window.location.protocol, //http: or https:
                        host: data.server + ":" + data.port, //localhost:8080
                        rootpath: '/', // '/'
                        token: state.token,
                        hlsver: 'v1', //v1 is for ts, v2 is for fmp4
                        session: "86aa0185-7ca6-44b4-8e1d-8bcdeebd8873" //session got from login  注意现在h5新平台都要登录
                    };
                    state.conf = H5sPlayerCreate(conf);
                    state.conf.connect();
                }
            }
        };
        const PtzAction = (action) => {
            if (state.type == 2) {
                let param = {
                    ip: state.data.ip,
                    username: state.data.username,
                    password: state.data.password,
                    profileToken: state.data.profileToken,
                }
                if (action == "stop") {
                    //onvif协议控制云台停止操作
                    proxy.$api.stopMovePtz(param);
                } else {
                    proxy.$api.movePtz(Object.assign({}, param, {
                        position: action
                    }));
                }
            } else {
                if (props.url) {
                    Ptz(props.url.split('|')[3], action, "http://" + props.url.split('|')[1] + ":" + props.url.split('|')[2], state.session);
                }
            }
        };
        const Ptz = (token, action, host, session) => {
            let ptzcmd = "token=" + token + "&action=" + action + "&speed=0.5";
            proxy.$axios.get(host + "/api/v1/Ptz?" + ptzcmd + "session=" + session).then((result) => {
                console.log(result);
            }).catch((err) => {
                console.log(err);
            });
        };
        const full = () => {
            const element = fullRef.value;
            if (element.requestFullscreen) {
                element.requestFullscreen();
            } else if (element.mozRequestFullScreen) {
                element.mozRequestFullScreen();
            } else if (element.msRequestFullscreen) {
                element.msRequestFullscreen();
            } else if (element.webkitRequestFullscreen) {
                element.webkitRequestFullScreen();
            }
        }
        const getSession = async (ip, port) => {
            const { data } = await proxy.$api.getVideoServer({
                projectId: getCookie("gh_projectId"),
            })
            if (data) {
                state.session = data[0].session || '86aa0185-7ca6-44b4-8e1d-8bcdeebd8873';
            }
        }

        return {
            ...toRefs(state),
            Close,
            opened,
            PtzAction,
            Ptz,
            fullRef,
            full,
            getSession,
        }
    }
})
</script>

<style lang="scss" scoped>
.h5video {
    width: 100%;
    height: 480px;
    position: relative;
}

video {
    width: 100%;
    height: 100%;
    object-fit: fill;
}

.ptz {
    position: absolute;
    bottom: 0;
    left: 0;
    z-index: 6008;
    border: 0 !important;

    div {
        border: 0 !important;
    }

    display: flex;
    align-items: center;

    .ptzbtn {
        display: flex;
        flex-wrap: wrap;
        flex-direction: column;
        align-content: space-between;
        align-items: center;
        margin-right: 20px;

        .mid {
            display: flex;
            justify-content: space-between;

            div:nth-of-type(1) {
                margin-right: 20px;
            }
        }
    }
}
</style>
