<template>
  <div class="history_chart" ref="myEcharts"></div>
</template>

<script>
import { onMounted, ref, inject, nextTick, watch } from "vue";
export default {
  props: ["data"],
  setup (props) {
    const myEcharts = ref(null);
    let echarts = inject("ec"); //引入

    watch(props, (newVal, oldVal) => {
      if (newVal) {
        initChart();
      }
    });
    onMounted(() => {
      nextTick(() => {
        initChart();
      });
    });
    const initChart = () => {
      var myChart = echarts.init(myEcharts.value);
      let politicsFenBu_data = props.data;
      let politicsFenBu_total = 0; //总数
      politicsFenBu_data.forEach((item) => (politicsFenBu_total += item.value));
      let option = {
        color: [
          "#FFEB6D",
          "#81D9FF",
          "#427EDF",
          "#C7DFFF",
          "#FFD29D",
          "#B797FF",
        ],
        title: {
          itemGap: 10,
          text: "总数",
          subtext: politicsFenBu_total,
          left: "30%",
          top: "40%",
          textAlign: "center",
          textStyle: {
            fontWeight: "400",
            fontSize: 16,
            color: "#fff",
          },
          subtextStyle: {
            fontFamily: "Arial",
            fontWeight: "400",
            fontSize: 25,
            color: "#fff",
          },
        },
        tooltip: {
          trigger: "item",
          formatter: "{a} <br/>{b} : {c} 个",
          backgroundColor: "rgba(47, 54, 60, 0.8)",
          borderColor: "#2F363C",
          padding: 15,
          textStyle: {
            color: "#fff",
            fontSize: 14,
          },
        },
        legend: {
          itemGap: 20,
          icon: "circle",
          type: "scroll",
          orient: "vertical",
          right: '20%',
          top: "center",
          textStyle: {
            rich: {
              a: {
                color: "#778897",
                fontSize: 14,
                width: 70,
              },
              b: {
                color: "#999",
                width: 40,
                align: "right",
              },
              c: {
                color: "#C7DFFF",
                width: 60,
                fontSize: 25,
                fontFamily: "'DINAlternate-Bold', 'DINAlternate'",
                align: "right",
              },
            },
          },
          formatter: function (name) {
            let target;
            for (let i = 0; i < politicsFenBu_data.length; i++) {
              if (politicsFenBu_data[i].name == name) {
                target = politicsFenBu_data[i].value;
              }
            }
            return `{a|${name?name:''}}{c|${target?target:''} 个}`;
          },
        },
        series: [
          {
            center: ["30%", "50%"],
            name: "巡检",
            type: "pie",
            radius: ["55%", "70%"],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: "center",
            },
            emphasis: {
              label: {
                formatter: "",
                show: true,
                fontSize: "40",
                fontWeight: "bold",
              },
            },
            data: politicsFenBu_data,
          },
        ],
      };
      myChart.setOption(option); //展示
      myChart.resize(); //刷新画布
      window.addEventListener("resize", () => {
        myChart.resize(); //刷新画布
      });
    };

    return {
      myEcharts,
      initChart,
    };
  },
};
</script>

<style lang="scss">
.history_chart {
  width: 100%;
  height: 100%;
}
</style>
