<template>
    <div class="echart" ref="myEcharts"></div>
    </template>
    
    <script>
    import {
        ref,
        inject,
        onMounted,
        watch
    } from 'vue'
    export default {
        props: {
            title: {
                type: String,
                default: '总量',
            },
            echartData: {
                type: Array,
                defaule: () => {
                    return []
                },
            },
            colors: {
                type: Array,
                default: () => {
                    return []
                },
            },
        },
    
        setup(props) {
            const myEcharts = ref(null)
            let echarts = inject('ec') //引入
            watch(props, (v1, v2) => {
                init();
            });
            const init = () => {
                var myChart = echarts.init(myEcharts.value)
    
                var placeHolderStyle = {
                    normal: {
                        label: {
                            show: false
                        },
                        labelLine: {
                            show: false
                        },
                        color: 'rgba(0, 0, 0, 0)',
                        borderColor: 'rgba(0, 0, 0, 0)',
                        borderWidth: 0
                    }
                };
                let formatNumber = function (num) {
                   return num.toFixed(2);
                }
                let total = props.echartData.reduce((a, b) => {
                    return a + b.value * 1
                }, 0)
    
                var data = [];
                for (var i = 0; i < props.echartData.length; i++) {
                    data.push({
                        value: props.echartData[i].value,
                        name: props.echartData[i].name,
                        itemStyle: {
                            normal: {
                                borderWidth: 5,
                                shadowBlur: 30,
                            }
                        }
                    }, 
                    
                    // {
                    //     value: 300,
                    //     name: '',
                    //     itemStyle: placeHolderStyle
                    // }
                    
                    );
                }
    
                myChart.setOption({
                    color: props.colors,
                    tooltip: {
                        trigger: 'item',
                        backgroundColor: 'rgba(47, 54, 60, 0.8)',
                        borderColor: '#2F363C',
                        textStyle: {
                            color: '#fff'
                        },
                        formatter: function (parms) {
                            var str =
                                parms.marker +
                                '' +
                                parms.data.name +
                                '</br>' +
                                '数量：' +
                                parms.data.value +
                                '</br>' +
                                '占比：' +
                                parms.percent +
                                '%'
                            return str
                        },
                    },
                    title: [{
                        text: '{name|' + props.title + '}\n{val|' + formatNumber(total) + '}',
                        top: 'center',
                        left: 'center',
                        textStyle: {
                            rich: {
                                name: {
                                    fontSize: 14,
                                    fontWeight: 'normal',
                                    color: '#FFFFFF',
                                    padding: [10, 0],
                                },
                                val: {
                                    fontSize: 20,
                                    fontWeight: 'bold',
                                    color: '#FFFFFF',
                                },
                            },
                        },
                    }, ],
                    series: [{
                        type: 'pie',
                        radius: ['70%', '85%'],
                        data: data,
                        hoverAnimation: true,
                        label: {
                            show: false,
                            position: 'outside',
                            formatter: '{b}\n{d}%',
                            textStyle: {
                                color: '#fff'
                            }
                        },
                        labelLine: {
                            normal: {
                                show: false,
                            },
                        },
                    }, ],
                })
                myChart.resize() //刷新画布
                window.addEventListener('resize', () => {
                    myChart.resize() //刷新画布
                })
            }
            onMounted(() => {
                init();
            })
            return {
                myEcharts
            }
        },
    }
    </script>
    
    <style lang="scss" scoped>
    .echart {
        width: 100%;
        height: 100%;
    }
    </style>
    