<template>
  <div class="history_chart" ref="myEcharts"></div>
</template>
<script>
import { onMounted, ref, inject, nextTick, watch } from 'vue'
export default {
  props: ['total','online'],
  setup(props) {
    const myEcharts = ref(null)
    let echarts = inject('ec') //引入

    watch(props, (newVal, oldVal) => {
      if (newVal) {
        initChart()
      }
    })
    onMounted(() => {
      nextTick(() => {
        initChart()
      })
    })
    const initChart = () => {
      var myChart = echarts.init(myEcharts.value)
      let option = {
        series: [
          {
            name: 'Line 1',
            type: 'pie',
            clockWise: true,
            radius: ['80%', '95%'],
            itemStyle: {
              normal: {
                label: {
                  show: false,
                },
                labelLine: {
                  show: false,
                },
              },
            },
            hoverAnimation: false,
            data: [
              {
                value: 78,
                name: '',
                itemStyle: {
                  normal: {
                    color: 'rgba(39, 237, 187, 0.3)',
                    label: {
                      show: false,
                    },
                    labelLine: {
                      show: false,
                    },
                  },
                },
              },
              {
                name: '',
                value: ((props.online / props.total)*100).toFixed(2),
                itemStyle: {
                  normal: {
                    color: '#27EDBB',
                  },
                },
              },
            ],
          },
        ],
      }
      myChart.setOption(option) //展示
      myChart.resize() //刷新画布
      window.addEventListener('resize', () => {
        myChart.resize() //刷新画布
      })
    }

    return {
      myEcharts,
      initChart,
    }
  },
}
</script>
<style lang="scss" scope>
.history_chart {
  width: 100%;
  height: 100%;
}
</style>
