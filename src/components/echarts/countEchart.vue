<template>
  <div class="echart" ref="myEcharts"></div>
</template>
<script>
import { ref, inject, onMounted, nextTick, watch } from 'vue'
export default {
  props: {
    xAxisData: {
      type: Array,
      default: () => {
        return []
      },
    },
    echartData: {
      type: Array,
      default: () => {
        return []
      },
    },
  },
  setup (props) {
    const myEcharts = ref(null)
    let echarts = inject('ec') //引入

    watch(props, (newVal, oldVal) => {
      if (newVal) {
        initChart()
      }
    })
    onMounted(() => {
      nextTick(() => {
        initChart()
      })
    })

    const initChart = () => {
      var myChart = echarts.init(myEcharts.value)
      myChart.setOption({
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          backgroundColor: 'rgba(47, 54, 60, 0.8)',
          borderColor: '#2F363C',
          textStyle: {
            fontSize: 12,
            lineHeight: 24,
            color: '#fff'
          },
        },
        grid: {
          left: '2%',
          top: '10',
          right: '2%',
          bottom: '5%',
          containLabel: true,
        },
        barWidth: 10,
        color: 'rgba(27, 192, 237, 1)',
        xAxis: {
          type: 'category',
          axisLine: {
            lineStyle: {
              color: '#778897'
            }
          },
          axisLabel: {
            margin: 10,
            color: '#778897',
            textStyle: {
              fontSize: 12
            },
          },
          axisTick: {
            show: false,
          },
          data: props.xAxisData,
        },
        yAxis: {
          type: 'value',
          splitLine: {
            show: false,
          },
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
        },
        series: [
          {
            data: props.echartData,
            type: 'bar',
            barWidth: '12px',
          },
        ],
      })
      myChart.resize() //刷新画布
      window.addEventListener('resize', () => {
        myChart.resize() //刷新画布
      })
    }
    return {
      myEcharts,
      initChart
    }
  },
}
</script>
<style lang="scss" scoped>
.echart {
  width: 100%;
  height: 100%;
}
</style>
