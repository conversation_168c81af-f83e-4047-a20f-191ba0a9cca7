<template>
<div class="echart" ref="myEcharts"></div>
</template>

<script>
import {
        ref,
        inject,
        onMounted,
        watch,
        nextTick
} from 'vue'
export default {
        props: {
                title: {
                        type: String,
                        default: ''
                },
                xAxisData: {
                        type: Array,
                        default: () => {
                                return []
                        },
                },
                echartData: {
                        type: Array,
                        default: () => {
                                return []
                        },
                },
        },
        setup(props) {
                const myEcharts = ref(null)
                let echarts = inject('ec') //引入

                watch(props, (newVal, oldVal) => {
                        if (newVal) initChart()
                })
                onMounted(() => {
                        nextTick(() => {
                                initChart()
                        })

                })
                const initChart = () => {
                        var myChart = echarts.init(myEcharts.value)
                        myChart.setOption({
                                title: {
                                        text: props.title,
                                        x: "0",
                                        y: "0",
                                        textStyle: {
                                                color: '#889CC3',
                                                fontSize: '16'
                                        }
                                },
                                grid: {
                                        containLabel: true,
                                        left: '5%',
                                        top: '0',
                                        bottom: '5%',
                                        right: '5%',
                                },
                                xAxis: {
                                        data: props.xAxisData,
                                        axisLine: {
                                                show: false
                                        },
                                        axisTick: {
                                                show: false,
                                        },
                                        axisLabel: {
                                                color: '#778897',
                                                fontSize: 14,
                                        },
                                },
                                yAxis: {
                                        axisLine: {
                                                show: false,
                                        },
                                        axisLabel: {
                                                show: false,
                                        },
                                        splitLine: {
                                                show: false,
                                        },
                                        axisTick: {
                                                show: false,
                                        },
                                        interval: 500,
                                },
                                series: [{
                                        type: 'bar',
                                        barWidth: 14,
                                        itemStyle: {
                                                normal: {
                                                        color: new echarts.graphic.LinearGradient(
                                                                0,
                                                                0,
                                                                0,
                                                                1,
                                                                [{
                                                                                offset: 0,
                                                                                color: 'rgba(66, 83, 229, 0.3)',
                                                                        },
                                                                        {
                                                                                offset: 1,
                                                                                color: '#4253E5',
                                                                        },
                                                                ],
                                                                false
                                                        ),
                                                },
                                        },
                                        label: {
                                                normal: {
                                                        show: true,
                                                        fontSize: 14,
                                                        fontWeight: '400',
                                                        color: '#ffffff',
                                                        position: 'top',
                                                },
                                        },
                                        data: props.echartData,
                                }, ],
                        })
                        myChart.resize() //刷新画布
                        window.addEventListener('resize', () => {
                                myChart.resize() //刷新画布
                        })
                }

                return {
                        myEcharts,
                        initChart
                }
        },
}
</script>

<style lang="scss" scoped>
.echart {
        width: 100%;
        height: 100%;
}
</style>
