<template>
  <div class="history_chart" ref="myEcharts"></div>
</template>

<script>
import {
  onMounted,
  ref,
  inject,
  nextTick,
  watch
} from 'vue'
export default {
  props: ['xAxisData', 'seriesData', 'seriesName'],
  setup (props) {
    const myEcharts = ref(null)
    let echarts = inject('ec') //引入

    watch(props, (newVal, oldVal) => {
      if (newVal) {
        initChart()
      }
    })
    onMounted(() => {
      nextTick(() => {
        initChart()
      })
    })
    const initChart = () => {
      var myChart = echarts.init(myEcharts.value)
      var series = [];
      for (var i = 0; i < props.seriesName.length; i++) {
        series.push({
          name: props.seriesName[i].label,
          type: 'bar',
          stack: '1',
          barMaxWidth: 30,
          barGap: '10%',
          itemStyle: {
            normal: {
              color: props.seriesName[i].color,
              opacity: 1,
            },
          },
          data: props.seriesData[i]
        })
      }
      let option = {
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(47, 54, 60, 0.8)',
          borderColor: '#2F363C',
          textStyle: {
            color: '#fff'
          },
          axisPointer: {
            type: 'shadow',
            textStyle: {
              color: '#fff',
            },
          },
        },
        grid: {
          borderWidth: 0,
          top: 40,
          bottom: 80,
          left: 55,
          right: 45,
        },
        calculable: true,
        xAxis: [{
          type: 'category',
          axisLine: {
            lineStyle: {
              color: 'rgba(255,255,255,.1)',
            },
          },
          splitLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          splitArea: {
            show: false,
          },
          axisLabel: {
            interval: 0,
            color: '#778897',
            fontSize: 12,
          },
          data: props.xAxisData,
        },],
        yAxis: [{
          name: '',
          nameTextStyle: {
            color: '#FDFDFD',
            padding: [0, 0, 0, -50],
          },
          nameGap: 15,
          type: 'value',
          splitLine: {
            show: true,
            lineStyle: {
              color: 'RGBA(255,255,255, 0.1)',
            },
          },
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            interval: 0,
            color: 'rgba(255,255,255,0.5)',
            fontSize: 10,
          },
          splitArea: {
            show: false,
          },
        },],
        series: series
      }
      myChart.setOption(option) //展示
      myChart.resize() //刷新画布
      window.addEventListener('resize', () => {
        myChart.resize() //刷新画布
      })
    }

    return {
      myEcharts,
      initChart,
    }
  },
}
</script>

<style lang="scss">
.history_chart {
  width: 100%;
  height: 100%;
}
</style>
