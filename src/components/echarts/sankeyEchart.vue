<template>
<div class="history_chart" ref="myEcharts"></div>
</template>

<script>
import {
        onMounted,
        ref,
        inject,
        nextTick,
        watch
} from 'vue'
export default {
        props: ['seriesData'],
        setup(props) {
                const myEcharts = ref(null)
                let echarts = inject('ec') //引入

                watch(props.seriesData, (newVal, oldVal) => {
                        if (newVal) {
                                initChart()
                        }
                })
                onMounted(() => {
                        nextTick(() => {
                                initChart()
                        })
                })
                const initChart = () => {
                        var myChart = echarts.init(myEcharts.value)
                        let option = {
                                tooltip: {
                                        trigger: 'item',
                                        backgroundColor: 'rgba(47, 54, 60, 0.8)',
                                        borderColor: '#2F363C',
                                        textStyle: {
                                                color: '#fff'
                                        },
                                },
                                series: [{
                                        type: 'tree',
                                        name: 'Total Command List',
                                        data: props.seriesData,
                                        top: '2%',
                                        bottom: '250',
                                        left: "5",
                                        right: "10",
                                        symbol: 'circle',
                                        containLabel: true,
                                        symbolSize: 7,
                                        orient: 'vertical',
                                        initialTreeDepth: 4,
                                        zoom: 1,
                                        roam: true,
                                        scaleLimit: {
                                                min: 0.5,
                                                max: 10
                                        },
                                        label: {
                                                normal: {
                                                        position: 'right',
                                                        verticalAlign: 'middle',
                                                        align: 'left',
                                                        fontSize: 12,
                                                        rotate: -90,
                                                        color: 'white',
                                                }
                                        },
                                        itemStyle: {
                                                normal: {
                                                        lineStyle: {
                                                                color: "#616365a6"
                                                        }
                                                }
                                        },
                                        leaves: {
                                                label: {
                                                        normal: {
                                                                position: 'bottom',
                                                                verticalAlign: 'middle',
                                                                rotate: -90,
                                                                align: 'bottom'
                                                        }
                                                }
                                        },

                                        expandAndCollapse: true,

                                        animationDuration: 550,
                                        animationDurationUpdate: 750
                                }]
                        }
                        myChart.setOption(option) //展示
                        myChart.resize() //刷新画布
                        window.addEventListener('resize', () => {
                                myChart.resize() //刷新画布
                        })
                }

                return {
                        myEcharts,
                        initChart,
                }
        },
}
</script>

<style lang="scss">
.history_chart {
        width: 100%;
        height: 100%;
}
</style>
