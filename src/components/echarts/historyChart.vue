<template>
<div class="history_chart" ref="myEcharts"></div>
</template>

<script>
import {
    onMounted,
    ref,
    inject,
    nextTick,
    watch
} from 'vue'
export default {
    props: ['historyData'],
    setup(props) {
        const myEcharts = ref(null)
        let echarts = inject('ec') //引入

        watch(props, (newVal, oldVal) => {
            if (newVal) {
                initChart()
            }
        })
        onMounted(() => {
            nextTick(() => {
                initChart()
            })
        })
        const initChart = () => {
            var myChart = echarts.init(myEcharts.value)
            let option = {
                tooltip: {
                    trigger: 'axis',
                    backgroundColor: 'rgba(47, 54, 60, 0.8)',
                    borderColor: '#2F363C',
                    textStyle: {
                        color: '#fff'
                    },
                    axisPointer: {
                        type: 'shadow'
                    },
                },
                grid: {
                    top: '10%',
                    bottom: 40,
                    left: 20,
                    right: 20,
                    textStyle: {
                        color: '#fff',
                    },
                    containLabel: true,
                },
                calculable: true,
                xAxis: [{
                    type: 'category',
                    axisLine: {
                        lineStyle: {
                            color: '#1B2125',
                        },
                    },
                    axisLabel: {
                        color: '#778897',
                    },
                    splitLine: {
                        show: false,
                    },
                    axisTick: {
                        show: false,
                    },
                    data: props.historyData.time,
                }, ],

                yAxis: [{
                    type: 'value',
                    splitLine: {
                        show: false,
                    },
                    axisLine: {
                        lineStyle: {
                            color: '#ffffff',
                        },
                    },
                }, ],
                "dataZoom": [{
                    "show": true,
                    "height": 12,
                    "xAxisIndex": [
                        0
                    ],
                    bottom: '2%',
                    "start": 10,
                    "end": 90,
                    handleSize: '110%',
                    handleStyle: {
                        color: "#d3dee5",
                    },
                    textStyle: {
                        color: "#fff"
                    },
                    borderColor: "#90979c"
                }, {
                    "type": "inside",
                    "show": true,
                    "height": 15,
                    "start": 1,
                    "end": 35
                }],
                series: [{
                    name: props.historyData.name,
                    type: 'line',
                    symbolSize: 10,
                    symbol: 'circle',
                    itemStyle: {
                        color: '#3E82D2',
                    },
                    markPoint: {
                        label: {
                            normal: {
                                textStyle: {
                                    color: '#fff',
                                },
                            },
                        },
                        data: [{
                                type: 'max',
                                name: '最大值',
                            },
                            {
                                type: 'min',
                                name: '最小值',
                            },
                        ],
                    },
                    data: props.historyData.data,
                }]
            }
            myChart.setOption(option) //展示
            myChart.resize() //刷新画布
            window.addEventListener('resize', () => {
                myChart.resize() //刷新画布
            })
        }

        return {
            myEcharts,
            initChart,
        }
    },
}
</script>

<style lang="scss">
.history_chart {
    width: 100%;
    height: 100%;
}
</style>
