<template>
<div class="history_chart" ref="myEcharts"></div>
</template>

<script>
import {
        onMounted,
        ref,
        inject,
        nextTick,
        watch
} from 'vue'
export default {
        props: ['seriesData', 'radarData'],
        setup(props) {
                const myEcharts = ref(null)
                let echarts = inject('ec') //引入

                watch(props, (newVal, oldVal) => {
                        if (newVal) {
                                initChart()
                        }
                })
                onMounted(() => {
                        nextTick(() => {
                                initChart()
                        })
                })
                const initChart = () => {
                        var myChart = echarts.init(myEcharts.value)
                        let option = {
                                title: {
                                        text: '健康度分析',
                                        bottom: '10%',
                                        left: 'center',
                                        textStyle: {
                                                color: '#889CC3',
                                                fontWeight: 500,
                                                fontSize: 16
                                        }
                                },
                                radar: {
                                        center: ['50%', '50%'],
                                        radius: '60%',
                                        shape: 'circle',
                                        splitNumber: 5,
                                        name: {
                                                textStyle: {
                                                        color: '#fff',
                                                        borderRadius: 3,
                                                        fontSize: 12,
                                                },
                                        },
                                        nameGap: '5',
                                        indicator: props.radarData,
                                        // 圈圈网颜色
                                        splitLine: {
                                                show: true,
                                                lineStyle: {
                                                        color: [
                                                                '#4A5966',
                                                                '#4A5966',
                                                                '#4A5966',
                                                                '#4A5966',
                                                                '#4A5966',
                                                        ],
                                                        width: '1',
                                                },
                                        },
                                        splitArea: {
                                                areaStyle: {
                                                        color: 'rgba(119, 140, 162, 0.14)',
                                                },
                                        },
                                        axisLine: {
                                                show: true,
                                                lineStyle: {
                                                        color: 'rgba(0, 233, 234, 0.30196078431372547)',
                                                },
                                        },
                                },
                                series: [{
                                        name: '',
                                        type: 'radar',
                                        symbol: 'circle',
                                        symbolSize: '8',
                                        label: {
                                                show: false,
                                                // 让它的点没有显示格式
                                                formatter: '',
                                                color: '#fff',
                                                fontStyle: {
                                                        fontSize: 33,
                                                },
                                        },
                                        data: [{
                                                value: props.seriesData,
                                                name: '健康态势',
                                                lineStyle: {
                                                        //网调线
                                                        color: '#6CFEFF',
                                                        // color: "rgba(108, 254, 255, 0.4)",
                                                        background: 'radial-gradient(circle, rgba(108, 254, 255, 0) 0%, rgba(108, 254, 255, 0.4) 100%)',
                                                },
                                                symbolSize: 8, //圆圈大小
                                                itemStyle: {
                                                        //调点的样式
                                                        show: true,
                                                        color: 'rgba(108, 254, 255, 1)',
                                                        shadowColor: 'rgba(108, 254, 255, 1)',
                                                        shadowBlur: 10,
                                                        fontSize: 28,
                                                },
                                                areaStyle: {
                                                        // 内网颜色
                                                        normal: {
                                                                color: {
                                                                        type: 'radial',
                                                                        colorStops: [{
                                                                                        offset: 0,
                                                                                        color: 'rgba(19, 212, 217, 0)',
                                                                                },
                                                                                {
                                                                                        offset: 1,
                                                                                        color: 'rgba(19, 212, 217, 0.4)',
                                                                                },
                                                                        ],
                                                                        globalCoord: false,
                                                                },
                                                                opacity: 1,
                                                        },
                                                },
                                        }, ],
                                }, ],
                        }
                        myChart.setOption(option) //展示
                        myChart.resize() //刷新画布
                        window.addEventListener('resize', () => {
                                myChart.resize() //刷新画布
                        })
                }

                return {
                        myEcharts,
                        initChart,
                }
        },
}
</script>

<style lang="scss">
.history_chart {
        width: 100%;
        height: 100%;
}
</style>
