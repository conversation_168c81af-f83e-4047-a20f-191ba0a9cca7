<template>
  <div class="columnar_chart" ref="columnarChart"></div>
</template>

<script>
import { inject, onMounted, ref, nextTick, watch } from 'vue'
export default {
  props: {
    xAxisData: {
      type: Array,
      default: () => {
        return []
      },
    },
    seriesData: {
      type: Array,
      default: () => {
        return []
      },
    }
  },
  setup (props) {
    const columnarChart = ref(null)
    let echarts = inject('ec')

    onMounted(() => {
      nextTick(() => {
        initChart()
      })
    })
    watch(props, (newVal, oldVal) => {
      if (newVal) initChart()
    })
    const initChart = () => {
      let option = {
        title: {
          text: '个',
          left: '18',
          top: '20',
          textStyle: {
            color: '#fff',
            fontSize: 12,
            fontWeight: '400',
          },
        },
        color: ['#27EDBB', '#4253E5', '#B4061A'],
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(47, 54, 60, 0.8)',
          borderColor: '#2F363C',
          textStyle: {
            color: '#fff'
          },
          axisPointer: {
            type: 'cross',
            crossStyle: {
              color: '#999',
            },
            lineStyle: {
              type: 'dashed',
            },
          },
        },
        grid: {
          left: 25,
          right: 25,
          bottom: 24,
          top: 65,
          containLabel: true,
        },
        legend: {
          itemWidth: 14,
          itemHeight: 4,
          textStyle: {
            color: '#fff',
          },
          data: ['普通', '紧急', '严重'],
          orient: 'horizontal',
          icon: 'rect',
          show: true,
          right: 20,
          top: 25,
        },
        xAxis: {
          type: 'category',
          data: props.xAxisData,
          splitLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          axisLine: {
            show: false,
          },
          axisLabel: {
            color: 'rgba(255,255,255,0.8)',
          },
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            color: 'rgba(255,255,255,0.8)',
            textStyle: {
              fontSize: 12,
            },
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#515558',
            },
          },
          axisTick: {
            show: false,
          },
          axisLine: {
            show: false,
          },
        },
        series: [
          {
            name: '普通',
            type: 'line',
            data: props.seriesData[0],
          },
          {
            name: '紧急',
            type: 'line',
            data: props.seriesData[1],
          },
          {
            name: '严重',
            type: 'line',
            data: props.seriesData[2]
          },
        ],
      }
      var myChart = echarts.init(columnarChart.value)
      myChart.setOption(option) //展示
      myChart.resize() //刷新画布
      window.addEventListener('resize', () => {
        myChart.resize() //刷新画布
      })
    }
    return {
      columnarChart,
      initChart,
    }
  },
}
</script>

<style>
.columnar_chart {
  width: 100%;
  height: 100%;
}
</style>
