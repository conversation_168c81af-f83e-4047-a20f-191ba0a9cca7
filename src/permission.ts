import router from './router'
import store from './store'
import { ElMessage } from 'element-plus'

import { getCookie } from '@/utils/cookie' // get token from cookie
import getPageTitle from '@/utils/get-page-title'



const whiteList = ['/login', '/register'] // no redirect whitelist

router.beforeEach(async (to, from, next) => {

  document.title = getPageTitle(to.meta.title)

  const hasToken = getCookie("gh_token");
  const projectId = getCookie('gh_projectId');

  if (hasToken) {
    if (to.path === '/login') {
      if (projectId) {
        next({ path: '/home' })
      } else {
        next()
      }
    } else {
      const hasGetUserInfo = store.getters.name
      if (hasGetUserInfo) {
        next()
      } else {
        try {
          next();
        } catch (error:any) {
          await store.dispatch('user/resetToken')
          ElMessage.error(error || 'Has Error')
          next(`/login?redirect=${to.path}`)
        }
      }
    }
  } else {
    /* has no token*/
    if (whiteList.indexOf(to.path) !== -1) {
      // in the free login whitelist, go directly
      next()
    } else {
      // other pages that do not have permission to access are redirected to the login page.
      next(`/login?redirect=${to.path}`)
    }
  }
})

