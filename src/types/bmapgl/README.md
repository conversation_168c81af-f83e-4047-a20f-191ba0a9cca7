# Installation
> `npm install --save @types/bmapgl`

# Summary
This package contains type definitions for bmapgl-browser (http://lbsyun.baidu.com/index.php?title=jspopularGL).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/bmapgl.

### Additional Details
 * Last updated: Wed, 16 Sep 2020 22:59:19 GMT
 * Dependencies: none
 * Global values: `APE_RHOMBUS`, `APE_SQUARE`, `APE_STAR`, `APE_WATERDROP`, `BMAPGL_NORMAL_MAP`, `BMAP_ANCHOR_BOTTOM_LEFT`, `BMAP_ANCHOR_BOTTOM_RIGHT`, `BMAP_ANCHOR_TOP_LEFT`, `BMAP_ANCHOR_TOP_RIGHT`, `BMAP_ANIMATION_BOUNCE`, `BMAP_ANIMATION_DROP`, `BMAP_API_VERSION`, `BMAP_CONTEXT_MENU_ICON_ZOOMIN`, `BMAP_CONTEXT_MENU_ICON_ZOOMOUT`, `BMAP_DRAWING_CIRCLE`, `BMAP_DRAWING_MARKER`, `BMAP_DRAWING_POLYGON`, `BMAP_DRAWING_POLYLINE`, `BMAP_DRAWING_RECTANGLE`, `BMAP_DRIVING_POLICY_AVOID_HIGHWAYS`, `BMAP_DRIVING_POLICY_LEAST_DISTANCE`, `BMAP_DRIVING_POLICY_LEAST_TIME`, `BMAP_EARTH_MAP`, `BMAP_HIGHLIGHT_ROUTE`, `BMAP_HIGHLIGHT_STEP`, `BMAP_HYBRID_MAP`, `BMAP_LINE_TYPE_BUS`, `BMAP_LINE_TYPE_FERRY`, `BMAP_LINE_TYPE_SUBWAY`, `BMAP_MAPTYPE_CONTROL_DROPDOWN`, `BMAP_MAPTYPE_CONTROL_HORIZONTAL`, `BMAP_MAPTYPE_CONTROL_MAP`, `BMAP_NAVIGATION_CONTROL_LARGE`, `BMAP_NAVIGATION_CONTROL_PAN`, `BMAP_NAVIGATION_CONTROL_SMALL`, `BMAP_NAVIGATION_CONTROL_ZOOM`, `BMAP_NORMAL_MAP`, `BMAP_PANORAMA_INDOOR_SCENE`, `BMAP_PANORAMA_POI_CATERING`, `BMAP_PANORAMA_POI_HOTEL`, `BMAP_PANORAMA_POI_INDOOR_SCENE`, `BMAP_PANORAMA_POI_MOVIE`, `BMAP_PANORAMA_POI_NONE`, `BMAP_PANORAMA_POI_TRANSIT`, `BMAP_PANORAMA_STREET_SCENE`, `BMAP_PERSPECTIVE_MAP`, `BMAP_POINT_DENSITY_HIGH`, `BMAP_POINT_DENSITY_LOW`, `BMAP_POINT_DENSITY_MEDIUM`, `BMAP_POINT_SHAPE_CIRCLE`, `BMAP_POINT_SIZE_BIG`, `BMAP_POINT_SIZE_BIGGER`, `BMAP_POINT_SIZE_HUGE`, `BMAP_POINT_SIZE_NORMAL`, `BMAP_POINT_SIZE_SMALL`, `BMAP_POINT_SIZE_SMALLER`, `BMAP_POINT_SIZE_TINY`, `BMAP_POI_TYPE_BUSSTOP`, `BMAP_POI_TYPE_NORMAL`, `BMAP_POI_TYPE_SUBSTOP`, `BMAP_ROUTE_TYPE_DRIVING`, `BMAP_ROUTE_TYPE_WALKING`, `BMAP_SATELLITE_MAP`, `BMAP_STATUS_CITY_LIST`, `BMAP_STATUS_INVALID_KEY`, `BMAP_STATUS_INVALID_REQUEST`, `BMAP_STATUS_PERMISSION_DENIED`, `BMAP_STATUS_SERVICE_UNAVAILABLE`, `BMAP_STATUS_SUCCESS`, `BMAP_STATUS_TIMEOUT`, `BMAP_STATUS_UNKNOWN_LOCATION`, `BMAP_STATUS_UNKNOWN_ROUTE`, `BMAP_TRANSIT_POLICY_AVOID_SUBWAYS`, `BMAP_TRANSIT_POLICY_LEAST_TIME`, `BMAP_TRANSIT_POLICY_LEAST_TRANSFER`, `BMAP_TRANSIT_POLICY_LEAST_WALKING`, `BMAP_UNIT_IMPERIAL`, `BMAP_UNIT_METRIC`, `BMAP_ZOOM_IN`, `BMAP_ZOOM_OUT`, `BMapGL`, `BMapGLLib`, `BMap_Symbol_SHAPE_BACKWARD_CLOSED_ARROW`, `BMap_Symbol_SHAPE_BACKWARD_OPEN_ARROW`, `BMap_Symbol_SHAPE_CAMERA`, `BMap_Symbol_SHAPE_CIRCLE`, `BMap_Symbol_SHAPE_CLOCK`, `BMap_Symbol_SHAPE_FORWARD_CLOSED_ARROW`, `BMap_Symbol_SHAPE_FORWARD_OPEN_ARROW`, `BMap_Symbol_SHAPE_PLANE`, `BMap_Symbol_SHAPE_POINT`, `BMap_Symbol_SHAPE_RECTANGLE`, `BMap_Symbol_SHAPE_RHOMBUS`, `BMap_Symbol_SHAPE_SMILE`, `BMap_Symbol_SHAPE_STAR`, `BMap_Symbol_SHAPE_WARNING`, `MapVGL`

# Credits
These definitions were written by [Junior2ran](http://github.com/Junior2ran).
