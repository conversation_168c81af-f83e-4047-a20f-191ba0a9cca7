import {
  createRouter,
  createWebHashHistory
} from "vue-router";
import Layout from "@/views/Layout/index.vue";

const routes = [
  {
    path: "/",
    name: "dashboard",
    component: Layout,
    hidden: true,
    redirect: "/overview", //  /home  兼容原来版本路由
    children: [
  
      {
        path: "home",
        name: "home",
        redirect: "/overview", //兼容原来版本路由
        component: () => import("@/views/Home/index.vue"),
      },
      {
        path: "overview",
        name: "overview",
        component: () => import("@/overview/index.vue"),
        meta: {
          title: "总览",
          noCache: true,
        },
      }, 

    ],
  },
  {
    path: "/login",
    name: "Login",
    hidden: false,
    component: () => import("@/views/Login/Login.vue"),
  },
  {
    path: "/404",
    name: "404",
    hidden: false,
    component: () => import("@/views/404.vue"),
  },
  {
    path: "/:catchAll(.*)", //重定向
    redirect: "/404",
  },
];
const router = createRouter({
  history: createWebHashHistory(),
  routes: routes,
});

export default router;
