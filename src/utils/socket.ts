const socket = {

    subscribe(socket: ({ connected: any; emit: (arg0: string, arg1: string) => void; }|null), batchDefinitionId: any, clientId: any, requestItems: any) {
        if (socket && socket.connected) {
            socket.emit('Subscribe', JSON.stringify({ batchDefinitionId, clientId, requestItems }));
        }
    },

    unsubscribe(socket: ({ connected: any; emit: (arg0: string, arg1: any, arg2: any) => void; }|null), clientid: any, batchid: any) {
        if (socket && socket.connected) {
            socket.emit('UnSubscribeBatch', clientid, batchid);
        }
    },
    writeValue(socket: ({ connected: any; emit: (arg0: string, arg1: string,arg2:any,arg3:any) => void; }|null),
     variable: string, value: any,batchDefinitionId: any, clientId: any,projectId:any,userId:any) {
        if (socket && socket.connected) {
            let data = {
                batchDefinitionId,
                clientId,
                area: '1',
                level: 1,
                requestItems: [{
                    "iosvrKey": variable.split(':')[0],
                    "chlKey": variable.split(':')[1],
                    "ctrlKey": variable.split(':')[2],
                    "varKey": variable.split(':')[3],
                    "value": value,
                    "level": 1,
                    "id": "id",
                    "propName": 'Value',
                }]
            }

            socket.emit('WriteVarValue', JSON.stringify(data),projectId,userId);
        }
    },



    ManualWrite(socket: { connected: any; emit: (arg0: string, arg1: any, arg2: any, arg3: any, arg4: any, arg5: any, arg6: any, arg7: any, arg8: any, arg9: any, arg10: any) => void; }, value: any, variable: any, deviceName: any, standardName: any, userId: any,
        menuId: any, batchId: any, clientId: any, id: any, projectId: any) {
        if (socket && socket.connected) {

            socket.emit('ManualWrite', value, variable, deviceName, standardName, userId,
                menuId, batchId, clientId, id, projectId);
        }
    },
    ProcessAlarm(socket: { emit: (arg0: string, arg1: any, arg2: any, arg3: any, arg4: any, arg5: any) => void; }, variable: any, uuid: any, content: any, userId: any, projectId: any) {
        socket.emit('ProcessAlarm', variable, uuid, content, userId, projectId);
    },
    SnapPhoto(socket: { emit: (arg0: string, arg1: any) => void; }, variable: any) {
        socket.emit('SnapPhoto', variable);
    },
    SnapVideo(socket: { emit: (arg0: string, arg1: any) => void; }, variable: any) {
        socket.emit('SnapVideo', variable);
    },
}

export default socket;