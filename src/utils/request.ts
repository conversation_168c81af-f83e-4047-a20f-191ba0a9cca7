import { ElMessage } from 'element-plus';
import { getCookie } from '@/utils/cookie'
import axios, { AxiosResponse, AxiosRequestConfig } from 'axios'

const service = axios.create({
        timeout: 10000,// 超时时间
        baseURL: process.env.NODE_ENV === 'production' ? window.PROD_BASE_API : window.DEV_BASE_API
})
service.interceptors.request.use(
        (config: AxiosRequestConfig) => {
                if (getCookie('gh_token')) {
                        config.headers['Authorization'] = "bearer " + getCookie("gh_token");
                }
                config.headers['project'] = getCookie('gh_projectId') || -1;
                return config
        },
        (error: any) => {
                console.log(error) // for debug
                return Promise.reject(error)
        }
)
service.interceptors.response.use(
        (response: AxiosResponse) => {
                const res = response.data
                if (!res.success && (res.config && res.config.type !== 'blob')) {
                        ElMessage.error(res.msg);
                        return Promise.reject(new Error(res.msg || 'Error'))
                } else if (!res.success&&!res.type) {
                        ElMessage.error(res.msg);
                        return false;
                }
                else {
                        return res
                }
        },
        (error: any) => {
                return Promise.reject(error)
        }
)
export default service