
const path = require('path')

function resolve(dir) {
    return path.join(__dirname, dir)
}
// 1. 引入等比适配插件
const px2rem = require('postcss-px2rem')

// 2. 配置基本大小
const postcss = px2rem({
    // 基准大小 baseSize，需要和rem.js中相同
    remUnit: 100
})
module.exports = {
    publicPath: './',
    devServer: {
        // open:true,
        // port:8081,
        // disableHostCheck:true

        before(app) {
            app.get("*.gz", function (req, res, next) {
                res.setHeader("Content-Encoding", "gzip");
                next();
            });
        },

    },
    css: {
        loaderOptions: {
            // 等比缩放的插件
            postcss: {
                plugins: [
                    postcss
                ]
            }
        }
    },
    configureWebpack: {
        devtool:"source-map",
        resolve: {
            extensions: [".ts", ".tsx", ".js", '.vue', '.json'],
            alias: {
                '@': resolve('src')
            }
        },
        module: {
            rules: [
                {
                    test: /\.mjs$/,
                    include: /node_modules/,
                    type: "javascript/auto"
                }
            ]
        }
    },

}